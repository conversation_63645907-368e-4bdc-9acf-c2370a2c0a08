(()=>{var e={};e.id=6158,e.ids=[6158],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23908:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>q});var r={};s.r(r),s.d(r,{GET:()=>d,POST:()=>p});var i=s(26142),o=s(94327),n=s(34862),a=s(37838),u=s(18815),c=s(26239);async function d(){try{let{userId:e}=await (0,a.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});let s=await u.database.extensionSession.findMany({where:{userId:t.id,isActive:!0,lastActiveAt:{gte:new Date(Date.now()-3e5)}},orderBy:{lastActiveAt:"desc"}}),r=await u.database.usageMetrics.aggregate({where:{userId:t.id,date:{gte:new Date(Date.now()-864e5)}},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),i=await u.database.usageMetrics.aggregate({where:{userId:t.id},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),o=s.length>0,n=s[0],d={status:{connected:o,lastActive:n?.lastActiveAt||null,activeSessions:s.length,health:o?"healthy":"disconnected"},user:{id:e,subscriptionTier:t.subscriptionTier||"FREE",subscriptionStatus:t.subscriptionStatus||"ACTIVE",extensionEnabled:t.extensionEnabled||!1},usage:{recent24h:{tokensUsed:r._sum.tokensUsed||0,requestsMade:r._sum.requestsMade||0,costAccrued:r._sum.costAccrued||0},total:{tokensUsed:i._sum.tokensUsed||0,requestsMade:i._sum.requestsMade||0,costAccrued:i._sum.costAccrued||0}},sessions:s.map(e=>({id:e.id,sessionId:e.sessionId,extensionVersion:e.extensionVersion,vscodeVersion:e.vscodeVersion,platform:e.platform,lastActiveAt:e.lastActiveAt,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade})),serverTime:new Date().toISOString()};return c.NextResponse.json(d)}catch(e){return console.error("Extension status error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(){try{let{userId:e}=await (0,a.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});return await u.database.user.update({where:{id:t.id},data:{lastActiveAt:new Date}}),c.NextResponse.json({success:!0,timestamp:new Date().toISOString(),message:"Heartbeat received"})}catch(e){return console.error("Extension heartbeat error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/extension/status/route",pathname:"/api/extension/status",filename:"route",bundlePath:"app/api/extension/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\status\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:q,serverHooks:h}=x;function v(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:q})}},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(23908));module.exports=r})();