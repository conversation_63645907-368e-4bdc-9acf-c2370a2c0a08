exports.id=9652,exports.ids=[9652],exports.modules={446:(e,t,n)=>{"use strict";n.d(t,{CreateOrganization:()=>r,GoogleOneTap:()=>s,OrganizationList:()=>i,OrganizationProfile:()=>a,OrganizationSwitcher:()=>l,PricingTable:()=>c,SignIn:()=>d,SignInButton:()=>u,SignInWithMetamaskButton:()=>m,SignOutButton:()=>p,SignUp:()=>f,SignUpButton:()=>b,UserButton:()=>C,UserProfile:()=>h,Waitlist:()=>g});var o=n(6340);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call CreateOrganization() from the server but CreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","CreateOrganization"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call GoogleOneTap() from the server but GoogleOneTap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","GoogleOneTap"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call OrganizationList() from the server but OrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","OrganizationList"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call OrganizationProfile() from the server but OrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","OrganizationProfile"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call OrganizationSwitcher() from the server but OrganizationSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","OrganizationSwitcher"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call PricingTable() from the server but PricingTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","PricingTable"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignIn() from the server but SignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignIn"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignInButton() from the server but SignInButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignInButton"),m=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignInWithMetamaskButton() from the server but SignInWithMetamaskButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignInWithMetamaskButton"),p=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignOutButton() from the server but SignOutButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignOutButton"),f=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignUp() from the server but SignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignUp"),b=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignUpButton() from the server but SignUpButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignUpButton"),C=(0,o.registerClientReference)(function(){throw Error("Attempted to call UserButton() from the server but UserButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","UserButton"),h=(0,o.registerClientReference)(function(){throw Error("Attempted to call UserProfile() from the server but UserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","UserProfile"),g=(0,o.registerClientReference)(function(){throw Error("Attempted to call Waitlist() from the server but Waitlist is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","Waitlist")},5027:(e,t,n)=>{"use strict";let o;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{arrayBufferToString:function(){return a},decrypt:function(){return d},encrypt:function(){return c},getActionEncryptionKey:function(){return b},getClientReferenceManifestForRsc:function(){return f},getServerModuleMap:function(){return p},setReferenceManifestsSingleton:function(){return m},stringToUint8Array:function(){return l}});let r=n(888),s=n(74369),i=n(29294);function a(e){let t=new Uint8Array(e),n=t.byteLength;if(n<65535)return String.fromCharCode.apply(null,t);let o="";for(let e=0;e<n;e++)o+=String.fromCharCode(t[e]);return o}function l(e){let t=e.length,n=new Uint8Array(t);for(let o=0;o<t;o++)n[o]=e.charCodeAt(o);return n}function c(e,t,n){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,n)}function d(e,t,n){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,n)}let u=Symbol.for("next.server.action-manifests");function m({page:e,clientReferenceManifest:t,serverActionsManifest:n,serverModuleMap:o}){var r;let i=null==(r=globalThis[u])?void 0:r.clientReferenceManifestsPerPage;globalThis[u]={clientReferenceManifestsPerPage:{...i,[(0,s.normalizeAppPath)(e)]:t},serverActionsManifest:n,serverModuleMap:o}}function p(){let e=globalThis[u];if(!e)throw Object.defineProperty(new r.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function f(){let e=globalThis[u];if(!e)throw Object.defineProperty(new r.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,n=i.workAsyncStorage.getStore();if(!n){var o=t;let e=Object.values(o),n={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)n.clientModules={...n.clientModules,...t.clientModules},n.edgeRscModuleMapping={...n.edgeRscModuleMapping,...t.edgeRscModuleMapping},n.rscModuleMapping={...n.rscModuleMapping,...t.rscModuleMapping};return n}let s=t[n.route];if(!s)throw Object.defineProperty(new r.InvariantError(`Missing Client Reference Manifest for ${n.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return s}async function b(){if(o)return o;let e=globalThis[u];if(!e)throw Object.defineProperty(new r.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new r.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return o=await crypto.subtle.importKey("raw",l(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},5768:(e,t,n)=>{"use strict";n.d(t,{PromisifiedAuthProvider:()=>r});var o=n(6340);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call PromisifiedAuthProvider() from the server but PromisifiedAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js","PromisifiedAuthProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call usePromisifiedAuth() from the server but usePromisifiedAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js","usePromisifiedAuth")},13342:(e,t,n)=>{"use strict";n.d(t,{useAuth:()=>r,useClerk:()=>s,useEmailLink:()=>i,useOrganization:()=>a,useOrganizationList:()=>l,useReverification:()=>c,useSession:()=>d,useSessionList:()=>u,useSignIn:()=>m,useSignUp:()=>p,useUser:()=>f});var o=n(6340);(0,o.registerClientReference)(function(){throw Error("Attempted to call EmailLinkErrorCode() from the server but EmailLinkErrorCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","EmailLinkErrorCode"),(0,o.registerClientReference)(function(){throw Error("Attempted to call EmailLinkErrorCodeStatus() from the server but EmailLinkErrorCodeStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","EmailLinkErrorCodeStatus"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isClerkAPIResponseError() from the server but isClerkAPIResponseError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isClerkAPIResponseError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isClerkRuntimeError() from the server but isClerkRuntimeError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isClerkRuntimeError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isEmailLinkError() from the server but isEmailLinkError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isEmailLinkError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isKnownError() from the server but isKnownError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isKnownError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isMetamaskError() from the server but isMetamaskError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isMetamaskError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isReverificationCancelledError() from the server but isReverificationCancelledError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isReverificationCancelledError");let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useAuth"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call useClerk() from the server but useClerk is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useClerk"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call useEmailLink() from the server but useEmailLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useEmailLink"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call useOrganization() from the server but useOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useOrganization"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call useOrganizationList() from the server but useOrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useOrganizationList"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call useReverification() from the server but useReverification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useReverification"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSession"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSessionList() from the server but useSessionList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSessionList"),m=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSignIn() from the server but useSignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSignIn"),p=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSignUp() from the server but useSignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSignUp"),f=(0,o.registerClientReference)(function(){throw Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useUser")},19593:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ClientClerkProvider:()=>o});let o=(0,n(6340).registerClientReference)(function(){throw Error("Attempted to call ClientClerkProvider() from the server but ClientClerkProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\ClerkProvider.js","ClientClerkProvider")},24767:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var o=n(23233);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),i=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:d,...u},m)=>(0,o.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:r?24*Number(n)/Number(t):n,className:a("lucide",s),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let n=(0,o.forwardRef)(({className:n,...s},l)=>(0,o.createElement)(d,{ref:l,iconNode:t,className:a(`lucide-${r(i(e))}`,`lucide-${e}`,n),...s}));return n.displayName=i(e),n}},32177:(e,t,n)=>{"use strict";n.d(t,{ai:()=>C,at:()=>h,ot:()=>b});var o=n(52661);n(37091);var r=n(62644),s=n(62923);let i=(0,n(62016)._r)({packageName:"@clerk/nextjs"});var a=n(97495),l=n(65931);let c="__clerk_keys_";async function d(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function u(){let e=process.env.PWD;if(!e)return`${c}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),n=await d(t);return`${c}${n}`}async function m(e){let t;if(!l.I)return;let n=await u();try{n&&(t=JSON.parse(e(n)||"{}"))}catch{t=void 0}return t}var p=n(76635);let f={secure:!1,httpOnly:!1,sameSite:"lax"};async function b(e){let{claimUrl:t,publishableKey:n,secretKey:o,returnUrl:i}=e,l=await (0,r.cookies)(),c=new Request("https://placeholder.com",{headers:await (0,r.headers)()}),d=await m(e=>{var t;return null==(t=l.get(e))?void 0:t.value}),p=(null==d?void 0:d.publishableKey)===n,b=(null==d?void 0:d.secretKey)===o;if((!p||!b)&&(l.set(await u(),JSON.stringify({claimUrl:t,publishableKey:n,secretKey:o}),f),(0,a.Zd)(c)))return void(0,s.redirect)(`/clerk-sync-keyless?returnUrl=${i}`,s.RedirectType.replace)}async function C(){if(!l.I)return null;let e=await n.e(3460).then(n.bind(n,3460)).then(e=>e.createOrReadKeyless()).catch(()=>null);if(!e)return i.throwMissingPublishableKeyError(),null;let{clerkDevelopmentCache:t,createKeylessModeMessage:o}=await n.e(2295).then(n.bind(n,62295));null==t||t.log({cacheKey:e.publishableKey,msg:o(e)});let{claimUrl:s,publishableKey:a,secretKey:c,apiKeysUrl:d}=e;return(await (0,r.cookies)()).set(await u(),JSON.stringify({claimUrl:s,publishableKey:a,secretKey:c}),f),{claimUrl:s,publishableKey:a,apiKeysUrl:d}}async function h(){l.I&&await n.e(3460).then(n.bind(n,3460)).then(e=>e.removeKeyless()).catch(()=>{})}(0,p.D)([C,h,b]),(0,o.A)(C,"7fa248ee4cee001992d543e3927d536ddea63c121c",null),(0,o.A)(h,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f",null),(0,o.A)(b,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937",null)},32787:(e,t,n)=>{let{createProxy:o}=n(20867);e.exports=o("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js")},36137:(e,t,n)=>{"use strict";n.d(t,{default:()=>r.a});var o=n(71280),r=n.n(o)},37091:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{decryptActionBoundArgs:function(){return b},encryptActionBoundArgs:function(){return f}}),n(1447);let o=n(6340),r=n(52946),s=n(71384),i=n(5027),a=n(63033),l=n(11640),c=function(e){return e&&e.__esModule?e:{default:e}}(n(23233)),d=new TextEncoder,u=new TextDecoder;async function m(e,t){let n=await (0,i.getActionEncryptionKey)();if(void 0===n)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let o=atob(t),r=o.slice(0,16),s=o.slice(16),a=u.decode(await (0,i.decrypt)(n,(0,i.stringToUint8Array)(r),(0,i.stringToUint8Array)(s)));if(!a.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return a.slice(e.length)}async function p(e,t){let n=await (0,i.getActionEncryptionKey)();if(void 0===n)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let o=new Uint8Array(16);a.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(o));let r=(0,i.arrayBufferToString)(o.buffer),s=await (0,i.encrypt)(n,o,d.encode(e+t));return btoa(r+(0,i.arrayBufferToString)(s))}let f=c.default.cache(async function e(t,...n){let{clientModules:r}=(0,i.getClientReferenceManifestForRsc)(),c=Error();Error.captureStackTrace(c,e);let d=!1,u=a.workUnitAsyncStorage.getStore(),m=(null==u?void 0:u.type)==="prerender"?(0,l.createHangingInputAbortSignal)(u):void 0,f=await (0,s.streamToString)((0,o.renderToReadableStream)(n,r,{signal:m,onError(e){(null==m||!m.aborted)&&(d||(d=!0,c.message=e instanceof Error?e.message:String(e)))}}),m);if(d)throw c;if(!u)return p(t,f);let b=(0,a.getPrerenderResumeDataCache)(u),C=(0,a.getRenderResumeDataCache)(u),h=t+f,g=(null==b?void 0:b.encryptedBoundArgs.get(h))??(null==C?void 0:C.encryptedBoundArgs.get(h));if(g)return g;let v="prerender"===u.type?u.cacheSignal:void 0;null==v||v.beginRead();let _=await p(t,f);return null==v||v.endRead(),null==b||b.encryptedBoundArgs.set(h,_),_});async function b(e,t){let n,o=await t,s=a.workUnitAsyncStorage.getStore();if(s){let t="prerender"===s.type?s.cacheSignal:void 0,r=(0,a.getPrerenderResumeDataCache)(s),i=(0,a.getRenderResumeDataCache)(s);(n=(null==r?void 0:r.decryptedBoundArgs.get(o))??(null==i?void 0:i.decryptedBoundArgs.get(o)))||(null==t||t.beginRead(),n=await m(e,o),null==t||t.endRead(),null==r||r.decryptedBoundArgs.set(o,n))}else n=await m(e,o);let{edgeRscModuleMapping:l,rscModuleMapping:c}=(0,i.getClientReferenceManifestForRsc)();return await (0,r.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(d.encode(n)),(null==s?void 0:s.type)==="prerender"?s.renderSignal.aborted?e.close():s.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:c,serverModuleMap:(0,i.getServerModuleMap)()}})}},39928:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let o=n(94752),r=n(23233),s=n(32787),i=n(47390);function a(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},n=(0,r.lazy)(()=>t.loader().then(a)),c=t.loading;function d(e){let a=c?(0,o.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,d=l?r.Suspense:r.Fragment,u=t.ssr?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.PreloadChunks,{moduleIds:t.modules}),(0,o.jsx)(n,{...e})]}):(0,o.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,o.jsx)(n,{...e})});return(0,o.jsx)(d,{...l?{fallback:a}:{},children:u})}return d.displayName="LoadableComponent",d}},43303:(e,t,n)=>{"use strict";n.d(t,{AuthenticateWithRedirectCallback:()=>r,ClerkDegraded:()=>s,ClerkFailed:()=>i,ClerkLoaded:()=>a,ClerkLoading:()=>l,RedirectToCreateOrganization:()=>c,RedirectToOrganizationProfile:()=>d,RedirectToSignIn:()=>u,RedirectToSignUp:()=>m,RedirectToUserProfile:()=>p});var o=n(6340);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthenticateWithRedirectCallback() from the server but AuthenticateWithRedirectCallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","AuthenticateWithRedirectCallback"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call ClerkDegraded() from the server but ClerkDegraded is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","ClerkDegraded"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call ClerkFailed() from the server but ClerkFailed is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","ClerkFailed"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call ClerkLoaded() from the server but ClerkLoaded is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","ClerkLoaded"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call ClerkLoading() from the server but ClerkLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","ClerkLoading");(0,o.registerClientReference)(function(){throw Error("Attempted to call MultisessionAppSupport() from the server but MultisessionAppSupport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","MultisessionAppSupport"),(0,o.registerClientReference)(function(){throw Error("Attempted to call Protect() from the server but Protect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","Protect");let c=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToCreateOrganization() from the server but RedirectToCreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToCreateOrganization"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToOrganizationProfile() from the server but RedirectToOrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToOrganizationProfile"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToSignIn() from the server but RedirectToSignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToSignIn"),m=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToSignUp() from the server but RedirectToSignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToSignUp"),p=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToUserProfile() from the server but RedirectToUserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToUserProfile");(0,o.registerClientReference)(function(){throw Error("Attempted to call SignedIn() from the server but SignedIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","SignedIn"),(0,o.registerClientReference)(function(){throw Error("Attempted to call SignedOut() from the server but SignedOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","SignedOut")},47390:(e,t,n)=>{let{createProxy:o}=n(20867);e.exports=o("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-chunks.js")},51672:(e,t,n)=>{"use strict";n.d(t,{CreateOrganization:()=>o.ul,GoogleOneTap:()=>o.PQ,OrganizationList:()=>o.oE,OrganizationProfile:()=>p,OrganizationSwitcher:()=>o.NC,PricingTable:()=>o.nm,SignIn:()=>f,SignInButton:()=>o.hZ,SignInWithMetamaskButton:()=>o.M_,SignOutButton:()=>o.ct,SignUp:()=>b,SignUpButton:()=>o.Ny,UserButton:()=>o.uF,UserProfile:()=>m,Waitlist:()=>o.cP});var o=n(2233),r=n(57752),s=n.n(r),i=n(48563),a=n(23074),l=n(67715);let c=(e,t,n,r=!0)=>{let i=s().useRef(0),{pagesRouter:c}=(0,l.r)(),{session:d,isLoaded:u}=(0,o.wV)();(0,a.Fj)()||s().useEffect(()=>{if(!u||n&&"path"!==n||r&&!d)return;let o=new AbortController,s=()=>{let n=c?`${t}/[[...index]].tsx`:`${t}/[[...rest]]/page.tsx`;throw Error(`
Clerk: The <${e}/> component is not configured correctly. The most likely reasons for this error are:

1. The "${t}" route is not a catch-all route.
It is recommended to convert this route to a catch-all route, eg: "${n}". Alternatively, you can update the <${e}/> component to use hash-based routing by setting the "routing" prop to "hash".

2. The <${e}/> component is mounted in a catch-all route, but all routes under "${t}" are protected by the middleware.
To resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the "createRouteMatcher" helper, consider adding "(.*)" to the end of the route pattern, eg: "${t}(.*)". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher
`)};return c?c.pathname.match(/\[\[\.\.\..+]]/)||s():(async()=>{let t;if(i.current++,!(i.current>1)){try{let n=`${window.location.origin}${window.location.pathname}/${e}_clerk_catchall_check_${Date.now()}`;t=await fetch(n,{signal:o.signal})}catch{}(null==t?void 0:t.status)===404&&s()}})(),()=>{i.current>1&&o.abort()}},[u])},d=()=>{let e=s().useRef(),{pagesRouter:t}=(0,l.r)();if(t)if(e.current)return e.current;else return e.current=t.pathname.replace(/\/\[\[\.\.\..*/,""),e.current;let o=n(53172).usePathname,r=n(53172).useParams,i=(o()||"").split("/").filter(Boolean),a=Object.values(r()||{}).filter(e=>Array.isArray(e)).flat(1/0);return e.current||(e.current=`/${i.slice(0,i.length-a.length).join("/")}`),e.current};function u(e,t,n=!0){let o=d(),r=(0,i.yC)(e,t,{path:o});return c(e,o,r.routing,n),r}let m=Object.assign(e=>s().createElement(o.Fv,{...u("UserProfile",e)}),{...o.Fv}),p=Object.assign(e=>s().createElement(o.nC,{...u("OrganizationProfile",e)}),{...o.nC}),f=e=>s().createElement(o.Ls,{...u("SignIn",e,!1)}),b=e=>s().createElement(o.Hx,{...u("SignUp",e,!1)})},52661:(e,t,n)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o.registerServerReference}});let o=n(6340)},59986:(e,t,n)=>{"use strict";function o(e,t){if(e instanceof Promise)throw Error(t)}function r(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,n]of Object.entries(t))""===n&&delete t[e];if(e.skipValidation)return t;let n="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},s="object"==typeof e.shared?e.shared:{},i=e.isServer??("undefined"==typeof window||"Deno"in window),a=i?{...r,...s,...n}:{...n,...s},l=e.createFinalSchema?.(a,i)["~standard"].validate(t)??function(e,t){let n={},r=[];for(let s in e){let i=e[s]["~standard"].validate(t[s]);if(o(i,`Validation must be synchronous, but ${s} returned a Promise.`),i.issues){r.push(...i.issues.map(e=>({...e,path:[s,...e.path??[]]})));continue}n[s]=i.value}return r.length?{issues:r}:{value:n}}(a,t);o(l,"Validation must be synchronous");let c=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),d=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(l.issues)return c(l.issues);let u=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in s),m=e=>i||!u(e),p=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),l.value),{get(e,t){if("string"==typeof t&&!p(t))return m(t)?Reflect.get(e,t):d(t)}})}n.d(t,{w:()=>r})},62906:(e,t,n)=>{"use strict";n.d(t,{useAuth:()=>r.d,useClerk:()=>o.ho,useEmailLink:()=>o.ui,useOrganization:()=>o.Z5,useOrganizationList:()=>o.D_,useReverification:()=>o.Wp,useSession:()=>o.wV,useSessionList:()=>o.g7,useSignIn:()=>o.go,useSignUp:()=>o.yC,useUser:()=>o.Jd});var o=n(2233);n(32147);var r=n(93078)},71166:(e,t,n)=>{"use strict";n.d(t,{w:()=>r});var o=n(59986);function r(e){let t="object"==typeof e.client?e.client:{},n="object"==typeof e.server?e.server:{},r=e.shared,s=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return(0,o.w)({...e,shared:r,client:t,server:n,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:s})}},71280:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}});let o=n(32446)._(n(39928));function r(e,t){var n;let r={};"function"==typeof e&&(r.loader=e);let s={...r,...t};return(0,o.default)({...s,modules:null==(n=s.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75264:(e,t,n)=>{"use strict";n.r(t),n.d(t,{KeylessCookieSync:()=>o});let o=(0,n(6340).registerClientReference)(function(){throw Error("Attempted to call KeylessCookieSync() from the server but KeylessCookieSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\keyless-cookie-sync.js","KeylessCookieSync")},76635:(e,t)=>{"use strict";function n(e){for(let t=0;t<e.length;t++){let n=e[t];if("function"!=typeof n)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof n}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return n}})},80278:(e,t,n)=>{"use strict";n.r(t),n.d(t,{KeylessCookieSync:()=>r});var o=n(53172);function r(e){var t;return null==(t=(0,o.useSelectedLayoutSegments)()[0])||t.startsWith("/_not-found"),e.children}n(57752),n(95505)},82578:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});let o=(0,n(24767).A)("command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]])},87641:(e,t,n)=>{"use strict";n.d(t,{AuthenticateWithRedirectCallback:()=>o.B$,ClerkDegraded:()=>o.wF,ClerkFailed:()=>o.lT,ClerkLoaded:()=>o.z0,ClerkLoading:()=>o.A0,RedirectToCreateOrganization:()=>o.rm,RedirectToOrganizationProfile:()=>o.m2,RedirectToSignIn:()=>o.W5,RedirectToSignUp:()=>o.mO,RedirectToUserProfile:()=>o.eG});var o=n(2233);n(48563)},93078:(e,t,n)=>{"use strict";n.d(t,{PromisifiedAuthProvider:()=>c,d:()=>d});var o=n(2233),r=n(48563),s=n(70764),i=n(57752),a=n.n(i);let l=a().createContext(null);function c({authPromise:e,children:t}){return a().createElement(l.Provider,{value:e},t)}function d(e={}){let t=(0,s.useRouter)(),n=a().useContext(l),i=n;return(n&&"then"in n&&(i=a().use(n)),t)?(0,o.As)(e):(0,r.hP)({...i,...e})}}};