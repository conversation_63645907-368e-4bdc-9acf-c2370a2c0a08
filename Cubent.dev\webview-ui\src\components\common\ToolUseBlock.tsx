import { cn } from "@/lib/utils"

import { CODE_BLOCK_BG_COLOR } from "./CodeBlock"

export const ToolUseBlock = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
	<div
		className={cn("relative overflow-hidden rounded-xs cursor-pointer", className)}
		style={{
			backgroundColor: CODE_BLOCK_BG_COLOR,
			border: "0.5px solid #333",
		}}
		{...props}
	/>
)

// Header component for tool use blocks - controls spacing and styling for tool selections
export const ToolUseBlockHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
	<div
		className={cn("flex items-center select-none text-white px-3 py-2 min-h-[40px]", className)}
		style={{
			backgroundColor: "var(--vscode-editor-background, #1e1e1e)",
			borderBottom: "1px solid var(--vscode-panel-border, #3e3e42)"
		}}
		{...props}
	/>
)
