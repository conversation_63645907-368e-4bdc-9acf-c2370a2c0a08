/**
 * Debug script to check extension authentication and usage tracking
 */

import { PrismaClient } from './packages/database/generated/client/index.js';

const prisma = new PrismaClient();
const API_BASE_URL = 'http://localhost:3000';

async function debugExtensionAuth() {
  try {
    console.log('🔍 Debugging Extension Authentication and Usage Tracking...\n');

    // 1. Check database for authentication data
    console.log('📊 Database Status:');
    
    // Check users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        clerkId: true,
        email: true,
        cubentUnitsUsed: true,
        cubentUnitsLimit: true,
        subscriptionTier: true,
        lastExtensionSync: true,
        createdAt: true,
      },
      take: 5,
    });
    console.log(`  - Total users: ${users.length}`);
    users.forEach(user => {
      console.log(`    • ${user.email}: ${user.cubentUnitsUsed}/${user.cubentUnitsLimit} units (${user.subscriptionTier})`);
      console.log(`      Last extension sync: ${user.lastExtensionSync || 'Never'}`);
    });
    console.log('');

    // Check pending logins (extension auth tokens)
    const pendingLogins = await prisma.pendingLogin.findMany({
      select: {
        id: true,
        deviceId: true,
        userId: true,
        token: true,
        expiresAt: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });
    console.log(`📱 Pending Logins (Extension Auth Tokens): ${pendingLogins.length}`);
    pendingLogins.forEach(login => {
      const isExpired = login.expiresAt < new Date();
      const timeLeft = isExpired ? 'EXPIRED' : `${Math.round((login.expiresAt.getTime() - Date.now()) / 1000 / 60)}min left`;
      console.log(`  - Device: ${login.deviceId.slice(0, 12)}... | User: ${login.userId} | ${timeLeft}`);
      console.log(`    Token: ${login.token.slice(0, 20)}...`);
    });
    console.log('');

    // Check extension sessions
    const extensionSessions = await prisma.extensionSession.findMany({
      select: {
        id: true,
        userId: true,
        sessionId: true,
        isActive: true,
        extensionVersion: true,
        lastActiveAt: true,
        createdAt: true,
      },
      orderBy: { lastActiveAt: 'desc' },
      take: 10,
    });
    console.log(`🔌 Extension Sessions: ${extensionSessions.length}`);
    extensionSessions.forEach(session => {
      const timeSinceActive = Math.round((Date.now() - session.lastActiveAt.getTime()) / 1000 / 60);
      console.log(`  - User: ${session.userId} | Session: ${session.sessionId.slice(0, 12)}... | Active: ${session.isActive}`);
      console.log(`    Version: ${session.extensionVersion} | Last active: ${timeSinceActive}min ago`);
    });
    console.log('');

    // Check usage analytics
    const usageAnalytics = await prisma.usageAnalytics.findMany({
      select: {
        id: true,
        userId: true,
        modelId: true,
        cubentUnitsUsed: true,
        requestsMade: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });
    console.log(`📈 Recent Usage Analytics: ${usageAnalytics.length}`);
    usageAnalytics.forEach(record => {
      console.log(`  - User: ${record.userId} | Model: ${record.modelId} | Units: ${record.cubentUnitsUsed} | Requests: ${record.requestsMade}`);
    });
    console.log('');

    // 2. Test API endpoints with valid tokens
    if (pendingLogins.length > 0) {
      const validToken = pendingLogins.find(login => login.expiresAt > new Date());
      
      if (validToken) {
        console.log('🧪 Testing API with valid token...');
        console.log(`Using token: ${validToken.token.slice(0, 20)}...`);
        
        // Test usage tracking endpoint
        try {
          const response = await fetch(`${API_BASE_URL}/api/extension/units/track`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${validToken.token}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              modelId: 'test-model-debug',
              provider: 'test-provider',
              configName: 'test-config',
              cubentUnits: 0.1,
              messageCount: 1,
              timestamp: Date.now(),
            }),
          });
          
          console.log(`  - Usage tracking status: ${response.status}`);
          const responseText = await response.text();
          console.log(`  - Response: ${responseText.slice(0, 200)}...`);
          
          if (response.ok) {
            console.log('  ✅ Usage tracking is working!');
          } else {
            console.log('  ❌ Usage tracking failed');
          }
        } catch (error) {
          console.log(`  ❌ Usage tracking error: ${error.message}`);
        }
        
        // Test usage stats endpoint
        try {
          const response = await fetch(`${API_BASE_URL}/api/extension/usage/stats`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${validToken.token}`,
              'Content-Type': 'application/json',
            },
          });
          
          console.log(`  - Usage stats status: ${response.status}`);
          const responseText = await response.text();
          console.log(`  - Response: ${responseText.slice(0, 200)}...`);
          
          if (response.ok) {
            console.log('  ✅ Usage stats is working!');
          } else {
            console.log('  ❌ Usage stats failed');
          }
        } catch (error) {
          console.log(`  ❌ Usage stats error: ${error.message}`);
        }
      } else {
        console.log('❌ No valid (non-expired) tokens found for testing');
      }
    } else {
      console.log('❌ No pending logins found - extension may not be authenticated');
    }

    // 3. Summary and recommendations
    console.log('\n📋 Summary:');
    const activeTokens = pendingLogins.filter(login => login.expiresAt > new Date()).length;
    const activeSessions = extensionSessions.filter(session => session.isActive).length;
    const totalUsage = usageAnalytics.length;
    
    console.log(`- Active auth tokens: ${activeTokens}`);
    console.log(`- Active extension sessions: ${activeSessions}`);
    console.log(`- Total usage records: ${totalUsage}`);
    
    if (activeTokens === 0) {
      console.log('\n🔧 Recommendations:');
      console.log('1. Extension needs to authenticate with the website');
      console.log('2. User should sign in through the extension');
      console.log('3. Check if extension is properly connected to the website');
    } else if (totalUsage === 0) {
      console.log('\n🔧 Recommendations:');
      console.log('1. Extension is authenticated but not tracking usage');
      console.log('2. Check if extension is actually sending usage data');
      console.log('3. Verify CloudService authentication in extension');
    } else {
      console.log('\n✅ Everything looks good! Usage tracking should be working.');
    }

  } catch (error) {
    console.error('❌ Debug error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugExtensionAuth();
