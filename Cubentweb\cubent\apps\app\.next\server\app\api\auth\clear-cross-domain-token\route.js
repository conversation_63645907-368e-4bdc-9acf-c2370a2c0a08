(()=>{var e={};e.id=9458,e.ids=[9458],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},84763:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>p});var a=t(26142),o=t(94327),n=t(34862),u=t(26239),i=t(62644);async function p(){try{return(await (0,i.cookies)()).set("cubent_auth_token","",{domain:".cubent.dev",httpOnly:!1,secure:!0,sameSite:"lax",maxAge:0,path:"/"}),u.NextResponse.json({success:!0})}catch(e){return console.error("Error clearing cross-domain token:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/clear-cross-domain-token/route",pathname:"/api/auth/clear-cross-domain-token",filename:"route",bundlePath:"app/api/auth/clear-cross-domain-token/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\auth\\clear-cross-domain-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},89259:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2644],()=>t(84763));module.exports=s})();