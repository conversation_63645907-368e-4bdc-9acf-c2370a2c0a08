(()=>{var e={};e.id=893,e.ids=[893],e.modules={1359:(e,t,s)=>{"use strict";s.d(t,{N:()=>a});var r=s(12901),i=s(37838);async function a(){s(1447);let{userId:e}=await (0,i.j)();return e?(await (0,r.$)()).users.getUser(e):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6725:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{GET:()=>c});var i=s(26142),a=s(94327),n=s(34862),o=s(26239),u=s(37838),l=s(1359),d=s(18815);async function c(e){try{let{userId:t}=await (0,u.j)(),s=await (0,l.N)();if(!t||!s)return o.NextResponse.json({error:"Unauthorized"},{status:401});let r=await d.database.user.findUnique({where:{clerkId:t},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0}});r||(r=await d.database.user.create({data:{clerkId:t,email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0}}));let i=await d.database.usageAnalytics.count({where:{userId:r.id}}),{searchParams:a}=new URL(e.url),n=parseInt(a.get("days")||"0"),c={};if(n>0){let e=new Date;e.setDate(e.getDate()-n),c={date:{gte:e}}}let p=await d.database.usageMetrics.findMany({where:{userId:r.id,...c},orderBy:{date:"desc"}}),h=await d.database.usageAnalytics.findMany({where:{userId:r.id,...n>0?{createdAt:{gte:new Date(Date.now()-24*n*36e5)}}:{}},orderBy:{createdAt:"desc"}}),m=n>0?p.reduce((e,t)=>e+(t.cubentUnitsUsed||0),0):r.cubentUnitsUsed||0,g=p.reduce((e,t)=>e+(t.requestsMade||0),0),f={};h.forEach(e=>{f[e.modelId]||(f[e.modelId]={cubentUnits:0,messages:0}),f[e.modelId].cubentUnits+=e.cubentUnitsUsed||0,f[e.modelId].messages+=e.requestsMade||0});let b=h.map(e=>({timestamp:e.createdAt.getTime(),modelId:e.modelId,cubentUnits:e.cubentUnitsUsed||0,messageCount:e.requestsMade||0,provider:e.metadata?.provider||"unknown",configName:e.metadata?.configName||"default"})),y=new Date,w=new Date(y.getFullYear(),y.getMonth(),1),S=new Date(y.getFullYear(),y.getMonth()+1,0),k=await d.database.usageMetrics.aggregate({where:{userId:r.id,date:{gte:w,lte:S}},_sum:{cubentUnitsUsed:!0,requestsMade:!0}});return o.NextResponse.json({success:!0,totalCubentUnits:m,totalMessages:n>0?g:i,userLimit:r.cubentUnitsLimit||50,subscriptionTier:r.subscriptionTier||"free_trial",lastUpdated:h.length>0?h[0].createdAt.getTime():Date.now(),entries:b,modelBreakdown:f,monthlyUsage:{cubentUnits:k._sum.cubentUnitsUsed||0,messages:k._sum.requestsMade||0}})}catch(e){return console.error("Error fetching usage stats:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/extension/usage/stats/route",pathname:"/api/extension/usage/stats",filename:"route",bundlePath:"app/api/extension/usage/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\usage\\stats\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:g}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(8741),i=s(23056),a=s(76315),n=s(97495);let o=new(s(16698)).AsyncLocalStorage;var u=s(60606);let l=async()=>{var e,t;let s;try{let e=await (0,i.TG)(),t=(0,n._b)(e,r.AA.Headers.ClerkRequestData);s=(0,u.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let l=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:s;return(null==l?void 0:l.secretKey)||(null==l?void 0:l.publishableKey)?(0,a.n)(l):(0,a.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},26790:(e,t,s)=>{"use strict";s.d(t,{z:()=>j});var r,i,a,n,o,u,l,d,c,p,h,m,g,f,b,y,w,S,k,x=s(45940);s(92867);var v=s(37081);s(27322),s(6264);var q=s(49530),U=s(57136),M=s(94051),K=class{constructor(){(0,M.VK)(this,a),(0,M.VK)(this,r,"clerk_telemetry_throttler"),(0,M.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,M.S7)(this,a,u))return!1;let t=Date.now(),s=(0,M.jq)(this,a,n).call(this,e),l=(0,M.S7)(this,a,o)?.[s];if(!l){let e={...(0,M.S7)(this,a,o),[s]:t};localStorage.setItem((0,M.S7)(this,r),JSON.stringify(e))}if(l&&t-l>(0,M.S7)(this,i)){let e=(0,M.S7)(this,a,o);delete e[s],localStorage.setItem((0,M.S7)(this,r),JSON.stringify(e))}return!!l}};r=new WeakMap,i=new WeakMap,a=new WeakSet,n=function(e){let{sk:t,pk:s,payload:r,...i}=e,a={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>a[e]))},o=function(){let e=localStorage.getItem((0,M.S7)(this,r));return e?JSON.parse(e):{}},u=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,M.S7)(this,r)),!1}};var R={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},E=class{constructor(e){(0,M.VK)(this,m),(0,M.VK)(this,l),(0,M.VK)(this,d),(0,M.VK)(this,c,{}),(0,M.VK)(this,p,[]),(0,M.VK)(this,h),(0,M.OV)(this,l,{maxBufferSize:e.maxBufferSize??R.maxBufferSize,samplingRate:e.samplingRate??R.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:R.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,M.S7)(this,c).clerkVersion=e.clerkVersion??"":(0,M.S7)(this,c).clerkVersion="",(0,M.S7)(this,c).sdk=e.sdk,(0,M.S7)(this,c).sdkVersion=e.sdkVersion,(0,M.S7)(this,c).publishableKey=e.publishableKey??"";let t=(0,U.q5)(e.publishableKey);t&&((0,M.S7)(this,c).instanceType=t.instanceType),e.secretKey&&((0,M.S7)(this,c).secretKey=e.secretKey.substring(0,16)),(0,M.OV)(this,d,new K)}get isEnabled(){return!("development"!==(0,M.S7)(this,c).instanceType||(0,M.S7)(this,l).disabled||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,M.S7)(this,l).debug||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,M.jq)(this,m,k).call(this,e.event,e.payload);(0,M.jq)(this,m,w).call(this,t.event,t),(0,M.jq)(this,m,g).call(this,t,e.eventSamplingRate)&&((0,M.S7)(this,p).push(t),(0,M.jq)(this,m,b).call(this))}};l=new WeakMap,d=new WeakMap,c=new WeakMap,p=new WeakMap,h=new WeakMap,m=new WeakSet,g=function(e,t){return this.isEnabled&&!this.isDebug&&(0,M.jq)(this,m,f).call(this,e,t)},f=function(e,t){let s=Math.random();return!!(s<=(0,M.S7)(this,l).samplingRate&&(void 0===t||s<=t))&&!(0,M.S7)(this,d).isEventThrottled(e)},b=function(){if("undefined"==typeof window)return void(0,M.jq)(this,m,y).call(this);if((0,M.S7)(this,p).length>=(0,M.S7)(this,l).maxBufferSize){(0,M.S7)(this,h)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,M.S7)(this,h)),(0,M.jq)(this,m,y).call(this);return}(0,M.S7)(this,h)||("requestIdleCallback"in window?(0,M.OV)(this,h,requestIdleCallback(()=>{(0,M.jq)(this,m,y).call(this)})):(0,M.OV)(this,h,setTimeout(()=>{(0,M.jq)(this,m,y).call(this)},0)))},y=function(){fetch(new URL("/v1/event",(0,M.S7)(this,l).endpoint),{method:"POST",body:JSON.stringify({events:(0,M.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,M.OV)(this,p,[])}).catch(()=>void 0)},w=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},S=function(){let e={name:(0,M.S7)(this,c).sdk,version:(0,M.S7)(this,c).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},k=function(e,t){let s=(0,M.jq)(this,m,S).call(this);return{event:e,cv:(0,M.S7)(this,c).clerkVersion??"",it:(0,M.S7)(this,c).instanceType??"",sdk:s.name,sdkv:s.version,...(0,M.S7)(this,c).publishableKey?{pk:(0,M.S7)(this,c).publishableKey}:{},...(0,M.S7)(this,c).secretKey?{sk:(0,M.S7)(this,c).secretKey}:{},payload:t}};function j(e){let t={...e},s=(0,x.y3)(t),r=(0,x.Bs)({options:t,apiClient:s}),i=new E({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}}(0,v.C)(x.nr)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76315:(e,t,s)=>{"use strict";s.d(t,{n:()=>n});var r=s(26790),i=s(54726);let a={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},n=e=>(0,r.z)({...a,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(6725));module.exports=r})();