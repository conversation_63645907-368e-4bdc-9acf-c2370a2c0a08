(()=>{var e={};e.id=893,e.ids=[893],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6725:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{GET:()=>p});var a=s(26142),i=s(94327),u=s(34862),n=s(26239),o=s(37838),d=s(1359),c=s(18815);async function p(e){try{let{userId:t}=await (0,o.j)(),s=await (0,d.N)();if(!t||!s)return n.NextResponse.json({error:"Unauthorized"},{status:401});let r=await c.database.user.findUnique({where:{clerkId:t},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0}});r||(r=await c.database.user.create({data:{clerkId:t,email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0}}));let a=await c.database.usageAnalytics.count({where:{userId:r.id}}),{searchParams:i}=new URL(e.url),u=parseInt(i.get("days")||"0"),p={};if(u>0){let e=new Date;e.setDate(e.getDate()-u),p={date:{gte:e}}}let l=await c.database.usageMetrics.findMany({where:{userId:r.id,...p},orderBy:{date:"desc"}}),x=await c.database.usageAnalytics.findMany({where:{userId:r.id,...u>0?{createdAt:{gte:new Date(Date.now()-24*u*36e5)}}:{}},orderBy:{createdAt:"desc"}}),m=u>0?l.reduce((e,t)=>e+(t.cubentUnitsUsed||0),0):r.cubentUnitsUsed||0,g=l.reduce((e,t)=>e+(t.requestsMade||0),0),q={};x.forEach(e=>{q[e.modelId]||(q[e.modelId]={cubentUnits:0,messages:0}),q[e.modelId].cubentUnits+=e.cubentUnitsUsed||0,q[e.modelId].messages+=e.requestsMade||0});let U=x.map(e=>({timestamp:e.createdAt.getTime(),modelId:e.modelId,cubentUnits:e.cubentUnitsUsed||0,messageCount:e.requestsMade||0,provider:e.metadata?.provider||"unknown",configName:e.metadata?.configName||"default"})),b=new Date,w=new Date(b.getFullYear(),b.getMonth(),1),h=new Date(b.getFullYear(),b.getMonth()+1,0),f=await c.database.usageMetrics.aggregate({where:{userId:r.id,date:{gte:w,lte:h}},_sum:{cubentUnitsUsed:!0,requestsMade:!0}});return n.NextResponse.json({success:!0,totalCubentUnits:m,totalMessages:u>0?g:a,userLimit:r.cubentUnitsLimit||50,subscriptionTier:r.subscriptionTier||"free_trial",lastUpdated:x.length>0?x[0].createdAt.getTime():Date.now(),entries:U,modelBreakdown:q,monthlyUsage:{cubentUnits:f._sum.cubentUnitsUsed||0,messages:f._sum.requestsMade||0}})}catch(e){return console.error("Error fetching usage stats:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/usage/stats/route",pathname:"/api/extension/usage/stats",filename:"route",bundlePath:"app/api/extension/usage/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\usage\\stats\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:g}=l;function q(){return(0,u.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,7873,3887,5480,1359,864],()=>s(6725));module.exports=r})();