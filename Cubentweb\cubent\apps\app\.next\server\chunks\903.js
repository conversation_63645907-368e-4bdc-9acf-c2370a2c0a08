exports.id=903,exports.ids=[903],exports.modules={1447:()=>{},1702:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),s=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,a,n=new WeakMap)=>{if(a={deep:!1,target:{},...a},n.has(e))return n.get(e);n.set(e,a.target);let{target:o}=a;delete a.target;let l=e=>e.map(e=>s(e)?i(e,t,a,n):e);if(Array.isArray(e))return l(e);for(let[c,d]of Object.entries(e)){let u=t(c,d,e);if(u===r)continue;let[h,p,{shouldRecurse:m=!0}={}]=u;"__proto__"!==h&&(a.deep&&m&&s(p)&&(p=Array.isArray(p)?l(p):i(p,t,a,n)),o[h]=p)}return o};e.exports=(e,r,s)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,s)},e.exports.mapObjectSkip=r},3680:e=>{"use strict";e.exports={rE:"15.3.2"}},6264:(e,t,r)=>{"use strict";r.d(t,{jn:()=>i,qu:()=>s,xy:()=>o,z:()=>a,zF:()=>n});var s={InvalidSecretKey:"clerk_key_invalid"},i={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},a={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},n=class e extends Error{constructor({action:t,message:r,reason:s}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=s,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},o=class extends Error{}},8741:(e,t,r)=>{"use strict";r.d(t,{TD:()=>s.TD,AA:()=>s.AA,tl:()=>s.tl,vH:()=>o,Z5:()=>s.Z5,wI:()=>s.wI});var s=r(45940),i=r(92867);r(27322),r(6264),r(94051);var a=(e,t,r,i)=>{if(""===e)return n(t.toString(),r?.toString());let a=new URL(e),o=r?new URL(r,a):void 0,l=new URL(t,a);return o&&l.searchParams.set("redirect_url",o.toString()),i&&a.hostname!==l.hostname&&l.searchParams.set(s.AA.QueryParameters.DevBrowser,i),l.toString()},n=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let s=new URL(t);r=new URL(e,s.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},o=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:s,signUpUrl:n,baseUrl:o,sessionStatus:l}=e,c=(0,i.q5)(t),d=c?.frontendApi,u=c?.instanceType==="development",h=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(d),p="pending"===l,m=(t,{returnBackUrl:s})=>r(a(o,`${t}/tasks`,s,u?e.devBrowserToken:null));return{redirectToSignUp:({returnBackUrl:t}={})=>{n||h||i.sb.throwMissingPublishableKeyError();let l=`${h}/sign-up`,c=n||function(e){if(!e)return;let t=new URL(e,o);return t.pathname=`${t.pathname}/create`,t.toString()}(s)||l;return p?m(c,{returnBackUrl:t}):r(a(o,c,t,u?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{s||h||i.sb.throwMissingPublishableKeyError();let n=`${h}/sign-in`,l=s||n;return p?m(l,{returnBackUrl:t}):r(a(o,l,t,u?e.devBrowserToken:null))}}}},21757:(e,t,r)=>{"use strict";r.d(t,{Fj:()=>s.Fj,b_:()=>s.b_});var s=r(77071);r(94051)},27322:(e,t,r)=>{"use strict";r.d(t,{l3:()=>k,qf:()=>y,r0:()=>l,iU:()=>E,hJ:()=>m,nk:()=>I,Fh:()=>T,fA:()=>o,J0:()=>A});var s=r(6264),i=r(77598),a=r(55911);r(94051);var n=fetch.bind(globalThis),o={crypto:i.webcrypto,get fetch(){return n},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},l={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let s=e.length;for(;"="===e[s-1];)if(--s,!r.loose&&!((e.length-s)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(s*t.bits/8|0),a=0,n=0,o=0;for(let r=0;r<s;++r){let s=t.codes[e[r]];if(void 0===s)throw SyntaxError("Invalid character "+e[r]);n=n<<t.bits|s,(a+=t.bits)>=8&&(a-=8,i[o++]=255&n>>a)}if(a>=t.bits||255&n<<8-a)throw SyntaxError("Unexpected end of data");return i})(e,c,t),stringify:(e,t)=>(function(e,t,r={}){let{pad:s=!0}=r,i=(1<<t.bits)-1,a="",n=0,o=0;for(let r=0;r<e.length;++r)for(o=o<<8|255&e[r],n+=8;n>t.bits;)n-=t.bits,a+=t.chars[i&o>>n];if(n&&(a+=t.chars[i&o<<t.bits-n]),s)for(;a.length*t.bits&7;)a+="=";return a})(e,c,t)},c={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},d={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},u="RSASSA-PKCS1-v1_5",h={RS256:u,RS384:u,RS512:u},p=Object.keys(d);function m(e){let t=d[e],r=h[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${p.join(",")}.`);return{hash:{name:d[e]},name:h[e]}}var f=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),g=(e,t)=>{let r=[t].flat().filter(e=>!!e),i=[e].flat().filter(e=>!!e);if(r.length>0&&i.length>0){if("string"==typeof e){if(!r.includes(e))throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(f(e)&&!e.some(e=>r.includes(e)))throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},y=e=>{if(void 0!==e&&"JWT"!==e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},k=e=>{if(!p.includes(e))throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${p}.`})},_=e=>{if("string"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},v=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new s.zF({reason:s.jn.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},w=(e,t)=>{if("number"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()<=r.getTime()-t)throw new s.zF({reason:s.jn.TokenExpired,message:`JWT is expired. Expiry date: ${i.toUTCString()}, Current date: ${r.toUTCString()}.`})},S=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new s.zF({reason:s.jn.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})},b=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new s.zF({reason:s.jn.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})};function T(e,t,r){if("object"==typeof e)return o.crypto.subtle.importKey("jwk",e,t,!1,[r]);let s=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,a.y)(t),s=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)s[e]=r.charCodeAt(e);return s}(e),i="sign"===r?"pkcs8":"spki";return o.crypto.subtle.importKey(i,s,t,!1,[r])}async function I(e,t){let{header:r,signature:i,raw:a}=e,n=new TextEncoder().encode([a.header,a.payload].join(".")),l=m(r.alg);try{let e=await T(t,l,"verify");return{data:await o.crypto.subtle.verify(l.name,e,i,n)}}catch(e){return{errors:[new s.zF({reason:s.jn.TokenInvalidSignature,message:e?.message})]}}}function E(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new s.zF({reason:s.jn.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,i,a]=t,n=new TextDecoder,o=JSON.parse(n.decode(l.parse(r,{loose:!0}))),c=JSON.parse(n.decode(l.parse(i,{loose:!0})));return{data:{header:o,payload:c,signature:l.parse(a,{loose:!0}),raw:{header:r,payload:i,signature:a,text:e}}}}async function A(e,t){let{audience:r,authorizedParties:i,clockSkewInMs:a,key:n}=t,o=a||5e3,{data:l,errors:c}=E(e);if(c)return{errors:c};let{header:d,payload:u}=l;try{let{typ:e,alg:t}=d;y(e),k(t);let{azp:s,sub:a,aud:n,iat:l,exp:c,nbf:h}=u;_(a),g([n],[r]),v(s,i),w(c,o),S(h,o),b(l,o)}catch(e){return{errors:[e]}}let{data:h,errors:p}=await I(l,n);return p?{errors:[new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Error verifying JWT signature. ${p[0]}`})]}:h?{data:u}:{errors:[new s.zF({reason:s.jn.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},44401:(e,t,r)=>{"use strict";r.d(t,{RZ:()=>s.RZ,ky:()=>s.ky,mC:()=>s.mC,q5:()=>s.q5,qS:()=>s.qS});var s=r(57136);r(94051)},45940:(e,t,r)=>{"use strict";r.d(t,{TD:()=>tk,AA:()=>q,Bs:()=>tQ,y3:()=>tp,tl:()=>tE,Z5:()=>tf,wI:()=>tg,nr:()=>tz});var s=r(92867),i=r(27322),a=r(6264),n=r(62016),o=r(76190),l={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},c=new Set(["first_factor","second_factor","multi_factor"]),d=new Set(["strict_mfa","strict","moderate","lax"]),u=e=>"number"==typeof e&&e>0,h=e=>c.has(e),p=e=>d.has(e),m=e=>e.startsWith("org:")?e:`org:${e}`,f=(e,t)=>{let{orgId:r,orgRole:s,orgPermissions:i}=t;return(e.role||e.permission)&&r&&s&&i?e.permission?i.includes(m(e.permission)):e.role?s===m(e.role):null:null},g=(e,t)=>{let{org:r,user:s}=k(e),[i,a]=t.split(":"),n=a||i;return"org"===i?r.includes(n):"user"===i?s.includes(n):[...r,...s].includes(n)},y=(e,t)=>{let{features:r,plans:s}=t;return e.feature&&r?g(r,e.feature):e.plan&&s?g(s,e.plan):null},k=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},_=e=>{if(!e)return!1;let t="string"==typeof e&&p(e),r="object"==typeof e&&h(e.level)&&u(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?l[e]:e).bind(null,e)},v=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=_(e.reverification);if(!r)return null;let{level:s,afterMinutes:i}=r(),[a,n]=t,o=-1!==a?i>a:null,l=-1!==n?i>n:null;switch(s){case"first_factor":return o;case"second_factor":return -1!==n?l:o;case"multi_factor":return -1===n?o:o&&l}},w=e=>t=>{if(!e.userId)return!1;let r=y(t,e),s=f(t,e),i=v(t,e);return[r||s,i].some(e=>null===e)?[r||s,i].some(e=>!0===e):[r||s,i].every(e=>!0===e)};r(94051);var S=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),s=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:s}},b=e=>{let t,r,s,i,a=e.fva??null,n=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,s=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:a}=k(e.fea),{permissions:n,featurePermissionMap:o}=S({per:e.o?.per,fpm:e.o?.fpm});i=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let s=[];for(let i=0;i<e.length;i++){let a=e[i];if(i>=r.length)continue;let n=r[i];if(n)for(let e=0;e<n.length;e++)1===n[e]&&s.push(`org:${a}:${t[e]}`)}return s}({features:a,featurePermissionMap:o,permissions:n})}}else t=e.org_id,r=e.org_role,s=e.org_slug,i=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:n,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:s,orgPermissions:i,factorVerificationAge:a}},T=r(72645);function I(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function E(e){return e&&e.sensitive?"":"i"}var A="https://api.clerk.com",C="@clerk/backend@1.33.0",x="2025-04-10",O={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},P={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:O.DevBrowser,Handshake:O.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:O.HandshakeNonce},q={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:O,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:P},U=RegExp("(?<!:)/{1,}","g");function N(...e){return e.filter(e=>e).join("/").replace(U,"/")}var z=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},J="/actor_tokens",R=class extends z{async create(e){return this.request({method:"POST",path:J,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:N(J,e,"revoke")})}},H="/accountless_applications",j=class extends z{async createAccountlessApplication(){return this.request({method:"POST",path:H})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:N(H,"complete")})}},M="/allowlist_identifiers",L=class extends z{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:M,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:M,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:N(M,e)})}},F=class extends z{async changeDomain(e){return this.request({method:"POST",path:N("/beta_features","change_domain"),bodyParams:e})}},D="/blocklist_identifiers",K=class extends z{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:D,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:D,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:N(D,e)})}},W="/clients",$=class extends z{async getClientList(e={}){return this.request({method:"GET",path:W,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:N(W,e)})}verifyClient(e){return this.request({method:"POST",path:N(W,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:N(W,"handshake_payload"),queryParams:e})}},B="/domains",G=class extends z{async list(){return this.request({method:"GET",path:B})}async add(e){return this.request({method:"POST",path:B,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:N(B,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:N(B,e)})}},V="/email_addresses",Q=class extends z{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:N(V,e)})}async createEmailAddress(e){return this.request({method:"POST",path:V,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:N(V,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:N(V,e)})}},Y="/instance",Z=class extends z{async get(){return this.request({method:"GET",path:Y})}async update(e){return this.request({method:"PATCH",path:Y,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:N(Y,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:N(Y,"organization_settings"),bodyParams:e})}},X="/invitations",ee=class extends z{async getInvitationList(e={}){return this.request({method:"GET",path:X,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:X,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:N(X,e,"revoke")})}},et=class extends z{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},er="/jwt_templates",es=class extends z{async list(e={}){return this.request({method:"GET",path:er,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:N(er,e)})}async create(e){return this.request({method:"POST",path:er,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:N(er,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:N(er,e)})}},ei="/organizations",ea=class extends z{async getOrganizationList(e){return this.request({method:"GET",path:ei,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:ei,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:N(ei,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:N(ei,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new i.fA.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:N(ei,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:N(ei,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:N(ei,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:N(ei,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:N(ei,t,"memberships"),queryParams:r})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:N(ei,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...s}=e;return this.requireId(t),this.request({method:"PATCH",path:N(ei,t,"memberships",r),bodyParams:s})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...s}=e;return this.request({method:"PATCH",path:N(ei,t,"memberships",r,"metadata"),bodyParams:s})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:N(ei,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:N(ei,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:N(ei,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:N(ei,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:N(ei,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...s}=e;return this.requireId(t),this.request({method:"POST",path:N(ei,t,"invitations",r,"revoke"),bodyParams:s})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:N(ei,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:N(ei,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...s}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:N(ei,t,"domains",r),bodyParams:s})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:N(ei,t,"domains",r)})}},en="/oauth_applications",eo=class extends z{async list(e={}){return this.request({method:"GET",path:en,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:N(en,e)})}async create(e){return this.request({method:"POST",path:en,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:N(en,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:N(en,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:N(en,e,"rotate_secret")})}},el="/phone_numbers",ec=class extends z{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:N(el,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:el,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:N(el,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:N(el,e)})}},ed=class extends z{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},eu="/redirect_urls",eh=class extends z{async getRedirectUrlList(){return this.request({method:"GET",path:eu,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:N(eu,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:eu,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:N(eu,e)})}},ep="/saml_connections",em=class extends z{async getSamlConnectionList(e={}){return this.request({method:"GET",path:ep,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:ep,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:N(ep,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:N(ep,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:N(ep,e)})}},ef="/sessions",eg=class extends z{async getSessionList(e={}){return this.request({method:"GET",path:ef,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:N(ef,e)})}async createSession(e){return this.request({method:"POST",path:ef,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:N(ef,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:N(ef,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:N(ef,e,"tokens",t||"")})}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...s}=t;return this.request({method:"POST",path:N(ef,e,"refresh"),bodyParams:s,queryParams:{suffixed_cookies:r}})}},ey="/sign_in_tokens",ek=class extends z{async createSignInToken(e){return this.request({method:"POST",path:ey,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:N(ey,e,"revoke")})}},e_="/sign_ups",ev=class extends z{async get(e){return this.requireId(e),this.request({method:"GET",path:N(e_,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:N(e_,t),bodyParams:r})}},ew=class extends z{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},eS="/users",eb=class extends z{async getUserList(e={}){let{limit:t,offset:r,orderBy:s,...i}=e,[a,n]=await Promise.all([this.request({method:"GET",path:eS,queryParams:e}),this.getCount(i)]);return{data:a,totalCount:n}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:N(eS,e)})}async createUser(e){return this.request({method:"POST",path:eS,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:N(eS,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new i.fA.FormData;return r.append("file",t?.file),this.request({method:"POST",path:N(eS,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:N(eS,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:N(eS,e)})}async getCount(e={}){return this.request({method:"GET",path:N(eS,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),i=r?t:`oauth_${t}`;return r&&(0,s.io)("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:N(eS,e,"oauth_access_tokens",i),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:N(eS,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:s}=e;return this.requireId(t),this.request({method:"GET",path:N(eS,t,"organization_memberships"),queryParams:{limit:r,offset:s}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:N(eS,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:N(eS,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:N(eS,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:N(eS,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:N(eS,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:N(eS,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:N(eS,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:N(eS,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:N(eS,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:N(eS,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:N(eS,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:N(eS,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:N(eS,e,"totp")})}},eT="/waitlist_entries",eI=class extends z{async list(e={}){return this.request({method:"GET",path:eT,queryParams:e})}async create(e){return this.request({method:"POST",path:eT,bodyParams:e})}},eE="/webhooks",eA=class extends z{async createSvixApp(){return this.request({method:"POST",path:N(eE,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:N(eE,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:N(eE,"svix")})}};function eC(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var ex=class e{constructor(e,t,r,s,i,a,n,o){this.id=e,this.status=t,this.userId=r,this.actor=s,this.token=i,this.url=a,this.createdAt=n,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},eO=class e{constructor(e,t,r,s){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=s}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},eP=class e{constructor(e,t,r,s,i,a,n){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=s,this.updatedAt=i,this.instanceId=a,this.invitationId=n}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},eq=class e{constructor(e,t,r,s,i,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=s,this.updatedAt=i,this.instanceId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},eU=class e{constructor(e,t,r,s,i,a,n,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=s,this.country=i,this.browserVersion=a,this.browserName=n,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},eN=class e{constructor(e,t,r,s,i,a,n,o,l,c,d,u=null){this.id=e,this.clientId=t,this.userId=r,this.status=s,this.lastActiveAt=i,this.expireAt=a,this.abandonAt=n,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=c,this.latestActivity=d,this.actor=u}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&eU.fromJSON(t.latest_activity),t.actor)}},ez=class e{constructor(e,t,r,s,i,a,n,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=s,this.signUpId=i,this.lastActiveSessionId=a,this.createdAt=n,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>eN.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},eJ=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},eR=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},eH=class e{constructor(e,t,r,s){this.object=e,this.id=t,this.slug=r,this.deleted=s}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},ej=class e{constructor(e,t,r,s,i,a,n,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=s,this.developmentOrigin=i,this.cnameTargets=a,this.accountsPortalUrl=n,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>eJ.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},eM=class e{constructor(e,t,r,s,i,a,n,o,l,c,d){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=s,this.subject=i,this.body=a,this.bodyPlain=n,this.status=o,this.slug=l,this.data=c,this.deliveredByClerk=d}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},eL=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eF=class e{constructor(e,t,r=null,s=null,i=null,a=null,n=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=s,this.expireAt=i,this.nonce=a,this.message=n}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},eD=class e{constructor(e,t,r,s){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eF.fromJSON(t.verification),t.linked_to.map(e=>eL.fromJSON(e)))}},eK=class e{constructor(e,t,r,s,i,a,n,o,l,c,d,u={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=s,this.approvedScopes=i,this.emailAddress=a,this.firstName=n,this.lastName=o,this.imageUrl=l,this.username=c,this.phoneNumber=d,this.publicMetadata=u,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&eF.fromJSON(t.verification))}},eW=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},e$=class e{constructor(e,t,r,s,i){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=s,this.ignoreDotsForGmailAddresses=i}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},eB=class e{constructor(e,t,r,s,i){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=s,this.enhancedEmailDeliverability=i}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},eG=class e{constructor(e,t,r,s,i,a,n,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=s,this.updatedAt=i,this.status=a,this.url=n,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},eV={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},eQ=class e{constructor(e,t,r,s,i,a,n,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=s,this.allowedClockSkew=i,this.customSigningKey=a,this.signingAlgorithm=n,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},eY=class e{constructor(e,t,r,s={},i,a,n,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=s,this.label=i,this.scopes=a,this.tokenSecret=n,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},eZ=class e{constructor(e,t,r,s,i,a,n,o,l,c,d,u,h,p,m){this.id=e,this.instanceId=t,this.name=r,this.clientId=s,this.isPublic=i,this.scopes=a,this.redirectUris=n,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=c,this.discoveryUrl=d,this.tokenIntrospectionUrl=u,this.createdAt=h,this.updatedAt=p,this.clientSecret=m}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},eX=class e{constructor(e,t,r,s,i,a,n,o={},l={},c,d,u,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=s,this.hasImage=i,this.createdAt=a,this.updatedAt=n,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=c,this.adminDeleteEnabled=d,this.membersCount=u,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},e0=class e{constructor(e,t,r,s,i,a,n,o,l,c,d={},u={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=s,this.organizationId=i,this.createdAt=a,this.updatedAt=n,this.expiresAt=o,this.url=l,this.status=c,this.publicMetadata=d,this.privateMetadata=u,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},e1=class e{constructor(e,t,r,s={},i={},a,n,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=s,this.privateMetadata=i,this.createdAt=a,this.updatedAt=n,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,eX.fromJSON(t.organization),e5.fromJSON(t.public_user_data));return r._raw=t,r}},e5=class e{constructor(e,t,r,s,i,a){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=s,this.hasImage=i,this.userId=a}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},e2=class e{constructor(e,t,r,s,i,a,n,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=s,this.creatorRole=i,this.adminDeleteEnabled=a,this.domainsEnabled=n,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},e3=class e{constructor(e,t,r,s,i,a){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=s,this.verification=i,this.linkedTo=a}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eF.fromJSON(t.verification),t.linked_to.map(e=>eL.fromJSON(e)))}},e7=class e{constructor(e,t,r,s,i,a,n){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=s,this.successful=i,this.createdAt=a,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},e4=class e{constructor(e,t,r,s){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=s}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},e6=class e{constructor(e,t,r,s,i,a,n){this.id=e,this.userId=t,this.token=r,this.status=s,this.url=i,this.createdAt=a,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},e9=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},e8=class e{constructor(e,t,r,s){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=s}static fromJSON(t){return new e(t.email_address&&e9.fromJSON(t.email_address),t.phone_number&&e9.fromJSON(t.phone_number),t.web3_wallet&&e9.fromJSON(t.web3_wallet),t.external_account)}},te=class e{constructor(e,t,r,s,i,a,n,o,l,c,d,u,h,p,m,f,g,y,k,_,v,w){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=s,this.missingFields=i,this.unverifiedFields=a,this.verifications=n,this.username=o,this.emailAddress=l,this.phoneNumber=c,this.web3Wallet=d,this.passwordEnabled=u,this.firstName=h,this.lastName=p,this.customAction=m,this.externalId=f,this.createdSessionId=g,this.createdUserId=y,this.abandonAt=k,this.legalAcceptedAt=_,this.publicMetadata=v,this.unsafeMetadata=w}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?e8.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},tt=class e{constructor(e,t,r,s,i,a,n){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=s,this.status=i,this.phoneNumberId=a,this.data=n}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},tr=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},ts=class e{constructor(e,t,r,s,i,a,n,o,l,c){this.id=e,this.name=t,this.domain=r,this.active=s,this.provider=i,this.syncUserAttributes=a,this.allowSubdomains=n,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},ti=class e{constructor(e,t,r,s,i,a,n,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=s,this.emailAddress=i,this.firstName=a,this.lastName=n,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&eF.fromJSON(t.verification),t.saml_connection&&ts.fromJSON(t.saml_connection))}},ta=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eF.fromJSON(t.verification))}},tn=class e{constructor(e,t,r,s,i,a,n,o,l,c,d,u,h,p,m,f,g,y,k,_={},v={},w={},S=[],b=[],T=[],I=[],E=[],A,C,x=null,O,P){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=s,this.twoFactorEnabled=i,this.banned=a,this.locked=n,this.createdAt=o,this.updatedAt=l,this.imageUrl=c,this.hasImage=d,this.primaryEmailAddressId=u,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=m,this.externalId=f,this.username=g,this.firstName=y,this.lastName=k,this.publicMetadata=_,this.privateMetadata=v,this.unsafeMetadata=w,this.emailAddresses=S,this.phoneNumbers=b,this.web3Wallets=T,this.externalAccounts=I,this.samlAccounts=E,this.lastActiveAt=A,this.createOrganizationEnabled=C,this.createOrganizationsLimit=x,this.deleteSelfEnabled=O,this.legalAcceptedAt=P,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>eD.fromJSON(e)),(t.phone_numbers||[]).map(e=>e3.fromJSON(e)),(t.web3_wallets||[]).map(e=>ta.fromJSON(e)),(t.external_accounts||[]).map(e=>eK.fromJSON(e)),(t.saml_accounts||[]).map(e=>ti.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},to=class e{constructor(e,t,r,s,i,a,n){this.id=e,this.emailAddress=t,this.status=r,this.invitation=s,this.createdAt=i,this.updatedAt=a,this.isLocked=n}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&eG.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function tl(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return eH.fromJSON(e);switch(e.object){case eV.AccountlessApplication:return eO.fromJSON(e);case eV.ActorToken:return ex.fromJSON(e);case eV.AllowlistIdentifier:return eP.fromJSON(e);case eV.BlocklistIdentifier:return eq.fromJSON(e);case eV.Client:return ez.fromJSON(e);case eV.Cookies:return eR.fromJSON(e);case eV.Domain:return ej.fromJSON(e);case eV.EmailAddress:return eD.fromJSON(e);case eV.Email:return eM.fromJSON(e);case eV.Instance:return eW.fromJSON(e);case eV.InstanceRestrictions:return e$.fromJSON(e);case eV.InstanceSettings:return eB.fromJSON(e);case eV.Invitation:return eG.fromJSON(e);case eV.JwtTemplate:return eQ.fromJSON(e);case eV.OauthAccessToken:return eY.fromJSON(e);case eV.OAuthApplication:return eZ.fromJSON(e);case eV.Organization:return eX.fromJSON(e);case eV.OrganizationInvitation:return e0.fromJSON(e);case eV.OrganizationMembership:return e1.fromJSON(e);case eV.OrganizationSettings:return e2.fromJSON(e);case eV.PhoneNumber:return e3.fromJSON(e);case eV.ProxyCheck:return e7.fromJSON(e);case eV.RedirectUrl:return e4.fromJSON(e);case eV.SignInToken:return e6.fromJSON(e);case eV.SignUpAttempt:return te.fromJSON(e);case eV.Session:return eN.fromJSON(e);case eV.SmsMessage:return tt.fromJSON(e);case eV.Token:return tr.fromJSON(e);case eV.TotalCount:return e.total_count;case eV.User:return tn.fromJSON(e);case eV.WaitlistEntry:return to.fromJSON(e);default:return e}}function tc(e){var t;return t=async t=>{let r,{secretKey:s,requireSecretKey:a=!0,apiUrl:n=A,apiVersion:l="v1",userAgent:c=C}=e,{path:d,method:u,queryParams:h,headerParams:p,bodyParams:m,formData:f}=t;a&&eC(s);let g=new URL(N(n,l,d));if(h)for(let[e,t]of Object.entries(o({...h})))t&&[t].flat().forEach(t=>g.searchParams.append(e,t));let y={"Clerk-API-Version":x,"User-Agent":c,...p};s&&(y.Authorization=`Bearer ${s}`);try{var k;f?r=await i.fA.fetch(g.href,{method:u,headers:y,body:f}):(y["Content-Type"]="application/json",r=await i.fA.fetch(g.href,{method:u,headers:y,...(()=>{if(!("GET"!==u&&m&&Object.keys(m).length>0))return null;let e=e=>o(e,{deep:!1});return{body:JSON.stringify(Array.isArray(m)?m.map(e):e(m))}})()}));let e=r?.headers&&r.headers?.get(q.Headers.ContentType)===q.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:th(t),status:r?.status,statusText:r?.statusText,clerkTraceId:td(t,r?.headers),retryAfter:tu(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>tl(e))}:(k=t)&&"object"==typeof k&&"data"in k&&Array.isArray(k.data)&&void 0!==k.data?{data:t.data.map(e=>tl(e)),totalCount:t.total_count}:{data:tl(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:td(e,r?.headers)};return{data:null,errors:th(e),status:r?.status,statusText:r?.statusText,clerkTraceId:td(e,r?.headers),retryAfter:tu(r?.headers)}}},async(...e)=>{let{data:r,errors:s,totalCount:i,status:a,statusText:o,clerkTraceId:l,retryAfter:c}=await t(...e);if(s){let e=new n.LR(o||"",{data:[],status:a,clerkTraceId:l,retryAfter:c});throw e.errors=s,e}return void 0!==i?{data:r,totalCount:i}:r}}function td(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function tu(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function th(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(n.u$):[]}return[]}function tp(e){let t=tc(e);return{__experimental_accountlessApplications:new j(tc({...e,requireSecretKey:!1})),actorTokens:new R(t),allowlistIdentifiers:new L(t),betaFeatures:new F(t),blocklistIdentifiers:new K(t),clients:new $(t),domains:new G(t),emailAddresses:new Q(t),instance:new Z(t),invitations:new ee(t),jwks:new et(t),jwtTemplates:new es(t),oauthApplications:new eo(t),organizations:new ea(t),phoneNumbers:new ec(t),proxyChecks:new ed(t),redirectUrls:new eh(t),samlConnections:new em(t),sessions:new eg(t),signInTokens:new ek(t),signUps:new ev(t),testingTokens:new ew(t),users:new eb(t),waitlistEntries:new eI(t),webhooks:new eA(t)}}var tm=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function tf(e,t,r){let{actor:s,sessionId:i,sessionStatus:a,userId:n,orgId:o,orgRole:l,orgSlug:c,orgPermissions:d,factorVerificationAge:u}=b(r),h=tp(e),p=ty({sessionId:i,sessionToken:t,fetcher:async(...e)=>(await h.sessions.getToken(...e)).jwt});return{actor:s,sessionClaims:r,sessionId:i,sessionStatus:a,userId:n,orgId:o,orgRole:l,orgSlug:c,orgPermissions:d,factorVerificationAge:u,getToken:p,has:w({orgId:o,orgRole:l,orgPermissions:d,userId:n,factorVerificationAge:u,features:r.fea||"",plans:r.pla||""}),debug:tm({...e,sessionToken:t})}}function tg(e,t){return{sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:tm(e)}}var ty=e=>{let{fetcher:t,sessionToken:r,sessionId:s}=e||{};return async(e={})=>s?e.template?t(s,e.template):r:null},tk={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},t_={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function tv(e,t,r=new Headers,s){let i=tf(e,s,t);return{status:tk.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:({treatPendingAsSignedOut:e=!0}={})=>e&&"pending"===i.sessionStatus?tg(void 0,i.sessionStatus):i,headers:r,token:s}}function tw(e,t,r="",s=new Headers){return tS({status:tk.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:s,toAuth:()=>tg({...e,status:tk.SignedOut,reason:t,message:r}),token:null})}var tS=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(q.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(q.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(q.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},tb=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},tT=(...e)=>new tb(...e),tI=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(q.Headers.ForwardedProto),s=e.headers.get(q.Headers.ForwardedHost),i=e.headers.get(q.Headers.Host),a=t.protocol,n=this.getFirstValueFromHeader(s)??i,o=this.getFirstValueFromHeader(r)??a?.replace(/[:/]/,""),l=n&&o?`${o}://${n}`:t.origin;return l===t.origin?tT(t):tT(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,T.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},tE=(...e)=>e[0]instanceof tI?e[0]:new tI(...e),tA={},tC=0;function tx(e,t=!0){tA[e.kid]=e,tC=t?Date.now():-1}var tO="local";function tP(e){if(!tA[tO]){if(!e)throw new a.zF({action:a.z.SetClerkJWTKey,message:"Missing local JWK.",reason:a.jn.LocalJWKMissing});tx({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return tA[tO]}async function tq({secretKey:e,apiUrl:t=A,apiVersion:r="v1",kid:i,skipJwksCache:n}){if(n||function(){if(-1===tC)return!1;let e=Date.now()-tC>=3e5;return e&&(tA={}),e}()||!tA[i]){if(!e)throw new a.zF({action:a.z.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:a.jn.RemoteJWKFailedToLoad});let{keys:i}=await (0,s.L5)(()=>tU(t,e,r));if(!i||!i.length)throw new a.zF({action:a.z.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:a.jn.RemoteJWKFailedToLoad});i.forEach(e=>tx(e))}let o=tA[i];if(!o){let e=Object.values(tA).map(e=>e.kid).sort().join(", ");throw new a.zF({action:`Go to your Dashboard and validate your secret and public keys are correct. ${a.z.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${i}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:a.jn.JWKKidMismatch})}return o}async function tU(e,t,r){if(!t)throw new a.zF({action:a.z.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:a.jn.RemoteJWKFailedToLoad});let s=new URL(e);s.pathname=N(s.pathname,r,"/jwks");let n=await i.fA.fetch(s.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":x,"Content-Type":"application/json","User-Agent":C}});if(!n.ok){let e=await n.json(),t=tN(e?.errors,a.qu.InvalidSecretKey);if(t){let e=a.jn.InvalidSecretKey;throw new a.zF({action:a.z.ContactSupport,message:t.message,reason:e})}throw new a.zF({action:a.z.ContactSupport,message:`Error loading Clerk JWKS from ${s.href} with code=${n.status}`,reason:a.jn.RemoteJWKFailedToLoad})}return n.json()}var tN=(e,t)=>e?e.find(e=>e.code===t):null;async function tz(e,t){let{data:r,errors:s}=(0,i.iU)(e);if(s)return{errors:s};let{header:n}=r,{kid:o}=n;try{let r;if(t.jwtKey)r=tP(t.jwtKey);else{if(!t.secretKey)return{errors:[new a.zF({action:a.z.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:a.jn.JWKFailedToResolve})]};r=await tq({...t,kid:o})}return await (0,i.J0)(e,{...t,key:r})}catch(e){return{errors:[e]}}}var tJ=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(q.Cookies.ClientUat),t=this.getCookie(q.Cookies.ClientUat),r=this.getSuffixedCookie(q.Cookies.Session)||"",s=this.getCookie(q.Cookies.Session)||"";if(s&&!this.tokenHasIssuer(s))return!1;if(s&&!this.tokenBelongsToInstance(s))return!0;if(!e&&!r)return!1;let{data:a}=(0,i.iU)(s),n=a?.payload.iat||0,{data:o}=(0,i.iU)(r),l=o?.payload.iat||0;if("0"!==e&&"0"!==t&&n>l||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(o);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,s.q5)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,s.q5)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=r.instanceType,this.frontendApi=r.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.parseAuthorizationHeader(this.getHeader(q.Headers.Authorization)),this.origin=this.getHeader(q.Headers.Origin),this.host=this.getHeader(q.Headers.Host),this.forwardedHost=this.getHeader(q.Headers.ForwardedHost),this.forwardedProto=this.getHeader(q.Headers.CloudFrontForwardedProto)||this.getHeader(q.Headers.ForwardedProto),this.referrer=this.getHeader(q.Headers.Referrer),this.userAgent=this.getHeader(q.Headers.UserAgent),this.secFetchDest=this.getHeader(q.Headers.SecFetchDest),this.accept=this.getHeader(q.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(q.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(q.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(q.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(q.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(q.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(q.QueryParameters.Handshake)||this.getCookie(q.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(q.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(q.QueryParameters.HandshakeNonce)||this.getCookie(q.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,s.ky)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=(0,i.iU)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,i.iU)(e);if(r)return!1;let s=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===s}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},tR=async(e,t)=>new tJ(t.publishableKey?await (0,s.qS)(t.publishableKey,i.fA.crypto.subtle):"",e,t),tH=e=>e.split(";")[0]?.split("=")[0],tj=e=>e.split(";")[0]?.split("=")[1];async function tM(e,{key:t}){let{data:r,errors:s}=(0,i.iU)(e);if(s)throw s[0];let{header:n,payload:o}=r,{typ:l,alg:c}=n;(0,i.qf)(l),(0,i.l3)(c);let{data:d,errors:u}=await (0,i.nk)(r,t);if(u)throw new a.zF({reason:a.jn.TokenVerificationFailed,message:`Error verifying handshake token. ${u[0]}`});if(!d)throw new a.zF({reason:a.jn.TokenInvalidSignature,message:"Handshake signature is invalid."});return o}async function tL(e,t){let r,{secretKey:s,apiUrl:n,apiVersion:o,jwksCacheTtlInMs:l,jwtKey:c,skipJwksCache:d}=t,{data:u,errors:h}=(0,i.iU)(e);if(h)throw h[0];let{kid:p}=u.header;if(c)r=tP(c);else if(s)r=await tq({secretKey:s,apiUrl:n,apiVersion:o,kid:p,jwksCacheTtlInMs:l,skipJwksCache:d});else throw new a.zF({action:a.z.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:a.jn.JWKFailedToResolve});return await tM(e,{key:r})}var tF=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.replace(/http(s)?:\/\//,""),s=new URL(`https://${r}/v1/client/handshake`);s.searchParams.append("redirect_url",t?.href||""),s.searchParams.append("__clerk_api_version",x),s.searchParams.append(q.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),s.searchParams.append(q.QueryParameters.HandshakeReason,e),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&s.searchParams.append(q.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let i=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return i&&this.getOrganizationSyncQueryParams(i).forEach((e,t)=>{s.searchParams.append(t,e)}),new Headers({[q.Headers.Location]:s.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await tL(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),tH(t).startsWith(q.Cookies.Session)&&(r=tj(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(q.QueryParameters.Handshake),t.searchParams.delete(q.QueryParameters.HandshakeHelp),e.append(q.Headers.Location,t.toString()),e.set(q.Headers.CacheControl,"no-store")}if(""===r)return tw(this.authenticateContext,t_.SessionTokenMissing,"",e);let{data:s,errors:[i]=[]}=await tz(r,this.authenticateContext);if(s)return tv(this.authenticateContext,s,e,r);if("development"===this.authenticateContext.instanceType&&(i?.reason===a.jn.TokenExpired||i?.reason===a.jn.TokenNotActiveYet||i?.reason===a.jn.TokenIatInTheFuture)){let t=new a.zF({action:i.action,message:i.message,reason:i.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:s,errors:[n]=[]}=await tz(r,{...this.authenticateContext,clockSkewInMs:864e5});if(s)return tv(this.authenticateContext,s,e,r);throw Error(n?.message||"Clerk: Handshake retry failed.")}throw Error(i?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===a.jn.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=q.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(q.QueryParameters.DevBrowser),t.searchParams.delete(q.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},tD=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,s,i,a,n,o,l;return r=void 0,s=[],i=function e(t,r,s){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,s=0,i=r.exec(e.source);i;)t.push({name:i[1]||s++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,s).source}),new RegExp("(?:".concat(i.join("|"),")"),E(s))):function(e,t,r){void 0===r&&(r={});for(var s=r.strict,i=void 0!==s&&s,a=r.start,n=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,c=r.delimiter,d=r.endsWith,u="[".concat(I(void 0===d?"":d),"]|$"),h="[".concat(I(void 0===c?"/#?":c),"]"),p=void 0===a||a?"^":"",m=0;m<e.length;m++){var f=e[m];if("string"==typeof f)p+=I(l(f));else{var g=I(l(f.prefix)),y=I(l(f.suffix));if(f.pattern)if(t&&t.push(f),g||y)if("+"===f.modifier||"*"===f.modifier){var k="*"===f.modifier?"?":"";p+="(?:".concat(g,"((?:").concat(f.pattern,")(?:").concat(y).concat(g,"(?:").concat(f.pattern,"))*)").concat(y,")").concat(k)}else p+="(?:".concat(g,"(").concat(f.pattern,")").concat(y,")").concat(f.modifier);else{if("+"===f.modifier||"*"===f.modifier)throw TypeError('Can not repeat "'.concat(f.name,'" without a prefix and suffix'));p+="(".concat(f.pattern,")").concat(f.modifier)}else p+="(?:".concat(g).concat(y,")").concat(f.modifier)}}if(void 0===n||n)i||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(u,")"):"$";else{var _=e[e.length-1],v="string"==typeof _?h.indexOf(_[_.length-1])>-1:void 0===_;i||(p+="(?:".concat(h,"(?=").concat(u,"))?")),v||(p+="(?=".concat(h,"|").concat(u,")"))}return new RegExp(p,E(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var s=e[r];if("*"===s||"+"===s||"?"===s){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===s){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===s){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===s){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===s){for(var i="",a=r+1;a<e.length;){var n=e.charCodeAt(a);if(n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||95===n){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===s){var o=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '.concat(a));for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at ".concat(a));l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),s=t.prefixes,i=void 0===s?"./":s,a=t.delimiter,n=void 0===a?"/#?":a,o=[],l=0,c=0,d="",u=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},h=function(e){var t=u(e);if(void 0!==t)return t;var s=r[c],i=s.type,a=s.index;throw TypeError("Unexpected ".concat(i," at ").concat(a,", expected ").concat(e))},p=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t},m=function(e){for(var t=0;t<n.length;t++){var r=n[t];if(e.indexOf(r)>-1)return!0}return!1},f=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||m(r)?"[^".concat(I(n),"]+?"):"(?:(?!".concat(I(r),")[^").concat(I(n),"])+?")};c<r.length;){var g=u("CHAR"),y=u("NAME"),k=u("PATTERN");if(y||k){var _=g||"";-1===i.indexOf(_)&&(d+=_,_=""),d&&(o.push(d),d=""),o.push({name:y||l++,prefix:_,suffix:"",pattern:k||f(_),modifier:u("MODIFIER")||""});continue}var v=g||u("ESCAPED_CHAR");if(v){d+=v;continue}if(d&&(o.push(d),d=""),u("OPEN")){var _=p(),w=u("NAME")||"",S=u("PATTERN")||"",b=p();h("CLOSE"),o.push({name:w||(S?l++:""),pattern:w&&!S?f(_):S,prefix:_,suffix:b,modifier:u("MODIFIER")||""});continue}h("END")}return o}(t,s),r,s)}(e,s,r),a=s,n=r,void 0===n&&(n={}),o=n.decode,l=void 0===o?function(e){return e}:o,function(e){var t=i.exec(e);if(!t)return!1;for(var r=t[0],s=t.index,n=Object.create(null),o=1;o<t.length;o++)!function(e){if(void 0!==t[e]){var r=a[e-1];"*"===r.modifier||"+"===r.modifier?n[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):n[r.name]=l(t[e],r)}}(o);return{path:r,index:s,params:n}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},tK={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};async function tW(e,t){let r=await tR(tE(e),t);if(eC(r.secretKey),r.isSatellite){var n=r.signInUrl,o=r.secretKey;if(!n&&(0,s.Ve)(o))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let l=new tD(t.organizationSyncOptions),c=new tF(r,{organizationSyncOptions:t.organizationSyncOptions},l);async function d(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:tK.MissingApiClient}}};let{sessionToken:s,refreshTokenInCookie:a}=r;if(!s)return{data:null,error:{message:"Session token must be provided.",cause:{reason:tK.MissingSessionToken}}};if(!a)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:tK.MissingRefreshToken}}};let{data:n,errors:o}=(0,i.iU)(s);if(!n||o)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:tK.ExpiredSessionTokenDecodeFailed,errors:o}}};if(!n?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:tK.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(n.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:s||"",refresh_token:a||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:tK.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:tK.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function u(e){let{data:t,error:r}=await d(e);if(!t||0===t.length)return{data:null,error:r};let s=new Headers,i="";t.forEach(e=>{s.append("Set-Cookie",e),tH(e).startsWith(q.Cookies.Session)&&(i=tj(e))});let{data:a,errors:n}=await tz(i,e);return n?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:tK.InvalidSessionToken,errors:n}}}:{data:{jwtPayload:a,sessionToken:i,headers:s},error:null}}function h(e,t,r,s){if(!c.isRequestEligibleForHandshake())return tw(e,t,r);let i=s??c.buildRedirectToHandshake(t);return(i.get(q.Headers.Location)&&i.set(q.Headers.CacheControl,"no-store"),c.checkAndTrackRedirectLoop(i))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),tw(e,t,r)):function(e,t,r="",s){return tS({status:tk.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:s,toAuth:()=>null,token:null})}(e,t,r,i)}async function p(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:s}=await tz(e,r);if(s)throw s[0];return tv(r,t,void 0,e)}catch(e){return f(e,"header")}}async function m(){let e=r.clientUat,t=!!r.sessionTokenInCookie,s=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await c.resolveHandshake()}catch(e){e instanceof a.zF&&"development"===r.instanceType?c.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(q.QueryParameters.DevBrowser))return h(r,t_.DevBrowserSync,"");let n=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&n)return h(r,t_.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&n&&!r.clerkUrl.searchParams.has(q.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(q.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[q.Headers.Location]:e.toString()});return h(r,t_.SatelliteCookieNeedsSyncing,"",t)}let o=new URL(r.clerkUrl).searchParams.get(q.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&o){let e=new URL(o);r.devBrowserToken&&e.searchParams.append(q.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(q.QueryParameters.ClerkSynced,"true");let t=new Headers({[q.Headers.Location]:e.toString()});return h(r,t_.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!s)return h(r,t_.DevBrowserMissing,"");if(!e&&!t)return tw(r,t_.SessionTokenAndUATMissing,"");if(!e&&t)return h(r,t_.SessionTokenWithoutClientUAT,"");if(e&&!t)return h(r,t_.ClientUATWithoutSessionToken,"");let{data:d,errors:u}=(0,i.iU)(r.sessionTokenInCookie);if(u)return f(u[0],"cookie");if(d.payload.iat<r.clientUat)return h(r,t_.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await tz(r.sessionTokenInCookie,r);if(t)throw t[0];let s=tv(r,e,void 0,r.sessionTokenInCookie),i=s.toAuth();if(i.userId){let e=function(e,t){let r=l.findTarget(e.clerkUrl);if(!r)return null;let s=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(s=!0),r.organizationId&&r.organizationId!==t.orgId&&(s=!0)),"personalAccount"===r.type&&t.orgId&&(s=!0),!s)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let i=h(e,t_.ActiveOrganizationMismatch,"");return"handshake"!==i.status?null:i}(r,i);if(e)return e}return s}catch(e){return f(e,"cookie")}}async function f(t,s){let i;if(!(t instanceof a.zF))return tw(r,t_.UnexpectedError);if(t.reason===a.jn.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await u(r);if(e)return tv(r,e.jwtPayload,e.headers,e.sessionToken);i=t?.cause?.reason?t.cause.reason:tK.UnexpectedSDKError}else i="GET"!==e.method?tK.NonEligibleNonGet:r.refreshTokenInCookie?null:tK.NonEligibleNoCookie;return(t.tokenCarrier=s,[a.jn.TokenExpired,a.jn.TokenNotActiveYet,a.jn.TokenIatInTheFuture].includes(t.reason))?h(r,tB({tokenError:t.reason,refreshError:i}),t.getFullMessage()):tw(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?p():m()}var t$=e=>{let{isSignedIn:t,proxyUrl:r,reason:s,message:i,publishableKey:a,isSatellite:n,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:s,message:i,publishableKey:a,isSatellite:n,domain:o}},tB=({tokenError:e,refreshError:t})=>{switch(e){case a.jn.TokenExpired:return`${t_.SessionTokenExpired}-refresh-${t}`;case a.jn.TokenNotActiveYet:return t_.SessionTokenNBF;case a.jn.TokenIatInTheFuture:return t_.SessionTokenIatInTheFuture;default:return t_.UnexpectedError}};function tG(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var tV={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function tQ(e){let t=tG(tV,e.options),r=e.apiClient;return{authenticateRequest:(e,s={})=>{let{apiUrl:i,apiVersion:a}=t,n=tG(t,s);return tW(e,{...s,...n,apiUrl:i,apiVersion:a,apiClient:r})},debugRequestState:t$}}},49530:(e,t,r)=>{"use strict";r.d(t,{zz:()=>i});var s=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let s={...r};for(let r of Object.keys(s)){let i=e(r.toString());i!==r&&(s[i]=s[r],delete s[r]),"object"==typeof s[i]&&(s[i]=t(s[i]))}return s};return t};function i(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}s(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),s(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},54726:(e,t,r)=>{"use strict";r.d(t,{H$:()=>d,mG:()=>n,V2:()=>u,o7:()=>c,fS:()=>p,ev:()=>_,Rg:()=>h,At:()=>l,tm:()=>g,rB:()=>o,qW:()=>m,sE:()=>f,Mh:()=>k,nN:()=>y});var s=r(57136),i=r(86525);r(94051);var a=r(87553);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let n=process.env.CLERK_API_VERSION||"v1",o=process.env.CLERK_SECRET_KEY||"",l="pk_live_Y2xlcmsuY3ViZW50LmRldiQ",c=process.env.CLERK_ENCRYPTION_KEY||"",d=process.env.CLERK_API_URL||(e=>{let t=(0,s.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&i.iM.some(e=>t?.endsWith(e))?i.FW:i.mG.some(e=>t?.endsWith(e))?i.Vc:i.ub.some(e=>t?.endsWith(e))?i.HG:i.FW})(l),u="cubent.dev",h=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",p=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,m="/sign-in",f="/sign-up",g={name:"@clerk/nextjs",version:"6.20.0",environment:"production"},y=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),k=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),_=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1},55911:(e,t,r)=>{"use strict";r.d(t,{y:()=>s});var s=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e},57136:(e,t,r)=>{"use strict";r.d(t,{RZ:()=>c,qS:()=>u,ky:()=>h,mC:()=>d,q5:()=>o});var s=r(55911),i=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,a=r(86525),n="pk_live_";function o(e,t={}){if(!(e=e||"")||!l(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!l(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(n)?"production":"development",i=(0,s.y)(e.split("_")[2]);return i=i.slice(0,-1),t.proxyUrl?i=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:r,frontendApi:i}}function l(e=""){try{let t=e.startsWith(n)||e.startsWith("pk_test_"),r=(0,s.y)(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function c(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,s=e.get(r);return void 0===s&&(s=a.gE.some(e=>r.endsWith(e)),e.set(r,s)),s}}}function d(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function u(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return i(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var h=(e,t)=>`${e}_${t}`},62016:(e,t,r)=>{"use strict";r.d(t,{LR:()=>i,_r:()=>n,u$:()=>s});function s(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var i=class e extends Error{constructor(t,{data:r,status:i,clerkTraceId:a,retryAfter:n}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=i,this.message=t,this.clerkTraceId=a,this.retryAfter=n,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(s):[]}(r)}},a=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function n({packageName:e,customMessages:t}){let r=e,s={...a,...t};function i(e,t){if(!t)return`${r}: ${e}`;let s=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();s=s.replace(`{{${r[1]}}}`,e)}return`${r}: ${s}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(s,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(s.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(s.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(s.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(s.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(s.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}r(94051)},65931:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});var s=r(21757),i=r(54726);let a=!r(74365).M&&(0,s.b_)()&&!i.ev},72645:(e,t)=>{"use strict";t.qg=function(e,t){let n=new r,o=e.length;if(o<2)return n;let l=t?.decode||a,c=0;do{let t=e.indexOf("=",c);if(-1===t)break;let r=e.indexOf(";",c),a=-1===r?o:r;if(t>a){c=e.lastIndexOf(";",t-1)+1;continue}let d=s(e,c,t),u=i(e,t,d),h=e.slice(d,u);if(void 0===n[h]){let r=s(e,t+1,a),o=i(e,a,r),c=l(e.slice(r,o));n[h]=c}c=a+1}while(c<o);return n},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function a(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},74365:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var s=r(3680);let i=s.rE.startsWith("13.")||s.rE.startsWith("14.0")},76190:(e,t,r)=>{"use strict";let s=r(1702),{snakeCase:i}=r(79488),a={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==a))throw Error("obj must be array of plain objects")}else if(e.constructor!==a)throw Error("obj must be an plain object");return s(e,function(e,r){var s,a,n,o,l;return[(s=t.exclude,a=e,s.some(function(e){return"string"==typeof e?e===a:e.test(a)}))?e:i(e,t.parsingOptions),r,(n=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(n,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},77071:(e,t,r)=>{"use strict";r.d(t,{Fj:()=>a,MC:()=>i,b_:()=>s});var s=()=>!1,i=()=>!1,a=()=>{try{return!0}catch{}return!1}},79488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>l});var s=function(){return(s=Object.assign||function(e){for(var t,r=1,s=arguments.length;r<s;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function i(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var a=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],n=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=s({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,s=t.stripRegexp,l=t.transform,c=t.delimiter,d=o(o(e,void 0===r?a:r,"$1\0$2"),void 0===s?n:s,"\0"),u=0,h=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(h-1);)h--;return d.slice(u,h).split("\0").map(void 0===l?i:l).join(void 0===c?" ":c)}(e,s({delimiter:"."},r))}},86525:(e,t,r)=>{"use strict";r.d(t,{FW:()=>c,HG:()=>l,Vc:()=>o,gE:()=>i,iM:()=>s,mG:()=>a,ub:()=>n});var s=[".lcl.dev",".lclstage.dev",".lclclerk.com"],i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],a=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],n=[".accountsstage.dev"],o="https://api.lclclerk.com",l="https://api.clerkstage.dev",c="https://api.clerk.com"},87553:(e,t,r)=>{"use strict";r.d(t,{zz:()=>s.zz});var s=r(49530);r(94051)},92867:(e,t,r)=>{"use strict";r.d(t,{io:()=>u,sb:()=>h,qS:()=>l.qS,ky:()=>l.ky,Ve:()=>l.mC,q5:()=>l.q5,L5:()=>o}),r(94051);var s={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},i=async e=>new Promise(t=>setTimeout(t,e)),a=(e,t)=>t?e*(1+Math.random()):e,n=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=a(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await i(r()),t++}},o=async(e,t={})=>{let r=0,{shouldRetry:o,initialDelay:l,maxDelayBetweenRetries:c,factor:d,retryImmediately:u,jitter:h}={...s,...t},p=n({initialDelay:l,maxDelayBetweenRetries:c,factor:d,jitter:h});for(;;)try{return await e()}catch(e){if(!o(e,++r))throw e;u&&1===r?await i(a(100,h)):await p()}},l=r(44401),c=r(77071),d=new Set,u=(e,t,r)=>{let s=(0,c.MC)()||(0,c.Fj)(),i=r??e;d.has(i)||s||(d.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},h=(0,r(62016)._r)({packageName:"@clerk/backend"}),{isDevOrStagingUrl:p}=(0,l.RZ)()},94051:(e,t,r)=>{"use strict";r.d(t,{OV:()=>u,S7:()=>c,VK:()=>d,jq:()=>h});var s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,o=e=>{throw TypeError(e)},l=(e,t,r)=>t.has(e)||o("Cannot "+r),c=(e,t,r)=>(l(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?o("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),u=(e,t,r,s)=>(l(e,t,"write to private field"),s?s.call(e,r):t.set(e,r),r),h=(e,t,r)=>(l(e,t,"access private method"),r)},97495:(e,t,r)=>{"use strict";r.d(t,{NE:()=>i,Zd:()=>n,_b:()=>a});var s=r(8741);function i(e,t){var r;return((r=s.AA.Attributes[t])in e?e[r]:void 0)||a(e,s.AA.Headers[t])}function a(e,t){var r,s;return function(e){try{let{headers:t,nextUrl:r,cookies:s}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==s?void 0:s.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(s=null==(r=e.socket)?void 0:r._httpMessage)?void 0:s.getHeader(t))}function n(e){return!!i(e,"AuthStatus")}}};