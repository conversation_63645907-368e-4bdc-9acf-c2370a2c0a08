(()=>{var e={};e.id=3414,e.ids=[3414],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74929:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>c,OPTIONS:()=>d});var o=t(26142),n=t(94327),a=t(34862),i=t(37838),u=t(1359),l=t(26239);async function c(){try{let{userId:e}=await (0,i.j)();if(!e){let e=l.NextResponse.json({error:"Not authenticated"},{status:401});return e.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),e.headers.set("Access-Control-Allow-Credentials","true"),e}let r=await (0,u.N)();if(!r){let e=l.NextResponse.json({error:"User not found"},{status:404});return e.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),e.headers.set("Access-Control-Allow-Credentials","true"),e}let t={id:r.id,fullName:r.fullName,firstName:r.firstName,lastName:r.lastName,emailAddresses:r.emailAddresses.map(e=>({emailAddress:e.emailAddress})),imageUrl:r.imageUrl},s=l.NextResponse.json(t);return s.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),s.headers.set("Access-Control-Allow-Credentials","true"),s}catch(r){console.error("Error fetching user:",r);let e=l.NextResponse.json({error:"Internal server error"},{status:500});return e.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),e.headers.set("Access-Control-Allow-Credentials","true"),e}}async function d(){return new l.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"https://cubent.dev","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type","Access-Control-Allow-Credentials":"true"}})}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/user/route",pathname:"/api/auth/user",filename:"route",bundlePath:"app/api/auth/user/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\auth\\user\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:A,workUnitAsyncStorage:h,serverHooks:x}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:h})}},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},89259:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,7873,3887,1359],()=>t(74929));module.exports=s})();