(()=>{var e={};e.id=1823,e.ids=[1823],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8473:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{POST:()=>d});var n=r(26142),i=r(94327),a=r(34862),o=r(37838),u=r(18815),l=r(26239),c=r(55511);async function d(){try{let{userId:e}=await (0,o.j)();if(!e)return l.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return l.NextResponse.json({error:"User not found"},{status:404});let r=`cubent_${(0,c.randomBytes)(32).toString("hex")}`;return await u.database.user.update({where:{id:t.id},data:{extensionApiKey:r}}),await u.database.extensionSession.updateMany({where:{userId:t.id},data:{isActive:!1}}),l.NextResponse.json({success:!0,message:"New API key generated successfully",apiKey:r})}catch(e){return console.error("API key generation error:",e),l.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/generate-key/route",pathname:"/api/extension/generate-key",filename:"route",bundlePath:"app/api/extension/generate-key/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\generate-key\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:x}=p;function y(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>$});var s=r(8741),n=r(62923),i=r(54726),a=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},l=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),c=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,i;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((i=e,`[clerk debug end: ${i}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},d=(e,t)=>(...r)=>{let s=("string"==typeof e?c(e,l):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),g=r(37081),h=r(27322),x=r(6264);function y(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return h.r0.stringify(r,{pad:!1})}async function f(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,h.hJ)(r.algorithm);if(!n)return{errors:[new x.xy(`Unsupported algorithm ${r.algorithm}`)]};let i=await (0,h.Fh)(t,n,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=y(a),u=y(e),l=`${o}.${u}`;try{let e=await h.fA.crypto.subtle.sign(n,i,s.encode(l));return{data:`${l}.${h.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new x.xy(e?.message)]}}}(0,g.C)(h.J0);var m=(0,g.R)(h.iU);(0,g.C)(f),(0,g.C)(h.nk);var w=r(97495),A=r(60606);function b(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,a,o;let u,l=(0,w.NE)(e,"AuthStatus"),c=(0,w.NE)(e,"AuthToken"),d=(0,w.NE)(e,"AuthMessage"),p=(0,w.NE)(e,"AuthReason"),g=(0,w.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:l,authMessage:d,authReason:p});let h=(0,w._b)(e,s.AA.Headers.ClerkRequestData),x=(0,A.Kk)(h),y={secretKey:(null==r?void 0:r.secretKey)||x.secretKey||i.rB,publishableKey:x.publishableKey||i.At,apiUrl:i.H$,apiVersion:i.mG,authStatus:l,authMessage:d,authReason:p,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",y),l&&l===s.TD.SignedIn){(0,A._l)(c,y.secretKey,g);let e=m(c);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(y,e.raw.text,e.payload)}else u=(0,s.wI)(y);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(y,u.sessionStatus)),u}var v=r(68478);let q=({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>async(n,i)=>{if((0,a.zz)((0,w._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,w.Zd)(n)){p.M&&(0,A.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,A.$K)(n,t)}return b(n,{...i,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>(r,n)=>((0,a.zz)((0,w._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,A.$K)(r,t),b(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,v.AG)()});let S={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},U=e=>{var t,r;return!!e.headers.get(S.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(S.Headers.NextAction))},k=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||N(e)||E(e)},N=e=>!!e.headers.get(S.Headers.NextUrl)&&!U(e)||j(),j=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},E=e=>!!e.headers.get(S.Headers.NextjsData);var T=r(23056);let $=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,T.TG)(),a=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await q({debugLoggerName:"auth()",noAuthStatusMessage:(0,v.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),u=(0,w.NE)(t,"ClerkUrl"),l=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,s.tl)(t),l=a.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||a.cookies.get(s.AA.Cookies.DevBrowser),c=(0,w._b)(t,s.AA.Headers.ClerkRequestData),d=(0,A.Kk)(c);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:l,baseUrl:a.clerkUrl.toString(),publishableKey:d.publishableKey||i.At,signInUrl:d.signInUrl||i.qW,signUpUrl:d.signUpUrl||i.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=l(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=l(e);return t.redirectToSignUp({returnBackUrl:r})}})};$.protect=async(...e)=>{r(1447);let t=await (0,T.TG)(),s=await $();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:i}=e;return async(...e)=>{var a,o,u,l,c,d;let p=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],g=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(l=e[1])?void 0:l.unauthenticatedUrl),h=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),x=()=>h?s(h):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:x():r.has(p)?r:x():r:g?s(g):k(i)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(8473));module.exports=s})();