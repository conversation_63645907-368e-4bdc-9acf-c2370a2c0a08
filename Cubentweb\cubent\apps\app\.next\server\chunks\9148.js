exports.id=9148,exports.ids=[9148],exports.modules={12375:(e,t,a)=>{"use strict";a.d(t,{ModeToggle:()=>s});let s=(0,a(6340).registerClientReference)(function(){throw Error("Attempted to call ModeToggle() from the server but ModeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\mode-toggle.tsx","ModeToggle")},18290:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(94752),r=a(12375),n=a(82578);let i=({children:e})=>(0,s.jsxs)("div",{className:"container relative grid h-dvh flex-col items-center justify-center lg:max-w-none lg:grid-cols-2 lg:px-0",children:[(0,s.jsxs)("div",{className:"relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-zinc-900"}),(0,s.jsxs)("div",{className:"relative z-20 flex items-center font-medium text-lg",children:[(0,s.jsx)(n.A,{className:"mr-2 h-6 w-6"}),"Acme Inc"]}),(0,s.jsx)("div",{className:"absolute top-4 right-4",children:(0,s.jsx)(r.ModeToggle,{})}),(0,s.jsx)("div",{className:"relative z-20 mt-auto",children:(0,s.jsxs)("blockquote",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-lg",children:"“This library has saved me countless hours of work and helped me deliver stunning designs to my clients faster than ever before.”"}),(0,s.jsx)("footer",{className:"text-sm",children:"Sofia Davis"})]})})]}),(0,s.jsx)("div",{className:"lg:p-8",children:(0,s.jsx)("div",{className:"mx-auto flex w-full max-w-[400px] flex-col justify-center space-y-6",children:e})})]})},19647:(e,t,a)=>{Promise.resolve().then(a.bind(a,19593)),Promise.resolve().then(a.bind(a,75264)),Promise.resolve().then(a.bind(a,43303)),Promise.resolve().then(a.bind(a,13342)),Promise.resolve().then(a.bind(a,5768)),Promise.resolve().then(a.bind(a,446)),Promise.resolve().then(a.t.bind(a,32787,23)),Promise.resolve().then(a.t.bind(a,47390,23))},23167:(e,t,a)=>{"use strict";a.d(t,{H:()=>n});var s=a(71166),r=a(25);let n=()=>(0,s.w)({server:{DATABASE_URL:r.z.string().url()},runtimeEnv:{DATABASE_URL:process.env.DATABASE_URL}})},34069:(e,t,a)=>{"use strict";a.d(t,{w:()=>d});var s=a(81121),r=a.n(s);let n="next-forge",i={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,d=({title:e,description:t,image:a,...s})=>{let d=`${e} | ${n}`,l={title:d,description:t,applicationName:n,metadataBase:o?new URL(`https://${o}`):void 0,authors:[i],creator:i.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:d},openGraph:{title:d,description:t,type:"website",siteName:n,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},c=r()(l,s);return a&&c.openGraph&&(c.openGraph.images=[{url:a,width:1200,height:630,alt:e}]),c}},36334:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(46097);let r=async e=>[{type:"image/png",width:1200,height:630,url:(0,s.fillMetadataSegment)(".",await e.params,"opengraph-image.png")+"?1de9f909622c0a32"}]},53330:(e,t,a)=>{"use strict";a.d(t,{ModeToggle:()=>h});var s=a(99730),r=a(62458),n=a(48585),i=a(74938);a(57752);var o=a(33187),d=a(83590);function l({...e}){return(0,s.jsx)(o.bL,{"data-slot":"dropdown-menu",...e})}function c({...e}){return(0,s.jsx)(o.l9,{"data-slot":"dropdown-menu-trigger",...e})}function m({className:e,sideOffset:t=4,...a}){return(0,s.jsx)(o.ZL,{children:(0,s.jsx)(o.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function u({className:e,inset:t,variant:a="default",...r}){return(0,s.jsx)(o.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}let v=[{label:"Light",value:"light"},{label:"Dark",value:"dark"},{label:"System",value:"system"}],h=()=>{let{setTheme:e}=(0,n.D)();return(0,s.jsxs)(l,{children:[(0,s.jsx)(c,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"ghost",size:"icon",className:"shrink-0 text-foreground",children:[(0,s.jsx)(r.gLX,{className:"dark:-rotate-90 h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:scale-0"}),(0,s.jsx)(r.rRK,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsx)(m,{children:v.map(({label:t,value:a})=>(0,s.jsx)(u,{onClick:()=>e(a),children:t},a))})]})}},54526:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(46097);let r=async e=>[{type:"image/png",sizes:"192x192",url:(0,s.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?6b949b4e1bd36892"}]},56940:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(46097);let r=async e=>[{type:"image/png",sizes:"32x32",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.png")+"?0fb4a24cefe3ddc0"}]},79895:(e,t,a)=>{Promise.resolve().then(a.bind(a,46704)),Promise.resolve().then(a.bind(a,80278)),Promise.resolve().then(a.bind(a,87641)),Promise.resolve().then(a.bind(a,62906)),Promise.resolve().then(a.bind(a,93078)),Promise.resolve().then(a.bind(a,51672)),Promise.resolve().then(a.bind(a,49389)),Promise.resolve().then(a.bind(a,29224))},80780:(e,t,a)=>{"use strict";a.r(t),a.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>s.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>s.ot,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>s.at});var s=a(32177)},87466:(e,t,a)=>{"use strict";a.r(t),a.d(t,{"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>s.y});var s=a(44089)},89141:(e,t,a)=>{Promise.resolve().then(a.bind(a,53330))},98869:(e,t,a)=>{Promise.resolve().then(a.bind(a,12375))}};