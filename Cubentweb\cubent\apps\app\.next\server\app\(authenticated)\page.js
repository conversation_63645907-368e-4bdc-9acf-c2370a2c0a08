(()=>{var e={};e.id=9555,e.ids=[9555],e.modules={2589:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return i.ImageResponse},NextRequest:function(){return n.NextRequest},NextResponse:function(){return s.NextResponse},URLPattern:function(){return a.URLPattern},after:function(){return l.after},connection:function(){return c.connection},unstable_rootParams:function(){return u.unstable_rootParams},userAgent:function(){return o.userAgent},userAgentFromString:function(){return o.userAgentFromString}});let i=r(24532),n=r(11284),s=r(93880),o=r(74532),a=r(70693),l=r(34225),c=r(27236),u=r(21311)},2878:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f124736fc7a0581d76303be1b915991f73d158aa9":()=>t$,"7f88b12a1676b98b8bfa4374bd6560d2d1206ba4d4":()=>eQ,"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>i.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>i.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>n.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>i.at});var i=r(54841),n=r(44089),s=r(68602);r(98668);var o=r(59608),a=r(3636),l=r(86072),c=r(36870),u=r(47633);let d=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},h=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?d(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,d(t)])),null,2)).join(", "),f=(e,t)=>()=>{let r=[],i=!1;return{enable:()=>{i=!0},debug:(...e)=>{i&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(i){var n,s;for(let i of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(i);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,i=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return i.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((s=e,`[clerk debug end: ${s}] (@clerk/nextjs=6.20.0,next=${u.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},p=(e,t)=>(...r)=>{let i=("string"==typeof e?f(e,h):e)(),n=t(i);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(i.commit(),e)).catch(e=>{throw i.commit(),e});return i.commit(),e}catch(e){throw i.commit(),e}};var m=r(48917),g=r(58878),b=r(43226),y=r(83625);function v(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return b.r0.stringify(r,{pad:!1})}async function w(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let i=new TextEncoder,n=(0,b.hJ)(r.algorithm);if(!n)return{errors:[new y.xy(`Unsupported algorithm ${r.algorithm}`)]};let s=await (0,b.Fh)(t,n,"sign"),o=r.header||{typ:"JWT"};o.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let a=v(o),l=v(e),c=`${a}.${l}`;try{let e=await b.fA.crypto.subtle.sign(n,s,i.encode(c));return{data:`${c}.${b.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new y.xy(e?.message)]}}}(0,g.C)(b.J0);var _=(0,g.R)(b.iU);(0,g.C)(w),(0,g.C)(b.nk);var E=r(97296);r(25398),r(85932);var S,A,x,I,T,k,O,R=r(24290),C=r(2589),P=r(37012),N=Object.defineProperty,D=(e,t,r)=>t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,L=(null==(S="undefined"!=typeof globalThis?globalThis:void 0)?void 0:S.crypto)||(null==(A="undefined"!=typeof global?global:void 0)?void 0:A.crypto)||(null==(x="undefined"!=typeof window?window:void 0)?void 0:x.crypto)||(null==(I="undefined"!=typeof self?self:void 0)?void 0:I.crypto)||(null==(k=null==(T="undefined"!=typeof frames?frames:void 0)?void 0:T[0])?void 0:k.crypto);O=L?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(L.getRandomValues(new Uint32Array(1))[0]);return new j(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let i=0,n;i<e;i+=4){let e=r(0x100000000*(n||Math.random()));n=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new j(t,e)};var M=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},j=class extends M{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let i=0;i<e;i+=1)t[i>>>2]|=r[i]<<24-i%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=U){return e.stringify(this)}concat(e){let t=this.words,r=e.words,i=this.sigBytes,n=e.sigBytes;if(this.clamp(),i%4)for(let e=0;e<n;e+=1){let n=r[e>>>2]>>>24-e%4*8&255;t[i+e>>>2]|=n<<24-(i+e)%4*8}else for(let e=0;e<n;e+=4)t[i+e>>>2]=r[e>>>2];return this.sigBytes+=n,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>D(e,"symbol"!=typeof t?t+"":t,r))(j,"random",O);var U={stringify(e){let{words:t,sigBytes:r}=e,i=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;i.push((r>>>4).toString(16)),i.push((15&r).toString(16))}return i.join("")},parse(e){let t=e.length,r=[];for(let i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new j(r,t/2)}},F={stringify(e){let{words:t,sigBytes:r}=e,i=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;i.push(String.fromCharCode(r))}return i.join("")},parse(e){let t=e.length,r=[];for(let i=0;i<t;i+=1)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new j(r,t)}},B={stringify(e){try{return decodeURIComponent(escape(F.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>F.parse(unescape(encodeURIComponent(e)))},q=class extends M{constructor(){super(),this._minBufferSize=0}reset(){this._data=new j,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=B.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:i}=this,n=r.words,s=r.sigBytes,o=s/(4*i),a=(o=e?Math.ceil(o):Math.max((0|o)-this._minBufferSize,0))*i,l=Math.min(4*a,s);if(a){for(let e=0;e<a;e+=i)this._doProcessBlock(n,e);t=n.splice(0,a),r.sigBytes-=l}return new j(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},$=class extends q{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new M,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new z(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},z=class extends M{constructor(e,t){super();let r=new e;this._hasher=r;let i=t;"string"==typeof i&&(i=B.parse(i));let n=r.blockSize,s=4*n;i.sigBytes>s&&(i=r.finalize(t)),i.clamp();let o=i.clone();this._oKey=o;let a=i.clone();this._iKey=a;let l=o.words,c=a.words;for(let e=0;e<n;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;o.sigBytes=s,a.sigBytes=s,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},H=(e,t,r)=>{let i=[],n=0;for(let s=0;s<t;s+=1)if(s%4){let t=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;i[n>>>2]|=t<<24-n%4*8,n+=1}return j.create(i,n)},V={stringify(e){let{words:t,sigBytes:r}=e,i=this._map;e.clamp();let n=[];for(let e=0;e<r;e+=3){let s=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)n.push(i.charAt(s>>>6*(3-t)&63))}let s=i.charAt(64);if(s)for(;n.length%4;)n.push(s);return n.join("")},parse(e){let t=e.length,r=this._map,i=this._reverseMap;if(!i){this._reverseMap=[],i=this._reverseMap;for(let e=0;e<r.length;e+=1)i[r.charCodeAt(e)]=e}let n=r.charAt(64);if(n){let r=e.indexOf(n);-1!==r&&(t=r)}return H(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},K=[];for(let e=0;e<64;e+=1)K[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var W=(e,t,r,i,n,s,o)=>{let a=e+(t&r|~t&i)+n+o;return(a<<s|a>>>32-s)+t},G=(e,t,r,i,n,s,o)=>{let a=e+(t&i|r&~i)+n+o;return(a<<s|a>>>32-s)+t},X=(e,t,r,i,n,s,o)=>{let a=e+(t^r^i)+n+o;return(a<<s|a>>>32-s)+t},Y=(e,t,r,i,n,s,o)=>{let a=e+(r^(t|~i))+n+o;return(a<<s|a>>>32-s)+t},J=class extends ${_doReset(){this._hash=new j([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let i=t+r,n=e[i];e[i]=(n<<8|n>>>24)&0xff00ff|(n<<24|n>>>8)&0xff00ff00}let r=this._hash.words,i=e[t+0],n=e[t+1],s=e[t+2],o=e[t+3],a=e[t+4],l=e[t+5],c=e[t+6],u=e[t+7],d=e[t+8],h=e[t+9],f=e[t+10],p=e[t+11],m=e[t+12],g=e[t+13],b=e[t+14],y=e[t+15],v=r[0],w=r[1],_=r[2],E=r[3];v=W(v,w,_,E,i,7,K[0]),E=W(E,v,w,_,n,12,K[1]),_=W(_,E,v,w,s,17,K[2]),w=W(w,_,E,v,o,22,K[3]),v=W(v,w,_,E,a,7,K[4]),E=W(E,v,w,_,l,12,K[5]),_=W(_,E,v,w,c,17,K[6]),w=W(w,_,E,v,u,22,K[7]),v=W(v,w,_,E,d,7,K[8]),E=W(E,v,w,_,h,12,K[9]),_=W(_,E,v,w,f,17,K[10]),w=W(w,_,E,v,p,22,K[11]),v=W(v,w,_,E,m,7,K[12]),E=W(E,v,w,_,g,12,K[13]),_=W(_,E,v,w,b,17,K[14]),w=W(w,_,E,v,y,22,K[15]),v=G(v,w,_,E,n,5,K[16]),E=G(E,v,w,_,c,9,K[17]),_=G(_,E,v,w,p,14,K[18]),w=G(w,_,E,v,i,20,K[19]),v=G(v,w,_,E,l,5,K[20]),E=G(E,v,w,_,f,9,K[21]),_=G(_,E,v,w,y,14,K[22]),w=G(w,_,E,v,a,20,K[23]),v=G(v,w,_,E,h,5,K[24]),E=G(E,v,w,_,b,9,K[25]),_=G(_,E,v,w,o,14,K[26]),w=G(w,_,E,v,d,20,K[27]),v=G(v,w,_,E,g,5,K[28]),E=G(E,v,w,_,s,9,K[29]),_=G(_,E,v,w,u,14,K[30]),w=G(w,_,E,v,m,20,K[31]),v=X(v,w,_,E,l,4,K[32]),E=X(E,v,w,_,d,11,K[33]),_=X(_,E,v,w,p,16,K[34]),w=X(w,_,E,v,b,23,K[35]),v=X(v,w,_,E,n,4,K[36]),E=X(E,v,w,_,a,11,K[37]),_=X(_,E,v,w,u,16,K[38]),w=X(w,_,E,v,f,23,K[39]),v=X(v,w,_,E,g,4,K[40]),E=X(E,v,w,_,i,11,K[41]),_=X(_,E,v,w,o,16,K[42]),w=X(w,_,E,v,c,23,K[43]),v=X(v,w,_,E,h,4,K[44]),E=X(E,v,w,_,m,11,K[45]),_=X(_,E,v,w,y,16,K[46]),w=X(w,_,E,v,s,23,K[47]),v=Y(v,w,_,E,i,6,K[48]),E=Y(E,v,w,_,u,10,K[49]),_=Y(_,E,v,w,b,15,K[50]),w=Y(w,_,E,v,l,21,K[51]),v=Y(v,w,_,E,m,6,K[52]),E=Y(E,v,w,_,o,10,K[53]),_=Y(_,E,v,w,f,15,K[54]),w=Y(w,_,E,v,n,21,K[55]),v=Y(v,w,_,E,d,6,K[56]),E=Y(E,v,w,_,y,10,K[57]),_=Y(_,E,v,w,c,15,K[58]),w=Y(w,_,E,v,g,21,K[59]),v=Y(v,w,_,E,a,6,K[60]),E=Y(E,v,w,_,p,10,K[61]),_=Y(_,E,v,w,s,15,K[62]),w=Y(w,_,E,v,h,21,K[63]),r[0]=r[0]+v|0,r[1]=r[1]+w|0,r[2]=r[2]+_|0,r[3]=r[3]+E|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32;let n=Math.floor(r/0x100000000);t[(i+64>>>9<<4)+15]=(n<<8|n>>>24)&0xff00ff|(n<<24|n>>>8)&0xff00ff00,t[(i+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let s=this._hash,o=s.words;for(let e=0;e<4;e+=1){let t=o[e];o[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return s}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};$._createHelper(J),$._createHmacHelper(J);var Z=class extends M{constructor(e){super(),this.cfg=Object.assign(new M,{keySize:4,hasher:J,iterations:1},e)}compute(e,t){let r,{cfg:i}=this,n=i.hasher.create(),s=j.create(),o=s.words,{keySize:a,iterations:l}=i;for(;o.length<a;){r&&n.update(r),r=n.update(e).finalize(t),n.reset();for(let e=1;e<l;e+=1)r=n.finalize(r),n.reset();s.concat(r)}return s.sigBytes=4*a,s}},Q=class extends q{constructor(e,t,r){super(),this.cfg=Object.assign(new M,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?ea:eo;return{encrypt:(r,i,n)=>t(i).encrypt(e,r,i,n),decrypt:(r,i,n)=>t(i).decrypt(e,r,i,n)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};Q._ENC_XFORM_MODE=1,Q._DEC_XFORM_MODE=2,Q.keySize=4,Q.ivSize=4;var ee=class extends M{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function et(e,t,r){let i,n=this._iv;n?(i=n,this._iv=void 0):i=this._prevBlock;for(let n=0;n<r;n+=1)e[t+n]^=i[n]}var er=class extends ee{};er.Encryptor=class extends er{processBlock(e,t){let r=this._cipher,{blockSize:i}=r;et.call(this,e,t,i),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}},er.Decryptor=class extends er{processBlock(e,t){let r=this._cipher,{blockSize:i}=r,n=e.slice(t,t+i);r.decryptBlock(e,t),et.call(this,e,t,i),this._prevBlock=n}};var ei={pad(e,t){let r=4*t,i=r-e.sigBytes%r,n=i<<24|i<<16|i<<8|i,s=[];for(let e=0;e<i;e+=4)s.push(n);let o=j.create(s,i);e.concat(o)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},en=class extends Q{constructor(e,t,r){super(e,t,Object.assign({mode:er,padding:ei},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:i}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=i.createEncryptor:(e=i.createDecryptor,this._minBufferSize=1),this._mode=e.call(i,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},es=class extends M{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},eo=class extends M{static encrypt(e,t,r,i){let n=Object.assign(new M,this.cfg,i),s=e.createEncryptor(r,n),o=s.finalize(t),a=s.cfg;return es.create({ciphertext:o,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:s.blockSize,formatter:n.format})}static decrypt(e,t,r,i){let n=t,s=Object.assign(new M,this.cfg,i);return n=this._parse(n,s.format),e.createDecryptor(r,s).finalize(n.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};eo.cfg=Object.assign(new M,{format:{stringify(e){let t,{ciphertext:r,salt:i}=e;return(i?j.create([0x53616c74,0x65645f5f]).concat(i).concat(r):r).toString(V)},parse(e){let t,r=V.parse(e),i=r.words;return 0x53616c74===i[0]&&0x65645f5f===i[1]&&(t=j.create(i.slice(2,4)),i.splice(0,4),r.sigBytes-=16),es.create({ciphertext:r,salt:t})}}});var ea=class extends eo{static encrypt(e,t,r,i){let n=Object.assign(new M,this.cfg,i),s=n.kdf.execute(r,e.keySize,e.ivSize,n.salt,n.hasher);n.iv=s.iv;let o=eo.encrypt.call(this,e,t,s.key,n);return o.mixIn(s),o}static decrypt(e,t,r,i){let n=t,s=Object.assign(new M,this.cfg,i);n=this._parse(n,s.format);let o=s.kdf.execute(r,e.keySize,e.ivSize,n.salt,s.hasher);return s.iv=o.iv,eo.decrypt.call(this,e,n,o.key,s)}};ea.cfg=Object.assign(eo.cfg,{kdf:{execute(e,t,r,i,n){let s,o=i;o||(o=j.random(8)),s=n?Z.create({keySize:t+r,hasher:n}).compute(e,o):Z.create({keySize:t+r}).compute(e,o);let a=j.create(s.words.slice(t),4*r);return s.sigBytes=4*t,es.create({key:s,iv:a,salt:o})}}});var el=[],ec=[],eu=[],ed=[],eh=[],ef=[],ep=[],em=[],eg=[],eb=[],ey=[];for(let e=0;e<256;e+=1)e<128?ey[e]=e<<1:ey[e]=e<<1^283;var ev=0,ew=0;for(let e=0;e<256;e+=1){let e=ew^ew<<1^ew<<2^ew<<3^ew<<4;e=e>>>8^255&e^99,el[ev]=e,ec[e]=ev;let t=ey[ev],r=ey[t],i=ey[r],n=257*ey[e]^0x1010100*e;eu[ev]=n<<24|n>>>8,ed[ev]=n<<16|n>>>16,eh[ev]=n<<8|n>>>24,ef[ev]=n,n=0x1010101*i^65537*r^257*t^0x1010100*ev,ep[e]=n<<24|n>>>8,em[e]=n<<16|n>>>16,eg[e]=n<<8|n>>>24,eb[e]=n,ev?(ev=t^ey[ey[ey[i^t]]],ew^=ey[ey[ew]]):ev=ew=1}var e_=[0,1,2,4,8,16,32,64,128,27,54],eE=class extends en{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,i=t.sigBytes/4;this._nRounds=i+6;let n=(this._nRounds+1)*4;this._keySchedule=[];let s=this._keySchedule;for(let t=0;t<n;t+=1)t<i?s[t]=r[t]:(e=s[t-1],t%i?i>6&&t%i==4&&(e=el[e>>>24]<<24|el[e>>>16&255]<<16|el[e>>>8&255]<<8|el[255&e]):e=(el[(e=e<<8|e>>>24)>>>24]<<24|el[e>>>16&255]<<16|el[e>>>8&255]<<8|el[255&e])^e_[t/i|0]<<24,s[t]=s[t-i]^e);this._invKeySchedule=[];let o=this._invKeySchedule;for(let t=0;t<n;t+=1){let r=n-t;e=t%4?s[r]:s[r-4],t<4||r<=4?o[t]=e:o[t]=ep[el[e>>>24]]^em[el[e>>>16&255]]^eg[el[e>>>8&255]]^eb[el[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,eu,ed,eh,ef,el)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,ep,em,eg,eb,ec),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,i,n,s,o,a){let l=this._nRounds,c=e[t]^r[0],u=e[t+1]^r[1],d=e[t+2]^r[2],h=e[t+3]^r[3],f=4;for(let e=1;e<l;e+=1){let e=i[c>>>24]^n[u>>>16&255]^s[d>>>8&255]^o[255&h]^r[f];f+=1;let t=i[u>>>24]^n[d>>>16&255]^s[h>>>8&255]^o[255&c]^r[f];f+=1;let a=i[d>>>24]^n[h>>>16&255]^s[c>>>8&255]^o[255&u]^r[f];f+=1;let l=i[h>>>24]^n[c>>>16&255]^s[u>>>8&255]^o[255&d]^r[f];f+=1,c=e,u=t,d=a,h=l}let p=(a[c>>>24]<<24|a[u>>>16&255]<<16|a[d>>>8&255]<<8|a[255&h])^r[f];f+=1;let m=(a[u>>>24]<<24|a[d>>>16&255]<<16|a[h>>>8&255]<<8|a[255&c])^r[f];f+=1;let g=(a[d>>>24]<<24|a[h>>>16&255]<<16|a[c>>>8&255]<<8|a[255&u])^r[f];f+=1;let b=(a[h>>>24]<<24|a[c>>>16&255]<<16|a[u>>>8&255]<<8|a[255&d])^r[f];f+=1,e[t]=p,e[t+1]=m,e[t+2]=g,e[t+3]=b}};eE.keySize=8;var eS=en._createHelper(eE),eA=[],ex=class extends ${_doReset(){this._hash=new j([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,i=r[0],n=r[1],s=r[2],o=r[3],a=r[4];for(let r=0;r<80;r+=1){if(r<16)eA[r]=0|e[t+r];else{let e=eA[r-3]^eA[r-8]^eA[r-14]^eA[r-16];eA[r]=e<<1|e>>>31}let l=(i<<5|i>>>27)+a+eA[r];r<20?l+=(n&s|~n&o)+0x5a827999:r<40?l+=(n^s^o)+0x6ed9eba1:r<60?l+=(n&s|n&o|s&o)-0x70e44324:l+=(n^s^o)-0x359d3e2a,a=o,o=s,s=n<<30|n>>>2,n=i,i=l}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+o|0,r[4]=r[4]+a|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[(i+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(i+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},eI=($._createHelper(ex),$._createHmacHelper(ex));let eT=(e="auth",t)=>`Clerk: ${e}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:
- ${t?[...t,""].join("\n- "):" "}clerkMiddleware() is used in your Next.js Middleware.
- Your Middleware matcher is configured to match this route or page.
- If you are using the src directory, make sure the Middleware file is inside of it.

For more details, see https://clerk.com/docs/quickstarts/nextjs
`,ek=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`,eO="x-middleware-override-headers",eR="x-middleware-request";function eC(e,t){if(!(0,E.Zd)(e))throw Error(t)}let eP="clerk_keyless_dummy_key";function eN(e){if(!e)return{};let t=(0,R.Fj)()?l.o7||l.rB:l.o7||l.rB||eP;try{return eL(e,t)}catch{if(P.I)try{return eL(e,eP)}catch{eD()}eD()}}function eD(){if((0,R.Fj)())throw Error("Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)");throw Error(ek)}function eL(e,t){return JSON.parse(eS.decrypt(e,t).toString(B))}function eM(e,{treatPendingAsSignedOut:t=!0,...r}={}){let i,n=(0,E.NE)(e,"AuthStatus"),s=(0,E.NE)(e,"AuthToken"),a=(0,E.NE)(e,"AuthMessage"),c=(0,E.NE)(e,"AuthReason"),u=(0,E.NE)(e,"AuthSignature");null==(f=r.logger)||f.debug("headers",{authStatus:n,authMessage:a,authReason:c});let d=eN((0,E._b)(e,o.AA.Headers.ClerkRequestData)),h={secretKey:(null==r?void 0:r.secretKey)||d.secretKey||l.rB,publishableKey:d.publishableKey||l.At,apiUrl:l.H$,apiVersion:l.mG,authStatus:n,authMessage:a,authReason:c,treatPendingAsSignedOut:t};if(null==(p=r.logger)||p.debug("auth options",h),n&&n===o.TD.SignedIn){var f,p,m,g=h.secretKey;if(!u||eI(s,g).toString()!==u)throw Error("Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)");let e=_(s);null==(m=r.logger)||m.debug("jwt",e.raw),i=(0,o.Z5)(h,e.raw.text,e.payload)}else i=(0,o.wI)(h);return t&&"pending"===i.sessionStatus&&(i=(0,o.wI)(h,i.sessionStatus)),i}let ej=({debugLoggerName:e,noAuthStatusMessage:t})=>p(e,e=>async(i,n)=>{if((0,c.zz)((0,E._b)(i,o.AA.Headers.EnableDebug))&&e.enable(),!(0,E.Zd)(i)){m.M&&eC(i,t);let e=await r.e(7654).then(r.bind(r,97654)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);eC(i,t)}return eM(i,{...n,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>p(e,e=>(r,i)=>((0,c.zz)((0,E._b)(r,o.AA.Headers.EnableDebug))&&e.enable(),eC(r,t),eM(r,{...i,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:eT("getAuth")});let eU={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},eF=e=>{var t,r;return!!e.headers.get(eU.Headers.NextUrl)&&((null==(t=e.headers.get(o.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(o.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(eU.Headers.NextAction))},eB=e=>{var t;return"document"===e.headers.get(o.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(o.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(o.AA.Headers.Accept))?void 0:t.includes("text/html"))||eq(e)||ez(e)},eq=e=>!!e.headers.get(eU.Headers.NextUrl)&&!eF(e)||e$(),e$=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},ez=e=>!!e.headers.get(eU.Headers.NextjsData),eH=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),i=r.includes("dynamic server usage"),n=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||i||n};async function eV(){try{let{headers:e}=await Promise.resolve().then(r.bind(r,87749)),t=await e();return new C.NextRequest("https://placeholder.com",{headers:t})}catch(e){if(e&&eH(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}let eK=async({treatPendingAsSignedOut:e}={})=>{r(84736);let t=await eV(),i=async()=>{if(m.M)return[];try{let e=await r.e(7654).then(r.bind(r,97654)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},n=await ej({debugLoggerName:"auth()",noAuthStatusMessage:eT("auth",await i())})(t,{treatPendingAsSignedOut:e}),s=(0,E.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},i=(0,o.tl)(t),c=i.clerkUrl.searchParams.get(o.AA.QueryParameters.DevBrowser)||i.cookies.get(o.AA.Cookies.DevBrowser),u=eN((0,E._b)(t,o.AA.Headers.ClerkRequestData));return[(0,o.vH)({redirectAdapter:a.redirect,devBrowserToken:c,baseUrl:i.clerkUrl.toString(),publishableKey:u.publishableKey||l.At,signInUrl:u.signInUrl||l.qW,signUpUrl:u.signUpUrl||l.sE,sessionStatus:n.sessionStatus}),null===r?"":r||(null==s?void 0:s.toString())]};return Object.assign(n,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};eK.protect=async(...e)=>{r(84736);let t=await eV(),i=await eK();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:i,notFound:n,request:s}=e;return async(...e)=>{var o,a,l,c,u,d;let h=(null==(o=e[0])?void 0:o.unauthenticatedUrl)||(null==(a=e[0])?void 0:a.unauthorizedUrl)?void 0:e[0],f=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),p=(null==(u=e[0])?void 0:u.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),m=()=>p?i(p):n();return"pending"!==r.sessionStatus&&r.userId?h?"function"==typeof h?h(r.has)?r:m():r.has(h)?r:m():r:f?i(f):eB(s)?t():n()}})({request:t,authObject:i,redirectToSignIn:i.redirectToSignIn,notFound:a.notFound,redirect:a.redirect})(...e)};var eW=r(8917);let eG=new(r(16698)).AsyncLocalStorage,eX=async()=>{var e,t;let r;try{let e=await eV(),t=(0,E._b)(e,o.AA.Headers.ClerkRequestData);r=eN(t)}catch(e){if(e&&eH(e))throw e}let i=null!=(t=null==(e=eG.getStore())?void 0:e.get("requestData"))?t:r;return(null==i?void 0:i.secretKey)||(null==i?void 0:i.publishableKey)?(0,eW.n)(i):(0,eW.n)({})};var eY=r(92516);let eJ=e=>{let t=e.publicUserData?.firstName;return t&&e.publicUserData?.lastName?t=`${t} ${e.publicUserData.lastName}`:t||(t=e.publicUserData?.identifier),t},eZ=["var(--color-red-500)","var(--color-orange-500)","var(--color-amber-500)","var(--color-yellow-500)","var(--color-lime-500)","var(--color-green-500)","var(--color-emerald-500)","var(--color-teal-500)","var(--color-cyan-500)","var(--color-sky-500)","var(--color-blue-500)","var(--color-indigo-500)","var(--color-violet-500)","var(--color-purple-500)","var(--color-fuchsia-500)","var(--color-pink-500)","var(--color-rose-500)"],eQ=async e=>{try{let{orgId:t}=await eK();if(!t)throw Error("Not logged in");let r=await eX();return{data:(await r.organizations.getOrganizationMembershipList({organizationId:t,limit:100})).data.filter(t=>t.publicUserData?.userId&&e.includes(t.publicUserData.userId)).map(e=>({name:eJ(e)??"Unknown user",picture:e.publicUserData?.imageUrl??"",color:eZ[Math.floor(Math.random()*eZ.length)]}))}}catch(e){return{error:e}}};function e0(e){return Array.isArray?Array.isArray(e):"[object Array]"===e8(e)}(0,eY.D)([eQ]),(0,s.A)(eQ,"7f88b12a1676b98b8bfa4374bd6560d2d1206ba4d4",null);let e1=1/0;function e2(e){return"string"==typeof e}function e3(e){return"number"==typeof e}function e4(e){return"object"==typeof e}function e5(e){return null!=e}function e6(e){return!e.trim().length}function e8(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}let e7=e=>`Invalid value for key ${e}`,e9=e=>`Pattern length exceeds max of ${e}.`,te=e=>`Missing ${e} property in key`,tt=e=>`Property 'weight' in key '${e}' must be a positive integer`,tr=Object.prototype.hasOwnProperty;class ti{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach(e=>{let r=tn(e);this._keys.push(r),this._keyMap[r.id]=r,t+=r.weight}),this._keys.forEach(e=>{e.weight/=t})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function tn(e){let t=null,r=null,i=null,n=1,s=null;if(e2(e)||e0(e))i=e,t=ts(e),r=to(e);else{if(!tr.call(e,"name"))throw Error(te("name"));let o=e.name;if(i=o,tr.call(e,"weight")&&(n=e.weight)<=0)throw Error(tt(o));t=ts(o),r=to(o),s=e.getFn}return{path:t,id:r,weight:n,src:i,getFn:s}}function ts(e){return e0(e)?e:e.split(".")}function to(e){return e0(e)?e.join("."):e}var ta={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(e,t){let r=[],i=!1,n=(e,t,s)=>{if(e5(e))if(t[s]){var o,a;let l=e[t[s]];if(!e5(l))return;if(s===t.length-1&&(e2(l)||e3(l)||!0===(o=l)||!1===o||e4(a=o)&&null!==a&&"[object Boolean]"==e8(o)))r.push(null==l?"":function(e){if("string"==typeof e)return e;let t=e+"";return"0"==t&&1/e==-e1?"-0":t}(l));else if(e0(l)){i=!0;for(let e=0,r=l.length;e<r;e+=1)n(l[e],t,s+1)}else t.length&&n(l,t,s+1)}else r.push(e)};return n(e,e2(t)?t.split("."):t,0),i?r:r[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};let tl=/[^ ]+/g;class tc{constructor({getFn:e=ta.getFn,fieldNormWeight:t=ta.fieldNormWeight}={}){this.norm=function(e=1,t=3){let r=new Map,i=Math.pow(10,t);return{get(t){let n=t.match(tl).length;if(r.has(n))return r.get(n);let s=parseFloat(Math.round(1/Math.pow(n,.5*e)*i)/i);return r.set(n,s),s},clear(){r.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((e,t)=>{this._keysMap[e.id]=t})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,e2(this.docs[0])?this.docs.forEach((e,t)=>{this._addString(e,t)}):this.docs.forEach((e,t)=>{this._addObject(e,t)}),this.norm.clear())}add(e){let t=this.size();e2(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,r=this.size();t<r;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!e5(e)||e6(e))return;let r={v:e,i:t,n:this.norm.get(e)};this.records.push(r)}_addObject(e,t){let r={i:t,$:{}};this.keys.forEach((t,i)=>{let n=t.getFn?t.getFn(e):this.getFn(e,t.path);if(e5(n)){if(e0(n)){let e=[],t=[{nestedArrIndex:-1,value:n}];for(;t.length;){let{nestedArrIndex:r,value:i}=t.pop();if(e5(i))if(e2(i)&&!e6(i)){let t={v:i,i:r,n:this.norm.get(i)};e.push(t)}else e0(i)&&i.forEach((e,r)=>{t.push({nestedArrIndex:r,value:e})})}r.$[i]=e}else if(e2(n)&&!e6(n)){let e={v:n,n:this.norm.get(n)};r.$[i]=e}}}),this.records.push(r)}toJSON(){return{keys:this.keys,records:this.records}}}function tu(e,t,{getFn:r=ta.getFn,fieldNormWeight:i=ta.fieldNormWeight}={}){let n=new tc({getFn:r,fieldNormWeight:i});return n.setKeys(e.map(tn)),n.setSources(t),n.create(),n}function td(e,{errors:t=0,currentLocation:r=0,expectedLocation:i=0,distance:n=ta.distance,ignoreLocation:s=ta.ignoreLocation}={}){let o=t/e.length;if(s)return o;let a=Math.abs(i-r);return n?o+a/n:a?1:o}let th=String.prototype.normalize?e=>e.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):e=>e;class tf{constructor(e,{location:t=ta.location,threshold:r=ta.threshold,distance:i=ta.distance,includeMatches:n=ta.includeMatches,findAllMatches:s=ta.findAllMatches,minMatchCharLength:o=ta.minMatchCharLength,isCaseSensitive:a=ta.isCaseSensitive,ignoreDiacritics:l=ta.ignoreDiacritics,ignoreLocation:c=ta.ignoreLocation}={}){if(this.options={location:t,threshold:r,distance:i,includeMatches:n,findAllMatches:s,minMatchCharLength:o,isCaseSensitive:a,ignoreDiacritics:l,ignoreLocation:c},e=a?e:e.toLowerCase(),e=l?th(e):e,this.pattern=e,this.chunks=[],!this.pattern.length)return;let u=(e,t)=>{this.chunks.push({pattern:e,alphabet:function(e){let t={};for(let r=0,i=e.length;r<i;r+=1){let n=e.charAt(r);t[n]=(t[n]||0)|1<<i-r-1}return t}(e),startIndex:t})},d=this.pattern.length;if(d>32){let e=0,t=d%32,r=d-t;for(;e<r;)u(this.pattern.substr(e,32),e),e+=32;if(t){let e=d-32;u(this.pattern.substr(e),e)}}else u(this.pattern,0)}searchIn(e){let{isCaseSensitive:t,ignoreDiacritics:r,includeMatches:i}=this.options;if(e=t?e:e.toLowerCase(),e=r?th(e):e,this.pattern===e){let t={isMatch:!0,score:0};return i&&(t.indices=[[0,e.length-1]]),t}let{location:n,distance:s,threshold:o,findAllMatches:a,minMatchCharLength:l,ignoreLocation:c}=this.options,u=[],d=0,h=!1;this.chunks.forEach(({pattern:t,alphabet:r,startIndex:f})=>{let{isMatch:p,score:m,indices:g}=function(e,t,r,{location:i=ta.location,distance:n=ta.distance,threshold:s=ta.threshold,findAllMatches:o=ta.findAllMatches,minMatchCharLength:a=ta.minMatchCharLength,includeMatches:l=ta.includeMatches,ignoreLocation:c=ta.ignoreLocation}={}){let u;if(t.length>32)throw Error(e9(32));let d=t.length,h=e.length,f=Math.max(0,Math.min(i,h)),p=s,m=f,g=a>1||l,b=g?Array(h):[];for(;(u=e.indexOf(t,m))>-1;)if(p=Math.min(td(t,{currentLocation:u,expectedLocation:f,distance:n,ignoreLocation:c}),p),m=u+d,g){let e=0;for(;e<d;)b[u+e]=1,e+=1}m=-1;let y=[],v=1,w=d+h,_=1<<d-1;for(let i=0;i<d;i+=1){let s=0,a=w;for(;s<a;)td(t,{errors:i,currentLocation:f+a,expectedLocation:f,distance:n,ignoreLocation:c})<=p?s=a:w=a,a=Math.floor((w-s)/2+s);w=a;let l=Math.max(1,f-a+1),u=o?h:Math.min(f+a,h)+d,E=Array(u+2);E[u+1]=(1<<i)-1;for(let s=u;s>=l;s-=1){let o=s-1,a=r[e.charAt(o)];if(g&&(b[o]=+!!a),E[s]=(E[s+1]<<1|1)&a,i&&(E[s]|=(y[s+1]|y[s])<<1|1|y[s+1]),E[s]&_&&(v=td(t,{errors:i,currentLocation:o,expectedLocation:f,distance:n,ignoreLocation:c}))<=p){if(p=v,(m=o)<=f)break;l=Math.max(1,2*f-m)}}if(td(t,{errors:i+1,currentLocation:f,expectedLocation:f,distance:n,ignoreLocation:c})>p)break;y=E}let E={isMatch:m>=0,score:Math.max(.001,v)};if(g){let e=function(e=[],t=ta.minMatchCharLength){let r=[],i=-1,n=-1,s=0;for(let o=e.length;s<o;s+=1){let o=e[s];o&&-1===i?i=s:o||-1===i||((n=s-1)-i+1>=t&&r.push([i,n]),i=-1)}return e[s-1]&&s-i>=t&&r.push([i,s-1]),r}(b,a);e.length?l&&(E.indices=e):E.isMatch=!1}return E}(e,t,r,{location:n+f,distance:s,threshold:o,findAllMatches:a,minMatchCharLength:l,includeMatches:i,ignoreLocation:c});p&&(h=!0),d+=m,p&&g&&(u=[...u,...g])});let f={isMatch:h,score:h?d/this.chunks.length:1};return h&&i&&(f.indices=u),f}}class tp{constructor(e){this.pattern=e}static isMultiMatch(e){return tm(e,this.multiRegex)}static isSingleMatch(e){return tm(e,this.singleRegex)}search(){}}function tm(e,t){let r=e.match(t);return r?r[1]:null}class tg extends tp{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){let t=e===this.pattern;return{isMatch:t,score:+!t,indices:[0,this.pattern.length-1]}}}class tb extends tp{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){let t=-1===e.indexOf(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class ty extends tp{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){let t=e.startsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,this.pattern.length-1]}}}class tv extends tp{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){let t=!e.startsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class tw extends tp{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){let t=e.endsWith(this.pattern);return{isMatch:t,score:+!t,indices:[e.length-this.pattern.length,e.length-1]}}}class t_ extends tp{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){let t=!e.endsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class tE extends tp{constructor(e,{location:t=ta.location,threshold:r=ta.threshold,distance:i=ta.distance,includeMatches:n=ta.includeMatches,findAllMatches:s=ta.findAllMatches,minMatchCharLength:o=ta.minMatchCharLength,isCaseSensitive:a=ta.isCaseSensitive,ignoreDiacritics:l=ta.ignoreDiacritics,ignoreLocation:c=ta.ignoreLocation}={}){super(e),this._bitapSearch=new tf(e,{location:t,threshold:r,distance:i,includeMatches:n,findAllMatches:s,minMatchCharLength:o,isCaseSensitive:a,ignoreDiacritics:l,ignoreLocation:c})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class tS extends tp{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,r=0,i=[],n=this.pattern.length;for(;(t=e.indexOf(this.pattern,r))>-1;)r=t+n,i.push([t,r-1]);let s=!!i.length;return{isMatch:s,score:+!s,indices:i}}}let tA=[tg,tS,ty,tv,t_,tw,tb,tE],tx=tA.length,tI=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,tT=new Set([tE.type,tS.type]);class tk{constructor(e,{isCaseSensitive:t=ta.isCaseSensitive,ignoreDiacritics:r=ta.ignoreDiacritics,includeMatches:i=ta.includeMatches,minMatchCharLength:n=ta.minMatchCharLength,ignoreLocation:s=ta.ignoreLocation,findAllMatches:o=ta.findAllMatches,location:a=ta.location,threshold:l=ta.threshold,distance:c=ta.distance}={}){this.query=null,this.options={isCaseSensitive:t,ignoreDiacritics:r,includeMatches:i,minMatchCharLength:n,findAllMatches:o,ignoreLocation:s,location:a,threshold:l,distance:c},e=t?e:e.toLowerCase(),e=r?th(e):e,this.pattern=e,this.query=function(e,t={}){return e.split("|").map(e=>{let r=e.trim().split(tI).filter(e=>e&&!!e.trim()),i=[];for(let e=0,n=r.length;e<n;e+=1){let n=r[e],s=!1,o=-1;for(;!s&&++o<tx;){let e=tA[o],r=e.isMultiMatch(n);r&&(i.push(new e(r,t)),s=!0)}if(!s)for(o=-1;++o<tx;){let e=tA[o],r=e.isSingleMatch(n);if(r){i.push(new e(r,t));break}}}return i})}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){let t=this.query;if(!t)return{isMatch:!1,score:1};let{includeMatches:r,isCaseSensitive:i,ignoreDiacritics:n}=this.options;e=i?e:e.toLowerCase(),e=n?th(e):e;let s=0,o=[],a=0;for(let i=0,n=t.length;i<n;i+=1){let n=t[i];o.length=0,s=0;for(let t=0,i=n.length;t<i;t+=1){let i=n[t],{isMatch:l,indices:c,score:u}=i.search(e);if(l){if(s+=1,a+=u,r){let e=i.constructor.type;tT.has(e)?o=[...o,...c]:o.push(c)}}else{a=0,s=0,o.length=0;break}}if(s){let e={isMatch:!0,score:a/s};return r&&(e.indices=o),e}}return{isMatch:!1,score:1}}}let tO=[];function tR(e,t){for(let r=0,i=tO.length;r<i;r+=1){let i=tO[r];if(i.condition(e,t))return new i(e,t)}return new tf(e,t)}let tC={AND:"$and",OR:"$or"},tP={PATH:"$path",PATTERN:"$val"},tN=e=>!!(e[tC.AND]||e[tC.OR]),tD=e=>!!e[tP.PATH],tL=e=>!e0(e)&&e4(e)&&!tN(e),tM=e=>({[tC.AND]:Object.keys(e).map(t=>({[t]:e[t]}))});function tj(e,t,{auto:r=!0}={}){let i=e=>{let n=Object.keys(e),s=tD(e);if(!s&&n.length>1&&!tN(e))return i(tM(e));if(tL(e)){let i=s?e[tP.PATH]:n[0],o=s?e[tP.PATTERN]:e[i];if(!e2(o))throw Error(e7(i));let a={keyId:to(i),pattern:o};return r&&(a.searcher=tR(o,t)),a}let o={children:[],operator:n[0]};return n.forEach(t=>{let r=e[t];e0(r)&&r.forEach(e=>{o.children.push(i(e))})}),o};return tN(e)||(e=tM(e)),i(e)}function tU(e,t){let r=e.matches;t.matches=[],e5(r)&&r.forEach(e=>{if(!e5(e.indices)||!e.indices.length)return;let{indices:r,value:i}=e,n={indices:r,value:i};e.key&&(n.key=e.key.src),e.idx>-1&&(n.refIndex=e.idx),t.matches.push(n)})}function tF(e,t){t.score=e.score}class tB{constructor(e,t={},r){this.options={...ta,...t},this.options.useExtendedSearch,this._keyStore=new ti(this.options.keys),this.setCollection(e,r)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof tc))throw Error("Incorrect 'index' type");this._myIndex=t||tu(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){e5(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){let t=[];for(let r=0,i=this._docs.length;r<i;r+=1){let n=this._docs[r];e(n,r)&&(this.removeAt(r),r-=1,i-=1,t.push(n))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){let{includeMatches:r,includeScore:i,shouldSort:n,sortFn:s,ignoreFieldNorm:o}=this.options,a=e2(e)?e2(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return!function(e,{ignoreFieldNorm:t=ta.ignoreFieldNorm}){e.forEach(e=>{let r=1;e.matches.forEach(({key:e,norm:i,score:n})=>{let s=e?e.weight:null;r*=Math.pow(0===n&&s?Number.EPSILON:n,(s||1)*(t?1:i))}),e.score=r})}(a,{ignoreFieldNorm:o}),n&&a.sort(s),e3(t)&&t>-1&&(a=a.slice(0,t)),function(e,t,{includeMatches:r=ta.includeMatches,includeScore:i=ta.includeScore}={}){let n=[];return r&&n.push(tU),i&&n.push(tF),e.map(e=>{let{idx:r}=e,i={item:t[r],refIndex:r};return n.length&&n.forEach(t=>{t(e,i)}),i})}(a,this._docs,{includeMatches:r,includeScore:i})}_searchStringList(e){let t=tR(e,this.options),{records:r}=this._myIndex,i=[];return r.forEach(({v:e,i:r,n:n})=>{if(!e5(e))return;let{isMatch:s,score:o,indices:a}=t.searchIn(e);s&&i.push({item:e,idx:r,matches:[{score:o,value:e,norm:n,indices:a}]})}),i}_searchLogical(e){let t=tj(e,this.options),r=(e,t,i)=>{if(!e.children){let{keyId:r,searcher:n}=e,s=this._findMatches({key:this._keyStore.get(r),value:this._myIndex.getValueForItemAtKeyId(t,r),searcher:n});return s&&s.length?[{idx:i,item:t,matches:s}]:[]}let n=[];for(let s=0,o=e.children.length;s<o;s+=1){let o=r(e.children[s],t,i);if(o.length)n.push(...o);else if(e.operator===tC.AND)return[]}return n},i=this._myIndex.records,n={},s=[];return i.forEach(({$:e,i:i})=>{if(e5(e)){let o=r(t,e,i);o.length&&(n[i]||(n[i]={idx:i,item:e,matches:[]},s.push(n[i])),o.forEach(({matches:e})=>{n[i].matches.push(...e)}))}}),s}_searchObjectList(e){let t=tR(e,this.options),{keys:r,records:i}=this._myIndex,n=[];return i.forEach(({$:e,i:i})=>{if(!e5(e))return;let s=[];r.forEach((r,i)=>{s.push(...this._findMatches({key:r,value:e[i],searcher:t}))}),s.length&&n.push({idx:i,item:e,matches:s})}),n}_findMatches({key:e,value:t,searcher:r}){if(!e5(t))return[];let i=[];if(e0(t))t.forEach(({v:t,i:n,n:s})=>{if(!e5(t))return;let{isMatch:o,score:a,indices:l}=r.searchIn(t);o&&i.push({score:a,key:e,value:t,idx:n,norm:s,indices:l})});else{let{v:n,n:s}=t,{isMatch:o,score:a,indices:l}=r.searchIn(n);o&&i.push({score:a,key:e,value:n,norm:s,indices:l})}return i}}tB.version="7.1.0",tB.createIndex=tu,tB.parseIndex=function(e,{getFn:t=ta.getFn,fieldNormWeight:r=ta.fieldNormWeight}={}){let{keys:i,records:n}=e,s=new tc({getFn:t,fieldNormWeight:r});return s.setKeys(i),s.setIndexRecords(n),s},tB.config=ta,tB.parseQuery=tj,function(...e){tO.push(...e)}(tk);let tq=e=>{let t=e.publicUserData?.firstName;return t&&e.publicUserData?.lastName?t=`${t} ${e.publicUserData.lastName}`:t||(t=e.publicUserData?.identifier),t},t$=async e=>{try{let{orgId:t}=await eK();if(!t)throw Error("Not logged in");let r=await eX(),i=(await r.organizations.getOrganizationMembershipList({organizationId:t,limit:100})).data.map(e=>({id:e.id,name:tq(e)??e.publicUserData?.identifier,imageUrl:e.publicUserData?.imageUrl}));return{data:new tB(i,{keys:["name"],minMatchCharLength:1,threshold:.3}).search(e).map(e=>e.item.id)}}catch(e){return{error:e}}};(0,eY.D)([t$]),(0,s.A)(t$,"7f124736fc7a0581d76303be1b915991f73d158aa9",null)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5567:(e,t,r)=>{"use strict";r.d(t,{Cursors:()=>i});let i=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call Cursors() from the server but Cursors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\components\\cursors.tsx","Cursors")},7828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return n}});let i=r(19487);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,i.parsePath)(e);return""+t+r+n+s}},8086:e=>{"use strict";e.exports=require("module")},8106:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CollaborationProvider:()=>d});var i=r(99730),n=r(96242);let s=(0,n.createServerReference)("7f88b12a1676b98b8bfa4374bd6560d2d1206ba4d4",n.callServer,void 0,n.findSourceMapURL,"getUsers"),o=(0,n.createServerReference)("7f124736fc7a0581d76303be1b915991f73d158aa9",n.callServer,void 0,n.findSourceMapURL,"searchUsers");var a=r(62703),l=r(57752);function c(e){let[t,r]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{r(!0)},[]),(0,i.jsx)(l.Suspense,{fallback:e.fallback,children:t?"function"==typeof e.children?e.children():e.children:e.fallback})}let u=({id:e,children:t,authEndpoint:r,fallback:n,...s})=>(0,i.jsx)(a.aq,{authEndpoint:r,...s,children:(0,i.jsx)(a.qo,{id:e,initialPresence:{cursor:null},children:(0,i.jsx)(c,{fallback:n,children:t})})}),d=({orgId:e,children:t})=>{let r=async({userIds:e})=>{let t=await s(e);if("error"in t)throw Error("Problem resolving users");return t.data},n=async({text:e})=>{let t=await o(e);if("error"in t)throw Error("Problem resolving mention suggestions");return t.data};return(0,i.jsx)(u,{id:`${e}:presence`,authEndpoint:"/api/collaboration/auth",fallback:(0,i.jsx)("div",{className:"px-3 text-muted-foreground text-xs",children:"Loading..."}),resolveUsers:r,resolveMentionSuggestions:n,children:t})}},8917:(e,t,r)=>{"use strict";r.d(t,{n:()=>P});var i,n,s,o,a,l,c,u,d,h,f,p,m,g,b,y,v,w,_,E=r(96158);r(28634);var S=r(58878);r(43226),r(83625);var A=r(72391),x=r(61541),I=r(85932),T=class{constructor(){(0,I.VK)(this,s),(0,I.VK)(this,i,"clerk_telemetry_throttler"),(0,I.VK)(this,n,864e5)}isEventThrottled(e){if(!(0,I.S7)(this,s,l))return!1;let t=Date.now(),r=(0,I.jq)(this,s,o).call(this,e),c=(0,I.S7)(this,s,a)?.[r];if(!c){let e={...(0,I.S7)(this,s,a),[r]:t};localStorage.setItem((0,I.S7)(this,i),JSON.stringify(e))}if(c&&t-c>(0,I.S7)(this,n)){let e=(0,I.S7)(this,s,a);delete e[r],localStorage.setItem((0,I.S7)(this,i),JSON.stringify(e))}return!!c}};i=new WeakMap,n=new WeakMap,s=new WeakSet,o=function(e){let{sk:t,pk:r,payload:i,...n}=e,s={...i,...n};return JSON.stringify(Object.keys({...i,...n}).sort().map(e=>s[e]))},a=function(){let e=localStorage.getItem((0,I.S7)(this,i));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,I.S7)(this,i)),!1}};var k={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},O=class{constructor(e){(0,I.VK)(this,p),(0,I.VK)(this,c),(0,I.VK)(this,u),(0,I.VK)(this,d,{}),(0,I.VK)(this,h,[]),(0,I.VK)(this,f),(0,I.OV)(this,c,{maxBufferSize:e.maxBufferSize??k.maxBufferSize,samplingRate:e.samplingRate??k.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:k.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,I.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,I.S7)(this,d).clerkVersion="",(0,I.S7)(this,d).sdk=e.sdk,(0,I.S7)(this,d).sdkVersion=e.sdkVersion,(0,I.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,x.q5)(e.publishableKey);t&&((0,I.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,I.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,I.OV)(this,u,new T)}get isEnabled(){return!("development"!==(0,I.S7)(this,d).instanceType||(0,I.S7)(this,c).disabled||"undefined"!=typeof process&&(0,A.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,I.S7)(this,c).debug||"undefined"!=typeof process&&(0,A.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,I.jq)(this,p,_).call(this,e.event,e.payload);(0,I.jq)(this,p,v).call(this,t.event,t),(0,I.jq)(this,p,m).call(this,t,e.eventSamplingRate)&&((0,I.S7)(this,h).push(t),(0,I.jq)(this,p,b).call(this))}};c=new WeakMap,u=new WeakMap,d=new WeakMap,h=new WeakMap,f=new WeakMap,p=new WeakSet,m=function(e,t){return this.isEnabled&&!this.isDebug&&(0,I.jq)(this,p,g).call(this,e,t)},g=function(e,t){let r=Math.random();return!!(r<=(0,I.S7)(this,c).samplingRate&&(void 0===t||r<=t))&&!(0,I.S7)(this,u).isEventThrottled(e)},b=function(){if("undefined"==typeof window)return void(0,I.jq)(this,p,y).call(this);if((0,I.S7)(this,h).length>=(0,I.S7)(this,c).maxBufferSize){(0,I.S7)(this,f)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,I.S7)(this,f)),(0,I.jq)(this,p,y).call(this);return}(0,I.S7)(this,f)||("requestIdleCallback"in window?(0,I.OV)(this,f,requestIdleCallback(()=>{(0,I.jq)(this,p,y).call(this)})):(0,I.OV)(this,f,setTimeout(()=>{(0,I.jq)(this,p,y).call(this)},0)))},y=function(){fetch(new URL("/v1/event",(0,I.S7)(this,c).endpoint),{method:"POST",body:JSON.stringify({events:(0,I.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,I.OV)(this,h,[])}).catch(()=>void 0)},v=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},w=function(){let e={name:(0,I.S7)(this,d).sdk,version:(0,I.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},_=function(e,t){let r=(0,I.jq)(this,p,w).call(this);return{event:e,cv:(0,I.S7)(this,d).clerkVersion??"",it:(0,I.S7)(this,d).instanceType??"",sdk:r.name,sdkv:r.version,...(0,I.S7)(this,d).publishableKey?{pk:(0,I.S7)(this,d).publishableKey}:{},...(0,I.S7)(this,d).secretKey?{sk:(0,I.S7)(this,d).secretKey}:{},payload:t}};(0,S.C)(E.nr);var R=r(86072);let C={secretKey:R.rB,publishableKey:R.At,apiUrl:R.H$,apiVersion:R.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:R.Rg,domain:R.V2,isSatellite:R.fS,sdkMetadata:R.tm,telemetry:{disabled:R.nN,debug:R.Mh}},P=e=>(function(e){let t={...e},r=(0,E.y3)(t),i=(0,E.Bs)({options:t,apiClient:r}),n=new O({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...i,telemetry:n}})({...C,...e})},8991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return i},RemovedUAError:function(){return n}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class i extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class n extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},10177:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.default,__next_app__:()=>u,pages:()=>c,routeModule:()=>d,tree:()=>l});var i=r(57864),n=r(94327),s=r(70814),o=r(17984),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);r.d(t,a);let l={children:["",{children:["(authenticated)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39418)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},d=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(authenticated)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10783:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>S,AvatarFallback:()=>x,AvatarImage:()=>A});var i=r(99730),n=r(57752),s=r(1493),o=r(18526),a=r(62676),l=r(56750),c=r(52578);function u(){return()=>{}}var d="Avatar",[h,f]=(0,s.A)(d),[p,m]=h(d),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...s}=e,[o,a]=n.useState("idle");return(0,i.jsx)(p,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:(0,i.jsx)(l.sG.span,{...s,ref:t})})});g.displayName=d;var b="AvatarImage",y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:s,onLoadingStatusChange:d=()=>{},...h}=e,f=m(b,r),p=function(e,{referrerPolicy:t,crossOrigin:r}){let i=(0,c.useSyncExternalStore)(u,()=>!0,()=>!1),s=n.useRef(null),o=i?(s.current||(s.current=new window.Image),s.current):null,[l,d]=n.useState(()=>_(o,e));return(0,a.N)(()=>{d(_(o,e))},[o,e]),(0,a.N)(()=>{let e=e=>()=>{d(e)};if(!o)return;let i=e("loaded"),n=e("error");return o.addEventListener("load",i),o.addEventListener("error",n),t&&(o.referrerPolicy=t),"string"==typeof r&&(o.crossOrigin=r),()=>{o.removeEventListener("load",i),o.removeEventListener("error",n)}},[o,r,t]),l}(s,h),g=(0,o.c)(e=>{d(e),f.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==p&&g(p)},[p,g]),"loaded"===p?(0,i.jsx)(l.sG.img,{...h,ref:t,src:s}):null});y.displayName=b;var v="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:s,...o}=e,a=m(v,r),[c,u]=n.useState(void 0===s);return n.useEffect(()=>{if(void 0!==s){let e=window.setTimeout(()=>u(!0),s);return()=>window.clearTimeout(e)}},[s]),c&&"loaded"!==a.imageLoadingStatus?(0,i.jsx)(l.sG.span,{...o,ref:t}):null});function _(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=v;var E=r(83590);function S({className:e,...t}){return(0,i.jsx)(g,{"data-slot":"avatar",className:(0,E.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function A({className:e,...t}){return(0,i.jsx)(y,{"data-slot":"avatar-image",className:(0,E.cn)("aspect-square size-full",e),...t})}function x({className:e,...t}){return(0,i.jsx)(w,{"data-slot":"avatar-fallback",className:(0,E.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11284:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return a},NextRequest:function(){return l}});let i=r(60200),n=r(19354),s=r(8991),o=r(46046),a=Symbol("internal request");class l extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,n.validateURL)(r),t.body&&"half"!==t.duplex&&(t.duplex="half"),e instanceof Request?super(e,t):super(r,t);let s=new i.NextURL(r,{headers:(0,n.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[a]={cookies:new o.RequestCookies(this.headers),nextUrl:s,url:s.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[a].cookies}get nextUrl(){return this[a].nextUrl}get page(){throw new s.RemovedPageError}get ua(){throw new s.RemovedUAError}get url(){return this[a].url}}},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},15006:(e,t,r)=>{"use strict";r.d(t,{AvatarStack:()=>l});var i=r(99730),n=r(62703),s=r(10783),o=r(41033);let a=({info:e})=>(0,i.jsxs)(o.m_,{delayDuration:0,children:[(0,i.jsx)(o.k$,{children:(0,i.jsxs)(s.Avatar,{className:"h-7 w-7 bg-secondary ring-1 ring-background",children:[(0,i.jsx)(s.AvatarImage,{src:e?.avatar,alt:e?.name}),(0,i.jsx)(s.AvatarFallback,{className:"text-xs",children:e?.name?.slice(0,2)})]})}),(0,i.jsx)(o.ZI,{collisionPadding:4,children:(0,i.jsx)("p",{children:e?.name??"Unknown"})})]}),l=()=>{let e=(0,n.Ac)(),t=(0,n.Z5)(),r=e.length>3;return(0,i.jsxs)("div",{className:"-space-x-1 flex items-center px-4",children:[e.slice(0,3).map(({connectionId:e,info:t})=>(0,i.jsx)(a,{info:t},e)),r&&(0,i.jsx)(a,{info:{name:`+${e.length-3}`,color:"var(--color-muted-foreground)"}}),t&&(0,i.jsx)(a,{info:t.info})]})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},17301:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return C},CACHE_ONE_YEAR:function(){return S},DOT_NEXT_ALIAS:function(){return O},ESLINT_DEFAULT_DIRS:function(){return Y},GSP_NO_RETURNED_VALUE:function(){return H},GSSP_COMPONENT_MEMBER_ERROR:function(){return W},GSSP_NO_RETURNED_VALUE:function(){return V},INFINITE_CACHE:function(){return A},INSTRUMENTATION_HOOK_FILENAME:function(){return T},MATCHED_PATH_HEADER:function(){return n},MIDDLEWARE_FILENAME:function(){return x},MIDDLEWARE_LOCATION_REGEXP:function(){return I},NEXT_BODY_SUFFIX:function(){return p},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return E},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return g},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return b},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return _},NEXT_CACHE_TAGS_HEADER:function(){return m},NEXT_CACHE_TAG_MAX_ITEMS:function(){return v},NEXT_CACHE_TAG_MAX_LENGTH:function(){return w},NEXT_DATA_SUFFIX:function(){return h},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return i},NEXT_META_SUFFIX:function(){return f},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return y},NON_STANDARD_NODE_ENV:function(){return G},PAGES_DIR_ALIAS:function(){return k},PRERENDER_REVALIDATE_HEADER:function(){return s},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return U},ROOT_DIR_ALIAS:function(){return R},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return j},RSC_ACTION_ENCRYPTION_ALIAS:function(){return M},RSC_ACTION_PROXY_ALIAS:function(){return D},RSC_ACTION_VALIDATE_ALIAS:function(){return N},RSC_CACHE_WRAPPER_ALIAS:function(){return L},RSC_MOD_REF_PROXY_ALIAS:function(){return P},RSC_PREFETCH_SUFFIX:function(){return a},RSC_SEGMENTS_DIR_SUFFIX:function(){return l},RSC_SEGMENT_SUFFIX:function(){return c},RSC_SUFFIX:function(){return u},SERVER_PROPS_EXPORT_ERROR:function(){return z},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return B},SERVER_PROPS_SSG_CONFLICT:function(){return q},SERVER_RUNTIME:function(){return J},SSG_FALLBACK_EXPORT_ERROR:function(){return X},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return F},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return $},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return K},WEBPACK_LAYERS:function(){return Q},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",i="nxtI",n="x-matched-path",s="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",a=".prefetch.rsc",l=".segments",c=".segment.rsc",u=".rsc",d=".action",h=".json",f=".meta",p=".body",m="x-next-cache-tags",g="x-next-revalidated-tags",b="x-next-revalidate-tag-token",y="next-resume",v=128,w=256,_=1024,E="_N_T_",S=31536e3,A=0xfffffffe,x="middleware",I=`(?:src/)?${x}`,T="instrumentation",k="private-next-pages",O="private-dot-next",R="private-next-root-dir",C="private-next-app-dir",P="next/dist/build/webpack/loaders/next-flight-loader/module-proxy",N="private-next-rsc-action-validate",D="private-next-rsc-server-reference",L="private-next-rsc-cache-wrapper",M="private-next-rsc-action-encryption",j="private-next-rsc-action-client-wrapper",U="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",F="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",B="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",q="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",$="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",z="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",H="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",V="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",K="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",W="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",G='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',X="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",Y=["app","pages","components","lib","src"],J={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Z={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Q={...Z,GROUP:{builtinReact:[Z.reactServerComponents,Z.actionBrowser],serverOnly:[Z.reactServerComponents,Z.actionBrowser,Z.instrument,Z.middleware],neutralTarget:[Z.apiNode,Z.apiEdge],clientOnly:[Z.serverSideRendering,Z.appPagesBrowser],bundled:[Z.reactServerComponents,Z.actionBrowser,Z.serverSideRendering,Z.appPagesBrowser,Z.shared,Z.instrument,Z.middleware],appPages:[Z.reactServerComponents,Z.serverSideRendering,Z.appPagesBrowser,Z.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19354:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return n},normalizeNextQueryParam:function(){return l},splitCookiesString:function(){return s},toNodeOutgoingHttpHeaders:function(){return o},validateURL:function(){return a}});let i=r(17301);function n(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function s(e){var t,r,i,n,s,o=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;l();)if(","===(r=e.charAt(a))){for(i=a,a+=1,l(),n=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=n,o.push(e.substring(t,i)),t=a):a=i+1}else a+=1;(!s||a>=e.length)&&o.push(e.substring(t,e.length))}return o}function o(e){let t={},r=[];if(e)for(let[i,n]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...s(n)),t[i]=1===r.length?r[0]:r):t[i]=n;return t}function a(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function l(e){for(let t of[i.NEXT_QUERY_PARAM_PREFIX,i.NEXT_INTERCEPTION_MARKER_PREFIX])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},19487:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},19771:e=>{"use strict";e.exports=require("process")},21311:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rootParams",{enumerable:!0,get:function(){return u}});let i=r(69737),n=r(3881),s=r(29294),o=r(63033),a=r(14026),l=r(83089),c=new WeakMap;async function u(){let e=s.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new i.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=o.workUnitAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){let i=t.fallbackRouteParams;if(i){let h=!1;for(let t in e)if(i.has(t)){h=!0;break}if(h){if("prerender"===r.type){let t=c.get(e);if(t)return t;let i=(0,a.makeHangingPromise)(r.renderSignal,"`unstable_rootParams`");return c.set(e,i),i}var s=e,o=i,u=t,d=r;let h=c.get(s);if(h)return h;let f={...s},p=Promise.resolve(f);return c.set(s,p),Object.keys(s).forEach(e=>{l.wellKnownProperties.has(e)||(o.has(e)?Object.defineProperty(f,e,{get(){let t=(0,l.describeStringPropertyAccess)("unstable_rootParams",e);"prerender-ppr"===d.type?(0,n.postponeWithTracking)(u.route,t,d.dynamicTracking):(0,n.throwToInterruptStaticGeneration)(t,u,d)},enumerable:!0}):p[e]=s[e])}),p}}return Promise.resolve(e)}(t.rootParams,e,t);default:return Promise.resolve(t.rootParams)}}},21820:e=>{"use strict";e.exports=require("os")},22099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return o}});let i=r(80005),n=r(85513),s=r(80631);function o(e,t){var r,o;let{basePath:a,i18n:l,trailingSlash:c}=null!=(r=t.nextConfig)?r:{},u={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):c};a&&(0,s.pathHasPrefix)(u.pathname,a)&&(u.pathname=(0,n.removePathPrefix)(u.pathname,a),u.basePath=a);let d=u.pathname;if(u.pathname.startsWith("/_next/data/")&&u.pathname.endsWith(".json")){let e=u.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");u.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(u.pathname=d)}if(l){let e=t.i18nProvider?t.i18nProvider.analyze(u.pathname):(0,i.normalizeLocalePath)(u.pathname,l.locales);u.locale=e.detectedLocale,u.pathname=null!=(o=e.pathname)?o:u.pathname,!e.detectedLocale&&u.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,i.normalizeLocalePath)(d,l.locales)).detectedLocale&&(u.locale=e.detectedLocale)}return u}},24532:(e,t)=>{"use strict";function r(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})},24767:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var i=r(23233);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:s="",children:o,iconNode:u,...d},h)=>(0,i.createElement)("svg",{ref:h,...c,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:a("lucide",s),...!o&&!l(d)&&{"aria-hidden":"true"},...d},[...u.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(o)?o:[o]])),d=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...s},l)=>(0,i.createElement)(u,{ref:l,iconNode:t,className:a(`lucide-${n(o(e))}`,`lucide-${e}`,r),...s}));return r.displayName=o(e),r}},27236:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"connection",{enumerable:!0,get:function(){return c}});let i=r(29294),n=r(63033),s=r(3881),o=r(86621),a=r(14026),l=r(97135);function c(){let e=i.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type)return(0,a.makeHangingPromise)(t.renderSignal,"`connection()`");else"prerender-ppr"===t.type?(0,s.postponeWithTracking)(e.route,"connection",t.dynamicTracking):"prerender-legacy"===t.type&&(0,s.throwToInterruptStaticGeneration)("connection",e,t);(0,s.trackDynamicDataInDynamicRender)(e,t)}return Promise.resolve(void 0)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30409:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o});var i=r(23233);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var s=r(94752),o=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...s}=e;if(i.isValidElement(r)){var o;let e,a,l=(o=r,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(e,t){let r={...t};for(let i in t){let n=e[i],s=t[i];/^on[A-Z]/.test(i)?n&&s?r[i]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[i]=n):"style"===i?r[i]={...n,...s}:"className"===i&&(r[i]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==i.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,i=e.map(e=>{let i=n(e,t);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let t=0;t<i.length;t++){let r=i[t];"function"==typeof r?r():n(e[t],null)}}}}(t,l):l),i.cloneElement(r,c)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:n,...o}=e,a=i.Children.toArray(n),c=a.find(l);if(c){let e=c.props.children,n=a.map(t=>t!==c?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...o,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}("Slot"),a=Symbol("radix.slottable");function l(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},31421:e=>{"use strict";e.exports=require("node:child_process")},32787:(e,t,r)=>{let{createProxy:i}=r(20867);e.exports=i("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js")},33485:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},33873:e=>{"use strict";e.exports=require("path")},34225:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(52723),t)},34631:e=>{"use strict";e.exports=require("tls")},34651:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CollaborationProvider:()=>i});let i=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call CollaborationProvider() from the server but CollaborationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\components\\collaboration-provider.tsx","CollaborationProvider")},35069:(e,t,r)=>{"use strict";r.d(t,{Cursors:()=>a});var i=r(99730),n=r(62703),s=r(57752);let o=({name:e,color:t,x:r,y:n})=>(0,i.jsxs)("div",{className:"pointer-events-none absolute top-0 left-0 z-[999] select-none transition-transform duration-100",style:{transform:`translateX(${r}px) translateY(${n}px)`},children:[(0,i.jsxs)("svg",{className:"absolute top-0 left-0",width:"24",height:"36",viewBox:"0 0 24 36",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("title",{children:"Cursor"}),(0,i.jsx)("path",{d:"M5.65376 12.3673H5.46026L5.31717 12.4976L0.500002 16.8829L0.500002 1.19841L11.7841 12.3673H5.65376Z",fill:t})]}),(0,i.jsx)("div",{className:"absolute top-4 left-1.5 whitespace-nowrap rounded-full px-2 py-0.5 text-white text-xs",style:{backgroundColor:t},children:e})]}),a=()=>{let[e,t]=(0,n.o6)(),r=(0,n.Ac)();return(0,s.useEffect)(()=>{let e=e=>{t({cursor:{x:Math.round(e.clientX),y:Math.round(e.clientY)}})},r=()=>{t({cursor:null})};return document.body.addEventListener("pointermove",e),document.body.addEventListener("pointerleave",r),()=>{document.body.removeEventListener("pointermove",e),document.body.removeEventListener("pointerleave",r)}},[t]),r.map(({connectionId:e,presence:t,info:r})=>t.cursor?(0,i.jsx)(o,{color:r.color,x:t.cursor.x,y:t.cursor.y,name:r?.name},`cursor-${e}`):null)}},36137:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var i=r(71280),n=r.n(i)},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},39418:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>f});var i=r(94752),n=r(2382),s=r(37838),o=r(18815),a=r(36137),l=r(62923),c=r(44568),u=r(5567),d=r(63913);let h=(0,a.default)(()=>Promise.resolve().then(r.bind(r,34651)).then(e=>e.CollaborationProvider),{loadableGenerated:{modules:["app\\(authenticated)\\page.tsx -> ./components/collaboration-provider"]}}),f={title:"Acme Inc",description:"My application."},p=async()=>{let e=await o.database.page.findMany(),{orgId:t}=await (0,s.j)();return t||(0,l.notFound)(),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(d.Y,{pages:["Building Your Application"],page:"Data Fetching",children:n._.LIVEBLOCKS_SECRET&&(0,i.jsxs)(h,{orgId:t,children:[(0,i.jsx)(c.AvatarStack,{}),(0,i.jsx)(u.Cursors,{})]})}),(0,i.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:[(0,i.jsx)("div",{className:"grid auto-rows-min gap-4 md:grid-cols-3",children:e.map(e=>(0,i.jsx)("div",{className:"aspect-video rounded-xl bg-muted/50",children:e.name},e.id))}),(0,i.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"})]})]})}},39928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let i=r(94752),n=r(23233),s=r(32787),o=r(47390);function a(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},r=(0,n.lazy)(()=>t.loader().then(a)),c=t.loading;function u(e){let a=c?(0,i.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,u=l?n.Suspense:n.Fragment,d=t.ssr?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o.PreloadChunks,{moduleIds:t.modules}),(0,i.jsx)(r,{...e})]}):(0,i.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,i.jsx)(r,{...e})});return(0,i.jsx)(u,{...l?{fallback:a}:{},children:d})}return u.displayName="LoadableComponent",u}},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44568:(e,t,r)=>{"use strict";r.d(t,{AvatarStack:()=>i});let i=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call AvatarStack() from the server but AvatarStack is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\components\\avatar-stack.tsx","AvatarStack")},44708:e=>{"use strict";e.exports=require("node:https")},47390:(e,t,r)=>{let{createProxy:i}=r(20867);e.exports=i("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-chunks.js")},48161:e=>{"use strict";e.exports=require("node:os")},52578:(e,t,r)=>{"use strict";e.exports=r(64959)},52723:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"after",{enumerable:!0,get:function(){return n}});let i=r(29294);function n(e){let t=i.workAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return s}});let i=r(7828),n=r(80631);function s(e,t,r,s){if(!t||t===r)return e;let o=e.toLowerCase();return!s&&((0,n.pathHasPrefix)(o,"/api")||(0,n.pathHasPrefix)(o,"/"+t.toLowerCase()))?e:(0,i.addPathPrefix)(e,"/"+t)}},58878:(e,t,r)=>{"use strict";function i(e){return async(...t)=>{let{data:r,errors:i}=await e(...t);if(i)throw i[0];return r}}function n(e){return(...t)=>{let{data:r,errors:i}=e(...t);if(i)throw i[0];return r}}r.d(t,{C:()=>i,R:()=>n})},60200:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return u}});let i=r(82718),n=r(77107),s=r(65698),o=r(22099),a=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function l(e,t){return new URL(String(e).replace(a,"localhost"),t&&String(t).replace(a,"localhost"))}let c=Symbol("NextURLInternal");class u{constructor(e,t,r){let i,n;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,n=r||{}):n=r||t||{},this[c]={url:l(e,i??n.base),options:n,basePath:""},this.analyze()}analyze(){var e,t,r,n,a;let l=(0,o.getNextPathnameInfo)(this[c].url.pathname,{nextConfig:this[c].options.nextConfig,parseData:!0,i18nProvider:this[c].options.i18nProvider}),u=(0,s.getHostname)(this[c].url,this[c].options.headers);this[c].domainLocale=this[c].options.i18nProvider?this[c].options.i18nProvider.detectDomainLocale(u):(0,i.detectDomainLocale)(null==(t=this[c].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,u);let d=(null==(r=this[c].domainLocale)?void 0:r.defaultLocale)||(null==(a=this[c].options.nextConfig)||null==(n=a.i18n)?void 0:n.defaultLocale);this[c].url.pathname=l.pathname,this[c].defaultLocale=d,this[c].basePath=l.basePath??"",this[c].buildId=l.buildId,this[c].locale=l.locale??d,this[c].trailingSlash=l.trailingSlash}formatPathname(){return(0,n.formatNextPathnameInfo)({basePath:this[c].basePath,buildId:this[c].buildId,defaultLocale:this[c].options.forceLocale?void 0:this[c].defaultLocale,locale:this[c].locale,pathname:this[c].url.pathname,trailingSlash:this[c].trailingSlash})}formatSearch(){return this[c].url.search}get buildId(){return this[c].buildId}set buildId(e){this[c].buildId=e}get locale(){return this[c].locale??""}set locale(e){var t,r;if(!this[c].locale||!(null==(r=this[c].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[c].locale=e}get defaultLocale(){return this[c].defaultLocale}get domainLocale(){return this[c].domainLocale}get searchParams(){return this[c].url.searchParams}get host(){return this[c].url.host}set host(e){this[c].url.host=e}get hostname(){return this[c].url.hostname}set hostname(e){this[c].url.hostname=e}get port(){return this[c].url.port}set port(e){this[c].url.port=e}get protocol(){return this[c].url.protocol}set protocol(e){this[c].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[c].url=l(e),this.analyze()}get origin(){return this[c].url.origin}get pathname(){return this[c].url.pathname}set pathname(e){this[c].url.pathname=e}get hash(){return this[c].url.hash}set hash(e){this[c].url.hash=e}get search(){return this[c].url.search}set search(e){this[c].url.search=e}get password(){return this[c].url.password}set password(e){this[c].url.password=e}get username(){return this[c].url.username}set username(e){this[c].url.username=e}get basePath(){return this[c].basePath}set basePath(e){this[c].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new u(String(this),this[c].options)}}},60495:(e,t,r)=>{var i;(()=>{var n={226:function(n,s){!function(o,a){"use strict";var l="function",c="undefined",u="object",d="string",h="major",f="model",p="name",m="type",g="vendor",b="version",y="architecture",v="console",w="mobile",_="tablet",E="smarttv",S="wearable",A="embedded",x="Amazon",I="Apple",T="ASUS",k="BlackBerry",O="Browser",R="Chrome",C="Firefox",P="Google",N="Huawei",D="Microsoft",L="Motorola",M="Opera",j="Samsung",U="Sharp",F="Sony",B="Xiaomi",q="Zebra",$="Facebook",z="Chromium OS",H="Mac OS",V=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},K=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},W=function(e,t){return typeof e===d&&-1!==G(t).indexOf(G(e))},G=function(e){return e.toLowerCase()},X=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},Y=function(e,t){for(var r,i,n,s,o,c,d=0;d<t.length&&!o;){var h=t[d],f=t[d+1];for(r=i=0;r<h.length&&!o&&h[r];)if(o=h[r++].exec(e))for(n=0;n<f.length;n++)c=o[++i],typeof(s=f[n])===u&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,c):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=c?c.replace(s[1],s[2]):void 0:this[s[0]]=c?s[1].call(this,c,s[2]):void 0:4===s.length&&(this[s[0]]=c?s[3].call(this,c.replace(s[1],s[2])):a):this[s]=c||a;d+=2}},J=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(W(t[r][i],e))return"?"===r?a:r}else if(W(t[r],e))return"?"===r?a:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[b,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[b,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,b],[/opios[\/ ]+([\w\.]+)/i],[b,[p,M+" Mini"]],[/\bopr\/([\w\.]+)/i],[b,[p,M]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,b],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[b,[p,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[b,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[b,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[b,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[b,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[b,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+O],b],[/\bfocus\/([\w\.]+)/i],[b,[p,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[b,[p,M+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[b,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[b,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[b,[p,M+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[b,[p,"MIUI "+O]],[/fxios\/([-\w\.]+)/i],[b,[p,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+O]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+O],b],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],b],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,b],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,$],b],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,b],[/\bgsa\/([\w\.]+) .*safari\//i],[b,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[b,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[b,[p,R+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,R+" WebView"],b],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[b,[p,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,b],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[b,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[b,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[b,J,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,b],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],b],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[b,[p,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,b],[/(cobalt)\/([\w\.]+)/i],[p,[b,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[y,"amd64"]],[/(ia32(?=;))/i],[[y,G]],[/((?:i[346]|x)86)[;\)]/i],[[y,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/windows (ce|mobile); ppc;/i],[[y,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[y,/ower/,"",G]],[/(sun4\w)[;\)]/i],[[y,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[y,G]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[g,j],[m,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[g,j],[m,w]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[g,I],[m,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[g,I],[m,_]],[/(macintosh);/i],[f,[g,I]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[g,U],[m,w]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[g,N],[m,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[g,N],[m,w]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[g,B],[m,w]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[g,B],[m,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[g,"OPPO"],[m,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[g,"Vivo"],[m,w]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[g,"Realme"],[m,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[g,L],[m,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[g,L],[m,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[g,"LG"],[m,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[g,"LG"],[m,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[g,"Lenovo"],[m,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[g,"Nokia"],[m,w]],[/(pixel c)\b/i],[f,[g,P],[m,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[g,P],[m,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[g,F],[m,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[g,F],[m,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[g,"OnePlus"],[m,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[g,x],[m,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[g,x],[m,w]],[/(playbook);[-\w\),; ]+(rim)/i],[f,g,[m,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[g,k],[m,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[g,T],[m,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[g,T],[m,w]],[/(nexus 9)/i],[f,[g,"HTC"],[m,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[g,[f,/_/g," "],[m,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[g,"Acer"],[m,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[g,"Meizu"],[m,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[g,f,[m,w]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[g,f,[m,_]],[/(surface duo)/i],[f,[g,D],[m,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[g,"Fairphone"],[m,w]],[/(u304aa)/i],[f,[g,"AT&T"],[m,w]],[/\bsie-(\w*)/i],[f,[g,"Siemens"],[m,w]],[/\b(rct\w+) b/i],[f,[g,"RCA"],[m,_]],[/\b(venue[\d ]{2,7}) b/i],[f,[g,"Dell"],[m,_]],[/\b(q(?:mv|ta)\w+) b/i],[f,[g,"Verizon"],[m,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[g,"Barnes & Noble"],[m,_]],[/\b(tm\d{3}\w+) b/i],[f,[g,"NuVision"],[m,_]],[/\b(k88) b/i],[f,[g,"ZTE"],[m,_]],[/\b(nx\d{3}j) b/i],[f,[g,"ZTE"],[m,w]],[/\b(gen\d{3}) b.+49h/i],[f,[g,"Swiss"],[m,w]],[/\b(zur\d{3}) b/i],[f,[g,"Swiss"],[m,_]],[/\b((zeki)?tb.*\b) b/i],[f,[g,"Zeki"],[m,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[g,"Dragon Touch"],f,[m,_]],[/\b(ns-?\w{0,9}) b/i],[f,[g,"Insignia"],[m,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[g,"NextBook"],[m,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[g,"Voice"],f,[m,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[g,"LvTel"],f,[m,w]],[/\b(ph-1) /i],[f,[g,"Essential"],[m,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[g,"Envizen"],[m,_]],[/\b(trio[-\w\. ]+) b/i],[f,[g,"MachSpeed"],[m,_]],[/\btu_(1491) b/i],[f,[g,"Rotor"],[m,_]],[/(shield[\w ]+) b/i],[f,[g,"Nvidia"],[m,_]],[/(sprint) (\w+)/i],[g,f,[m,w]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[g,D],[m,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[g,q],[m,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[g,q],[m,w]],[/smart-tv.+(samsung)/i],[g,[m,E]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[g,j],[m,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[g,"LG"],[m,E]],[/(apple) ?tv/i],[g,[f,I+" TV"],[m,E]],[/crkey/i],[[f,R+"cast"],[g,P],[m,E]],[/droid.+aft(\w)( bui|\))/i],[f,[g,x],[m,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[g,U],[m,E]],[/(bravia[\w ]+)( bui|\))/i],[f,[g,F],[m,E]],[/(mitv-\w{5}) bui/i],[f,[g,B],[m,E]],[/Hbbtv.*(technisat) (.*);/i],[g,f,[m,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[g,X],[f,X],[m,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[g,f,[m,v]],[/droid.+; (shield) bui/i],[f,[g,"Nvidia"],[m,v]],[/(playstation [345portablevi]+)/i],[f,[g,F],[m,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[g,D],[m,v]],[/((pebble))app/i],[g,f,[m,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[g,I],[m,S]],[/droid.+; (glass) \d/i],[f,[g,P],[m,S]],[/droid.+; (wt63?0{2,3})\)/i],[f,[g,q],[m,S]],[/(quest( 2| pro)?)/i],[f,[g,$],[m,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[g,[m,A]],[/(aeobc)\b/i],[f,[g,x],[m,A]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[m,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[m,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,w]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[g,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[b,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[b,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,b],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[b,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,b],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[b,J,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[b,J,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[b,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,H],[b,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[b,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,b],[/\(bb(10);/i],[b,[p,k]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[b,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[b,[p,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[b,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[b,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[b,[p,R+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,z],b],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,b],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],b],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,b]]},ee=function(e,t){if(typeof e===u&&(t=e,e=a),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==c&&o.navigator?o.navigator:a,i=e||(r&&r.userAgent?r.userAgent:""),n=r&&r.userAgentData?r.userAgentData:a,s=t?V(Q,t):Q,v=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[p]=a,t[b]=a,Y.call(t,i,s.browser),t[h]=typeof(e=t[b])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:a,v&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[y]=a,Y.call(e,i,s.cpu),e},this.getDevice=function(){var e={};return e[g]=a,e[f]=a,e[m]=a,Y.call(e,i,s.device),v&&!e[m]&&n&&n.mobile&&(e[m]=w),v&&"Macintosh"==e[f]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[m]=_),e},this.getEngine=function(){var e={};return e[p]=a,e[b]=a,Y.call(e,i,s.engine),e},this.getOS=function(){var e={};return e[p]=a,e[b]=a,Y.call(e,i,s.os),v&&!e[p]&&n&&"Unknown"!=n.platform&&(e[p]=n.platform.replace(/chrome os/i,z).replace(/macos/i,H)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===d&&e.length>350?X(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=K([p,b,h]),ee.CPU=K([y]),ee.DEVICE=K([f,g,m,v,w,E,_,S,A]),ee.ENGINE=ee.OS=K([p,b]),typeof s!==c?(n.exports&&(s=n.exports=ee),s.UAParser=ee):r.amdO?void 0===(i=(function(){return ee}).call(t,r,t,e))||(e.exports=i):typeof o!==c&&(o.UAParser=ee);var et=typeof o!==c&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},s={};function o(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},i=!0;try{n[e].call(r.exports,r,r.exports,o),i=!1}finally{i&&delete s[e]}return r.exports}o.ab=__dirname+"/",e.exports=o(226)})()},61847:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>i});let i=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\separator.tsx","Separator")},62703:(e,t,r)=>{"use strict";r.d(t,{aq:()=>rC,qo:()=>rQ,o6:()=>r0,Ac:()=>r1,Z5:()=>r2});var i=r(57752),n=Object.defineProperty,s="@liveblocks/core",o="2.24.2",a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{};function l(e){console.error(e)}function c(e){let t=e.editedAt?new Date(e.editedAt):void 0,r=new Date(e.createdAt),i=e.reactions.map(e=>({...e,createdAt:new Date(e.createdAt)}));if(e.body)return{...e,reactions:i,createdAt:r,editedAt:t};{let n=new Date(e.deletedAt);return{...e,reactions:i,createdAt:r,editedAt:t,deletedAt:n}}}function u(e){let t=new Date(e.createdAt),r=new Date(e.updatedAt),i=e.comments.map(e=>c(e));return{...e,createdAt:t,updatedAt:r,comments:i}}function d(e){let t=new Date(e.notifiedAt),r=e.readAt?new Date(e.readAt):null;if("activities"in e){let i=e.activities.map(e=>({...e,createdAt:new Date(e.createdAt)}));return{...e,notifiedAt:t,readAt:r,activities:i}}return{...e,notifiedAt:t,readAt:r}}function h(e){let t=new Date(e.createdAt);return{...e,createdAt:t}}function f(e){let t=new Date(e.deletedAt);return{...e,deletedAt:t}}function p(e){let t=new Date(e.deletedAt);return{...e,deletedAt:t}}function m(e){let t=new Date(e.deletedAt);return{...e,deletedAt:t}}var g={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(g,{error:()=>w,errorWithTitle:()=>S,warn:()=>v,warnWithTitle:()=>E});var b="background:#0e0d12;border-radius:9999px;color:#fff;padding:3px 7px;font-family:sans-serif;font-weight:600;";function y(e){return"undefined"==typeof window?console[e]:(t,...r)=>console[e]("%cLiveblocks",b,t,...r)}var v=y("warn"),w=y("error");function _(e){return"undefined"==typeof window?console[e]:(t,r,...i)=>console[e](`%cLiveblocks%c ${t}`,b,"font-weight:600",r,...i)}var E=_("warn"),S=_("error");function A(e){return null!==e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)}function x(e){return A(e)&&"string"==typeof e.startsWith}function I(e){throw Error(e)}function T(e){return Object.entries(e)}function k(e){try{return JSON.parse(e)}catch(e){return}}function O(e){return JSON.parse(JSON.stringify(e))}function R(e){return e.filter(e=>null!=e)}function C(e){let t={...e};return Object.keys(e).forEach(e=>{void 0===t[e]&&delete t[e]}),t}async function P(e,t,r){let i;return Promise.race([e,new Promise((e,n)=>{i=setTimeout(()=>{n(Error(r))},t)})]).finally(()=>clearTimeout(i))}function N(e){let t=null;return()=>(null===t&&(t=e().catch(e=>{throw setTimeout(()=>{t=null},5e3),e})),t)}var D=class e extends Error{response;details;constructor(e,t,r){super(e),this.name="HttpError",this.response=t,this.details=r}static async fromResponse(t){let r,i,n;try{r=await t.text()}catch{}let s=r?k(r):void 0;A(s)&&(i=s);let o="";o||="string"==typeof i?.message?i.message:"",o||="string"==typeof i?.error?i.error:"",void 0===s&&(o||=r||""),o||=t.statusText;try{n=new URL(t.url).pathname}catch{}return new e(o+=void 0!==n?` (got status ${t.status} from ${n})`:` (got status ${t.status})`,t,i)}get status(){return this.response.status}},L=e=>e instanceof D&&e.status>=400&&e.status<500;async function M(e,t,r,i=L){let n=r.length>0?r[r.length-1]:0,s=0;for(;;){s++;try{return await e()}catch(e){if(i(e))throw e;if(s>=t)throw Error(`Failed after ${t} attempts: ${String(e)}`)}let o=r[s-1]??n;v(`Attempt ${s} was unsuccessful. Retrying in ${o} milliseconds.`),await function(e){return new Promise(t=>setTimeout(t,e))}(o)}}function j(){let e,t;return[new Promise((r,i)=>{e=r,t=i}),e,t]}function U(){let[e,t,r]=j();return{promise:e,resolve:t,reject:r}}function F(){let e=new Set;function t(t){return e.add(t),()=>e.delete(t)}function r(e){let r=t(t=>(r(),e(t)));return r}async function i(e){let r;return new Promise(i=>{r=t(t=>{(void 0===e||e(t))&&i(t)})}).finally(()=>r?.())}return{notify:function(t){let r=!1;for(let i of e)i(t),r=!0;return r},subscribe:t,subscribeOnce:r,count:function(){return e.size},waitUntil:i,dispose(){e.clear()},observable:{subscribe:t,subscribeOnce:r,waitUntil:i}}}var B=e=>e,q=Symbol("kSinks"),$=Symbol("kTrigger"),z=null,H=null;function V(e){if(null!==z)return void e();z=new Set;try{e()}finally{for(let e of z)e[$]();z=null}}function K(e){z||I("Expected to be in an active batch"),z.add(e)}function W(e,t){let r=!1,i={...e};return Object.keys(t).forEach(e=>{let n=t[e];i[e]!==n&&(void 0===n?delete i[e]:i[e]=n,r=!0)}),r?i:e}var G=class{equals;#e;[q];constructor(e){this.equals=e??Object.is,this.#e=F(),this[q]=new Set,this.get=this.get.bind(this),this.subscribe=this.subscribe.bind(this),this.subscribeOnce=this.subscribeOnce.bind(this)}dispose(){this.#e.dispose(),this.#e="(disposed)",this.equals="(disposed)"}get hasWatchers(){if(this.#e.count()>0)return!0;for(let e of this[q])if(e.hasWatchers)return!0;return!1}[$](){for(let e of(this.#e.notify(),this[q]))K(e)}subscribe(e){return 0===this.#e.count()&&this.get(),this.#e.subscribe(e)}subscribeOnce(e){let t=this.subscribe(()=>(t(),e()));return t}waitUntil(){throw Error("waitUntil not supported on Signals")}markSinksDirty(){for(let e of this[q])e.markDirty()}addSink(e){this[q].add(e)}removeSink(e){this[q].delete(e)}asReadonly(){return this}},X=class extends G{#t;constructor(e,t){super(t),this.#t=B(e)}dispose(){super.dispose(),this.#t="(disposed)"}get(){return H?.add(this),this.#t}set(e){V(()=>{"function"==typeof e&&(e=e(this.#t)),this.equals(this.#t,e)||(this.#t=B(e),this.markSinksDirty(),K(this))})}},Y=class extends X{constructor(e){super(B(C(e)))}set(){throw Error("Don't call .set() directly, use .patch()")}patch(e){super.set(t=>W(t,e))}},J=Symbol(),Z=class e extends G{#r;#i;#n;#s;#o;static from(...t){let r=t.pop();if("function"!=typeof r&&I("Invalid .from() call, last argument expected to be a function"),"function"!=typeof t[t.length-1])return new e(t,r);{let i=t.pop();return new e(t,i,r)}}constructor(e,t,r){super(r),this.#i=!0,this.#r=J,this.#s=e,this.#n=new Set,this.#o=t}dispose(){for(let e of this.#n)e.removeSink(this);this.#r="(disposed)",this.#n="(disposed)",this.#s="(disposed)",this.#o="(disposed)"}get isDirty(){return this.#i}#a(){let e,t=H;H=new Set;try{e=this.#o(...this.#s.map(e=>e.get()))}finally{let e=this.#n;for(let t of(this.#n=new Set,H))this.#n.add(t),e.delete(t);for(let t of e)t.removeSink(this);for(let e of this.#n)e.addSink(this);H=t}return this.#i=!1,!this.equals(this.#r,e)&&(this.#r=e,!0)}markDirty(){this.#i||(this.#i=!0,this.markSinksDirty())}get(){return this.#i&&this.#a(),H?.add(this),this.#r}[$](){this.hasWatchers&&this.#a()&&super[$]()}},Q=class extends G{#l;constructor(e){super(),this.#l=e}dispose(){super.dispose(),this.#l="(disposed)"}get(){return H?.add(this),this.#l}mutate(e){V(()=>{let t=!e||e(this.#l);null!==t&&"object"==typeof t&&"then"in t&&I("MutableSignal.mutate() does not support async callbacks"),!1!==t&&(this.markSinksDirty(),K(this))})}};function ee(e,t){return null===t||"object"!=typeof t||Array.isArray(t)?t:Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{})}function et(e){return JSON.stringify(e,ee)}function er(e){try{return JSON.stringify(e)}catch(t){throw console.error(`Could not stringify: ${t.message}`),console.error(e),t}}var ei=class{input;resolve;reject;promise;constructor(e){this.input=e;let{promise:t,resolve:r,reject:i}=U();this.promise=t,this.resolve=r,this.reject=i}},en=class{#c=[];#u;#d;#h;#f;error=!1;constructor(e,t){this.#u=e,this.#d=t.size??50,this.#h=t.delay}#p(){void 0!==this.#f&&(clearTimeout(this.#f),this.#f=void 0)}#m(){this.#c.length===this.#d?this.#g():1===this.#c.length&&(this.#p(),this.#f=setTimeout(()=>void this.#g(),this.#h))}async #g(){if(0===this.#c.length)return;let e=this.#c.splice(0),t=e.map(e=>e.input);try{let r=await this.#u(t);this.error=!1,e.forEach((t,i)=>{let n=r?.[i];Array.isArray(r)?e.length!==r.length?t.reject(Error(`Callback must return an array of the same length as the number of provided items. Expected ${e.length}, but got ${r.length}.`)):n instanceof Error?t.reject(n):t.resolve(n):t.reject(Error("Callback must return an array."))})}catch(t){this.error=!0,e.forEach(e=>{e.reject(t)})}}get(e){let t=this.#c.find(t=>et(t.input)===et(e));if(t)return t.promise;let r=new ei(e);return this.#c.push(r),this.#m(),r.promise}clear(){this.#c=[],this.error=!1,this.#p()}};function es(e){let t=new Q(new Map);function r(e,r){t.mutate(t=>{t.set(e,r)})}async function i(i){let n=et(i);if(!t.get().has(n))try{r(n,{isLoading:!0});let t=await e.get(i);r(n,{isLoading:!1,data:t})}catch(e){r(n,{isLoading:!1,error:e})}}return{subscribe:t.subscribe,enqueue:i,getItemState:function(e){let r=et(e);return t.get().get(r)},invalidate:function(e){t.mutate(t=>{if(Array.isArray(e))for(let r of e)t.delete(et(r));else t.clear()})},batch:e,_cacheKeys:function(){return[...t.get().keys()]}}}var eo=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t<63?"_":"-","");function ea(e){return`${e}_${eo()}`}var el=class extends Map{#b;constructor(e,t){super(t),this.#b=e}getOrCreate(e,t){if(super.has(e))return super.get(e);{let r=(t??this.#b??I("DefaultMap used without a factory function"))(e);return this.set(e,r),r}}},ec=/^[a-zA-Z_][a-zA-Z0-9_]*$/;function eu(e){let t=[],r=Object.entries(e),i=[],n=[],s=[];return r.forEach(([e,t])=>{if(!ec.test(e))throw Error("Key must only contain letters, numbers, _");ef(t)?i.push([e,t]):A(t)&&(x(t)?n.push([e,t]):s.push([e,t]))}),t=[...ed(i),...eh(n)],s.forEach(([e,r])=>{let i=Object.entries(r),n=[],s=[];i.forEach(([t,r])=>{if(em(t))throw Error("Key cannot be empty");ef(r)?n.push([ep(e,t),r]):x(r)&&s.push([ep(e,t),r])}),t=[...t,...ed(n),...eh(s)]}),t.map(({key:e,operator:t,value:r})=>`${e}${t}${eg(r)}`).join(" ")}var ed=e=>{let t=[];return e.forEach(([e,r])=>{t.push({key:e,operator:":",value:r})}),t},eh=e=>{let t=[];return e.forEach(([e,r])=>{"startsWith"in r&&"string"==typeof r.startsWith&&t.push({key:e,operator:"^",value:r.startsWith})}),t},ef=e=>"string"==typeof e||"number"==typeof e||"boolean"==typeof e||null===e,ep=(e,t)=>t?`${e}[${eg(t)}]`:e,em=e=>!e||""===e.toString().trim();function eg(e){let t=JSON.stringify(e);return"string"!=typeof e||t.includes("'")?t:`'${t.slice(1,-1).replace(/\\"/g,'"')}'`}function eb(e,...t){return e.reduce((e,r,i)=>e+encodeURIComponent(t[i-1]??"")+r)}function ey(e){return"public"===e.type?e.publicApiKey:e.token.raw}var ev=class{#y;#v;constructor(e,t){this.#y=e,this.#v=t}async #w(e,t,r,i){e.startsWith("/v2/c/")||I("This client can only be used to make /v2/c/* requests");let n=function(e,t,r){let i=new URL(t,e);return void 0!==r&&(i.search=(r instanceof URLSearchParams?r:function(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))null!=i&&t.set(r,i.toString());return t}(r)).toString()),i.toString()}(this.#y,e,i);return await this.#v(n,{...r,headers:{"Content-Type":"application/json; charset=utf-8",...r?.headers,Authorization:`Bearer ${ey(t)}`,"X-LB-Client":o||"dev"}})}async #_(e,t,r,i){let n,s=await this.#w(e,t,r,i);if(!s.ok)throw await D.fromResponse(s);try{n=await s.json()}catch{n={}}return n}async rawGet(e,t,r,i){return await this.#w(e,t,i,r)}async rawPost(e,t,r){return await this.#w(e,t,{method:"POST",body:er(r)})}async rawDelete(e,t){return await this.#w(e,t,{method:"DELETE"})}async get(e,t,r,i){return await this.#_(e,t,i,r)}async post(e,t,r,i,n){return await this.#_(e,t,{...i,method:"POST",body:er(r)},n)}async delete(e,t){return await this.#_(e,t,{method:"DELETE"})}async putBlob(e,t,r,i,n){return await this.#_(e,t,{...n,method:"PUT",headers:{"Content-Type":"application/octet-stream"},body:r},i)}};function ew(e,t){throw Error(t)}function e_(e,t="Expected value to be non-nullable"){return e}var eE=class{#E;constructor(e){this.#E=e}get current(){return this.#E}allowPatching(e){let t=this,r=!0;e({...this.#E,patch(e){if(r)for(let r of(t.#E=Object.assign({},t.#E,e),Object.entries(e))){let[e,t]=r;"patch"!==e&&(this[e]=t)}else throw Error("Can no longer patch stale context")}}),r=!1}},eS=1,eA=class{id;#S;#A;#x;#I;#T;#k;events;#O;#R;#C;get #P(){let e=this.#x.values()[Symbol.iterator]().next();if(!e.done)return e.value;throw Error("No states defined yet")}get currentState(){if(null===this.#I)if(0===this.#S)throw Error("Not started yet");else throw Error("Already stopped");return this.#I}start(){if(0!==this.#S)throw Error("State machine has already started");return this.#S=1,this.#I=this.#P,this.#N(null),this}stop(){if(1!==this.#S)throw Error("Cannot stop a state machine that hasn't started yet");this.#D(null),this.#S=2,this.#I=null}constructor(e){this.id=eS++,this.#S=0,this.#I=null,this.#x=new Set,this.#R=new Map,this.#O=[],this.#C=new Set,this.#T=new Map,this.#A=new eE(e),this.#k={didReceiveEvent:F(),willTransition:F(),didIgnoreEvent:F(),willExitState:F(),didEnterState:F()},this.events={didReceiveEvent:this.#k.didReceiveEvent.observable,willTransition:this.#k.willTransition.observable,didIgnoreEvent:this.#k.didIgnoreEvent.observable,willExitState:this.#k.willExitState.observable,didEnterState:this.#k.didEnterState.observable}}get context(){return this.#A.current}addState(e){if(0!==this.#S)throw Error("Already started");return this.#x.add(e),this}onEnter(e,t){if(0!==this.#S)throw Error("Already started");if(this.#R.has(e))throw Error(`enter/exit function for ${e} already exists`);return this.#R.set(e,t),this}onEnterAsync(e,t,r,i,n){return this.onEnter(e,()=>{let e=new AbortController,s=e.signal,o=n?setTimeout(()=>{let e=Error("Timed out");this.#L({type:"ASYNC_ERROR",reason:e},i)},n):void 0,a=!1;return t(this.#A.current,s).then(e=>{s.aborted||(a=!0,this.#L({type:"ASYNC_OK",data:e},r))},e=>{s.aborted||(a=!0,this.#L({type:"ASYNC_ERROR",reason:e},i))}),()=>{clearTimeout(o),a||e.abort()}})}#M(e){let t=[];if("*"===e)for(let e of this.#x)t.push(e);else if(e.endsWith(".*")){let r=e.slice(0,-1);for(let e of this.#x)e.startsWith(r)&&t.push(e)}else this.#x.has(e)&&t.push(e);if(0===t.length)throw Error(`No states match ${JSON.stringify(e)}`);return t}addTransitions(e,t){if(0!==this.#S)throw Error("Already started");for(let r of this.#M(e)){let i=this.#T.get(r);for(let[n,s]of(void 0===i&&(i=new Map,this.#T.set(r,i)),Object.entries(t))){if(i.has(n))throw Error(`Trying to set transition "${n}" on "${r}" (via "${e}"), but a transition already exists there.`);let t=s;if(this.#C.add(n),void 0!==t){let e="function"==typeof t?t:()=>t;i.set(n,e)}}}return this}addTimedTransition(e,t,r){return this.onEnter(e,()=>{let e=setTimeout(()=>{this.#L({type:"TIMER"},r)},"function"==typeof t?t(this.#A.current):t);return()=>{clearTimeout(e)}})}#j(e){return this.#T.get(this.currentState)?.get(e)}#D(e){this.#k.willExitState.notify(this.currentState),this.#A.allowPatching(t=>{e=e??this.#O.length;for(let r=0;r<e;r++)this.#O.pop()?.(t)})}#N(e){let t=function(e,t){let r=e.split(".");if(t<1||t>r.length+1)throw Error("Invalid number of levels");let i=[];t>r.length&&i.push("*");for(let e=r.length-t+1;e<r.length;e++){let t=r.slice(0,e);t.length>0&&i.push(t.join(".")+".*")}return i.push(e),i}(this.currentState,e??this.currentState.split(".").length+1);this.#A.allowPatching(e=>{for(let r of t){let t=this.#R.get(r),i=t?.(e);"function"==typeof i?this.#O.push(i):this.#O.push(null)}}),this.#k.didEnterState.notify(this.currentState)}send(e){if(!this.#C.has(e.type))throw Error(`Invalid event ${JSON.stringify(e.type)}`);if(2===this.#S)return;let t=this.#j(e.type);if(void 0!==t)return this.#L(e,t);this.#k.didIgnoreEvent.notify(e)}#L(e,t){let r,i;this.#k.didReceiveEvent.notify(e);let n=this.currentState,s=("function"==typeof t?t:()=>t)(e,this.#A.current);if(null===s)return void this.#k.didIgnoreEvent.notify(e);if("string"==typeof s?r=s:(r=s.target,i=Array.isArray(s.effect)?s.effect:[s.effect]),!this.#x.has(r))throw Error(`Invalid next state name: ${JSON.stringify(r)}`);this.#k.willTransition.notify({from:n,to:r});let[o,a]=function(e,t){if(e===t)return[0,0];let r=e.split("."),i=t.split("."),n=Math.min(r.length,i.length),s=0;for(;s<n&&r[s]===i[s];s++);return[r.length-s,i.length-s]}(this.currentState,r);if(o>0&&this.#D(o),this.#I=r,void 0!==i){let t=i;this.#A.allowPatching(r=>{for(let i of t)"function"==typeof i?i(r,e):r.patch(i)})}a>0&&this.#N(a)}},ex=(e=>(e[e.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",e[e.USER_JOINED=101]="USER_JOINED",e[e.USER_LEFT=102]="USER_LEFT",e[e.BROADCASTED_EVENT=103]="BROADCASTED_EVENT",e[e.ROOM_STATE=104]="ROOM_STATE",e[e.INITIAL_STORAGE_STATE=200]="INITIAL_STORAGE_STATE",e[e.UPDATE_STORAGE=201]="UPDATE_STORAGE",e[e.REJECT_STORAGE_OP=299]="REJECT_STORAGE_OP",e[e.UPDATE_YDOC=300]="UPDATE_YDOC",e[e.THREAD_CREATED=400]="THREAD_CREATED",e[e.THREAD_DELETED=407]="THREAD_DELETED",e[e.THREAD_METADATA_UPDATED=401]="THREAD_METADATA_UPDATED",e[e.THREAD_UPDATED=408]="THREAD_UPDATED",e[e.COMMENT_CREATED=402]="COMMENT_CREATED",e[e.COMMENT_EDITED=403]="COMMENT_EDITED",e[e.COMMENT_DELETED=404]="COMMENT_DELETED",e[e.COMMENT_REACTION_ADDED=405]="COMMENT_REACTION_ADDED",e[e.COMMENT_REACTION_REMOVED=406]="COMMENT_REACTION_REMOVED",e))(ex||{}),eI=(e=>(e[e.CLOSE_NORMAL=1e3]="CLOSE_NORMAL",e[e.CLOSE_ABNORMAL=1006]="CLOSE_ABNORMAL",e[e.UNEXPECTED_CONDITION=1011]="UNEXPECTED_CONDITION",e[e.TRY_AGAIN_LATER=1013]="TRY_AGAIN_LATER",e[e.INVALID_MESSAGE_FORMAT=4e3]="INVALID_MESSAGE_FORMAT",e[e.NOT_ALLOWED=4001]="NOT_ALLOWED",e[e.MAX_NUMBER_OF_MESSAGES_PER_SECONDS=4002]="MAX_NUMBER_OF_MESSAGES_PER_SECONDS",e[e.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS=4003]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS",e[e.MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP=4004]="MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP",e[e.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM=4005]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM",e[e.ROOM_ID_UPDATED=4006]="ROOM_ID_UPDATED",e[e.KICKED=4100]="KICKED",e[e.TOKEN_EXPIRED=4109]="TOKEN_EXPIRED",e[e.CLOSE_WITHOUT_RETRY=4999]="CLOSE_WITHOUT_RETRY",e))(eI||{});function eT(e){return 4999===e||e>=4e3&&e<4100}function ek(e){return 1013===e||e>=4200&&e<4300}function eO(e){let t=e.currentState;switch(t){case"@ok.connected":case"@ok.awaiting-pong":return"connected";case"@idle.initial":return"initial";case"@auth.busy":case"@auth.backoff":case"@connecting.busy":case"@connecting.backoff":case"@idle.zombie":return e.context.successCount>0?"reconnecting":"connecting";case"@idle.failed":return"disconnected";default:return ew(t,"Unknown state")}}var eR=[250,500,1e3,2e3,4e3,8e3,1e4],eC=eR[0]-1,eP=[2e3,3e4,6e4,3e5],eN=class extends Error{constructor(e){super(e)}};function eD(e,t){return t.find(t=>t>e)??t[t.length-1]}function eL(e){e.patch({backoffDelay:eD(e.backoffDelay,eR)})}function eM(e){e.patch({backoffDelay:eD(e.backoffDelay,eP)})}function ej(e){e.patch({successCount:0})}function eU(e,t){let r=2===e?w:1===e?v:()=>{};return()=>{r(t)}}function eF(e){let t="Connection to Liveblocks websocket server";return r=>{e instanceof Error?v(`${t} could not be established. ${String(e)}`):v(e$(e)?`${t} closed prematurely (code: ${e.code}). Retrying in ${r.backoffDelay}ms.`:`${t} could not be established.`)}}function eB(e){let t=[`code: ${e.code}`];return e.reason&&t.push(`reason: ${e.reason}`),e=>{v(`Connection to Liveblocks websocket server closed (${t.join(", ")}). Retrying in ${e.backoffDelay}ms.`)}}var eq=eU(1,"Connection to WebSocket closed permanently. Won't retry.");function e$(e){return!(e instanceof Error)&&"close"===e.type}var ez=e=>t=>t.patch(e),eH=class{#U;#F;events;constructor(e,t=!1,r=!0){let{machine:i,events:n,cleanups:s}=function(e,t){let r=function(){let e=F(),t=null;return{...e,notify:function(r){return null!==t?(t.push(r),!1):e.notify(r)},pause:function(){t=[]},unpause:function(){if(null!==t){for(let r of t)e.notify(r);t=null}},dispose(){e.dispose(),null!==t&&(t.length=0)}}}();r.pause();let i=F();function n(e,t){return()=>{i.notify({message:e,code:t})}}let s=new eA({successCount:0,authValue:null,socket:null,backoffDelay:eC}).addState("@idle.initial").addState("@idle.failed").addState("@idle.zombie").addState("@auth.busy").addState("@auth.backoff").addState("@connecting.busy").addState("@connecting.backoff").addState("@ok.connected").addState("@ok.awaiting-pong");s.addTransitions("*",{RECONNECT:{target:"@auth.backoff",effect:[eL,ej]},DISCONNECT:"@idle.initial"}),s.onEnter("@idle.*",ej).addTransitions("@idle.*",{CONNECT:(e,t)=>null!==t.authValue?"@connecting.busy":"@auth.busy"}),s.addTransitions("@auth.backoff",{NAVIGATOR_ONLINE:{target:"@auth.busy",effect:ez({backoffDelay:eC})}}).addTimedTransition("@auth.backoff",e=>e.backoffDelay,"@auth.busy").onEnterAsync("@auth.busy",()=>P(e.authenticate(),1e4,"Timed out during auth"),e=>({target:"@connecting.busy",effect:ez({authValue:e.data})}),e=>e.reason instanceof eN?{target:"@idle.failed",effect:[eU(2,e.reason.message),n(e.reason.message,-1)]}:{target:"@auth.backoff",effect:[eL,eU(2,`Authentication failed: ${e.reason instanceof Error?e.reason.message:String(e.reason)}`)]});let o=e=>s.send({type:"EXPLICIT_SOCKET_ERROR",event:e}),a=e=>s.send({type:"EXPLICIT_SOCKET_CLOSE",event:e}),l=e=>"pong"===e.data?s.send({type:"PONG"}):r.notify(e);function c(e){e&&(e.removeEventListener("error",o),e.removeEventListener("close",a),e.removeEventListener("message",l),e.close())}s.addTransitions("@connecting.backoff",{NAVIGATOR_ONLINE:{target:"@connecting.busy",effect:ez({backoffDelay:eC})}}).addTimedTransition("@connecting.backoff",e=>e.backoffDelay,"@connecting.busy").onEnterAsync("@connecting.busy",async(r,i)=>{let n=null,s=null;return P(new Promise((i,c)=>{if(null===r.authValue)throw Error("No auth authValue");let u=e.createSocket(r.authValue);function d(e){n=e,u.removeEventListener("message",l),c(e)}s=u;let[h,f]=j();function p(e){let t=k(e.data);t?.type===104&&f()}t.waitForActorId||f(),u.addEventListener("message",l),t.waitForActorId&&u.addEventListener("message",p),u.addEventListener("error",d),u.addEventListener("close",d),u.addEventListener("open",()=>{u.addEventListener("error",o),u.addEventListener("close",a);let e=()=>{u.removeEventListener("error",d),u.removeEventListener("close",d),u.removeEventListener("message",p)};h.then(()=>{i([u,e])})})}),1e4,"Timed out during websocket connection").then(([e,t])=>{if(t(),i.aborted)throw Error("Aborted");if(n)throw n;return e}).catch(e=>{throw c(s),e})},e=>({target:"@ok.connected",effect:ez({socket:e.data,backoffDelay:eC})}),e=>{let t=e.reason;if(t instanceof eN)return{target:"@idle.failed",effect:[eU(2,t.message),n(t.message,-1)]};if(e$(t)){if(4109===t.code)return"@auth.busy";if(ek(t.code))return{target:"@connecting.backoff",effect:[eM,eF(t)]};if(eT(t.code))return{target:"@idle.failed",effect:[eU(2,t.reason),n(t.reason,t.code)]}}return{target:"@auth.backoff",effect:[eL,eF(t)]}});let u={target:"@ok.awaiting-pong",effect:e=>{e.socket?.send("ping")}},d=()=>{let t="undefined"!=typeof document?document:void 0;return t?.visibilityState==="hidden"&&e.canZombie()?"@idle.zombie":u};if(s.addTimedTransition("@ok.connected",3e4,d).addTransitions("@ok.connected",{NAVIGATOR_OFFLINE:d,WINDOW_GOT_FOCUS:u}),s.addTransitions("@idle.zombie",{WINDOW_GOT_FOCUS:"@connecting.backoff"}),s.onEnter("@ok.*",e=>{e.patch({successCount:e.successCount+1});let t=setTimeout(r.unpause,0);return e=>{c(e.socket),e.patch({socket:null}),clearTimeout(t),r.pause()}}).addTransitions("@ok.awaiting-pong",{PONG:"@ok.connected"}).addTimedTransition("@ok.awaiting-pong",2e3,{target:"@connecting.busy",effect:eU(1,"Received no pong from server, assume implicit connection loss.")}).addTransitions("@ok.*",{EXPLICIT_SOCKET_ERROR:(e,t)=>t.socket?.readyState===1?null:{target:"@connecting.backoff",effect:eL},EXPLICIT_SOCKET_CLOSE:e=>{var t;if(eT(e.event.code))return{target:"@idle.failed",effect:[eq,n(e.event.reason,e.event.code)]};if((t=e.event.code)>=4100&&t<4200)if(4109===e.event.code)return"@auth.busy";else return{target:"@auth.backoff",effect:[eL,eB(e.event)]};return ek(e.event.code)?{target:"@connecting.backoff",effect:[eM,eB(e.event)]}:{target:"@connecting.backoff",effect:[eL,eB(e.event)]}}}),"undefined"!=typeof document){let e="undefined"!=typeof document?document:void 0,t="undefined"!=typeof window?window:void 0,r=t??e;s.onEnter("*",i=>{function n(){s.send({type:"NAVIGATOR_OFFLINE"})}function o(){s.send({type:"NAVIGATOR_ONLINE"})}function a(){e?.visibilityState==="visible"&&s.send({type:"WINDOW_GOT_FOCUS"})}return t?.addEventListener("online",o),t?.addEventListener("offline",n),r?.addEventListener("visibilitychange",a),()=>{r?.removeEventListener("visibilitychange",a),t?.removeEventListener("online",o),t?.removeEventListener("offline",n),c(i.socket)}})}let h=[],{statusDidChange:f,didConnect:p,didDisconnect:m,unsubscribe:g}=function(e){let t=F(),r=F(),i=F(),n=null,s=e.events.didEnterState.subscribe(()=>{let s=eO(e);s!==n&&t.notify(s),"connected"===n&&"connected"!==s?i.notify():"connected"!==n&&"connected"===s&&r.notify(),n=s});return{statusDidChange:t.observable,didConnect:r.observable,didDisconnect:i.observable,unsubscribe:s}}(s);return h.push(g),t.enableDebugLogging&&h.push(function(e){let t=new Date().getTime();function r(...i){v(`${((new Date().getTime()-t)/1e3).toFixed(2)} [FSM #${e.id}]`,...i)}let i=[e.events.didReceiveEvent.subscribe(e=>r(`Event ${e.type}`)),e.events.willTransition.subscribe(({from:e,to:t})=>r("Transitioning",e,"→",t)),e.events.didIgnoreEvent.subscribe(e=>r("Ignored event",e.type,e,"(current state won't handle it)"))];return()=>{for(let e of i)e()}}(s)),s.start(),{machine:s,cleanups:h,events:{statusDidChange:f,didConnect:p,didDisconnect:m,onMessage:r.observable,onConnectionError:i.observable}}}(e,{waitForActorId:r,enableDebugLogging:t});this.#U=i,this.events=n,this.#F=s}getStatus(){try{return eO(this.#U)}catch{return"initial"}}get authValue(){return this.#U.context.authValue}connect(){this.#U.send({type:"CONNECT"})}reconnect(){this.#U.send({type:"RECONNECT"})}disconnect(){this.#U.send({type:"DISCONNECT"})}destroy(){let e;for(this.#U.stop();e=this.#F.pop();)e()}send(e){let t=this.#U.context?.socket;null===t?v("Cannot send: not connected yet",e):1!==t.readyState?v("Cannot send: WebSocket no longer open",e):t.send(e)}_privateSendMachineEvent(e){this.#U.send(e)}},eV=(e=>(e.Read="room:read",e.Write="room:write",e.PresenceWrite="room:presence:write",e.CommentsWrite="comments:write",e.CommentsRead="comments:read",e))(eV||{});function eK(e){return e.includes("room:write")}function eW(e){return e.includes("comments:write")||e.includes("room:write")}function eG(e){var t;let r=e.split(".");if(3!==r.length)throw Error("Authentication error: invalid JWT token");let i=k(function(e){try{let t=e.replace(/-/g,"+").replace(/_/g,"/");return decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""))}catch(t){return atob(e)}}(r[1]));if(!(i&&A(t=i)&&("acc"===t.k||"id"===t.k||"sec-legacy"===t.k)))throw Error("Authentication error: expected a valid token but did not get one. Hint: if you are using a callback, ensure the room is passed when creating the token. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientCallback");return{raw:e,parsed:i}}async function eX(e,t,r){let i,n=await e(t,{method:"POST",headers:{"Content-Type":"application/json"},body:er(r)});if(!n.ok){let e=`${(await n.text()).trim()||"reason not provided in auth response"} (${n.status} returned by POST ${t})`;if(401===n.status||403===n.status)throw new eN(`Unauthorized: ${e}`);throw Error(`Failed to authenticate: ${e}`)}try{i=await n.json()}catch(e){throw Error(`Expected a JSON response when doing a POST request on "${t}". ${String(e)}`)}if(!A(i)||"string"!=typeof i.token)throw Error(`Expected a JSON response of the form \`{ token: "..." }\` when doing a POST request on "${t}", but got ${er(i)}`);let{token:s}=i;return{token:s}}var eY=Symbol();F().observable,Date.now();var eJ=Symbol("notification-settings-plain");function eZ(e){let t={[eJ]:{value:e,enumerable:!1}};for(let e of["email","slack","teams","webPush"])t[e]={enumerable:!0,get(){let t=this[eJ][e];return void 0===t?(w(`In order to use the '${e}' channel, please set up your project first. For more information: https://liveblocks.io/docs/errors/enable-a-notification-channel`),null):t}};return void 0!==t?Object.create(null,t):Object.create(null)}var eQ=e2(0),e0=e2(1),e1=eQ+e2(-1);function e2(e){let t=32+(e<0?95+e:e);if(t<32||t>126)throw Error(`Invalid n value: ${e}`);return String.fromCharCode(t)}function e3(e,t){if(void 0!==e&&void 0!==t){var r=e,i=t;if(r<i)return e4(r,i);if(r>i)return e4(i,r);throw Error("Cannot compute value between two equal positions")}if(void 0!==e){var n=e;for(let e=0;e<=n.length-1;e++){let t=n.charCodeAt(e);if(!(t>=126))return n.substring(0,e)+String.fromCharCode(t+1)}return n+e0}if(void 0===t)return e0;{var s=t;let e=s.length-1;for(let t=0;t<=e;t++){let r=s.charCodeAt(t);if(!(r<=32))if(t!==e)return s.substring(0,t+1);else if(33===r)return s.substring(0,t)+e1;else return s.substring(0,t)+String.fromCharCode(r-1)}return e0}}function e4(e,t){let r=0,i=e.length,n=t.length;for(;;){let a=r<i?e.charCodeAt(r):32,l=r<n?t.charCodeAt(r):126;if(a===l){r++;continue}if(l-a!=1){var s,o;return s=e,((o=r)<s.length?s.substring(0,o):s+eQ.repeat(o-s.length))+String.fromCharCode(l+a>>1)}{let t=r+1,i=e.substring(0,t);return i.length<t&&(i+=eQ.repeat(t-i.length)),i+e4(e.substring(t),"")}}}function e5(e){return!function(e){if(""===e)return!1;let t=e.length-1,r=e.charCodeAt(t);if(r<33||r>126)return!1;for(let r=0;r<t;r++){let t=e.charCodeAt(r);if(t<32||t>126)return!1}return!0}(e)?function(e){let t=[];for(let r=0;r<e.length;r++){let i=e.charCodeAt(r);t.push(i<32?32:i>126?126:i)}for(;t.length>0&&32===t[t.length-1];)t.length--;return t.length>0?String.fromCharCode(...t):e0}(e):e}var e6=(e=>(e[e.INIT=0]="INIT",e[e.SET_PARENT_KEY=1]="SET_PARENT_KEY",e[e.CREATE_LIST=2]="CREATE_LIST",e[e.UPDATE_OBJECT=3]="UPDATE_OBJECT",e[e.CREATE_OBJECT=4]="CREATE_OBJECT",e[e.DELETE_CRDT=5]="DELETE_CRDT",e[e.DELETE_OBJECT_KEY=6]="DELETE_OBJECT_KEY",e[e.CREATE_MAP=7]="CREATE_MAP",e[e.CREATE_REGISTER=8]="CREATE_REGISTER",e))(e6||{});function e8(e,t,r=e5(t)){return Object.freeze({type:"HasParent",node:e,key:t,pos:r})}var e7=Object.freeze({type:"NoParent"}),e9=class{#B;#q;#$=e7;_getParentKeyOrThrow(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldKey;default:return ew(this.parent,"Unknown state")}}get _parentPos(){switch(this.parent.type){case"HasParent":return this.parent.pos;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldPos;default:return ew(this.parent,"Unknown state")}}get _pool(){return this.#B}get roomId(){return this.#B?this.#B.roomId:null}get _id(){return this.#q}get parent(){return this.#$}get _parentKey(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":return null;case"Orphaned":return this.parent.oldKey;default:return ew(this.parent,"Unknown state")}}_apply(e,t){if(5===e.type&&"HasParent"===this.parent.type)return this.parent.node._detachChild(this);return{modified:!1}}_setParentLink(e,t){switch(this.parent.type){case"HasParent":if(this.parent.node!==e)throw Error("Cannot set parent: node already has a parent");this.#$=e8(e,t);return;case"Orphaned":case"NoParent":this.#$=e8(e,t);return;default:return ew(this.parent,"Unknown state")}}_attach(e,t){if(this.#q||this.#B)throw Error("Cannot attach node: already attached");t.addNode(e,this),this.#q=e,this.#B=t}_detach(){switch(this.#B&&this.#q&&this.#B.deleteNode(this.#q),this.parent.type){case"HasParent":this.#$=function(e,t=e5(e)){return Object.freeze({type:"Orphaned",oldKey:e,oldPos:t})}(this.parent.key,this.parent.pos);break;case"NoParent":this.#$=e7;break;case"Orphaned":break;default:ew(this.parent,"Unknown state")}this.#B=void 0}#z;#H;#V;invalidate(){(void 0!==this.#z||void 0!==this.#V)&&(this.#z=void 0,this.#V=void 0,"HasParent"===this.parent.type&&this.parent.node.invalidate())}toTreeNode(e){return(void 0===this.#V||this.#H!==e)&&(this.#H=e,this.#V=this._toTreeNode(e)),this.#V}toImmutable(){return void 0===this.#z&&(this.#z=this._toImmutable()),this.#z}},te=(e=>(e[e.OBJECT=0]="OBJECT",e[e.LIST=1]="LIST",e[e.MAP=2]="MAP",e[e.REGISTER=3]="REGISTER",e))(te||{}),tt=class e extends e9{#K;constructor(e){super(),this.#K=e}get data(){return this.#K}static _deserialize([t,r],i,n){let s=new e(r.data);return s._attach(t,n),s}_toOps(e,t,r){if(void 0===this._id)throw Error("Cannot serialize register if parentId or parentKey is undefined");return[{type:8,opId:r?.generateOpId(),id:this._id,parentId:e,parentKey:t,data:this.data}]}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveRegister if parent is missing");return{type:3,parentId:e_(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key,data:this.data}}_attachChild(e){throw Error("Method not implemented.")}_detachChild(e){throw Error("Method not implemented.")}_apply(e,t){return super._apply(e,t)}_toTreeNode(e){return{type:"Json",id:this._id??eo(),key:e,payload:this.#K}}_toImmutable(){return this.#K}clone(){return O(this.data)}};function tr(e,t){let r=e._parentPos,i=t._parentPos;return r===i?0:r<i?-1:1}var ti=class e extends e9{#W;#G;#X;constructor(e){let t;for(let r of(super(),this.#W=[],this.#G=new WeakSet,this.#X=new Map,e)){let e=e3(t),i=t_(r);i._setParentLink(this,e),this.#W.push(i),t=e}}static _deserialize([t],r,i){let n=new e([]);n._attach(t,i);let s=r.get(t);if(void 0===s)return n;for(let[e,t]of s){let s=tm([e,t],r,i);s._setParentLink(n,t.parentKey),n._insertAndSort(s)}return n}_toOps(e,t,r){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let i=[],n={id:this._id,opId:r?.generateOpId(),type:2,parentId:e,parentKey:t};for(let e of(i.push(n),this.#W)){let t=e._getParentKeyOrThrow(),n=tu(e._toOps(this._id,t,r),void 0),s=n[0].opId;void 0!==s&&this.#X.set(t,s),i.push(...n)}return i}_insertAndSort(e){this.#W.push(e),this._sortItems()}_sortItems(){this.#W.sort(tr),this.invalidate()}_indexOfPosition(e){return this.#W.findIndex(t=>t._getParentKeyOrThrow()===e)}_attach(e,t){for(let r of(super._attach(e,t),this.#W))r._attach(t.generateId(),t)}_detach(){for(let e of(super._detach(),this.#W))e._detach()}#Y(e){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:t,parentKey:r}=e,i=tf(e);i._attach(t,this._pool),i._setParentLink(this,r);let n=e.deletedId,s=this._indexOfPosition(r);if(-1!==s){let t=this.#W[s];if(t._id===n)return t._detach(),this.#W[s]=i,{modified:ts(this,[to(s,i)]),reverse:[]};{this.#G.add(t),this.#W[s]=i;let r=[to(s,i)],n=this.#J(e.deletedId);return n&&r.push(n),{modified:ts(this,r),reverse:[]}}}{let t=[],n=this.#J(e.deletedId);return n&&t.push(n),this._insertAndSort(i),t.push(tl(this._indexOfPosition(r),i)),{reverse:[],modified:ts(this,t)}}}#Z(e){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let t=[],r=this.#J(e.deletedId);r&&t.push(r);let i=this.#X.get(e.parentKey);if(void 0!==i)if(i!==e.opId)return 0===t.length?{modified:!1}:{modified:ts(this,t),reverse:[]};else this.#X.delete(e.parentKey);let n=this._indexOfPosition(e.parentKey),s=this.#W.find(t=>t._id===e.id);if(void 0!==s){if(s._parentKey===e.parentKey)return{modified:t.length>0&&ts(this,t),reverse:[]};if(-1!==n){this.#G.add(this.#W[n]);let[e]=this.#W.splice(n,1);t.push(ta(n,e))}let r=this.#W.indexOf(s);s._setParentLink(this,e.parentKey),this._sortItems();let i=this.#W.indexOf(s);return i!==r&&t.push(tc(r,i,s)),{modified:t.length>0&&ts(this,t),reverse:[]}}{let r=this._pool.getNode(e.id);if(r&&this.#G.has(r)){r._setParentLink(this,e.parentKey),this.#G.delete(r),this._insertAndSort(r);let i=this.#W.indexOf(r);return{modified:ts(this,[-1===n?tl(i,r):to(i,r),...t]),reverse:[]}}{-1!==n&&this.#W.splice(n,1);let{newItem:r,newIndex:i}=this.#Q(e,e.parentKey);return{modified:ts(this,[-1===n?tl(i,r):to(i,r),...t]),reverse:[]}}}}#J(e){if(void 0===e||void 0===this._pool)return null;let t=this._pool.getNode(e);if(void 0===t)return null;let r=this._detachChild(t);return!1===r.modified?null:r.modified.updates[0]}#ee(e){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let t=e5(e.parentKey),r=this._indexOfPosition(t);-1!==r&&this.#et(r,t);let{newItem:i,newIndex:n}=this.#Q(e,t);return{modified:ts(this,[tl(n,i)]),reverse:[]}}#er(e){let t=this.#W.find(t=>t._id===e.id),r=e5(e.parentKey),i=this._indexOfPosition(r);if(t)if(t._parentKey===r)return{modified:!1};else{let e=this.#W.indexOf(t);-1!==i&&this.#et(i,r),t._setParentLink(this,r),this._sortItems();let n=this._indexOfPosition(r);return n===e?{modified:!1}:{modified:ts(this,[tc(e,n,t)]),reverse:[]}}{let t=e_(this._pool).getNode(e.id);if(t&&this.#G.has(t))return t._setParentLink(this,r),this.#G.delete(t),this._insertAndSort(t),{modified:ts(this,[tl(this._indexOfPosition(r),t)]),reverse:[]};{-1!==i&&this.#et(i,r);let{newItem:t,newIndex:n}=this.#Q(e,r);return{modified:ts(this,[tl(n,t)]),reverse:[]}}}}#ei(e){let{id:t,parentKey:r}=e,i=tf(e);if(this._pool?.getNode(t)!==void 0)return{modified:!1};i._attach(t,e_(this._pool)),i._setParentLink(this,r);let n=this._indexOfPosition(r),s=r;return -1!==n&&(s=e3(this.#W[n]?._parentPos,this.#W[n+1]?._parentPos),i._setParentLink(this,s)),this._insertAndSort(i),{modified:ts(this,[tl(this._indexOfPosition(s),i)]),reverse:[{type:5,id:t}]}}#en(e){let{id:t,parentKey:r}=e,i=tf(e);if(this._pool?.getNode(t)!==void 0)return{modified:!1};this.#X.set(r,e_(e.opId));let n=this._indexOfPosition(r);if(i._attach(t,e_(this._pool)),i._setParentLink(this,r),-1===n)return this._insertAndSort(i),this.#J(e.deletedId),{reverse:[{type:5,id:t}],modified:ts(this,[tl(this._indexOfPosition(r),i)])};{let t=this.#W[n];t._detach(),this.#W[n]=i;let s=tu(t._toOps(e_(this._id),r,this._pool),e.id),o=[to(n,i)],a=this.#J(e.deletedId);return a&&o.push(a),{modified:ts(this,o),reverse:s}}}_attachChild(e,t){let r;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");return!1!==(r="set"===e.intent?1===t?this.#Y(e):2===t?this.#Z(e):this.#en(e):1===t?this.#ee(e):2===t?this.#er(e):this.#ei(e)).modified&&this.invalidate(),r}_detachChild(e){if(e){let t=e_(e._parentKey),r=e._toOps(e_(this._id),t,this._pool),i=this.#W.indexOf(e);if(-1===i)return{modified:!1};let[n]=this.#W.splice(i,1);return this.invalidate(),e._detach(),{modified:ts(this,[ta(i,n)]),reverse:r}}return{modified:!1}}#es(e,t){if(this.#G.has(t))return this.#G.delete(t),t._setParentLink(this,e),this._insertAndSort(t),{modified:ts(this,[tl(this.#W.indexOf(t),t)]),reverse:[]};if(e===t._parentKey)return{modified:!1};let r=this._indexOfPosition(e);if(-1===r){let r=this.#W.indexOf(t);t._setParentLink(this,e),this._sortItems();let i=this.#W.indexOf(t);return i===r?{modified:!1}:{modified:ts(this,[tc(r,i,t)]),reverse:[]}}{this.#W[r]._setParentLink(this,e3(e,this.#W[r+1]?._parentPos));let i=this.#W.indexOf(t);t._setParentLink(this,e),this._sortItems();let n=this.#W.indexOf(t);return n===i?{modified:!1}:{modified:ts(this,[tc(i,n,t)]),reverse:[]}}}#eo(e,t){let r=e_(t._parentKey);if(this.#G.has(t)){let r=this._indexOfPosition(e);return this.#G.delete(t),-1!==r&&this.#W[r]._setParentLink(this,e3(e,this.#W[r+1]?._parentPos)),t._setParentLink(this,e),this._insertAndSort(t),{modified:!1}}{if(e===r)return{modified:!1};let i=this.#W.indexOf(t),n=this._indexOfPosition(e);-1!==n&&this.#W[n]._setParentLink(this,e3(e,this.#W[n+1]?._parentPos)),t._setParentLink(this,e),this._sortItems();let s=this.#W.indexOf(t);return i===s?{modified:!1}:{modified:ts(this,[tc(i,s,t)]),reverse:[]}}}#ea(e,t){let r=e_(t._parentKey),i=this.#W.indexOf(t),n=this._indexOfPosition(e);-1!==n&&this.#W[n]._setParentLink(this,e3(e,this.#W[n+1]?._parentPos)),t._setParentLink(this,e),this._sortItems();let s=this.#W.indexOf(t);return i===s?{modified:!1}:{modified:ts(this,[tc(i,s,t)]),reverse:[{type:1,id:e_(t._id),parentKey:r}]}}_setChildKey(e,t,r){return 1===r?this.#es(e,t):2===r?this.#eo(e,t):this.#ea(e,t)}_apply(e,t){return super._apply(e,t)}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveList if parent is missing");return{type:1,parentId:e_(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get length(){return this.#W.length}push(e){return this._pool?.assertStorageIsWritable(),this.insert(e,this.length)}insert(e,t){if(this._pool?.assertStorageIsWritable(),t<0||t>this.#W.length)throw Error(`Cannot insert list item at index "${t}". index should be between 0 and ${this.#W.length}`);let r=e3(this.#W[t-1]?this.#W[t-1]._parentPos:void 0,this.#W[t]?this.#W[t]._parentPos:void 0),i=t_(e);if(i._setParentLink(this,r),this._insertAndSort(i),this._pool&&this._id){let e=this._pool.generateId();i._attach(e,this._pool),this._pool.dispatch(i._toOps(this._id,r,this._pool),[{type:5,id:e}],new Map([[this._id,ts(this,[tl(t,i)])]]))}}move(e,t){if(this._pool?.assertStorageIsWritable(),t<0)throw Error("targetIndex cannot be less than 0");if(t>=this.#W.length)throw Error("targetIndex cannot be greater or equal than the list length");if(e<0)throw Error("index cannot be less than 0");if(e>=this.#W.length)throw Error("index cannot be greater or equal than the list length");let r=null,i=null;e<t?(i=t===this.#W.length-1?void 0:this.#W[t+1]._parentPos,r=this.#W[t]._parentPos):(i=this.#W[t]._parentPos,r=0===t?void 0:this.#W[t-1]._parentPos);let n=e3(r,i),s=this.#W[e],o=s._getParentKeyOrThrow();if(s._setParentLink(this,n),this._sortItems(),this._pool&&this._id){let r=new Map([[this._id,ts(this,[tc(e,t,s)])]]);this._pool.dispatch([{type:1,id:e_(s._id),opId:this._pool.generateOpId(),parentKey:n}],[{type:1,id:e_(s._id),parentKey:o}],r)}}delete(e){if(this._pool?.assertStorageIsWritable(),e<0||e>=this.#W.length)throw Error(`Cannot delete list item at index "${e}". index should be between 0 and ${this.#W.length-1}`);let t=this.#W[e];t._detach();let[r]=this.#W.splice(e,1);if(this.invalidate(),this._pool){let i=t._id;if(i){let n=new Map;n.set(e_(this._id),ts(this,[ta(e,r)])),this._pool.dispatch([{id:i,opId:this._pool.generateOpId(),type:5}],t._toOps(e_(this._id),t._getParentKeyOrThrow()),n)}}}clear(){if(this._pool?.assertStorageIsWritable(),this._pool){let e=[],t=[],r=[];for(let i of this.#W){i._detach();let n=i._id;n&&(e.push({type:5,id:n,opId:this._pool.generateOpId()}),t.push(...i._toOps(e_(this._id),i._getParentKeyOrThrow())),r.push(ta(0,i)))}this.#W=[],this.invalidate();let i=new Map;i.set(e_(this._id),ts(this,r)),this._pool.dispatch(e,t,i)}else{for(let e of this.#W)e._detach();this.#W=[],this.invalidate()}}set(e,t){if(this._pool?.assertStorageIsWritable(),e<0||e>=this.#W.length)throw Error(`Cannot set list item at index "${e}". index should be between 0 and ${this.#W.length-1}`);let r=this.#W[e],i=r._getParentKeyOrThrow(),n=r._id;r._detach();let s=t_(t);if(s._setParentLink(this,i),this.#W[e]=s,this.invalidate(),this._pool&&this._id){let t=this._pool.generateId();s._attach(t,this._pool);let o=new Map;o.set(this._id,ts(this,[to(e,s)]));let a=tu(s._toOps(this._id,i,this._pool),n);this.#X.set(i,e_(a[0].opId));let l=tu(r._toOps(this._id,i,void 0),t);this._pool.dispatch(a,l,o)}}toArray(){return this.#W.map(e=>tw(e))}every(e){return this.toArray().every(e)}filter(e){return this.toArray().filter(e)}find(e){return this.toArray().find(e)}findIndex(e){return this.toArray().findIndex(e)}forEach(e){return this.toArray().forEach(e)}get(e){if(!(e<0)&&!(e>=this.#W.length))return tw(this.#W[e])}indexOf(e,t){return this.toArray().indexOf(e,t)}lastIndexOf(e,t){return this.toArray().lastIndexOf(e,t)}map(e){return this.#W.map((t,r)=>e(tw(t),r))}some(e){return this.toArray().some(e)}[Symbol.iterator](){return new tn(this.#W)}#Q(e,t){let r=tf(e);return r._attach(e.id,e_(this._pool)),r._setParentLink(this,t),this._insertAndSort(r),{newItem:r,newIndex:this._indexOfPosition(t)}}#et(e,t){let r=e3(t,this.#W.length>e+1?this.#W[e+1]?._parentPos:void 0);this.#W[e]._setParentLink(this,r)}_toTreeNode(e){return{type:"LiveList",id:this._id??eo(),key:e,payload:this.#W.map((e,t)=>e.toTreeNode(t.toString()))}}toImmutable(){return super.toImmutable()}_toImmutable(){return this.#W.map(e=>e.toImmutable())}clone(){return new e(this.#W.map(e=>e.clone()))}},tn=class{#el;constructor(e){this.#el=e[Symbol.iterator]()}[Symbol.iterator](){return this}next(){let e=this.#el.next();return e.done?{done:!0,value:void 0}:{value:tw(e.value)}}};function ts(e,t){return{node:e,type:"LiveList",updates:t}}function to(e,t){return{index:e,type:"set",item:t instanceof tt?t.data:t}}function ta(e,t){return{type:"delete",index:e,deletedItem:t instanceof tt?t.data:t}}function tl(e,t){return{index:e,type:"insert",item:t instanceof tt?t.data:t}}function tc(e,t,r){return{type:"move",index:t,item:r instanceof tt?r.data:r,previousIndex:e}}function tu(e,t){return e.map((e,r)=>0===r?{...e,intent:"set",deletedId:t}:e)}var td=class e extends e9{#ec;#eu;constructor(e){if(super(),this.#eu=new Map,e){let t=[];for(let[r,i]of e){let e=t_(i);e._setParentLink(this,r),t.push([r,e])}this.#ec=new Map(t)}else this.#ec=new Map}_toOps(e,t,r){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let i=[],n={id:this._id,opId:r?.generateOpId(),type:7,parentId:e,parentKey:t};for(let[e,t]of(i.push(n),this.#ec))i.push(...t._toOps(this._id,e,r));return i}static _deserialize([t,r],i,n){let s=new e;s._attach(t,n);let o=i.get(t);if(void 0===o)return s;for(let[e,t]of o){let r=tm([e,t],i,n);r._setParentLink(s,t.parentKey),s.#ec.set(t.parentKey,r),s.invalidate()}return s}_attach(e,t){for(let[r,i]of(super._attach(e,t),this.#ec))tb(i)&&i._attach(t.generateId(),t)}_attachChild(e,t){let r;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:i,parentKey:n,opId:s}=e,o=tf(e);if(void 0!==this._pool.getNode(i))return{modified:!1};if(2===t){let e=this.#eu.get(n);if(e===s)return this.#eu.delete(n),{modified:!1};if(void 0!==e)return{modified:!1}}else 1===t&&this.#eu.delete(n);let a=this.#ec.get(n);if(a){let e=e_(this._id);r=a._toOps(e,n),a._detach()}else r=[{type:5,id:i}];return o._setParentLink(this,n),o._attach(i,this._pool),this.#ec.set(n,o),this.invalidate(),{modified:{node:this,type:"LiveMap",updates:{[n]:{type:"update"}}},reverse:r}}_detach(){for(let e of(super._detach(),this.#ec.values()))e._detach()}_detachChild(e){let t=e_(this._id),r=e_(e._parentKey),i=e._toOps(t,r,this._pool);for(let[t,r]of this.#ec)r===e&&(this.#ec.delete(t),this.invalidate());return e._detach(),{modified:{node:this,type:"LiveMap",updates:{[r]:{type:"delete"}}},reverse:i}}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveMap if parent is missing");return{type:2,parentId:e_(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get(e){let t=this.#ec.get(e);if(void 0!==t)return tw(t)}set(e,t){this._pool?.assertStorageIsWritable();let r=this.#ec.get(e);r&&r._detach();let i=t_(t);if(i._setParentLink(this,e),this.#ec.set(e,i),this.invalidate(),this._pool&&this._id){let t=this._pool.generateId();i._attach(t,this._pool);let n=new Map;n.set(this._id,{node:this,type:"LiveMap",updates:{[e]:{type:"update"}}});let s=i._toOps(this._id,e,this._pool);this.#eu.set(e,e_(s[0].opId)),this._pool.dispatch(i._toOps(this._id,e,this._pool),r?r._toOps(this._id,e):[{type:5,id:t}],n)}}get size(){return this.#ec.size}has(e){return this.#ec.has(e)}delete(e){this._pool?.assertStorageIsWritable();let t=this.#ec.get(e);if(void 0===t)return!1;if(t._detach(),this.#ec.delete(e),this.invalidate(),this._pool&&t._id){let r=e_(this._id),i=new Map;i.set(r,{node:this,type:"LiveMap",updates:{[e]:{type:"delete"}}}),this._pool.dispatch([{type:5,id:t._id,opId:this._pool.generateOpId()}],t._toOps(r,e),i)}return!0}entries(){let e=this.#ec.entries();return{[Symbol.iterator](){return this},next(){let t=e.next();return t.done?{done:!0,value:void 0}:{value:[t.value[0],tw(t.value[1])]}}}}[Symbol.iterator](){return this.entries()}keys(){return this.#ec.keys()}values(){let e=this.#ec.values();return{[Symbol.iterator](){return this},next(){let t=e.next();return t.done?{done:!0,value:void 0}:{value:tw(t.value)}}}}forEach(e){for(let t of this)e(t[1],t[0],this)}_toTreeNode(e){return{type:"LiveMap",id:this._id??eo(),key:e,payload:Array.from(this.#ec.entries()).map(([e,t])=>t.toTreeNode(e))}}toImmutable(){return super.toImmutable()}_toImmutable(){let e=new Map;for(let[t,r]of this.#ec)e.set(t,r.toImmutable());return B(e)}clone(){return new e(Array.from(this.#ec).map(([e,t])=>[e,t.clone()]))}},th=class e extends e9{#ec;#ed;static #eh(e){let t=new Map,r=null;for(let[n,s]of e){var i;if(0!==s.type||void 0!==(i=s).parentId&&void 0!==i.parentKey){let e=[n,s],r=t.get(s.parentId);void 0!==r?r.push(e):t.set(s.parentId,[e])}else r=[n,s]}if(null===r)throw Error("Root can't be null");return[r,t]}static _fromItems(t,r){let[i,n]=e.#eh(t);return e._deserialize(i,n,r)}constructor(e={}){super(),this.#ed=new Map;let t=C(e);for(let e of Object.keys(t)){let r=t[e];tb(r)&&r._setParentLink(this,e)}this.#ec=new Map(Object.entries(t))}_toOps(e,t,r){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let i=r?.generateOpId(),n=[],s={type:4,id:this._id,opId:i,parentId:e,parentKey:t,data:{}};for(let[e,t]of(n.push(s),this.#ec))tb(t)?n.push(...t._toOps(this._id,e,r)):s.data[e]=t;return n}static _deserialize([t,r],i,n){let s=new e(r.data);return s._attach(t,n),this._deserializeChildren(s,i,n)}static _deserializeChildren(e,t,r){let i=t.get(e_(e._id));if(void 0===i)return e;for(let[n,s]of i){let i=function([e,t],r,i){switch(t.type){case 0:return th._deserialize([e,t],r,i);case 1:return ti._deserialize([e,t],r,i);case 2:return td._deserialize([e,t],r,i);case 3:return t.data;default:throw Error("Unexpected CRDT type")}}([n,s],t,r);tg(i)&&i._setParentLink(e,s.parentKey),e.#ec.set(s.parentKey,i),e.invalidate()}return e}_attach(e,t){for(let[r,i]of(super._attach(e,t),this.#ec))tb(i)&&i._attach(t.generateId(),t)}_attachChild(e,t){let r;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:i,opId:n,parentKey:s}=e,o=tp(e);if(void 0!==this._pool.getNode(i))return this.#ed.get(s)===n&&this.#ed.delete(s),{modified:!1};if(0===t)this.#ed.set(s,e_(n));else if(void 0===this.#ed.get(s));else if(this.#ed.get(s)===n)return this.#ed.delete(s),{modified:!1};else return{modified:!1};let a=e_(this._id),l=this.#ec.get(s);return tb(l)?(r=l._toOps(a,s),l._detach()):r=void 0===l?[{type:6,id:a,key:s}]:[{type:3,id:a,data:{[s]:l}}],this.#ec.set(s,o),this.invalidate(),tg(o)&&(o._setParentLink(this,s),o._attach(i,this._pool)),{reverse:r,modified:{node:this,type:"LiveObject",updates:{[s]:{type:"update"}}}}}_detachChild(e){if(e){let t=e_(this._id),r=e_(e._parentKey),i=e._toOps(t,r,this._pool);for(let[t,r]of this.#ec)r===e&&(this.#ec.delete(t),this.invalidate());return e._detach(),{modified:{node:this,type:"LiveObject",updates:{[r]:{type:"delete"}}},reverse:i}}return{modified:!1}}_detach(){for(let e of(super._detach(),this.#ec.values()))tb(e)&&e._detach()}_apply(e,t){return 3===e.type?this.#ef(e,t):6===e.type?this.#ep(e,t):super._apply(e,t)}_serialize(){let e={};for(let[t,r]of this.#ec)tb(r)||(e[t]=r);return"HasParent"===this.parent.type&&this.parent.node._id?{type:0,parentId:this.parent.node._id,parentKey:this.parent.key,data:e}:{type:0,data:e}}#ef(e,t){let r=!1,i=e_(this._id),n=[],s={type:3,id:i,data:{}};for(let t in e.data){let e=this.#ec.get(t);tb(e)?(n.push(...e._toOps(i,t)),e._detach()):void 0!==e?s.data[t]=e:void 0===e&&n.push({type:6,id:i,key:t})}let o={};for(let i in e.data){let n=e.data[i];if(void 0===n)continue;if(t)this.#ed.set(i,e_(e.opId));else if(void 0===this.#ed.get(i))r=!0;else{if(this.#ed.get(i)!==e.opId)continue;this.#ed.delete(i);continue}let s=this.#ec.get(i);tb(s)&&s._detach(),r=!0,o[i]={type:"update"},this.#ec.set(i,n),this.invalidate()}return 0!==Object.keys(s.data).length&&n.unshift(s),r?{modified:{node:this,type:"LiveObject",updates:o},reverse:n}:{modified:!1}}#ep(e,t){let r=e.key;if(!1===this.#ec.has(r)||!t&&void 0!==this.#ed.get(r))return{modified:!1};let i=this.#ec.get(r),n=e_(this._id),s=[];return tb(i)?(s=i._toOps(n,e.key),i._detach()):void 0!==i&&(s=[{type:3,id:n,data:{[r]:i}}]),this.#ec.delete(r),this.invalidate(),{modified:{node:this,type:"LiveObject",updates:{[e.key]:{type:"delete"}}},reverse:s}}toObject(){return Object.fromEntries(this.#ec)}set(e,t){this._pool?.assertStorageIsWritable(),this.update({[e]:t})}get(e){return this.#ec.get(e)}delete(e){let t;this._pool?.assertStorageIsWritable();let r=this.#ec.get(e);if(void 0===r)return;if(void 0===this._pool||void 0===this._id){tb(r)&&r._detach(),this.#ec.delete(e),this.invalidate();return}tb(r)?(r._detach(),t=r._toOps(this._id,e)):t=[{type:3,data:{[e]:r},id:this._id}],this.#ec.delete(e),this.invalidate();let i=new Map;i.set(this._id,{node:this,type:"LiveObject",updates:{[e]:{type:"delete"}}}),this._pool.dispatch([{type:6,key:e,id:this._id,opId:this._pool.generateOpId()}],t,i)}update(e){if(this._pool?.assertStorageIsWritable(),void 0===this._pool||void 0===this._id){for(let t in e){let r=e[t];if(void 0===r)continue;let i=this.#ec.get(t);tb(i)&&i._detach(),tb(r)&&r._setParentLink(this,t),this.#ec.set(t,r),this.invalidate()}return}let t=[],r=[],i=this._pool.generateOpId(),n={},s={id:this._id,type:3,data:{}},o={};for(let a in e){let l=e[a];if(void 0===l)continue;let c=this.#ec.get(a);if(tb(c)?(r.push(...c._toOps(this._id,a)),c._detach()):void 0===c?r.push({type:6,id:this._id,key:a}):s.data[a]=c,tb(l)){l._setParentLink(this,a),l._attach(this._pool.generateId(),this._pool);let e=l._toOps(this._id,a,this._pool),r=e.find(e=>e.parentId===this._id);r&&this.#ed.set(a,e_(r.opId)),t.push(...e)}else n[a]=l,this.#ed.set(a,i);this.#ec.set(a,l),this.invalidate(),o[a]={type:"update"}}0!==Object.keys(s.data).length&&r.unshift(s),0!==Object.keys(n).length&&t.unshift({opId:i,id:this._id,type:3,data:n});let a=new Map;a.set(this._id,{node:this,type:"LiveObject",updates:o}),this._pool.dispatch(t,r,a)}toImmutable(){return super.toImmutable()}toTreeNode(e){return super.toTreeNode(e)}_toTreeNode(e){let t=this._id??eo();return{type:"LiveObject",id:t,key:e,payload:Array.from(this.#ec.entries()).map(([e,r])=>tb(r)?r.toTreeNode(e):{type:"Json",id:`${t}:${e}`,key:e,payload:r})}}_toImmutable(){let e={};for(let[t,r]of this.#ec)e[t]=tg(r)?r.toImmutable():r;return e}clone(){return new e(Object.fromEntries(Array.from(this.#ec).map(([e,t])=>[e,tg(t)?t.clone():O(t)])))}};function tf(e){return t_(tp(e))}function tp(e){switch(e.type){case 8:return e.data;case 4:return new th(e.data);case 7:return new td;case 2:return new ti([]);default:return ew(e,"Unknown creation Op")}}function tm([e,t],r,i){switch(t.type){case 0:return th._deserialize([e,t],r,i);case 1:return ti._deserialize([e,t],r,i);case 2:return td._deserialize([e,t],r,i);case 3:return tt._deserialize([e,t],r,i);default:throw Error("Unexpected CRDT type")}}function tg(e){return ty(e)||e instanceof td||tv(e)}function tb(e){return tg(e)||e instanceof tt}function ty(e){return e instanceof ti}function tv(e){return e instanceof th}function tw(e){return e instanceof tt?e.data:e instanceof ti||e instanceof td||e instanceof th?e:ew(e,"Unknown AbstractCrdt")}function t_(e){return e instanceof th||e instanceof td||e instanceof ti?e:new tt(e)}function tE(e,t){if(void 0===e)return t;if("LiveObject"===e.type&&"LiveObject"===t.type||"LiveMap"===e.type&&"LiveMap"===t.type){let r=e.updates;for(let[e,i]of T(t.updates))r[e]=i;return{...t,updates:r}}if("LiveList"===e.type&&"LiveList"===t.type){let r=e.updates;return{...t,updates:r.concat(t.updates)}}return t}var tS=class{#K;#em;#eg;#d;constructor(){this.#K={},this.#em=0,this.#eg=1,this.#d=0}get length(){return this.#d}*[Symbol.iterator](){let e=this.#d,t=this.#em;for(let r=0;r<e;r++)yield this.#K[t+r]}push(e){let t=Array.isArray(e)?e:[e];for(let e of(this.#eg>Number.MAX_SAFE_INTEGER-t.length-1&&I("Deque full"),t))this.#K[this.#eg++-1]=e;this.#d+=t.length}pop(){if(this.#d<1)return;this.#eg--;let e=this.#K[this.#eg-1];return delete this.#K[this.#eg-1],this.#d--,e}pushLeft(e){let t=Array.isArray(e)?e:[e];this.#em<Number.MIN_SAFE_INTEGER+t.length&&I("Deque full");for(let e=t.length-1;e>=0;e--)this.#K[--this.#em]=t[e];this.#d+=t.length}popLeft(){if(this.#d<1)return;let e=this.#K[this.#em];return delete this.#K[this.#em],this.#em++,this.#d--,e}};function tA(e){return Array.isArray(e)}var tx=(e=>(e[e.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",e[e.BROADCAST_EVENT=103]="BROADCAST_EVENT",e[e.FETCH_STORAGE=200]="FETCH_STORAGE",e[e.UPDATE_STORAGE=201]="UPDATE_STORAGE",e[e.FETCH_YDOC=300]="FETCH_YDOC",e[e.UPDATE_YDOC=301]="UPDATE_YDOC",e))(tx||{}),tI=class{#eb;#ey;signal;constructor(){this.#eb=new Q({connections:new Map,presences:new Map}),this.signal=Z.from(this.#eb,e=>R(Array.from(this.#eb.get().presences.keys()).map(e=>this.getUser(Number(e))))),this.#ey=new Map}get(){return this.signal.get()}connectionIds(){return this.#eb.get().connections.keys()}clearOthers(){this.#eb.mutate(e=>{e.connections.clear(),e.presences.clear(),this.#ey.clear()})}#ev(e){let t=this.#eb.get(),r=t.connections.get(e),i=t.presences.get(e);if(void 0!==r&&void 0!==i){let{connectionId:e,id:t,info:n}=r,s=eK(r.scopes);return B(C({connectionId:e,id:t,info:n,canWrite:s,canComment:eW(r.scopes),isReadOnly:!s,presence:i}))}}getUser(e){let t=this.#ey.get(e);if(t)return t;let r=this.#ev(e);if(r)return this.#ey.set(e,r),r}#ew(e){this.#ey.delete(e)}setConnection(e,t,r,i){this.#eb.mutate(n=>(n.connections.set(e,B({connectionId:e,id:t,info:r,scopes:i})),!!n.presences.has(e)&&this.#ew(e)))}removeConnection(e){this.#eb.mutate(t=>{t.connections.delete(e),t.presences.delete(e),this.#ew(e)})}setOther(e,t){this.#eb.mutate(r=>(r.presences.set(e,B(C(t))),!!r.connections.has(e)&&this.#ew(e)))}patchOther(e,t){this.#eb.mutate(r=>{let i=r.presences.get(e);if(void 0===i)return!1;let n=W(i,t);return i!==n&&(r.presences.set(e,B(n)),this.#ew(e))})}},tT=class e extends Error{context;constructor(e,t,r){super(e,{cause:r}),this.context=t,this.name="LiveblocksError"}get roomId(){return this.context.roomId}get code(){return this.context.code}static from(t,r){return new e(function(e){switch(e.type){case"ROOM_CONNECTION_ERROR":switch(e.code){case 4001:return"Not allowed to connect to the room";case 4005:return"Room is already full";case 4006:return"Kicked out of the room, because the room ID changed";default:return"Could not connect to the room"}case"CREATE_THREAD_ERROR":return"Could not create new thread";case"DELETE_THREAD_ERROR":return"Could not delete thread";case"EDIT_THREAD_METADATA_ERROR":return"Could not edit thread metadata";case"MARK_THREAD_AS_RESOLVED_ERROR":return"Could not mark thread as resolved";case"MARK_THREAD_AS_UNRESOLVED_ERROR":return"Could not mark thread as unresolved";case"SUBSCRIBE_TO_THREAD_ERROR":return"Could not subscribe to thread";case"UNSUBSCRIBE_FROM_THREAD_ERROR":return"Could not unsubscribe from thread";case"CREATE_COMMENT_ERROR":return"Could not create new comment";case"EDIT_COMMENT_ERROR":return"Could not edit comment";case"DELETE_COMMENT_ERROR":return"Could not delete comment";case"ADD_REACTION_ERROR":return"Could not add reaction";case"REMOVE_REACTION_ERROR":return"Could not remove reaction";case"MARK_INBOX_NOTIFICATION_AS_READ_ERROR":return"Could not mark inbox notification as read";case"DELETE_INBOX_NOTIFICATION_ERROR":return"Could not delete inbox notification";case"MARK_ALL_INBOX_NOTIFICATIONS_AS_READ_ERROR":return"Could not mark all inbox notifications as read";case"DELETE_ALL_INBOX_NOTIFICATIONS_ERROR":return"Could not delete all inbox notifications";case"UPDATE_NOTIFICATION_SETTINGS_ERROR":case"UPDATE_USER_NOTIFICATION_SETTINGS_ERROR":return"Could not update notification settings";case"UPDATE_ROOM_SUBSCRIPTION_SETTINGS_ERROR":return"Could not update room subscription settings";default:return ew(e,"Unhandled case")}}(t),t,r)}};function tk(e,t){return{type:"User",id:`${t.connectionId}`,key:e,payload:{connectionId:t.connectionId,id:t.id,info:t.info,presence:t.presence,isReadOnly:!t.canWrite}}}function tO(e,t,r,i,n){if("number"!=typeof t||t<r||void 0!==i&&t>i)throw Error(void 0!==i?`${e} should be between ${n??r} and ${i}.`:`${e} should be at least ${n??r}.`);return t}function tR(e,...t){return()=>{}}var tC={paragraph:function(e){return"type"in e&&"paragraph"===e.type},text:function(e){return!("type"in e)&&"text"in e&&"string"==typeof e.text},link:function(e){return"type"in e&&"link"===e.type},mention:function(e){return"type"in e&&"mention"===e.type}},tP={paragraph:"block",text:"inline",link:"inline",mention:"inline"};function tN(e){let t=new Set;return!function(e,t,r){if(!e||!e?.content)return;let i="string"==typeof t?t:void 0,n=i?tP[i]:"all",s=i?tC[i]:()=>!0,o="function"==typeof t?t:r;for(let t of e.content)if(("all"===n||"block"===n)&&s(t)&&o?.(t),"all"===n||"inline"===n)for(let e of t.children)s(e)&&o?.(e)}(e,"mention",e=>t.add(e.id)),Array.from(t)}var tD={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},tL=RegExp(Object.keys(tD).map(e=>`\\${e}`).join("|"),"g"),tM=class{#e_;#eE;constructor(e,t){this.#e_=e,this.#eE=t}toString(){return this.#e_.reduce((e,t,r)=>e+function(e){if(e instanceof tM)return e.toString();if(Array.isArray(e))return(e.length<=0?new tM([""],[]):new tM(["",...Array(e.length-1).fill(""),""],e)).toString();return String(e).replace(tL,e=>tD[e])}(e_(this.#eE[r-1]))+t)}};function tj(e,...t){return new tM(e,t)}var tU={_:"\\_","*":"\\*","#":"\\#","`":"\\`","~":"\\~","!":"\\!","|":"\\|","(":"\\(",")":"\\)","{":"\\{","}":"\\}","[":"\\[","]":"\\]"},tF=RegExp(Object.keys(tU).map(e=>`\\${e}`).join("|"),"g"),tB=class{#e_;#eE;constructor(e,t){this.#e_=e,this.#eE=t}toString(){return this.#e_.reduce((e,t,r)=>e+function(e){if(e instanceof tB)return e.toString();if(Array.isArray(e))return(e.length<=0?new tB([""],[]):new tB(["",...Array(e.length-1).fill(""),""],e)).toString();return String(e).replace(tF,e=>tU[e])}(e_(this.#eE[r-1]))+t)}};function tq(e,...t){return new tB(e,t)}function t$(e){let t={};for(let r in e){let i=e[r];void 0!==i&&(t[r]=tz(i))}return t}function tz(e){if(e instanceof th)return t$(e.toObject());if(e instanceof ti)return e.toArray().map(tz);if(e instanceof td){let t={};for(let[r,i]of e.entries())t[r]=tz(i);return t}return e instanceof tt?e.data:Array.isArray(e)?e.map(tz):A(e)?t$(e):e}function tH(e){if(Array.isArray(e))return new ti(e.map(tH));if(!A(e))return e;{let t={};for(let r in e){let i=e[r];void 0!==i&&(t[r]=tH(i))}return new th(t)}}var tV=[1e3,2e3,4e3,8e3,1e4];function tK(e,t,r){let i=performance.now(),n="undefined"!=typeof document?document:void 0,s="undefined"!=typeof window?window:void 0,o=r?.maxStaleTimeMs??Number.POSITIVE_INFINITY,a={inForeground:n?.visibilityState!=="hidden",lastSuccessfulPollAt:i,count:0,backoff:0};function l(){return a.count>0&&a.inForeground}let c=new eA({}).addState("@idle").addState("@enabled").addState("@polling");function u(){l()?c.send({type:"START"}):c.send({type:"STOP"})}function d(){performance.now()-a.lastSuccessfulPollAt>o&&c.send({type:"POLL"})}function h(e){a.inForeground=e,u(),d()}function f(){h(n?.visibilityState!=="hidden")}return c.addTransitions("@idle",{START:"@enabled"}),c.addTransitions("@enabled",{STOP:"@idle",POLL:"@polling"}),c.addTimedTransition("@enabled",()=>Math.max(0,a.lastSuccessfulPollAt+t-performance.now())+a.backoff,"@polling"),c.onEnterAsync("@polling",async(t,r)=>{await e(r),r.aborted||(a.lastSuccessfulPollAt=performance.now())},()=>({target:l()?"@enabled":"@idle",effect:()=>{a.backoff=0}}),()=>({target:l()?"@enabled":"@idle",effect:()=>{a.backoff=tV.find(e=>e>a.backoff)??tV[tV.length-1]}}),3e4),n?.addEventListener("visibilitychange",f),s?.addEventListener("online",f),s?.addEventListener("focus",d),c.start(),{inc:function(){a.count++,u()},dec:function(){a.count--,a.count<0&&(a.count=0),u()},pollNowIfStale:d,markAsStale:function(){a.lastSuccessfulPollAt=performance.now()-o-1},setInForeground:h}}function tW(e,t){if(Object.is(e,t))return!0;let r=Array.isArray(e),i=Array.isArray(t);if(r||i){if(!r||!i)return!1;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(!Object.is(e[r],t[r]))return!1;return!0}if(!A(e)||!A(t))return!1;let n=Object.keys(e);return n.length===Object.keys(t).length&&n.every(r=>Object.prototype.hasOwnProperty.call(t,r)&&Object.is(e[r],t[r]))}function tG(e,t){if(!A(e)||!A(t))return tW(e,t);let r=Object.keys(e);return r.length===Object.keys(t).length&&r.every(r=>Object.prototype.hasOwnProperty.call(t,r)&&tW(e[r],t[r]))}var tX=class e{#K;#eS;constructor(e,t){this.#eS=t,this.#K=e}static from(t,r){let i=new e([],r);for(let e of t)i.add(e);return i}static fromAlreadySorted(t,r){return new e(t,r)}clone(){return new e(this.#K.slice(),this.#eS)}add(e){let t=function(e,t,r){let i=0,n=e.length;for(;i<n;){let s=i+(n-i>>1);r(t,e[s])?n=s:i=s+1}return i}(this.#K,e,this.#eS);this.#K.splice(t,0,e)}remove(e){let t=this.#K.indexOf(e);return t>=0&&(this.#K.splice(t,1),!0)}get length(){return this.#K.length}*filter(e){for(let t of this.#K)e(t)&&(yield t)}[Symbol.iterator](){return this.#K[Symbol.iterator]()}};function tY(e,t){return"string"==typeof e?`${e}:${t}`:`${e.kind}:${e.subjectId}`}var tJ=(e=>(e.Lexical="lexical",e.TipTap="tiptap",e.BlockNote="blocknote",e))(tJ||{});!function(e,t,r){let i=Symbol.for(e),n=`${t||"dev"} (esm)`;a[i]?a[i]===n||l(`Multiple copies of Liveblocks are being loaded in your project. This will cause issues! See https://liveblocks.io/docs/errors/dupes 

Conflicts:
- ${e} ${a[i]} (already loaded)
- ${e} ${n} (trying to load this now)`):a[i]=n,t&&o&&t!==o&&l(`Cross-linked versions of Liveblocks found, which will cause issues! See https://liveblocks.io/docs/errors/cross-linked 

Conflicts:
- ${s} is at ${o}
- ${e} is at ${t}

Always upgrade all Liveblocks packages to the same version number.`)}(s,o,"esm");var tZ=r(99730),tQ=(0,i.createContext)(null);function t0(){return(0,i.useContext)(tQ)}function t1(){return null!==t0()}function t2(e,t,r,n,s){let o,a=(0,i.useRef)(null);null===a.current?a.current=o={hasValue:!1,value:null}:o=a.current;let[l,c]=(0,i.useMemo)(()=>{let e,i,a=!1,l=t=>{var r;if(!a){a=!0,e=t;let r=n(t);if(void 0!==s&&o.hasValue){let e=o.value;if(s(e,r))return i=e,e}return i=r,r}let l=i;if((r=e)===t&&(0!==r||1/r==1/t)||r!=r&&t!=t)return l;let c=n(t);return void 0!==s&&s(l,c)?(e=t,l):(e=t,i=c,c)},c=void 0===r?null:r;return[()=>l(t()),null===c?void 0:()=>l(c())]},[t,r,n,s]),u=(0,i.useSyncExternalStore)(e,l,c);return(0,i.useEffect)(()=>{o.hasValue=!0,o.value=u},[u]),(0,i.useDebugValue)(u),u}var t3=e=>e;function t4(e,t,r){return t2(e.subscribe,e.get,e.get,t??t3,r)}var t5={SMOOTH_DELAY:1e3,NOTIFICATIONS_POLL_INTERVAL:6e4,NOTIFICATIONS_MAX_STALE_TIME:5e3,ROOM_THREADS_POLL_INTERVAL:3e5,ROOM_THREADS_MAX_STALE_TIME:5e3,USER_THREADS_POLL_INTERVAL:6e4,USER_THREADS_MAX_STALE_TIME:3e4,HISTORY_VERSIONS_POLL_INTERVAL:6e4,HISTORY_VERSIONS_MAX_STALE_TIME:5e3,ROOM_SUBSCRIPTION_SETTINGS_POLL_INTERVAL:6e4,ROOM_SUBSCRIPTION_SETTINGS_MAX_STALE_TIME:5e3,USER_NOTIFICATION_SETTINGS_INTERVAL:3e5,USER_NOTIFICATION_SETTINGS_MAX_STALE_TIME:6e4},t6=Object.freeze({isLoading:!0}),t8=e=>Object.freeze({isLoading:!1,error:e});function t7(e,t){return 1==arguments.length?Object.freeze({isLoading:!1,data:e}):Object.freeze({isLoading:!1,[e]:t})}function t9(){if("undefined"==typeof window)throw Error("You cannot use the Suspense version of Liveblocks hooks server side. Make sure to only call them client side by using a ClientSideSuspense wrapper.\nFor tips, see https://liveblocks.io/docs/api-reference/liveblocks-react#ClientSideSuspense")}function re(e){let t=(0,i.useRef)(e);return(0,i.useEffect)(()=>{t.current=e},[e]),t}var rt=e=>e;function rr(e){return(0,i.useReducer)(rt,e)[0]}function ri(e){let t=rr(e);if("function"!=typeof t)return t;{let t=re(e);return(0,i.useCallback)((...e)=>t.current(...e),[t])}}var rn=e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e};function rs(e){let t=new Set;t.add("constructor");let r=e.constructor.prototype;do for(let i of Reflect.ownKeys(r)){if(t.has(i))continue;let n=Reflect.getOwnPropertyDescriptor(r,i);"function"==typeof n?.value&&(t.add(i),e[i]=e[i].bind(e))}while((r=Reflect.getPrototypeOf(r))&&r!==Object.prototype)}var ro=class e{#eA;#ex;#eI;signal;constructor(){this.#ex=tX.from([],(e,t)=>{let r=e.createdAt,i=t.createdAt;return r<i||r===i&&e.id<t.id}),this.#eI=tX.from([],(e,t)=>{let r=t.updatedAt,i=e.updatedAt;return r<i||r===i&&t.id<e.id}),this.#eA=new Map,this.signal=new Q(this)}clone(){let t=new e;return t.#eA=new Map(this.#eA),t.#ex=this.#ex.clone(),t.#eI=this.#eI.clone(),t}get(e){let t=this.getEvenIfDeleted(e);return t?.deletedAt?void 0:t}getEvenIfDeleted(e){return this.#eA.get(e)}upsert(e){this.signal.mutate(()=>{var t;let r=(e=(t=e).deletedAt&&t.comments.length>0?{...t,comments:[]}:!t.comments.some(e=>!e.deletedAt)?{...t,deletedAt:new Date,comments:[]}:t).id,i=this.#eA.get(r);if(i){if(i.deletedAt)return!1;this.#ex.remove(i),this.#eI.remove(i)}return e.deletedAt||(this.#ex.add(e),this.#eI.add(e)),this.#eA.set(r,e),!0})}upsertIfNewer(e){let t=this.get(e.id);(!t||e.updatedAt>=t.updatedAt)&&this.upsert(e)}applyDelta(e,t){V(()=>{for(let t of e)this.upsertIfNewer(t);for(let{id:e,deletedAt:r}of t)this.getEvenIfDeleted(e)&&this.delete(e,r)})}delete(e,t){let r=this.#eA.get(e);r&&!r.deletedAt&&this.upsert({...r,deletedAt:t,updatedAt:t})}findMany(e,t,r){let i="desc"===r?this.#eI:this.#ex,n=[];return void 0!==e&&n.push(t=>t.roomId===e),void 0!==t&&n.push(e=>{var r,i;return r=e,(void 0===(i=t).resolved||r.resolved===i.resolved)&&function(e,t){let r=e.metadata;return void 0===t.metadata||Object.entries(t.metadata).every(([e,t])=>{var i,n;return void 0===t||(i=r[e],null===(n=t)?void 0===i:x(n)?"string"==typeof i&&i.startsWith(n.startsWith):i===n)})}(e,t)}),Array.from(i.filter(e=>n.every(t=>t(e))))}};function ra(e,t){return stableStringify([e,t??{}])}function rl(e){return stableStringify(e??{})}function rc(e){return"status"in e||(e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t})),e}var ru=Promise.resolve(),rd=class{#eT;signal;#ek;#eO;constructor(e){this.#eT=new X(t6),this.#ek=e,this.#eO=null,this.signal=this.#eT.asReadonly(),rs(this)}get(){return this.#eT.get()}#eR(e){let t=this.#eT.get();void 0!==t.data&&this.#eT.set(t7({...t.data,...e}))}async #eC(){let e=this.#eT.get();if(e.data?.cursor&&!e.data.isFetchingMore){this.#eR({isFetchingMore:!0});try{let t=await this.#ek(e.data.cursor);this.#eR({cursor:t,hasFetchedAll:null===t,fetchMoreError:void 0,isFetchingMore:!1})}catch(e){this.#eR({isFetchingMore:!1,fetchMoreError:e})}}}fetchMore(){let e=this.#eT.get();return e.data?.cursor?(this.#eO||(this.#eO=this.#eC().finally(()=>{this.#eO=null})),this.#eO):ru}#eP=null;waitUntilLoaded(){if(this.#eP)return this.#eP;let e=rc(M(()=>this.#ek(void 0),5,[5e3,5e3,1e4,15e3]));return e.then(e=>{this.#eT.set(t7({cursor:e,hasFetchedAll:null===e,isFetchingMore:!1,fetchMoreError:void 0,fetchMore:this.fetchMore}))},e=>{this.#eT.set(t8(e)),setTimeout(()=>{this.#eP=null,this.#eT.set(t6)},5e3)}),this.#eP=e,this.#eP}},rh=class{#eT;signal;#ek;constructor(e){this.#eT=new X(t6),this.signal=this.#eT.asReadonly(),this.#ek=e,rs(this)}get(){return this.#eT.get()}#eP=null;waitUntilLoaded(){if(this.#eP)return this.#eP;let e=rc(M(()=>this.#ek(),5,[5e3,5e3,1e4,15e3]));return e.then(()=>{this.#eT.set(t7(void 0))},e=>{this.#eT.set(t8(e)),setTimeout(()=>{this.#eP=null,this.#eT.set(t6)},5e3)}),this.#eP=e,e}},rf=class{#eN;threads;notifications;subscriptions;roomSubscriptionSettings;historyVersions;permissionHints;notificationSettings;optimisticUpdates;outputs;#eD=null;#eL;#eM=new Map;#ej=null;#eU=new Map;#eF;constructor(e){this.#eN=e[eY].as(),this.optimisticUpdates=function(e){let t=new X([]),r=e[eY].createSyncSource();return t.subscribe(()=>r.setSyncStatus(t.get().length>0?"synchronizing":"synchronized")),{signal:t.asReadonly(),add:function(e){let r=eo(),i={...e,id:r};return t.set(e=>[...e,i]),r},remove:function(e){t.set(t=>t.filter(t=>t.id!==e))}}}(this.#eN),this.permissionHints=function(){let e=new Q(new el(()=>new Set));return{signal:e.asReadonly(),update:function(t){e.mutate(e=>{for(let[r,i]of Object.entries(t)){let t=e.getOrCreate(r);for(let e of i)t.add(e)}})}}}(),this.#eL=new rd(async e=>{let t=await this.#eN.getInboxNotifications({cursor:e});return this.updateThreadifications(t.threads,t.inboxNotifications,t.subscriptions),null===this.#eD&&(this.#eD=t.requestedAt),t.nextCursor});let t=async()=>{let e=await this.#eN.getNotificationSettings();this.notificationSettings.update(e)};this.notificationSettings=function(e){let t=new X(eZ({}));return{signal:Z.from(t,e,(e,t)=>(function(e,t){let r=e;for(let e of t)"update-notification-settings"===e.type&&(r=function(e,t){let r=eZ({...e[eJ]});for(let e of Object.keys(t)){let i=t[e];if(void 0!==i){let t=Object.fromEntries(T(i).filter(([,e])=>void 0!==e));r[eJ][e]={...r[eJ][e],...t}}}return r}(r,e.settings));return r})(e,t)),update:function(e){t.set(e)}}}(this.optimisticUpdates.signal),this.#eF=new rh(t),this.threads=new ro,this.subscriptions=function(e,t){let r=new Q(new Map);return{signal:Z.from(r,e,(e,r)=>(function(e,t,r){let i=Object.fromEntries(e);for(let e of r)if("update-room-subscription-settings"===e.type){if(!e.settings.threads)continue;for(let r of t.findMany(e.roomId,void 0,"desc")){let t=tY("thread",r.id);switch(e.settings.threads){case"all":i[t]={kind:"thread",subjectId:r.id,createdAt:new Date};break;case"none":delete i[t];break;case"replies_and_mentions":(function(e,t){let r=!1;for(let i of e.comments)if(!i.deletedAt&&(i.userId===t||tN(i.body).includes(t))){r=!0;break}return r})(r,e.userId)&&!i[t]&&(i[t]={kind:"thread",subjectId:r.id,createdAt:new Date});break;default:ew(e.settings.threads,"Unexpected thread subscription settings.")}}}return i})(e,t,r)),applyDelta:function(e,t){r.mutate(r=>{let i=!1;for(let t of e)r.set(tY(t),t),i=!0;for(let e of t)r.delete(tY(e)),i=!0;return i})},create:function(e){r.mutate(t=>{t.set(tY(e),e)})},delete:function(e){r.mutate(t=>{t.delete(e)})}}}(this.optimisticUpdates.signal,this.threads),this.notifications=function(){let e=new Q(new Map);return{signal:e.asReadonly(),markAllRead:function(t){e.mutate(e=>{for(let r of e.values())r.readAt=t})},markRead:function(t,r){e.mutate(e=>{let i=e.get(t);return!!i&&(e.set(t,{...i,readAt:r}),!0)})},delete:function(t){e.mutate(e=>e.delete(t))},applyDelta:function(t,r){e.mutate(e=>{let i=!1;for(let r of t){var n,s;let t=e.get(r.id);(!t||1!==(n=t,s=r,n.notifiedAt>s.notifiedAt?1:n.notifiedAt<s.notifiedAt?-1:n.readAt&&s.readAt?n.readAt>s.readAt?1:n.readAt<s.readAt?-1:0:n.readAt||s.readAt?n.readAt?1:-1:0))&&(e.set(r.id,r),i=!0)}for(let t of r)e.delete(t.id),i=!0;return i})},clear:function(){e.mutate(e=>e.clear())},updateAssociatedNotification:function(t){e.mutate(e=>{let r=function(e,t){for(let r of e)if(t(r))return r}(e.values(),e=>"thread"===e.kind&&e.threadId===t.threadId);return!!r&&(e.set(r.id,{...r,notifiedAt:t.createdAt,readAt:t.createdAt}),!0)})},upsert:function(t){e.mutate(e=>{e.set(t.id,t)})}}}(),this.roomSubscriptionSettings=function(e){let t=new Q(new Map);return{signal:Z.from(t,e,(e,t)=>(function(e,t){let r=Object.fromEntries(e);for(let e of t)switch(e.type){case"update-room-subscription-settings":{let t=r[e.roomId];if(void 0===t)break;r[e.roomId]={...t,...e.settings}}}return r})(e,t)),update:function(e,r){t.mutate(t=>{t.set(e,r)})}}}(this.optimisticUpdates.signal),this.historyVersions=function(){let e=new Q(new el(()=>new Map));return{signal:Z.from(e,e=>Object.fromEntries([...e].map(([e,t])=>[e,Object.fromEntries(t)]))),update:function(t,r){e.mutate(e=>{let i=e.getOrCreate(t);for(let e of r)i.set(e.id,e)})}}}();let r=Z.from(this.threads.signal,this.notifications.signal,this.optimisticUpdates.signal,(e,t,r)=>(function(e,t,r){let i=e.clone(),n=Object.fromEntries(t);for(let e of r)switch(e.type){case"create-thread":i.upsert(e.thread);break;case"edit-thread-metadata":{let t=i.get(e.threadId);if(void 0===t||t.updatedAt>e.updatedAt)break;i.upsert({...t,updatedAt:e.updatedAt,metadata:{...t.metadata,...e.metadata}});break}case"mark-thread-as-resolved":{let t=i.get(e.threadId);if(void 0===t)break;i.upsert({...t,resolved:!0});break}case"mark-thread-as-unresolved":{let t=i.get(e.threadId);if(void 0===t)break;i.upsert({...t,resolved:!1});break}case"create-comment":{let t=i.get(e.comment.threadId);if(void 0===t)break;i.upsert(rp(t,e.comment));let r=Object.values(n).find(e=>"thread"===e.kind&&e.threadId===t.id);if(void 0===r)break;n[r.id]={...r,notifiedAt:e.comment.createdAt,readAt:e.comment.createdAt};break}case"edit-comment":{let t=i.get(e.comment.threadId);if(void 0===t)break;i.upsert(rp(t,e.comment));break}case"delete-comment":{let t=i.get(e.threadId);if(void 0===t)break;i.upsert(rm(t,e.commentId,e.deletedAt));break}case"delete-thread":{let t=i.get(e.threadId);if(void 0===t)break;i.upsert({...t,deletedAt:e.deletedAt,updatedAt:e.deletedAt,comments:[]});break}case"add-reaction":{let t=i.get(e.threadId);if(void 0===t)break;i.upsert(rg(t,e.commentId,e.reaction));break}case"remove-reaction":{let t=i.get(e.threadId);if(void 0===t)break;i.upsert(rb(t,e.commentId,e.emoji,e.userId,e.removedAt));break}case"mark-inbox-notification-as-read":{let t=n[e.inboxNotificationId];if(void 0===t)break;n[e.inboxNotificationId]={...t,readAt:e.readAt};break}case"mark-all-inbox-notifications-as-read":for(let t in n){let r=n[t];if(void 0===r)break;n[t]={...r,readAt:e.readAt}}break;case"delete-inbox-notification":delete n[e.inboxNotificationId];break;case"delete-all-inbox-notifications":n={}}return{sortedNotifications:Object.values(n).filter(e=>"thread"!==e.kind||void 0!==i.get(e.threadId)).sort((e,t)=>t.notifiedAt.getTime()-e.notifiedAt.getTime()),notificationsById:n,threadsDB:i}})(e,t,r)),i=Z.from(r,e=>e.threadsDB),n=Z.from(r,e=>({sortedNotifications:e.sortedNotifications,notificationsById:e.notificationsById}),tW),s=Z.from(n,this.subscriptions.signal,(e,t)=>({subscriptions:t,notifications:e.sortedNotifications})),o=new el(e=>{let t=JSON.parse(e),r=new rd(async e=>{let r=await this.#eN[eY].httpClient.getUserThreads_experimental({cursor:e,query:t});return this.updateThreadifications(r.threads,r.inboxNotifications,r.subscriptions),this.permissionHints.update(r.permissionHints),null===this.#ej&&(this.#ej=r.requestedAt),r.nextCursor});return{signal:Z.from(()=>{let e=r.get();if(e.isLoading||e.error)return e;let i=this.outputs.threads.get().findMany(void 0,t??{},"desc"),n=e.data;return{isLoading:!1,threads:i,hasFetchedAll:n.hasFetchedAll,isFetchingMore:n.isFetchingMore,fetchMoreError:n.fetchMoreError,fetchMore:n.fetchMore}},tG),waitUntilLoaded:r.waitUntilLoaded}}),a=new el(e=>{let[t,r]=JSON.parse(e),i=new rd(async e=>{let i=await this.#eN[eY].httpClient.getThreads({roomId:t,cursor:e,query:r});this.updateThreadifications(i.threads,i.inboxNotifications,i.subscriptions),this.permissionHints.update(i.permissionHints);let n=this.#eM.get(t);return(void 0===n||n>i.requestedAt)&&this.#eM.set(t,i.requestedAt),i.nextCursor});return{signal:Z.from(()=>{let e=i.get();if(e.isLoading||e.error)return e;let n=this.outputs.threads.get().findMany(t,r??{},"asc"),s=e.data;return{isLoading:!1,threads:n,hasFetchedAll:s.hasFetchedAll,isFetchingMore:s.isFetchingMore,fetchMoreError:s.fetchMoreError,fetchMore:s.fetchMore}},tG),waitUntilLoaded:i.waitUntilLoaded}}),l={signal:Z.from(()=>{let e=this.#eL.get();if(e.isLoading||e.error)return e;let t=e.data;return{isLoading:!1,inboxNotifications:this.outputs.notifications.get().sortedNotifications,hasFetchedAll:t.hasFetchedAll,isFetchingMore:t.isFetchingMore,fetchMoreError:t.fetchMoreError,fetchMore:t.fetchMore}}),waitUntilLoaded:this.#eL.waitUntilLoaded},c=new el(e=>{let t=new rh(async()=>{let t=this.#eN.getRoom(e);if(null===t)throw Error(`Room '${e}' is not available on client`);let r=await t.getSubscriptionSettings();this.roomSubscriptionSettings.update(e,r)});return{signal:Z.from(()=>{let r=t.get();return r.isLoading||r.error?r:t7("settings",e_(this.roomSubscriptionSettings.signal.get()[e]))},tW),waitUntilLoaded:t.waitUntilLoaded}}),u=new el(e=>{let t=new rh(async()=>{let t=this.#eN.getRoom(e);if(null===t)throw Error(`Room '${e}' is not available on client`);let r=await t[eY].listTextVersions();this.historyVersions.update(e,r.versions);let i=this.#eU.get(e);(void 0===i||i>r.requestedAt)&&this.#eU.set(e,r.requestedAt)});return{signal:Z.from(()=>{let r=t.get();return r.isLoading||r.error?r:t7("versions",Object.values(this.historyVersions.signal.get()[e]??{}))},tW),waitUntilLoaded:t.waitUntilLoaded}}),d={signal:Z.from(()=>{let e=this.#eF.get();return e.isLoading||e.error?e:t7("settings",e_(this.notificationSettings.signal.get()))},tW),waitUntilLoaded:this.#eF.waitUntilLoaded};this.outputs={threadifications:r,threads:i,loadingRoomThreads:a,loadingUserThreads:o,notifications:n,loadingNotifications:l,roomSubscriptionSettingsByRoomId:c,versionsByRoomId:u,notificationSettings:d,threadSubscriptions:s},rs(this)}markInboxNotificationRead(e,t,r){V(()=>{this.optimisticUpdates.remove(r),this.notifications.markRead(e,t)})}markAllInboxNotificationsRead(e,t){V(()=>{this.optimisticUpdates.remove(e),this.notifications.markAllRead(t)})}deleteInboxNotification(e,t){V(()=>{this.optimisticUpdates.remove(t),this.notifications.delete(e)})}deleteAllInboxNotifications(e){V(()=>{this.optimisticUpdates.remove(e),this.notifications.clear()})}createSubscription(e,t){V(()=>{this.optimisticUpdates.remove(t),this.subscriptions.create(e)})}deleteSubscription(e,t){V(()=>{this.optimisticUpdates.remove(t),this.subscriptions.delete(e)})}createThread(e,t){V(()=>{this.optimisticUpdates.remove(e),this.threads.upsert(t)})}#eB(e,t,r,i){V(()=>{null!==t&&this.optimisticUpdates.remove(t);let n=this.threads,s=n.get(e);s&&(i&&s.updatedAt>i||n.upsert(r(s)))})}patchThread(e,t,r,i){return this.#eB(e,t,e=>({...e,...C(r)}),i)}addReaction(e,t,r,i,n){this.#eB(e,t,e=>rg(e,r,i),n)}removeReaction(e,t,r,i,n,s){this.#eB(e,t,e=>rb(e,r,i,n,s),s)}deleteThread(e,t){return this.#eB(e,t,e=>({...e,updatedAt:new Date,deletedAt:new Date}))}createComment(e,t){V(()=>{this.optimisticUpdates.remove(t);let r=this.threads.get(e.threadId);r&&(this.threads.upsert(rp(r,e)),this.notifications.updateAssociatedNotification(e))})}editComment(e,t,r){return this.#eB(e,t,e=>rp(e,r))}deleteComment(e,t,r,i){return this.#eB(e,t,e=>rm(e,r,i),i)}updateThreadifications(e,t,r,i=[],n=[],s=[]){V(()=>{this.threads.applyDelta(e,i),this.notifications.applyDelta(t,n),this.subscriptions.applyDelta(r,s)})}updateRoomSubscriptionSettings(e,t,r){V(()=>{this.optimisticUpdates.remove(t),this.roomSubscriptionSettings.update(e,r)})}async fetchNotificationsDeltaUpdate(e){let t=this.#eD;if(null===t)return;let r=await this.#eN.getInboxNotificationsSince({since:t,signal:e});t<r.requestedAt&&(this.#eD=r.requestedAt),this.updateThreadifications(r.threads.updated,r.inboxNotifications.updated,r.subscriptions.updated,r.threads.deleted,r.inboxNotifications.deleted,r.subscriptions.deleted)}async fetchRoomThreadsDeltaUpdate(e,t){let r=this.#eM.get(e);if(void 0===r)return;let i=await this.#eN[eY].httpClient.getThreadsSince({roomId:e,since:r,signal:t});this.updateThreadifications(i.threads.updated,i.inboxNotifications.updated,i.subscriptions.updated,i.threads.deleted,i.inboxNotifications.deleted,i.subscriptions.deleted),this.permissionHints.update(i.permissionHints),r<i.requestedAt&&this.#eM.set(e,i.requestedAt)}async fetchUserThreadsDeltaUpdate(e){let t=this.#ej;if(null===t)return;let r=await this.#eN[eY].httpClient.getUserThreadsSince_experimental({since:t,signal:e});t<r.requestedAt&&(this.#eD=r.requestedAt),this.updateThreadifications(r.threads.updated,r.inboxNotifications.updated,r.subscriptions.updated,r.threads.deleted,r.inboxNotifications.deleted,r.subscriptions.deleted),this.permissionHints.update(r.permissionHints)}async fetchRoomVersionsDeltaUpdate(e,t){let r=this.#eU.get(e);if(void 0===r)return;let i=e_(this.#eN.getRoom(e),`Room with id ${e} is not available on client`),n=await i[eY].listTextVersionsSince({since:r,signal:t});this.historyVersions.update(e,n.versions),r<n.requestedAt&&this.#eU.set(e,n.requestedAt)}async refreshRoomSubscriptionSettings(e,t){let r=e_(this.#eN.getRoom(e),`Room with id ${e} is not available on client`),i=await r.getSubscriptionSettings({signal:t});this.roomSubscriptionSettings.update(e,i)}async refreshNotificationSettings(e){let t=await this.#eN.getNotificationSettings({signal:e});this.notificationSettings.update(t)}updateNotificationSettings_confirmOptimisticUpdate(e,t){V(()=>{this.optimisticUpdates.remove(t),this.notificationSettings.update(e)})}};function rp(e,t){if(void 0!==e.deletedAt)return e;if(t.threadId!==e.id)return g.warn(`Comment ${t.id} does not belong to thread ${e.id}`),e;let r=e.comments.find(e=>e.id===t.id);if(void 0===r){let r=new Date(Math.max(e.updatedAt.getTime(),t.createdAt.getTime()));return{...e,updatedAt:r,comments:[...e.comments,t]}}if(void 0!==r.deletedAt)return e;if(void 0===r.editedAt||void 0===t.editedAt||r.editedAt<=t.editedAt){let r=e.comments.map(e=>e.id===t.id?t:e);return{...e,updatedAt:new Date(Math.max(e.updatedAt.getTime(),t.editedAt?.getTime()||t.createdAt.getTime())),comments:r}}return e}function rm(e,t,r){if(void 0!==e.deletedAt)return e;let i=e.comments.find(e=>e.id===t);if(void 0===i||void 0!==i.deletedAt)return e;let n=e.comments.map(e=>e.id===t?{...e,deletedAt:r,body:void 0,attachments:[]}:e);return n.every(e=>void 0!==e.deletedAt)?{...e,deletedAt:r,updatedAt:r}:{...e,updatedAt:r,comments:n}}function rg(e,t,r){if(void 0!==e.deletedAt)return e;let i=e.comments.find(e=>e.id===t);if(void 0===i||void 0!==i.deletedAt)return e;let n=e.comments.map(e=>e.id===t?{...e,reactions:function(e,t){let r=e.find(e=>e.emoji===t.emoji);return void 0===r?[...e,{emoji:t.emoji,createdAt:t.createdAt,users:[{id:t.userId}]}]:!1===r.users.some(e=>e.id===t.userId)?e.map(e=>e.emoji===t.emoji?{...e,users:[...e.users,{id:t.userId}]}:e):e}(e.reactions,r)}:e);return{...e,updatedAt:new Date(Math.max(r.createdAt.getTime(),e.updatedAt.getTime())),comments:n}}function rb(e,t,r,i,n){if(void 0!==e.deletedAt)return e;let s=e.comments.find(e=>e.id===t);if(void 0===s||void 0!==s.deletedAt)return e;let o=e.comments.map(e=>e.id===t?{...e,reactions:e.reactions.map(e=>e.emoji===r?{...e,users:e.users.filter(e=>e.id!==i)}:e).filter(e=>e.users.length>0)}:e);return{...e,updatedAt:new Date(Math.max(n.getTime(),e.updatedAt.getTime())),comments:o}}var ry=(0,i.createContext)(null);function rv(e){return Error(`resolveUsers didn't return anything for user '${e}'`)}function rw(e){return Error(`resolveRoomsInfo didn't return anything for room '${e}'`)}function r_(e){return e}var rE=new WeakMap,rS=new WeakMap;function rA(e){return e.inboxNotifications?t7("count",function(e,t){let r=0;for(let i of e)t(i)&&r++;return r}(e.inboxNotifications,e=>null===e.readAt||e.readAt<e.notifiedAt)):e}function rx(e){let t=rE.get(e);return t||(t=new rf(e),rE.set(e,t)),t}function rI(e){let t=rS.get(e);return t||(t=function(e){let t=rx(e),r=makePoller(async e=>{try{return await t.fetchNotificationsDeltaUpdate(e)}catch(e){throw console.warn(`Polling new inbox notifications failed: ${String(e)}`),e}},t5.NOTIFICATIONS_POLL_INTERVAL,{maxStaleTimeMs:t5.NOTIFICATIONS_MAX_STALE_TIME}),i=makePoller(async e=>{try{return await t.fetchUserThreadsDeltaUpdate(e)}catch(e){throw console.warn(`Polling new user threads failed: ${String(e)}`),e}},t5.USER_THREADS_POLL_INTERVAL,{maxStaleTimeMs:t5.USER_THREADS_MAX_STALE_TIME}),n=makePoller(async e=>{try{return await t.refreshNotificationSettings(e)}catch(e){throw console.warn(`Polling new notification settings failed: ${String(e)}`),e}},t5.USER_NOTIFICATION_SETTINGS_INTERVAL,{maxStaleTimeMs:t5.USER_NOTIFICATION_SETTINGS_MAX_STALE_TIME});return{store:t,notificationsPoller:r,userThreadsPoller:i,notificationSettingsPoller:n}}(e),rS.set(e,t)),t}function rT(e,t,r){let{store:i,notificationsPoller:n}=rI(e);return useEffect3(()=>void i.outputs.loadingNotifications.waitUntilLoaded()),useEffect3(()=>(n.inc(),n.pollNowIfStale(),()=>{n.dec()}),[n]),t4(i.outputs.loadingNotifications.signal,t,r)}function rk(){return(0,i.useContext)(ry)}function rO(){return rk()??I("LiveblocksProvider is missing from the React tree.")}function rR(e){return function(e){let t=rk();if(!e?.allowNesting&&null!==t)throw Error("You cannot nest multiple LiveblocksProvider instances in the same React tree.")}(e),(0,tZ.jsx)(ry.Provider,{value:e.client,children:e.children})}function rC(e){let{children:t,...r}=e,n={publicApiKey:rr(r.publicApiKey),throttle:rr(r.throttle),lostConnectionTimeout:rr(r.lostConnectionTimeout),backgroundKeepAliveTimeout:rr(r.backgroundKeepAliveTimeout),polyfills:rr(r.polyfills),largeMessageStrategy:rr(r.largeMessageStrategy),unstable_fallbackToHTTP:rr(r.unstable_fallbackToHTTP),unstable_streamData:rr(r.unstable_streamData),preventUnsavedChanges:rr(r.preventUnsavedChanges),authEndpoint:ri(r.authEndpoint),resolveMentionSuggestions:ri(r.resolveMentionSuggestions),resolveUsers:ri(r.resolveUsers),resolveRoomsInfo:ri(r.resolveRoomsInfo),baseUrl:rr(r.baseUrl),enableDebugLogging:rr(r.enableDebugLogging)},s=(0,i.useMemo)(()=>(function(e){var t;let r=tO("throttle",e.throttle??100,16,1e3),i=tO("lostConnectionTimeout",e.lostConnectionTimeout??5e3,200,3e4,1e3),n=function(e){if(void 0!==e)return tO("backgroundKeepAliveTimeout",e,15e3)}(e.backgroundKeepAliveTimeout),s="string"==typeof(t=e.baseUrl)&&t.startsWith("http")?t:"https://api.liveblocks.io",a=new X(void 0),l=function(e,t){let r=function(e){let{publicApiKey:t,authEndpoint:r}=e;if(void 0!==r&&void 0!==t)throw Error("You cannot simultaneously use `publicApiKey` and `authEndpoint` options. Please pick one and leave the other option unspecified. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClient");if("string"==typeof t){if(t.startsWith("sk_"))throw Error("Invalid `publicApiKey` option. The value you passed is a secret key, which should not be used from the client. Please only ever pass a public key here. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientPublicKey");if(!t.startsWith("pk_"))throw Error("Invalid key. Please use the public key format: pk_<public key>. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientPublicKey");return{type:"public",publicApiKey:t}}if("string"==typeof r)return{type:"private",url:r};if("function"==typeof r)return{type:"custom",callback:r};if(void 0!==r)throw Error("The `authEndpoint` option must be a string or a function. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClientAuthEndpoint");throw Error("Invalid Liveblocks client options. Please provide either a `publicApiKey` or `authEndpoint` option. They cannot both be empty. For more information: https://liveblocks.io/docs/api-reference/liveblocks-client#createClient")}(e),i=new Set,n=[],s=[],o=new Map;function a(e,t){return"comments:read"===e?t.includes("comments:read")||t.includes("comments:write")||t.includes("room:read")||t.includes("room:write"):"room:read"===e&&(t.includes("room:read")||t.includes("room:write"))}async function l(n){let s=e.polyfills?.fetch??("undefined"==typeof window?void 0:window.fetch);if("private"===r.type){if(void 0===s)throw new eN("To use Liveblocks client in a non-DOM environment with a url as auth endpoint, you need to provide a fetch polyfill.");let e=eG((await eX(s,r.url,{room:n.roomId})).token);if(i.has(e.raw))throw new eN("The same Liveblocks auth token was issued from the backend before. Caching Liveblocks tokens is not supported.");return t?.(e.parsed),e}if("custom"===r.type){let e=await r.callback(n.roomId);if(e&&"object"==typeof e){if("string"==typeof e.token){let r=eG(e.token);return t?.(r.parsed),r}else if("string"==typeof e.error){let t=`Authentication failed: ${"reason"in e&&"string"==typeof e.reason?e.reason:"Forbidden"}`;if("forbidden"===e.error)throw new eN(t);throw Error(t)}}throw Error('Your authentication callback function should return a token, but it did not. Hint: the return value should look like: { token: "..." }')}throw Error("Unexpected authentication type. Must be private or custom.")}return{reset:function(){i.clear(),n.length=0,s.length=0,o.clear()},getAuthValue:async function(e){let t;if("public"===r.type)return{type:"public",publicApiKey:r.publicApiKey};let c=function(e){let t=Math.ceil(Date.now()/1e3);for(let r=n.length-1;r>=0;r--){let i=n[r];if(s[r]<=t){n.splice(r,1),s.splice(r,1);continue}if("id"===i.parsed.k)return i;if("acc"===i.parsed.k){if(!e.roomId&&0===Object.entries(i.parsed.perms).length)return i;for(let[t,r]of Object.entries(i.parsed.perms))if(e.roomId){if(t.includes("*")&&e.roomId.startsWith(t.replace("*",""))||e.roomId===t&&a(e.requestedScope,r))return i}else if(t.includes("*")&&a(e.requestedScope,r))return i}}}(e);if(void 0!==c)return{type:"secret",token:c};e.roomId?void 0===(t=o.get(e.roomId))&&(t=l(e),o.set(e.roomId,t)):void 0===(t=o.get("liveblocks-user-token"))&&(t=l(e),o.set("liveblocks-user-token",t));try{let e=await t,r=Math.floor(Date.now()/1e3)+(e.parsed.exp-e.parsed.iat)-30;return i.add(e.raw),"sec-legacy"!==e.parsed.k&&(n.push(e),s.push(r)),{type:"secret",token:e}}finally{e.roomId?o.delete(e.roomId):o.delete("liveblocks-user-token")}}}}(e,e=>{let t="sec-legacy"===e.k?e.id:e.uid;a.set(()=>t)}),g=function({baseUrl:e,authManager:t,fetchPolyfill:r}){let i=new ev(e,r);async function n(e){let r=await i.get(eb`/v2/c/rooms/${e.roomId}/threads/delta`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{since:e.since.toISOString()},{signal:e.signal});return{threads:{updated:r.data.map(u),deleted:r.deletedThreads.map(f)},inboxNotifications:{updated:r.inboxNotifications.map(d),deleted:r.deletedInboxNotifications.map(p)},subscriptions:{updated:r.subscriptions.map(h),deleted:r.deletedSubscriptions.map(m)},requestedAt:new Date(r.meta.requestedAt),permissionHints:r.meta.permissionHints}}async function s(e){let r;e.query&&(r=eu(e.query));try{let n=await i.get(eb`/v2/c/rooms/${e.roomId}/threads`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{cursor:e.cursor,query:r,limit:50});return{threads:n.data.map(u),inboxNotifications:n.inboxNotifications.map(d),subscriptions:n.subscriptions.map(h),nextCursor:n.meta.nextCursor,requestedAt:new Date(n.meta.requestedAt),permissionHints:n.meta.permissionHints}}catch(e){if(e instanceof D&&404===e.status)return{threads:[],inboxNotifications:[],subscriptions:[],nextCursor:null,requestedAt:new Date(Date.now()-216e5),permissionHints:{}};throw e}}async function o(e){let r=e.commentId??ea("cm"),n=e.threadId??ea("th");return u(await i.post(eb`/v2/c/rooms/${e.roomId}/threads`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{id:n,comment:{id:r,body:e.body,attachmentIds:e.attachmentIds},metadata:e.metadata}))}async function a(e){await i.delete(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function l(e){let r=await i.rawGet(eb`/v2/c/rooms/${e.roomId}/thread-with-notification/${e.threadId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}));if(r.ok){let e=await r.json();return{thread:u(e.thread),inboxNotification:e.inboxNotification?d(e.inboxNotification):void 0,subscription:e.subscription?h(e.subscription):void 0}}if(404===r.status)return{thread:void 0,inboxNotification:void 0,subscription:void 0};throw Error(`There was an error while getting thread ${e.threadId}.`)}async function g(e){return await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/metadata`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),e.metadata)}async function b(e){let r=e.commentId??ea("cm");return c(await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{id:r,body:e.body,attachmentIds:e.attachmentIds}))}async function y(e){return c(await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{body:e.body,attachmentIds:e.attachmentIds}))}async function v(e){await i.delete(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function w(e){var r;return{...r=await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}/reactions`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{emoji:e.emoji}),createdAt:new Date(r.createdAt)}}async function _(e){await i.delete(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/comments/${e.commentId}/reactions/${e.emoji}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function E(e){await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/mark-as-resolved`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function S(e){await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/mark-as-unresolved`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function A(e){return h(await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/subscribe`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId})))}async function x(e){await i.post(eb`/v2/c/rooms/${e.roomId}/threads/${e.threadId}/unsubscribe`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function I(e){let r=e.roomId,n=e.signal,s=e.attachment,o=n?new DOMException(`Upload of attachment ${e.attachment.id} was aborted.`,"AbortError"):void 0;if(n?.aborted)throw o;let a=e=>{if(n?.aborted)throw o;if(e instanceof D&&413===e.status)throw e;return!1},l=[2e3,2e3,2e3,2e3,2e3,2e3,2e3,2e3,2e3,2e3];if(s.size<=5242880)return M(async()=>i.putBlob(eb`/v2/c/rooms/${r}/attachments/${s.id}/upload/${encodeURIComponent(s.name)}`,await t.getAuthValue({requestedScope:"comments:read",roomId:r}),s.file,{fileSize:s.size},{signal:n}),10,l,a);{let e,c=[],u=await M(async()=>i.post(eb`/v2/c/rooms/${r}/attachments/${s.id}/multipart/${encodeURIComponent(s.name)}`,await t.getAuthValue({requestedScope:"comments:read",roomId:r}),void 0,{signal:n},{fileSize:s.size}),10,l,a);try{e=u.uploadId;let d=function(e){let t=[],r=0;for(;r<e.size;){let i=Math.min(r+5242880,e.size);t.push({partNumber:t.length+1,part:e.slice(r,i)}),r=i}return t}(s.file);if(n?.aborted)throw o;for(let e of function(e,t){let r=[];for(let t=0,i=e.length;t<i;t+=5)r.push(e.slice(t,t+5));return r}(d,5)){let o=[];for(let{part:c,partNumber:d}of e)o.push(M(async()=>i.putBlob(eb`/v2/c/rooms/${r}/attachments/${s.id}/multipart/${u.uploadId}/${String(d)}`,await t.getAuthValue({requestedScope:"comments:read",roomId:r}),c,void 0,{signal:n}),10,l,a));c.push(...await Promise.all(o))}if(n?.aborted)throw o;let h=c.sort((e,t)=>e.partNumber-t.partNumber);return i.post(eb`/v2/c/rooms/${r}/attachments/${s.id}/multipart/${e}/complete`,await t.getAuthValue({requestedScope:"comments:read",roomId:r}),{parts:h},{signal:n})}catch(n){if(e&&n?.name&&("AbortError"===n.name||"TimeoutError"===n.name))try{await i.rawDelete(eb`/v2/c/rooms/${r}/attachments/${s.id}/multipart/${e}`,await t.getAuthValue({requestedScope:"comments:read",roomId:r}))}catch(e){}throw n}}}let T=new el(e=>es(new en(async r=>{let n=r.flat(),{urls:s}=await i.post(eb`/v2/c/rooms/${e}/attachments/presigned-urls`,await t.getAuthValue({requestedScope:"comments:read",roomId:e}),{attachmentIds:n});return s.map(e=>e??Error("There was an error while getting this attachment's URL"))},{delay:50})));function k(e){return T.getOrCreate(e)}async function O(e){return i.get(eb`/v2/c/rooms/${e.roomId}/subscription-settings`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),void 0,{signal:e.signal})}async function R(e){return i.post(eb`/v2/c/rooms/${e.roomId}/subscription-settings`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),e.settings)}let C=new el(e=>new en(async r=>{let n=r.flat();return await i.post(eb`/v2/c/rooms/${e}/inbox-notifications/read`,await t.getAuthValue({requestedScope:"comments:read",roomId:e}),{inboxNotificationIds:n}),n},{delay:50}));async function P(e){return C.getOrCreate(e.roomId).get(e.inboxNotificationId)}async function N(e){await i.rawPost(eb`/v2/c/rooms/${e.roomId}/text-mentions`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{userId:e.userId,mentionId:e.mentionId})}async function L(e){await i.rawDelete(eb`/v2/c/rooms/${e.roomId}/text-mentions/${e.mentionId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function j(e){return i.rawGet(eb`/v2/c/rooms/${e.roomId}/y-version/${e.versionId}`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function U(e){await i.rawPost(eb`/v2/c/rooms/${e.roomId}/version`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}))}async function F(e){await i.rawPost(eb`/v2/c/rooms/${e.roomId}/text-metadata`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{type:e.type,rootKey:e.rootKey})}async function B(e){let r=await i.post(eb`/v2/c/rooms/${e.roomId}/ai/contextual-prompt`,await t.getAuthValue({requestedScope:"room:read",roomId:e.roomId}),{prompt:e.prompt,context:{beforeSelection:e.context.beforeSelection,selection:e.context.selection,afterSelection:e.context.afterSelection},previous:e.previous},{signal:e.signal});if(!r||0===r.content.length)throw Error("No content returned from server");return r.content[0].text}async function q(e){let r=await i.get(eb`/v2/c/rooms/${e.roomId}/versions`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}));return{versions:r.versions.map(({createdAt:e,...t})=>({createdAt:new Date(e),...t})),requestedAt:new Date(r.meta.requestedAt)}}async function $(e){let r=await i.get(eb`/v2/c/rooms/${e.roomId}/versions/delta`,await t.getAuthValue({requestedScope:"comments:read",roomId:e.roomId}),{since:e.since.toISOString()},{signal:e.signal});return{versions:r.versions.map(({createdAt:e,...t})=>({createdAt:new Date(e),...t})),requestedAt:new Date(r.meta.requestedAt)}}async function z(e){let r=await i.rawGet(eb`/v2/c/rooms/${e.roomId}/storage`,await t.getAuthValue({requestedScope:"room:read",roomId:e.roomId}));return await r.json()}async function H(e){return i.rawPost(eb`/v2/c/rooms/${e.roomId}/send-message`,await t.getAuthValue({requestedScope:"room:read",roomId:e.roomId}),{nonce:e.nonce,messages:e.messages})}async function V(e){let r=await i.get(eb`/v2/c/inbox-notifications`,await t.getAuthValue({requestedScope:"comments:read"}),{cursor:e?.cursor,limit:50});return{inboxNotifications:r.inboxNotifications.map(d),threads:r.threads.map(u),subscriptions:r.subscriptions.map(h),nextCursor:r.meta.nextCursor,requestedAt:new Date(r.meta.requestedAt)}}async function K(e){let r=await i.get(eb`/v2/c/inbox-notifications/delta`,await t.getAuthValue({requestedScope:"comments:read"}),{since:e.since.toISOString()},{signal:e.signal});return{inboxNotifications:{updated:r.inboxNotifications.map(d),deleted:r.deletedInboxNotifications.map(p)},threads:{updated:r.threads.map(u),deleted:r.deletedThreads.map(f)},subscriptions:{updated:r.subscriptions.map(h),deleted:r.deletedSubscriptions.map(m)},requestedAt:new Date(r.meta.requestedAt)}}async function W(){let{count:e}=await i.get(eb`/v2/c/inbox-notifications/count`,await t.getAuthValue({requestedScope:"comments:read"}));return e}async function G(){await i.post(eb`/v2/c/inbox-notifications/read`,await t.getAuthValue({requestedScope:"comments:read"}),{inboxNotificationIds:"all"})}async function X(e){await i.post(eb`/v2/c/inbox-notifications/read`,await t.getAuthValue({requestedScope:"comments:read"}),{inboxNotificationIds:e})}let Y=new en(async e=>{let t=e.flat();return await X(t),t},{delay:50});return{getThreads:s,getThreadsSince:n,createThread:o,getThread:l,deleteThread:a,editThreadMetadata:g,createComment:b,editComment:y,deleteComment:v,addReaction:w,removeReaction:_,markThreadAsResolved:E,markThreadAsUnresolved:S,subscribeToThread:A,unsubscribeFromThread:x,markRoomInboxNotificationAsRead:P,getSubscriptionSettings:O,updateSubscriptionSettings:R,createTextMention:N,deleteTextMention:L,getTextVersion:j,createTextVersion:U,reportTextEditor:F,listTextVersions:q,listTextVersionsSince:$,getAttachmentUrl:function(e){return k(e.roomId).batch.get(e.attachmentId)},uploadAttachment:I,getOrCreateAttachmentUrlsStore:k,streamStorage:z,sendMessages:H,getInboxNotifications:V,getInboxNotificationsSince:K,getUnreadInboxNotificationsCount:W,markAllInboxNotificationsAsRead:G,markInboxNotificationAsRead:async function(e){await Y.get(e)},deleteAllInboxNotifications:async function(){await i.delete(eb`/v2/c/inbox-notifications`,await t.getAuthValue({requestedScope:"comments:read"}))},deleteInboxNotification:async function(e){await i.delete(eb`/v2/c/inbox-notifications/${e}`,await t.getAuthValue({requestedScope:"comments:read"}))},getNotificationSettings:async function(e){return i.get(eb`/v2/c/notification-settings`,await t.getAuthValue({requestedScope:"comments:read"}),void 0,{signal:e?.signal})},updateNotificationSettings:async function(e){return i.post(eb`/v2/c/notification-settings`,await t.getAuthValue({requestedScope:"comments:read"}),e)},getUserThreads_experimental:async function(e){let r;e?.query&&(r=eu(e.query));let n=await i.get(eb`/v2/c/threads`,await t.getAuthValue({requestedScope:"comments:read"}),{cursor:e?.cursor,query:r,limit:50});return{threads:n.threads.map(u),inboxNotifications:n.inboxNotifications.map(d),subscriptions:n.subscriptions.map(h),nextCursor:n.meta.nextCursor,requestedAt:new Date(n.meta.requestedAt),permissionHints:n.meta.permissionHints}},getUserThreadsSince_experimental:async function(e){let r=await i.get(eb`/v2/c/threads/delta`,await t.getAuthValue({requestedScope:"comments:read"}),{since:e.since.toISOString()},{signal:e.signal});return{threads:{updated:r.threads.map(u),deleted:r.deletedThreads.map(f)},inboxNotifications:{updated:r.inboxNotifications.map(d),deleted:r.deletedInboxNotifications.map(p)},subscriptions:{updated:r.subscriptions.map(h),deleted:r.deletedSubscriptions.map(m)},requestedAt:new Date(r.meta.requestedAt),permissionHints:r.meta.permissionHints}},executeContextualPrompt:B}}({baseUrl:s,fetchPolyfill:e.polyfills?.fetch||globalThis.fetch?.bind(globalThis),authManager:l}),b=new Map;function y(e){let t=()=>{if(e.unsubs.delete(t)){var r,i;0===e.unsubs.size&&((r=e.room).id,b.delete(r.id),r.destroy())}else v("This leave function was already called. Calling it more than once has no effect.")};return e.unsubs.add(t),{room:e.room,leave:t}}let _=e.resolveUsers,E=tR(()=>!_,"Set the resolveUsers option in createClient to specify user info."),A=es(new en(async e=>{let t=e.flat(),r=await _?.({userIds:t});return E(),r??t.map(()=>void 0)},{delay:50})),x=e.resolveRoomsInfo,T=tR(()=>!x,"Set the resolveRoomsInfo option in createClient to specify room info."),C=es(new en(async e=>{let t=e.flat(),r=await x?.({roomIds:t});return T(),r??t.map(()=>void 0)},{delay:50})),P=new Map,L=[],j=new X("synchronized"),B=F();function q(){j.set(L.some(e=>"synchronizing"===e.get())?"synchronizing":L.some(e=>"has-local-changes"===e.get())?"has-local-changes":"synchronized")}function $(){let e=new X("synchronized");L.push(e);let t=e.subscribe(()=>q());return{setSyncStatus:function(t){e.set(t)},destroy:function(){t();let r=L.findIndex(t=>t===e);if(r>-1){let[e]=L.splice(r,1);"synchronized"!==e.get()&&q()}}}}{let t="undefined"!=typeof window?window:void 0;t?.addEventListener("beforeunload",t=>{e.preventUnsavedChanges&&"synchronized"!==j.get()&&t.preventDefault()})}async function z(e){return eZ(await g.getNotificationSettings(e))}async function H(e){return eZ(await g.updateNotificationSettings(e))}let V=Object.defineProperty({enterRoom:function(t,...a){var c;let u=b.get(t);if(void 0!==u)return y(u);let d=a[0]??{},h=function(e,t){var r,i,n;let s,o,a,l=t.roomId,c=e.initialPresence,u=e.initialStorage,d=t.roomHttpClient,[h,f]=function(){let e="undefined"!=typeof document?document:void 0,t={current:null};function r(){e?.visibilityState==="hidden"?t.current=t.current??Date.now():t.current=null}return e?.addEventListener("visibilitychange",r),[t,()=>{e?.removeEventListener("visibilitychange",r)}]}(),p=new eH({...t.delegates,canZombie:()=>void 0!==t.backgroundKeepAliveTimeout&&null!==h.current&&Date.now()>h.current+t.backgroundKeepAliveTimeout&&"synchronizing"!==ed()},t.enableDebugLogging),m={buffer:{flushTimerID:void 0,lastFlushedAt:0,presenceUpdates:{type:"full",data:c},messages:[],storageOperations:[]},staticSessionInfoSig:new X(null),dynamicSessionInfoSig:new X(null),myPresence:new Y(c),others:new tI,initialStorage:u,idFactory:null,yjsProvider:void 0,yjsProviderDidChange:F(),pool:function(e,t){let{getCurrentConnectionId:r,onDispatch:i,isStorageWritable:n=()=>!0}=t,s=0,o=0,a=new Map;return{roomId:e,nodes:a,getNode:e=>a.get(e),addNode:(e,t)=>void a.set(e,t),deleteNode:e=>void a.delete(e),generateId:()=>`${r()}:${s++}`,generateOpId:()=>`${r()}:${o++}`,dispatch(e,t,r){i?.(e,t,r)},assertStorageIsWritable:()=>{if(!n())throw Error("Cannot write to storage with a read only user, please ensure the user has write permissions")}}}(l,{getCurrentConnectionId:function(){let e=m.dynamicSessionInfoSig.get();if(e)return e.actor;throw Error("Internal. Tried to get connection id but connection was never open")},onDispatch:function(e,t,r){if(m.activeBatch){for(let t of e)m.activeBatch.ops.push(t);for(let[e,t]of r)m.activeBatch.updates.storageUpdates.set(e,tE(m.activeBatch.updates.storageUpdates.get(e),t));m.activeBatch.reverseOps.pushLeft(t)}else $(t),m.redoStack.length=0,Q(e),z({storageUpdates:r})},isStorageWritable:function(){let e=m.dynamicSessionInfoSig.get()?.scopes;return void 0===e||eK(e)}}),root:void 0,undoStack:[],redoStack:[],pausedHistory:null,activeBatch:null,unacknowledgedOps:new Map,opStackTraces:void 0},g=!1;p.events.onMessage.subscribe(function(e){if("string"!=typeof e.data)return;let t=function(e){let t=k(e);return void 0===t?null:tA(t)?R(t.map(e=>G(e))):R([G(t)])}(e.data);if(null===t||0===t.length)return;let r={storageUpdates:new Map,others:[]};for(let e of t)switch(e.type){case 101:{let t=function(e){m.others.setConnection(e.actor,e.id,e.info,e.scopes),m.buffer.messages.push({type:100,data:m.myPresence.get(),targetActor:e.actor}),J();let t=m.others.getUser(e.actor);return t?{type:"enter",user:t}:void 0}(e);t&&r.others.push(t);break}case 100:{let t=function(e){if(void 0!==e.targetActor){let t=m.others.getUser(e.actor);m.others.setOther(e.actor,e.data);let r=m.others.getUser(e.actor);if(void 0===t&&void 0!==r)return{type:"enter",user:r}}else m.others.patchOther(e.actor,e.data);let t=m.others.getUser(e.actor);return t?{type:"update",updates:e.data,user:t}:void 0}(e);t&&r.others.push(t);break}case 103:{let t=m.others.get();b.customEvent.notify({connectionId:e.actor,user:e.actor<0?null:t.find(t=>t.connectionId===e.actor)??null,event:e.event});break}case 102:{let t=function(e){let t=m.others.getUser(e.actor);return t?(m.others.removeConnection(e.actor),{type:"leave",user:t}):null}(e);t&&r.others.push(t);break}case 300:b.ydoc.notify(e);break;case 104:r.others.push(function(e){var t;let r;for(let i of(m.dynamicSessionInfoSig.set({actor:e.actor,nonce:e.nonce,scopes:e.scopes}),t=e.actor,r=0,m.idFactory=()=>`${t}:${r++}`,j(),m.others.connectionIds()))void 0===e.users[i]&&m.others.removeConnection(i);for(let t in e.users){let r=e.users[t],i=Number(t);m.others.setConnection(i,r.id,r.info,r.scopes)}return{type:"reset"}}(e));break;case 200:ei(e);break;case 201:for(let[t,i]of H(e.ops,!1).updates.storageUpdates)r.storageUpdates.set(t,tE(r.storageUpdates.get(t),i));break;case 299:S("Storage mutation rejection error",e.reason);break;case 400:case 407:case 401:case 408:case 405:case 406:case 402:case 403:case 404:b.comments.notify(e)}z(r)}),p.events.statusDidChange.subscribe(function(e){let t=p.authValue;if(null!==t){let e=ey(t);if(e!==s)if(s=e,"secret"===t.type){let e=t.token.parsed;m.staticSessionInfoSig.set({userId:"sec-legacy"===e.k?e.id:e.uid,userInfo:"sec-legacy"===e.k?e.info:e.ui})}else m.staticSessionInfoSig.set({userId:void 0,userInfo:void 0})}b.status.notify(e),j()}),p.events.statusDidChange.subscribe(function(e){"reconnecting"===e?o=setTimeout(()=>{b.lostConnection.notify("lost"),g=!0,m.others.clearOthers(),z({others:[{type:"reset"}]})},t.lostConnectionTimeout):(clearTimeout(o),g&&("disconnected"===e?b.lostConnection.notify("failed"):b.lostConnection.notify("restored"),g=!1))}),p.events.didConnect.subscribe(function(){m.buffer.presenceUpdates={type:"full",data:{...m.myPresence.get()}},null!==ee&&es({flush:!1}),J()}),p.events.didDisconnect.subscribe(function(){clearTimeout(m.buffer.flushTimerID)}),p.events.onConnectionError.subscribe(({message:e,code:r})=>{let i=new tT(e,{type:"ROOM_CONNECTION_ERROR",code:r,roomId:l});t.errorEventSource.notify(i)});let b={status:F(),lostConnection:F(),customEvent:F(),self:F(),myPresence:F(),others:F(),storageBatch:F(),history:F(),storageDidLoad:F(),storageStatus:F(),ydoc:F(),comments:F(),roomWillDestroy:F()};async function y(e,t){return d.createTextMention({roomId:l,userId:e,mentionId:t})}async function _(e){return d.deleteTextMention({roomId:l,mentionId:e})}async function E(e,t){await d.reportTextEditor({roomId:l,type:e,rootKey:t})}async function A(){return d.listTextVersions({roomId:l})}async function x(e){return d.listTextVersionsSince({roomId:l,since:e.since,signal:e.signal})}async function T(e){return d.getTextVersion({roomId:l,versionId:e})}async function C(){return d.createTextVersion({roomId:l})}async function P(e){return d.executeContextualPrompt({roomId:l,...e})}function D(e){return!(4*e.length<1048064)&&new TextEncoder().encode(e).length>=1048064}function L(e){let r=t.largeMessageStrategy??"default",i=er(e);if(!D(i))return p.send(i);switch(r){case"default":return void w("Message is too large for websockets, not sending. Configure largeMessageStrategy option to deal with this.");case"split":for(let t of(v("Message is too large for websockets, splitting into smaller chunks"),function* e(t){if(t.length<2)if(201===t[0].type)return void(yield*function* e(t){let{ops:r,...i}=t;if(r.length<2)throw Error("Cannot split ops into smaller chunks");let n=Math.floor(r.length/2);for(let t of[r.slice(0,n),r.slice(n)]){let r={ops:t,...i},n=er([r]);D(n)?yield*e(r):yield n}}(t[0]));else throw Error("Cannot split into chunks smaller than the allowed message size");let r=Math.floor(t.length/2);for(let i of[t.slice(0,r),t.slice(r)]){let t=er(i);D(t)?yield*e(i):yield t}}(e)))p.send(t);return;case"experimental-fallback-to-http":{v("Message is too large for websockets, so sending over HTTP instead");let t=m.dynamicSessionInfoSig.get()?.nonce??I("Session is not authorized to send message over HTTP");d.sendMessages({roomId:l,nonce:t,messages:e}).then(e=>{e.ok||403!==e.status||p.reconnect()});return}}}let M=Z.from(m.staticSessionInfoSig,m.dynamicSessionInfoSig,m.myPresence,(e,t,r)=>{if(null===e||null===t)return null;{let i=eK(t.scopes);return{connectionId:t.actor,id:e.userId,info:e.userInfo,presence:r,canWrite:i,canComment:eW(t.scopes)}}});function j(){let e=M.get();null!==e&&e!==a&&(b.self.notify(e),a=e)}let B=Z.from(M,e=>null!==e?tk("Me",e):null);function q(e){m.undoStack.length>=50&&m.undoStack.shift(),m.undoStack.push(e),W()}function $(e){null!==m.pausedHistory?m.pausedHistory.pushLeft(e):q(e)}function z(e){let t=e.storageUpdates,r=e.others;if(void 0!==r&&r.length>0){let e=m.others.get();for(let t of r)b.others.notify({...t,others:e})}if(e.presence&&(j(),b.myPresence.notify(m.myPresence.get())),void 0!==t&&t.size>0){let e=Array.from(t.values());b.storageBatch.notify(e)}ef()}function H(e,t){let r={reverse:new tS,storageUpdates:new Map,presence:!1},i=new Set,n=e.map(e=>"presence"===e.type||e.opId?e:{...e,opId:m.pool.generateOpId()});for(let e of n)if("presence"===e.type){let t={type:"presence",data:{}};for(let r in e.data)t.data[r]=m.myPresence.get()[r];if(m.myPresence.patch(e.data),null===m.buffer.presenceUpdates)m.buffer.presenceUpdates={type:"partial",data:e.data};else for(let t in e.data)m.buffer.presenceUpdates.data[t]=e.data[t];r.reverse.pushLeft(t),r.presence=!0}else{let n;if(t)n=0;else{let t=e_(e.opId);n=m.unacknowledgedOps.delete(t)?2:1}let s=function(e,t){if(5===e.type&&"ACK"===e.id)return{modified:!1};switch(e.type){case 6:case 3:case 5:{let r=m.pool.nodes.get(e.id);if(void 0===r)return{modified:!1};return r._apply(e,0===t)}case 1:{let r=m.pool.nodes.get(e.id);if(void 0===r)return{modified:!1};if("HasParent"===r.parent.type&&ty(r.parent.node))return r.parent.node._setChildKey(e5(e.parentKey),r,t);return{modified:!1}}case 4:case 2:case 7:case 8:{if(void 0===e.parentId)return{modified:!1};let r=m.pool.nodes.get(e.parentId);if(void 0===r)return{modified:!1};return r._attachChild(e,t)}}}(e,n);if(s.modified){let t=s.modified.node._id;t&&i.has(t)||(r.storageUpdates.set(e_(s.modified.node._id),tE(r.storageUpdates.get(e_(s.modified.node._id)),s.modified)),r.reverse.pushLeft(s.reverse)),(2===e.type||7===e.type||4===e.type)&&i.add(e_(e.id))}}return{ops:n,reverse:Array.from(r.reverse),updates:{storageUpdates:r.storageUpdates,presence:r.presence}}}function V(){return m.undoStack.length>0}function K(){return m.redoStack.length>0}function W(){b.history.notify({canUndo:V(),canRedo:K()})}function G(e){return!function(e){return null!==e&&"string"!=typeof e&&"number"!=typeof e&&"boolean"!=typeof e&&!tA(e)}(e)?null:e}function J(){let e=m.buffer.storageOperations;if(e.length>0){for(let t of e)m.unacknowledgedOps.set(e_(t.opId),t);ef()}if("connected"!==p.getStatus()){m.buffer.storageOperations=[];return}let r=Date.now(),i=r-m.buffer.lastFlushedAt;if(i>=t.throttleDelay){let e=function(){let e=[];for(let t of(m.buffer.presenceUpdates&&e.push("full"===m.buffer.presenceUpdates.type?{type:100,targetActor:-1,data:m.buffer.presenceUpdates.data}:{type:100,data:m.buffer.presenceUpdates.data}),m.buffer.messages))e.push(t);return m.buffer.storageOperations.length>0&&e.push({type:201,ops:m.buffer.storageOperations}),e}();if(0===e.length)return;L(e),m.buffer={flushTimerID:void 0,lastFlushedAt:r,messages:[],storageOperations:[],presenceUpdates:null}}else clearTimeout(m.buffer.flushTimerID),m.buffer.flushTimerID=setTimeout(J,t.throttleDelay-i)}function Q(e){let{storageOperations:t}=m.buffer;for(let r of e)t.push(r);J()}let ee=null,et=null;function ei(e){var t;let r=new Map(m.unacknowledgedOps);if(0===e.items.length)throw Error("Internal error: cannot load storage without items");void 0!==m.root?function(e){if(void 0===m.root)return;let t=new Map;for(let[e,r]of m.pool.nodes)t.set(e,r._serialize());z(H(function(e,t){let r=[];return e.forEach((e,i)=>{t.get(i)||r.push({type:5,id:i})}),t.forEach((t,i)=>{let n=e.get(i);if(n)0===t.type&&(0!==n.type||er(t.data)!==er(n.data))&&r.push({type:3,id:i,data:t.data}),t.parentKey!==n.parentKey&&r.push({type:1,id:i,parentKey:e_(t.parentKey,"Parent key must not be missing")});else switch(t.type){case 3:r.push({type:8,id:i,parentId:t.parentId,parentKey:t.parentKey,data:t.data});break;case 1:r.push({type:2,id:i,parentId:t.parentId,parentKey:t.parentKey});break;case 0:if(void 0===t.parentId||void 0===t.parentKey)throw Error("Internal error. Cannot serialize storage root into an operation");r.push({type:4,id:i,parentId:t.parentId,parentKey:t.parentKey,data:t.data});break;case 2:r.push({type:7,id:i,parentId:t.parentId,parentKey:t.parentKey})}}),r}(t,new Map(e)),!1).updates)}(e.items):m.root=th._fromItems(e.items,m.pool);let i=M.get()?.canWrite??!0,n=m.undoStack.length;for(let e in m.initialStorage)void 0===m.root.get(e)&&(i?m.root.set(e,void 0===(t=m.initialStorage[e])?void 0:tg(t)?t.clone():O(t)):v(`Attempted to populate missing storage key '${e}', but current user has no write access`));m.undoStack.length=n,function(e){if(0===e.size)return;let t=[],r=H(Array.from(e.values()),!0);t.push({type:201,ops:r.ops}),z(r.updates),L(t)}(r),et?.(),ef(),b.storageDidLoad.notify()}async function en(){p.authValue&&ei({type:200,items:await d.streamStorage({roomId:l})})}function es(e){let r=m.buffer.messages;t.unstable_streamData?en():r.some(e=>200===e.type)||r.push({type:200}),e.flush&&J()}function eo(){return null===ee&&(es({flush:!0}),ee=new Promise(e=>{et=e}),ef()),ee}function el(){let e=m.root;return void 0!==e?e:(eo(),null)}async function ec(){return void 0!==m.root?Promise.resolve({root:m.root}):(await eo(),{root:e_(m.root)})}let eu=t.createSyncSource();function ed(){return void 0===m.root?null===ee?"not-loaded":"loading":0===m.unacknowledgedOps.size?"synchronized":"synchronizing"}let eh=ed();function ef(){let e=ed();eh!==e&&(eh=e,b.storageStatus.notify(e)),eu.setSyncStatus("synchronizing"===e?"synchronizing":"synchronized")}function ep(){return null!==M.get()}async function em(){for(;!ep();){let{promise:e,resolve:t}=U(),r=eE.self.subscribeOnce(t),i=eE.status.subscribeOnce(t);await e,r(),i()}}function eg(){return null!==el()}async function eb(){for(;!eg();)await ec()}let ev=Z.from(m.others.signal,e=>e.map((e,t)=>tk(`Other ${t}`,e))),eE={status:b.status.observable,lostConnection:b.lostConnection.observable,customEvent:b.customEvent.observable,others:b.others.observable,self:b.self.observable,myPresence:b.myPresence.observable,storage:b.storageBatch.observable,storageBatch:b.storageBatch.observable,history:b.history.observable,storageDidLoad:b.storageDidLoad.observable,storageStatus:b.storageStatus.observable,ydoc:b.ydoc.observable,comments:b.comments.observable,roomWillDestroy:b.roomWillDestroy.observable};async function eS(e){return d.getThreadsSince({roomId:l,since:e.since,signal:e.signal})}async function eA(e){return d.getThreads({roomId:l,query:e?.query,cursor:e?.cursor})}async function ex(e){return d.getThread({roomId:l,threadId:e})}async function eI(e){return d.createThread({roomId:l,threadId:e.threadId,commentId:e.commentId,metadata:e.metadata,body:e.body,attachmentIds:e.attachmentIds})}async function eT(e){return d.deleteThread({roomId:l,threadId:e})}async function ek({metadata:e,threadId:t}){return d.editThreadMetadata({roomId:l,threadId:t,metadata:e})}async function eO(e){return d.markThreadAsResolved({roomId:l,threadId:e})}async function eR(e){return d.markThreadAsUnresolved({roomId:l,threadId:e})}async function eC(e){return d.subscribeToThread({roomId:l,threadId:e})}async function eP(e){return d.unsubscribeFromThread({roomId:l,threadId:e})}async function eN(e){return d.createComment({roomId:l,threadId:e.threadId,commentId:e.commentId,body:e.body,attachmentIds:e.attachmentIds})}async function eD(e){return d.editComment({roomId:l,threadId:e.threadId,commentId:e.commentId,body:e.body,attachmentIds:e.attachmentIds})}async function eL({threadId:e,commentId:t}){return d.deleteComment({roomId:l,threadId:e,commentId:t})}async function eM({threadId:e,commentId:t,emoji:r}){return d.addReaction({roomId:l,threadId:e,commentId:t,emoji:r})}async function ej({threadId:e,commentId:t,emoji:r}){return await d.removeReaction({roomId:l,threadId:e,commentId:t,emoji:r})}async function eU(e,t={}){return d.uploadAttachment({roomId:l,attachment:e,signal:t.signal})}function eF(e){return d.getSubscriptionSettings({roomId:l,signal:e?.signal})}function eB(e){return d.updateSubscriptionSettings({roomId:l,settings:e})}async function eq(e){await d.markRoomInboxNotificationAsRead({roomId:l,inboxNotificationId:e})}let e$=t.createSyncSource();function ez(e){return e$.setSyncStatus("synchronizing"===e?"synchronizing":"synchronized")}return Object.defineProperty({[eY]:{get presenceBuffer(){return O(m.buffer.presenceUpdates?.data??null)},get undoStack(){return O(m.undoStack)},get nodeCount(){return m.pool.nodes.size},getYjsProvider:()=>m.yjsProvider,setYjsProvider(e){m.yjsProvider?.off("status",ez),m.yjsProvider=e,e?.on("status",ez),m.yjsProviderDidChange.notify()},yjsProviderDidChange:m.yjsProviderDidChange.observable,reportTextEditor:E,createTextMention:y,deleteTextMention:_,listTextVersions:A,listTextVersionsSince:x,getTextVersion:T,createTextVersion:C,executeContextualPrompt:P,getSelf_forDevTools:()=>B.get(),getOthers_forDevTools:()=>ev.get(),simulate:{explicitClose:e=>p._privateSendMachineEvent({type:"EXPLICIT_SOCKET_CLOSE",event:e}),rawSend:e=>p.send(e)},attachmentUrlsStore:d.getOrCreateAttachmentUrlsStore(l)},id:l,subscribe:(r=l,i=eE,n=t.errorEventSource,function(e,t,s){var o;if("string"==typeof e&&("my-presence"===(o=e)||"others"===o||"event"===o||"error"===o||"history"===o||"status"===o||"storage-status"===o||"lost-connection"===o||"connection"===o||"comments"===o)){if("function"!=typeof t)throw Error("Second argument must be a callback function");switch(e){case"event":return i.customEvent.subscribe(t);case"my-presence":return i.myPresence.subscribe(t);case"others":return i.others.subscribe(e=>{let{others:r,...i}=e;return t(r,i)});case"error":return n.subscribe(e=>{if(e.roomId===r)return t(e)});case"status":return i.status.subscribe(t);case"lost-connection":return i.lostConnection.subscribe(t);case"history":return i.history.subscribe(t);case"storage-status":return i.storageStatus.subscribe(t);case"comments":return i.comments.subscribe(t);default:return ew(e,`"${String(e)}" is not a valid event name`)}}if(void 0===t||"function"==typeof e)if("function"==typeof e)return i.storageBatch.subscribe(e);else throw Error("Please specify a listener callback");if(tb(e))return s?.isDeep?i.storageBatch.subscribe(r=>{let i=r.filter(t=>(function e(t,r){return t===r||"HasParent"===t.parent.type&&e(t.parent.node,r)})(t.node,e));i.length>0&&t(i)}):i.storageBatch.subscribe(r=>{for(let i of r)i.node._id===e._id&&t(i.node)});throw Error(`${String(e)} is not a value that can be subscribed to.`)}),connect:()=>p.connect(),reconnect:()=>p.reconnect(),disconnect:()=>p.disconnect(),destroy:()=>{let{roomWillDestroy:e,...t}=b;for(let e of Object.values(t))e.dispose();b.roomWillDestroy.notify(),m.yjsProvider?.off("status",ez),eu.destroy(),e$.destroy(),f(),p.destroy(),e.dispose()},updatePresence:function(e,t){let r={};for(let t in null===m.buffer.presenceUpdates&&(m.buffer.presenceUpdates={type:"partial",data:{}}),e){let i=e[t];void 0!==i&&(m.buffer.presenceUpdates.data[t]=i,r[t]=m.myPresence.get()[t])}m.myPresence.patch(e),m.activeBatch?(t?.addToHistory&&m.activeBatch.reverseOps.pushLeft({type:"presence",data:r}),m.activeBatch.updates.presence=!0):(J(),t?.addToHistory&&$([{type:"presence",data:r}]),z({presence:!0}))},updateYDoc:function(e,t,r){let i={type:301,update:e,guid:t,v2:r};m.buffer.messages.push(i),b.ydoc.notify(i),J()},broadcastEvent:function(e,t={shouldQueueEventIfNotReady:!1}){("connected"===p.getStatus()||t.shouldQueueEventIfNotReady)&&(m.buffer.messages.push({type:103,event:e}),J())},batch:function(e){let t;if(m.activeBatch)return e();m.activeBatch={ops:[],updates:{storageUpdates:new Map,presence:!1,others:[]},reverseOps:new tS};try{t=e()}finally{let e=m.activeBatch;m.activeBatch=null,e.reverseOps.length>0&&$(Array.from(e.reverseOps)),e.ops.length>0&&(m.redoStack.length=0),e.ops.length>0&&Q(e.ops),z(e.updates),J()}return t},history:{undo:function(){if(m.activeBatch)throw Error("undo is not allowed during a batch");let e=m.undoStack.pop();if(void 0===e)return;m.pausedHistory=null;let t=H(e,!0);for(let e of(z(t.updates),m.redoStack.push(t.reverse),W(),t.ops))"presence"!==e.type&&m.buffer.storageOperations.push(e);J()},redo:function(){if(m.activeBatch)throw Error("redo is not allowed during a batch");let e=m.redoStack.pop();if(void 0===e)return;m.pausedHistory=null;let t=H(e,!0);for(let e of(z(t.updates),m.undoStack.push(t.reverse),W(),t.ops))"presence"!==e.type&&m.buffer.storageOperations.push(e);J()},canUndo:V,canRedo:K,clear:function(){m.undoStack.length=0,m.redoStack.length=0},pause:function(){null===m.pausedHistory&&(m.pausedHistory=new tS)},resume:function(){let e=m.pausedHistory;m.pausedHistory=null,null!==e&&e.length>0&&q(Array.from(e))}},fetchYDoc:function(e,t,r){m.buffer.messages.find(i=>300===i.type&&i.vector===e&&i.guid===t&&i.v2===r)||m.buffer.messages.push({type:300,vector:e,guid:t,v2:r}),J()},getStorage:ec,getStorageSnapshot:el,getStorageStatus:ed,isPresenceReady:ep,isStorageReady:eg,waitUntilPresenceReady:N(em),waitUntilStorageReady:N(eb),events:eE,getStatus:()=>p.getStatus(),getSelf:()=>M.get(),getPresence:()=>m.myPresence.get(),getOthers:()=>m.others.get(),getThreads:eA,getThreadsSince:eS,getThread:ex,createThread:eI,deleteThread:eT,editThreadMetadata:ek,markThreadAsResolved:eO,markThreadAsUnresolved:eR,subscribeToThread:eC,unsubscribeFromThread:eP,createComment:eN,editComment:eD,deleteComment:eL,addReaction:eM,removeReaction:ej,prepareAttachment:function(e){return{type:"localAttachment",status:"idle",id:ea("at"),name:e.name,size:e.size,mimeType:e.type,file:e}},uploadAttachment:eU,getAttachmentUrl:function(e){return d.getAttachmentUrl({roomId:l,attachmentId:e})},getNotificationSettings:eF,getSubscriptionSettings:eF,updateNotificationSettings:eB,updateSubscriptionSettings:eB,markInboxNotificationAsRead:eq},eY,{enumerable:!1})}({initialPresence:("function"==typeof d.initialPresence?d.initialPresence(t):d.initialPresence)??{},initialStorage:("function"==typeof d.initialStorage?d.initialStorage(t):d.initialStorage)??{}},{roomId:t,throttleDelay:r,lostConnectionTimeout:i,backgroundKeepAliveTimeout:n,polyfills:e.polyfills,delegates:e.mockedDelegates??{createSocket:(c=e.polyfills?.WebSocket,e=>{let r=c??("undefined"==typeof WebSocket?void 0:WebSocket);if(void 0===r)throw new eN("To use Liveblocks client in a non-DOM environment, you need to provide a WebSocket polyfill.");let i=new URL(s);if(i.protocol="http:"===i.protocol?"ws":"wss",i.pathname="/v7",i.searchParams.set("roomId",t),"secret"===e.type)i.searchParams.set("tok",e.token.raw);else{if("public"!==e.type)return ew(e,"Unhandled case");i.searchParams.set("pubkey",e.publicApiKey)}return i.searchParams.set("version",o||"dev"),new r(i.toString())}),authenticate:async()=>l.getAuthValue({requestedScope:"room:read",roomId:t})},enableDebugLogging:e.enableDebugLogging,baseUrl:s,errorEventSource:B,largeMessageStrategy:e.largeMessageStrategy??(e.unstable_fallbackToHTTP?"experimental-fallback-to-http":void 0),unstable_streamData:!!e.unstable_streamData,roomHttpClient:g,createSyncSource:$}),f={room:h,unsubs:new Set};if(b.set(t,f),d.autoConnect??!0){if("undefined"==typeof atob){if(e.polyfills?.atob===void 0)throw Error("You need to polyfill atob to use the client in your environment. Please follow the instructions at https://liveblocks.io/docs/errors/liveblocks-client/atob-polyfill");global.atob=e.polyfills.atob}h.connect()}return y(f)},getRoom:function(e){return b.get(e)?.room||null},logout:function(){for(let{room:t}of(l.reset(),a.set(()=>void 0),b.values())){var e;"initial"!==(e=t.getStatus())&&"disconnected"!==e&&t.reconnect()}},getInboxNotifications:g.getInboxNotifications,getInboxNotificationsSince:g.getInboxNotificationsSince,getUnreadInboxNotificationsCount:g.getUnreadInboxNotificationsCount,markAllInboxNotificationsAsRead:g.markAllInboxNotificationsAsRead,markInboxNotificationAsRead:g.markInboxNotificationAsRead,deleteAllInboxNotifications:g.deleteAllInboxNotifications,deleteInboxNotification:g.deleteInboxNotification,getNotificationSettings:z,updateNotificationSettings:H,resolvers:{invalidateUsers:function(e){A.invalidate(e)},invalidateRoomsInfo:function(e){C.invalidate(e)},invalidateMentionSuggestions:function(){P.clear()}},getSyncStatus:function(){let e=j.get();return"synchronizing"===e?e:"synchronized"},events:{error:B,syncStatus:j},[eY]:{currentUserId:a,mentionSuggestionsCache:P,resolveMentionSuggestions:e.resolveMentionSuggestions,usersStore:A,roomsInfoStore:C,getRoomIds:()=>Array.from(b.keys()),httpClient:g,as:()=>V,createSyncSource:$,emitError:(e,t)=>{let r=tT.from(e,t);B.notify(r)||w(r.message)}}},eY,{enumerable:!1});return V})(n),[]);return(0,tZ.jsx)(rR,{client:s,children:t})}function rP(e){let t=rO(),r=re(e);useEffect3(()=>t.events.error.subscribe(e=>r.current(e)),[t,r])}var rN=()=>{},rD=e=>e,rL=Object.freeze([]);function rM(){return rL}function rj(){return null}function rU(e){return e.map(e=>e.connectionId)}function rF(e){let t=e[kInternal3].currentUserId.get();return void 0===t?"anonymous":t}var rB=new WeakMap;function rq(e){let t=rB.get(e);return t||(t=function(e){let t=rx(e),r=new el(e=>tK(async r=>{try{return await t.fetchRoomThreadsDeltaUpdate(e,r)}catch(t){throw g.warn(`Polling new threads for '${e}' failed: ${String(t)}`),t}},t5.ROOM_THREADS_POLL_INTERVAL,{maxStaleTimeMs:t5.ROOM_THREADS_MAX_STALE_TIME})),i=new el(e=>tK(async r=>{try{return await t.fetchRoomVersionsDeltaUpdate(e,r)}catch(t){throw g.warn(`Polling new history versions for '${e}' failed: ${String(t)}`),t}},t5.HISTORY_VERSIONS_POLL_INTERVAL,{maxStaleTimeMs:t5.HISTORY_VERSIONS_MAX_STALE_TIME})),n=new el(e=>tK(async r=>{try{return await t.refreshRoomSubscriptionSettings(e,r)}catch(t){throw g.warn(`Polling subscription settings for '${e}' failed: ${String(t)}`),t}},t5.ROOM_SUBSCRIPTION_SETTINGS_POLL_INTERVAL,{maxStaleTimeMs:t5.ROOM_SUBSCRIPTION_SETTINGS_MAX_STALE_TIME}));return{store:t,onMutationFailure:function(r,i,n){if(t.optimisticUpdates.remove(r),n instanceof D){if(403===n.status){let e=[n.message,n.details?.suggestion,n.details?.docs].filter(Boolean).join("\n");g.error(e)}e[eY].emitError(i,n)}else throw n},pollThreadsForRoomId:e=>{let t=r.getOrCreate(e);t&&(t.markAsStale(),t.pollNowIfStale())},getOrCreateThreadsPollerForRoomId:r.getOrCreate.bind(r),getOrCreateVersionsPollerForRoomId:i.getOrCreate.bind(i),getOrCreateSubscriptionSettingsPollerForRoomId:n.getOrCreate.bind(n)}}(e),rB.set(e,t)),t}function r$(e){let t=rO(),{id:r,stableEnterRoom:n}=e,s=rr({initialPresence:e.initialPresence,initialStorage:e.initialStorage,autoConnect:e.autoConnect??"undefined"!=typeof window}),[{room:o},a]=(0,i.useState)(()=>n(r,{...s,autoConnect:!1}));return(0,i.useEffect)(()=>{let{store:e}=rq(t);async function r(t){if(t.type===ex.THREAD_DELETED)return void e.deleteThread(t.threadId,null);let r=await o.getThread(t.threadId);if(!r.thread)return void e.deleteThread(t.threadId,null);let{thread:i,inboxNotification:n,subscription:s}=r,a=e.outputs.threads.get().getEvenIfDeleted(t.threadId);switch(t.type){case ex.COMMENT_EDITED:case ex.THREAD_METADATA_UPDATED:case ex.THREAD_UPDATED:case ex.COMMENT_REACTION_ADDED:case ex.COMMENT_REACTION_REMOVED:case ex.COMMENT_DELETED:if(!a)break;e.updateThreadifications([i],n?[n]:[],s?[s]:[]);break;case ex.COMMENT_CREATED:e.updateThreadifications([i],n?[n]:[],s?[s]:[])}}return o.events.comments.subscribe(e=>void r(e))},[t,o]),(0,i.useEffect)(()=>{let e=n(r,s);a(e);let{room:t,leave:i}=e;return s.autoConnect&&t.connect(),()=>{i()}},[r,s,n]),(0,tZ.jsx)(tQ.Provider,{value:o,children:e.children})}function rz(e){let t=t0();if(null===t&&!e?.allowOutsideRoom)throw Error("RoomProvider is missing from the React tree.");return t}function rH(){return rz().history}function rV(e,t){let r=rz(),i=r.events.others.subscribe;return t2(i,r.getOthers,rM,e??rD,t)}var rK=Symbol();function rW(){let e=rz(),t=e.events.storageDidLoad.subscribeOnce;return useSyncExternalStore3(t,e.getStorageSnapshot,rj)}function rG(e){let t=rO();return useCallback3(r=>{let i=new Date,{store:n,onMutationFailure:s}=rq(t),o=n.optimisticUpdates.add({type:"subscribe-to-thread",threadId:r,subscribedAt:i});t[kInternal3].httpClient.subscribeToThread({roomId:e,threadId:r}).then(e=>{n.createSubscription(e,o)},t=>s(o,{type:"SUBSCRIBE_TO_THREAD_ERROR",roomId:e,threadId:r},t))},[t,e])}function rX(e){let t=rO();return useCallback3(r=>{let i=new Date,{store:n,onMutationFailure:s}=rq(t),o=n.optimisticUpdates.add({type:"unsubscribe-from-thread",threadId:r,unsubscribedAt:i});t[kInternal3].httpClient.unsubscribeFromThread({roomId:e,threadId:r}).then(()=>{n.deleteSubscription(getSubscriptionKey2("thread",r),o)},t=>s(o,{type:"UNSUBSCRIBE_FROM_THREAD_ERROR",roomId:e,threadId:r},t))},[t,e])}function rY(){t9(),rn(rz().waitUntilPresenceReady())}function rJ(){t9(),rn(rz().waitUntilStorageReady())}function rZ(e){return void 0===e||e?.isLoading?e??{isLoading:!0}:e.error?e:(assert2(void 0!==e.data,"Unexpected missing attachment URL"),{isLoading:!1,url:e.data})}var rQ=function(e){let t=rO(),[r]=(0,i.useState)(()=>new Map),n=(0,i.useCallback)((e,i)=>{let n=r.get(e);if(n)return n;let s=t.enterRoom(e,i),o=s.leave;return s.leave=()=>{o(),r.delete(e)},r.set(e,s),s},[t,r]);return(0,tZ.jsx)(r$,{...e,stableEnterRoom:n})},r0=function(){let e=rz(),t=e.events.myPresence.subscribe,r=e.getPresence;return[(0,i.useSyncExternalStore)(t,r,r),e.updatePresence]};function r1(...e){return function(e,t){return rY(),rV(e,t)}(...e)}function r2(...e){return function(e,t){return rY(),function(e,t){let r=rz(),n=r.events.self.subscribe,s=r.getSelf,o=e??rD;return t2(n,s,rj,(0,i.useCallback)(e=>null!==e?o(e):null,[o]),t)}(e,t)}(...e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63913:(e,t,r)=>{"use strict";r.d(t,{Y:()=>g});var i=r(94752),n=r(23233),s=r(30409);let o=(0,r(24767).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var a=r(58559);function l({...e}){return(0,i.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function c({className:e,...t}){return(0,i.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,a.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function u({className:e,...t}){return(0,i.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,a.cn)("inline-flex items-center gap-1.5",e),...t})}function d({asChild:e,className:t,...r}){let n=e?s.DX:"a";return(0,i.jsx)(n,{"data-slot":"breadcrumb-link",className:(0,a.cn)("hover:text-foreground transition-colors",t),...r})}function h({className:e,...t}){return(0,i.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,a.cn)("text-foreground font-normal",e),...t})}function f({children:e,className:t,...r}){return(0,i.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,a.cn)("[&>svg]:size-3.5",t),...r,children:e??(0,i.jsx)(o,{})})}var p=r(61847),m=r(68741);let g=({pages:e,page:t,children:r})=>(0,i.jsxs)("header",{className:"flex h-16 shrink-0 items-center justify-between gap-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,i.jsx)(m.SidebarTrigger,{className:"-ml-1"}),(0,i.jsx)(p.Separator,{orientation:"vertical",className:"mr-2 h-4"}),(0,i.jsx)(l,{children:(0,i.jsxs)(c,{children:[e.map((e,t)=>(0,i.jsxs)(n.Fragment,{children:[t>0&&(0,i.jsx)(f,{className:"hidden md:block"}),(0,i.jsx)(u,{className:"hidden md:block",children:(0,i.jsx)(d,{href:"#",children:e})})]},e)),(0,i.jsx)(f,{className:"hidden md:block"}),(0,i.jsx)(u,{children:(0,i.jsx)(h,{children:t})})]})})]}),r]})},64959:(e,t,r)=>{"use strict";var i=r(57752),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=i.useState,o=i.useEffect,a=i.useLayoutEffect,l=i.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),i=s({inst:{value:r,getSnapshot:t}}),n=i[0].inst,u=i[1];return a(function(){n.value=r,n.getSnapshot=t,c(n)&&u({inst:n})},[e,r,t]),o(function(){return c(n)&&u({inst:n}),e(function(){c(n)&&u({inst:n})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:u},65698:(e,t)=>{"use strict";function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},70693:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},71280:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let i=r(32446)._(r(39928));function n(e,t){var r;let n={};"function"==typeof e&&(n.loader=e);let s={...n,...t};return(0,i.default)({...s,modules:null==(r=s.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74532:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return n},userAgent:function(){return o},userAgentFromString:function(){return s}});let i=function(e){return e&&e.__esModule?e:{default:e}}(r(60495));function n(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function s(e){return{...(0,i.default)(e),isBot:void 0!==e&&n(e)}}function o({headers:e}){return s(e.get("user-agent")||void 0)}},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77107:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return a}});let i=r(33485),n=r(7828),s=r(94767),o=r(58144);function a(e){let t=(0,o.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,i.removeTrailingSlash)(t)),e.buildId&&(t=(0,s.addPathSuffix)((0,n.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,n.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,s.addPathSuffix)(t,"/"):(0,i.removeTrailingSlash)(t)}},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80005:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return i}});let r=new WeakMap;function i(e,t){let i;if(!t)return{pathname:e};let n=r.get(t);n||(n=t.map(e=>e.toLowerCase()),r.set(t,n));let s=e.split("/",2);if(!s[1])return{pathname:e};let o=s[1].toLowerCase(),a=n.indexOf(o);return a<0?{pathname:e}:(i=t[a],{pathname:e=e.slice(i.length+1)||"/",detectedLocale:i})}},80481:e=>{"use strict";e.exports=require("node:readline")},80631:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return n}});let i=r(19487);function n(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,i.parsePath)(e);return r===t||r.startsWith(t+"/")}},81630:e=>{"use strict";e.exports=require("http")},82718:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var i,n;if(t===(null==(i=s.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(n=s.locales)?void 0:n.some(e=>e.toLowerCase()===r)))return s}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},83089:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return i},wellKnownProperties:function(){return s}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function n(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let s=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},85513:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return n}});let i=r(80631);function n(e,t){if(!(0,i.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},85600:(e,t,r)=>{Promise.resolve().then(r.bind(r,15006)),Promise.resolve().then(r.bind(r,8106)),Promise.resolve().then(r.bind(r,35069)),Promise.resolve().then(r.bind(r,86332)),Promise.resolve().then(r.bind(r,49389)),Promise.resolve().then(r.bind(r,29224)),Promise.resolve().then(r.bind(r,22683)),Promise.resolve().then(r.bind(r,85733)),Promise.resolve().then(r.bind(r,12350))},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},93880:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return d}});let i=r(46046),n=r(60200),s=r(19354),o=r(18375),a=r(46046),l=Symbol("internal response"),c=new Set([301,302,303,307,308]);function u(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[i,n]of e.request.headers)t.set("x-middleware-request-"+i,n),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{constructor(e,t={}){super(e,t);let r=this.headers,c=new Proxy(new a.ResponseCookies(r),{get(e,n,s){switch(n){case"delete":case"set":return(...s)=>{let o=Reflect.apply(e[n],e,s),l=new Headers(r);return o instanceof a.ResponseCookies&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,i.stringifyCookie)(e)).join(",")),u(t,l),o};default:return o.ReflectAdapter.get(e,n,s)}}});this[l]={cookies:c,url:t.url?new n.NextURL(t.url,{headers:(0,s.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[l].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!c.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let i="object"==typeof t?t:{},n=new Headers(null==i?void 0:i.headers);return n.set("Location",(0,s.validateURL)(e)),new d(null,{...i,headers:n,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,s.validateURL)(e)),u(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),u(e,t),new d(null,{...e,headers:t})}}},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},94767:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return n}});let i=r(19487);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,i.parsePath)(e);return""+r+t+n+s}},98752:(e,t,r)=>{Promise.resolve().then(r.bind(r,44568)),Promise.resolve().then(r.bind(r,34651)),Promise.resolve().then(r.bind(r,5567)),Promise.resolve().then(r.t.bind(r,21034,23)),Promise.resolve().then(r.t.bind(r,32787,23)),Promise.resolve().then(r.t.bind(r,47390,23)),Promise.resolve().then(r.bind(r,93665)),Promise.resolve().then(r.bind(r,61847)),Promise.resolve().then(r.bind(r,68741))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5319,6239,2923,25,7873,3887,5480,1359,3319,2644,277,1988,5432,4841,8482,864,7209,6648],()=>r(10177));module.exports=i})();