(()=>{var e={};e.id=6158,e.ids=[6158],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23908:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>l});var n=r(26142),a=r(94327),i=r(34862),o=r(37838),u=r(18815),c=r(26239);async function d(){try{let{userId:e}=await (0,o.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});let r=await u.database.extensionSession.findMany({where:{userId:t.id,isActive:!0,lastActiveAt:{gte:new Date(Date.now()-3e5)}},orderBy:{lastActiveAt:"desc"}}),s=await u.database.usageMetrics.aggregate({where:{userId:t.id,date:{gte:new Date(Date.now()-864e5)}},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),n=await u.database.usageMetrics.aggregate({where:{userId:t.id},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),a=r.length>0,i=r[0],d={status:{connected:a,lastActive:i?.lastActiveAt||null,activeSessions:r.length,health:a?"healthy":"disconnected"},user:{id:e,subscriptionTier:t.subscriptionTier||"FREE",subscriptionStatus:t.subscriptionStatus||"ACTIVE",extensionEnabled:t.extensionEnabled||!1},usage:{recent24h:{tokensUsed:s._sum.tokensUsed||0,requestsMade:s._sum.requestsMade||0,costAccrued:s._sum.costAccrued||0},total:{tokensUsed:n._sum.tokensUsed||0,requestsMade:n._sum.requestsMade||0,costAccrued:n._sum.costAccrued||0}},sessions:r.map(e=>({id:e.id,sessionId:e.sessionId,extensionVersion:e.extensionVersion,vscodeVersion:e.vscodeVersion,platform:e.platform,lastActiveAt:e.lastActiveAt,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade})),serverTime:new Date().toISOString()};return c.NextResponse.json(d)}catch(e){return console.error("Extension status error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(){try{let{userId:e}=await (0,o.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});return await u.database.user.update({where:{id:t.id},data:{lastActiveAt:new Date}}),c.NextResponse.json({success:!0,timestamp:new Date().toISOString(),message:"Heartbeat received"})}catch(e){return console.error("Extension heartbeat error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/extension/status/route",pathname:"/api/extension/status",filename:"route",bundlePath:"app/api/extension/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\status\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:x}=p;function A(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>D});var s=r(8741),n=r(62923),a=r(54726),i=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),d=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,a;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((a=e,`[clerk debug end: ${a}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},l=(e,t)=>(...r)=>{let s=("string"==typeof e?d(e,c):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),g=r(37081),h=r(27322),x=r(6264);function A(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return h.r0.stringify(r,{pad:!1})}async function m(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,h.hJ)(r.algorithm);if(!n)return{errors:[new x.xy(`Unsupported algorithm ${r.algorithm}`)]};let a=await (0,h.Fh)(t,n,"sign"),i=r.header||{typ:"JWT"};i.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=A(i),u=A(e),c=`${o}.${u}`;try{let e=await h.fA.crypto.subtle.sign(n,a,s.encode(c));return{data:`${c}.${h.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new x.xy(e?.message)]}}}(0,g.C)(h.J0);var w=(0,g.R)(h.iU);(0,g.C)(m),(0,g.C)(h.nk);var f=r(97495),v=r(60606);function b(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,i,o;let u,c=(0,f.NE)(e,"AuthStatus"),d=(0,f.NE)(e,"AuthToken"),l=(0,f.NE)(e,"AuthMessage"),p=(0,f.NE)(e,"AuthReason"),g=(0,f.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:c,authMessage:l,authReason:p});let h=(0,f._b)(e,s.AA.Headers.ClerkRequestData),x=(0,v.Kk)(h),A={secretKey:(null==r?void 0:r.secretKey)||x.secretKey||a.rB,publishableKey:x.publishableKey||a.At,apiUrl:a.H$,apiVersion:a.mG,authStatus:c,authMessage:l,authReason:p,treatPendingAsSignedOut:t};if(null==(i=r.logger)||i.debug("auth options",A),c&&c===s.TD.SignedIn){(0,v._l)(d,A.secretKey,g);let e=w(d);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(A,e.raw.text,e.payload)}else u=(0,s.wI)(A);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(A,u.sessionStatus)),u}var y=r(68478);let U=({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>async(n,a)=>{if((0,i.zz)((0,f._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,f.Zd)(n)){p.M&&(0,v.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,v.$K)(n,t)}return b(n,{...a,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>(r,n)=>((0,i.zz)((0,f._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,v.$K)(r,t),b(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,y.AG)()});let q={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},S=e=>{var t,r;return!!e.headers.get(q.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(q.Headers.NextAction))},k=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||j(e)||E(e)},j=e=>!!e.headers.get(q.Headers.NextUrl)&&!S(e)||N(),N=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},E=e=>!!e.headers.get(q.Headers.NextjsData);var T=r(23056);let D=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,T.TG)(),i=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await U({debugLoggerName:"auth()",noAuthStatusMessage:(0,y.sd)("auth",await i())})(t,{treatPendingAsSignedOut:e}),u=(0,f.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},i=(0,s.tl)(t),c=i.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||i.cookies.get(s.AA.Cookies.DevBrowser),d=(0,f._b)(t,s.AA.Headers.ClerkRequestData),l=(0,v.Kk)(d);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:c,baseUrl:i.clerkUrl.toString(),publishableKey:l.publishableKey||a.At,signInUrl:l.signInUrl||a.qW,signUpUrl:l.signUpUrl||a.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};D.protect=async(...e)=>{r(1447);let t=await (0,T.TG)(),s=await D();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:a}=e;return async(...e)=>{var i,o,u,c,d,l;let p=(null==(i=e[0])?void 0:i.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],g=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),h=(null==(d=e[0])?void 0:d.unauthorizedUrl)||(null==(l=e[1])?void 0:l.unauthorizedUrl),x=()=>h?s(h):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:x():r.has(p)?r:x():r:g?s(g):k(a)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(23908));module.exports=s})();