(()=>{var e={};e.id=8003,e.ids=[8003],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19257:(e,t,r)=>{"use strict";r.r(t),r.d(t,{showBetaFeature:()=>u});var n=r(51153);let s=(0,r(28364).H)(),i=s.NEXT_PUBLIC_POSTHOG_KEY&&s.NEXT_PUBLIC_POSTHOG_HOST?new n.f2(s.NEXT_PUBLIC_POSTHOG_KEY,{host:s.NEXT_PUBLIC_POSTHOG_HOST,flushAt:1,flushInterval:0}):null;var a=r(37838),o=r(8392);let u=(e=>(0,o.Jt)({key:e,defaultValue:!1,async decide(){let{userId:t}=await (0,a.j)();return t?(i?await i.isFeatureEnabled(e,t):null)??this.defaultValue:this.defaultValue}}))("showBetaFeature")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},28364:(e,t,r)=>{"use strict";r.d(t,{H:()=>i});var n=r(71166),s=r(25);let i=()=>(0,n.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:s.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:s.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:s.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35657:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var n={};r.r(n),r.d(n,{GET:()=>c});var s=r(26142),i=r(94327),a=r(34862),o=r(26818),u=r(26239),l=r(19257);let c=async e=>{if(!await (0,o.uX)(e.headers.get("Authorization")))return u.NextResponse.json(null,{status:401});let t=Object.fromEntries(Object.values(l).map(e=>[e.key,{origin:e.origin,description:e.description,options:e.options}]));return u.NextResponse.json({definitions:t})},d=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/.well-known/vercel/flags/route",pathname:"/.well-known/vercel/flags",filename:"route",bundlePath:"app/.well-known/vercel/flags/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\.well-known\\vercel\\flags\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:g}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>j});var n=r(8741),s=r(62923),i=r(54726),a=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},l=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),c=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){var s,i;for(let n of(console.log((s=e,`[clerk debug start: ${s}]`)),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),s=r.encode(e).slice(0,4096);return n.decode(s).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((i=e,`[clerk debug end: ${i}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},d=(e,t)=>(...r)=>{let n=("string"==typeof e?c(e,l):e)(),s=t(n);try{let e=s(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};var p=r(74365),h=r(37081),g=r(27322),f=r(6264);function v(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return g.r0.stringify(r,{pad:!1})}async function x(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let n=new TextEncoder,s=(0,g.hJ)(r.algorithm);if(!s)return{errors:[new f.xy(`Unsupported algorithm ${r.algorithm}`)]};let i=await (0,g.Fh)(t,s,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=v(a),u=v(e),l=`${o}.${u}`;try{let e=await g.fA.crypto.subtle.sign(s,i,n.encode(l));return{data:`${l}.${g.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new f.xy(e?.message)]}}}(0,h.C)(g.J0);var m=(0,h.R)(g.iU);(0,h.C)(x),(0,h.C)(g.nk);var w=r(97495),y=r(60606);function b(e,{treatPendingAsSignedOut:t=!0,...r}={}){var s,a,o;let u,l=(0,w.NE)(e,"AuthStatus"),c=(0,w.NE)(e,"AuthToken"),d=(0,w.NE)(e,"AuthMessage"),p=(0,w.NE)(e,"AuthReason"),h=(0,w.NE)(e,"AuthSignature");null==(s=r.logger)||s.debug("headers",{authStatus:l,authMessage:d,authReason:p});let g=(0,w._b)(e,n.AA.Headers.ClerkRequestData),f=(0,y.Kk)(g),v={secretKey:(null==r?void 0:r.secretKey)||f.secretKey||i.rB,publishableKey:f.publishableKey||i.At,apiUrl:i.H$,apiVersion:i.mG,authStatus:l,authMessage:d,authReason:p,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",v),l&&l===n.TD.SignedIn){(0,y._l)(c,v.secretKey,h);let e=m(c);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,n.Z5)(v,e.raw.text,e.payload)}else u=(0,n.wI)(v);return t&&"pending"===u.sessionStatus&&(u=(0,n.wI)(v,u.sessionStatus)),u}var A=r(68478);let E=({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>async(s,i)=>{if((0,a.zz)((0,w._b)(s,n.AA.Headers.EnableDebug))&&e.enable(),!(0,w.Zd)(s)){p.M&&(0,y.$K)(s,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,y.$K)(s,t)}return b(s,{...i,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>(r,s)=>((0,a.zz)((0,w._b)(r,n.AA.Headers.EnableDebug))&&e.enable(),(0,y.$K)(r,t),b(r,{...s,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,A.AG)()});let _={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},T=e=>{var t,r;return!!e.headers.get(_.Headers.NextUrl)&&((null==(t=e.headers.get(n.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(n.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(_.Headers.NextAction))},S=e=>{var t;return"document"===e.headers.get(n.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(n.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(n.AA.Headers.Accept))?void 0:t.includes("text/html"))||U(e)||O(e)},U=e=>!!e.headers.get(_.Headers.NextUrl)&&!T(e)||N(),N=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},O=e=>!!e.headers.get(_.Headers.NextjsData);var P=r(23056);let j=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,P.TG)(),a=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await E({debugLoggerName:"auth()",noAuthStatusMessage:(0,A.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),u=(0,w.NE)(t,"ClerkUrl"),l=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,n.tl)(t),l=a.clerkUrl.searchParams.get(n.AA.QueryParameters.DevBrowser)||a.cookies.get(n.AA.Cookies.DevBrowser),c=(0,w._b)(t,n.AA.Headers.ClerkRequestData),d=(0,y.Kk)(c);return[(0,n.vH)({redirectAdapter:s.redirect,devBrowserToken:l,baseUrl:a.clerkUrl.toString(),publishableKey:d.publishableKey||i.At,signInUrl:d.signInUrl||i.qW,signUpUrl:d.signUpUrl||i.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=l(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=l(e);return t.redirectToSignUp({returnBackUrl:r})}})};j.protect=async(...e)=>{r(1447);let t=await (0,P.TG)(),n=await j();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:n,notFound:s,request:i}=e;return async(...e)=>{var a,o,u,l,c,d;let p=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],h=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(l=e[1])?void 0:l.unauthenticatedUrl),g=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),f=()=>g?n(g):s();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:f():r.has(p)?r:f():r:h?n(h):S(i)?t():s()}})({request:t,authObject:n,redirectToSignIn:n.redirectToSignIn,notFound:s.notFound,redirect:s.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},59986:(e,t,r)=>{"use strict";function n(e,t){if(e instanceof Promise)throw Error(t)}function s(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},s="object"==typeof e.server?e.server:{},i="object"==typeof e.shared?e.shared:{},a=e.isServer??("undefined"==typeof window||"Deno"in window),o=a?{...s,...i,...r}:{...r,...i},u=e.createFinalSchema?.(o,a)["~standard"].validate(t)??function(e,t){let r={},s=[];for(let i in e){let a=e[i]["~standard"].validate(t[i]);if(n(a,`Validation must be synchronous, but ${i} returned a Promise.`),a.issues){s.push(...a.issues.map(e=>({...e,path:[i,...e.path??[]]})));continue}r[i]=a.value}return s.length?{issues:s}:{value:r}}(o,t);n(u,"Validation must be synchronous");let l=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),c=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(u.issues)return l(u.issues);let d=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in i),p=e=>a||!d(e),h=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),u.value),{get(e,t){if("string"==typeof t&&!h(t))return p(t)?Reflect.get(e,t):c(t)}})}r.d(t,{w:()=>s})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71166:(e,t,r)=>{"use strict";r.d(t,{w:()=>s});var n=r(59986);function s(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},s=e.shared,i=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return(0,n.w)({...e,shared:s,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:i})}},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},80481:e=>{"use strict";e.exports=require("node:readline")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[5319,6239,2923,25,7873,3887,5432],()=>r(35657));module.exports=n})();