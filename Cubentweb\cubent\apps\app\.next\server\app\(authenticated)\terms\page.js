(()=>{var e={};e.id=2731,e.ids=[2731],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},9091:(e,t,r)=>{Promise.resolve().then(r.bind(r,70271))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},16985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>l,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(57864),i=r(94327),n=r(70814),a=r(17984),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);r.d(t,o);let c={children:["",{children:["(authenticated)",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64990)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\terms\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\terms\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(authenticated)/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34069:(e,t,r)=>{"use strict";r.d(t,{w:()=>c});var s=r(81121),i=r.n(s);let n="next-forge",a={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,c=({title:e,description:t,image:r,...s})=>{let c=`${e} | ${n}`,d={title:c,description:t,applicationName:n,metadataBase:o?new URL(`https://${o}`):void 0,authors:[a],creator:a.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:c},openGraph:{title:c,description:t,type:"website",siteName:n,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},l=i()(d,s);return r&&l.openGraph&&(l.openGraph.images=[{url:r,width:1200,height:630,alt:e}]),l}},34311:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19161).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55677:(e,t,r)=>{"use strict";r.d(t,{TermsAcceptance:()=>u});var s=r(99730),i=r(74938),n=r(58940),a=r(87785),o=r(57752),c=r(22683),d=r(62067),l=r(34311);function u({userId:e,termsAccepted:t,termsAcceptedAt:r}){let[u,p]=(0,o.useState)(!1),h=async()=>{p(!0);try{(await fetch("/api/terms/accept",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e})})).ok?(c.toast.success("Terms accepted successfully!"),setTimeout(()=>{let e=`vscode://cubent.cubent/connect?website=${encodeURIComponent(window.location.origin)}&termsAccepted=true`;window.open(e,"_blank"),c.toast.success("Opening VS Code extension...")},1e3),setTimeout(()=>{window.location.href="/profile"},3e3)):c.toast.error("Failed to accept terms")}catch(e){c.toast.error("Failed to accept terms")}finally{p(!1)}};return(0,s.jsxs)(n.Zp,{className:t?"border-green-200 bg-green-50 dark:bg-green-950":"border-orange-200 bg-orange-50 dark:bg-orange-950",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[t?(0,s.jsx)(d.A,{className:"h-5 w-5 text-green-600"}):(0,s.jsx)(l.A,{className:"h-5 w-5 text-orange-600"}),(0,s.jsx)(n.ZB,{children:t?"Terms Accepted":"Accept Terms to Continue"})]}),(0,s.jsx)(n.BT,{children:t?"You have accepted the terms of service and can use the extension.":"You must accept these terms to connect your VS Code extension."})]}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:"Status:"}),(0,s.jsx)(a.E,{variant:t?"default":"secondary",children:t?"Accepted":"Pending"})]}),r&&(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:"Accepted on:"}),(0,s.jsx)("span",{className:"text-sm",children:new Date(r).toLocaleDateString()})]}),!t&&(0,s.jsxs)("div",{className:"pt-4",children:[(0,s.jsx)(i.$,{onClick:h,disabled:u,className:"w-full",size:"lg",children:u?"Accepting Terms...":"Accept Terms and Connect Extension"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2 text-center",children:"By clicking this button, you agree to the terms of service above and authorize the connection between your VS Code extension and this website."})]}),t&&(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)(i.$,{variant:"outline",className:"w-full",onClick:()=>window.location.href="/profile",children:"Go to Profile"})})]})]})}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58940:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>a});var s=r(99730);r(57752);var i=r(83590);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}},59988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>s.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>s.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>i.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>s.at});var s=r(54841),i=r(44089)},62067:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19161).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64990:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>p});var s=r(94752),i=r(37838),n=r(1359),a=r(18815),o=r(34069),c=r(62923),d=r(70271);let l="Terms of Service",u="Terms of service for using the Cubent VS Code extension.",p=(0,o.w)({title:l,description:u}),h=async()=>{let{userId:e}=await (0,i.j)(),t=await (0,n.N)();e&&t||(0,c.redirect)("/sign-in");let r=await a.database.user.findUnique({where:{clerkId:e}});return r||(r=await a.database.user.create({data:{clerkId:e,email:t.emailAddresses[0]?.emailAddress||"",name:`${t.firstName||""} ${t.lastName||""}`.trim()||null,picture:t.imageUrl}})),(0,s.jsx)("div",{className:"container mx-auto p-6 max-w-4xl",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:l}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:u})]}),(0,s.jsxs)("div",{className:"prose prose-gray dark:prose-invert max-w-none",children:[(0,s.jsx)("h2",{children:"1. Acceptance of Terms"}),(0,s.jsx)("p",{children:"By using the Cubent VS Code extension and connecting it to this website, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our services."}),(0,s.jsx)("h2",{children:"2. Description of Service"}),(0,s.jsx)("p",{children:"Cubent is an AI-powered coding assistant that integrates with Visual Studio Code. Our service includes:"}),(0,s.jsxs)("ul",{children:[(0,s.jsx)("li",{children:"AI-powered code generation and completion"}),(0,s.jsx)("li",{children:"Code analysis and suggestions"}),(0,s.jsx)("li",{children:"Integration with multiple AI models"}),(0,s.jsx)("li",{children:"Usage analytics and reporting"}),(0,s.jsx)("li",{children:"Settings synchronization between devices"})]}),(0,s.jsx)("h2",{children:"3. User Accounts and Data"}),(0,s.jsx)("p",{children:"When you connect your extension to this website, we collect and store:"}),(0,s.jsxs)("ul",{children:[(0,s.jsx)("li",{children:"Your account information (name, email, profile picture)"}),(0,s.jsx)("li",{children:"Extension usage metrics (tokens used, requests made, costs)"}),(0,s.jsx)("li",{children:"Your extension settings and preferences"}),(0,s.jsx)("li",{children:"Session information for active connections"})]}),(0,s.jsx)("h2",{children:"4. Privacy and Data Protection"}),(0,s.jsx)("p",{children:"We are committed to protecting your privacy. Your code and personal data are handled according to our Privacy Policy. We do not:"}),(0,s.jsxs)("ul",{children:[(0,s.jsx)("li",{children:"Store or analyze your source code"}),(0,s.jsx)("li",{children:"Share your personal information with third parties without consent"}),(0,s.jsx)("li",{children:"Use your data for training AI models"})]}),(0,s.jsx)("h2",{children:"5. Usage Limitations"}),(0,s.jsx)("p",{children:"Your use of the service is subject to certain limitations:"}),(0,s.jsxs)("ul",{children:[(0,s.jsx)("li",{children:"Fair use policies for API requests and token usage"}),(0,s.jsx)("li",{children:"Subscription tier limitations"}),(0,s.jsx)("li",{children:"Rate limiting to prevent abuse"})]}),(0,s.jsx)("h2",{children:"6. Subscription and Billing"}),(0,s.jsx)("p",{children:"Some features require a paid subscription. By subscribing, you agree to:"}),(0,s.jsxs)("ul",{children:[(0,s.jsx)("li",{children:"Pay all applicable fees"}),(0,s.jsx)("li",{children:"Automatic renewal unless cancelled"}),(0,s.jsx)("li",{children:"Our refund and cancellation policies"})]}),(0,s.jsx)("h2",{children:"7. Intellectual Property"}),(0,s.jsx)("p",{children:"The Cubent extension and website are protected by intellectual property laws. You retain ownership of your code and content, while we retain ownership of our software and services."}),(0,s.jsx)("h2",{children:"8. Prohibited Uses"}),(0,s.jsx)("p",{children:"You may not use our service to:"}),(0,s.jsxs)("ul",{children:[(0,s.jsx)("li",{children:"Generate malicious or harmful code"}),(0,s.jsx)("li",{children:"Violate any laws or regulations"}),(0,s.jsx)("li",{children:"Infringe on others' intellectual property rights"}),(0,s.jsx)("li",{children:"Attempt to reverse engineer or hack our systems"})]}),(0,s.jsx)("h2",{children:"9. Service Availability"}),(0,s.jsx)("p",{children:"We strive to maintain high availability but cannot guarantee uninterrupted service. We may perform maintenance, updates, or experience outages that temporarily affect service availability."}),(0,s.jsx)("h2",{children:"10. Limitation of Liability"}),(0,s.jsx)("p",{children:"Our liability is limited to the maximum extent permitted by law. We are not responsible for any indirect, incidental, or consequential damages arising from your use of our service."}),(0,s.jsx)("h2",{children:"11. Changes to Terms"}),(0,s.jsx)("p",{children:"We may update these terms from time to time. We will notify you of significant changes and may require you to accept updated terms to continue using the service."}),(0,s.jsx)("h2",{children:"12. Termination"}),(0,s.jsx)("p",{children:"Either party may terminate this agreement at any time. Upon termination, your access to the service will be discontinued, and we may delete your data according to our retention policies."}),(0,s.jsx)("h2",{children:"13. Contact Information"}),(0,s.jsx)("p",{children:"If you have questions about these terms, please contact <NAME_EMAIL>."}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Last updated: ",new Date().toLocaleDateString()]})]}),(0,s.jsx)(d.TermsAcceptance,{userId:r.id,termsAccepted:r.termsAccepted,termsAcceptedAt:r.termsAcceptedAt})]})})}},70271:(e,t,r)=>{"use strict";r.d(t,{TermsAcceptance:()=>s});let s=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call TermsAcceptance() from the server but TermsAcceptance is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\terms\\components\\terms-acceptance.tsx","TermsAcceptance")},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74747:(e,t,r)=>{Promise.resolve().then(r.bind(r,55677))},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87785:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var s=r(99730);r(57752);var i=r(58576),n=r(72795),a=r(83590);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:r=!1,...n}){let c=r?i.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,a.cn)(o({variant:t}),e),...n})}},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,1359,3319,2644,277,1988,5432,4841,8482,1121,864,7209,6648],()=>r(16985));module.exports=s})();