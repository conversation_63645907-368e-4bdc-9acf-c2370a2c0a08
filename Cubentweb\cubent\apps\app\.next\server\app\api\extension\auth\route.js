(()=>{var e={};e.id=7466,e.ids=[7466],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98209:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>v,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>h,POST:()=>l});var i=r(26142),n=r(94327),o=r(34862),a=r(37838),u=r(1359),c=r(18815),p=r(26239),d=r(25);let x=d.z.object({extensionVersion:d.z.string(),sessionId:d.z.string()});async function l(e){try{let{userId:t}=await (0,a.j)(),r=await (0,u.N)();if(!t||!r)return p.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),{extensionVersion:i,sessionId:n}=x.parse(s),o=await c.database.user.findUnique({where:{clerkId:t},include:{extensionSessions:{where:{isActive:!0}}}});if(o||(o=await c.database.user.create({data:{clerkId:t,email:r.emailAddresses[0]?.emailAddress||"",name:`${r.firstName||""} ${r.lastName||""}`.trim()||null,picture:r.imageUrl},include:{extensionSessions:{where:{isActive:!0}}}})),!o.termsAccepted)return p.NextResponse.json({error:"Terms not accepted",requiresTermsAcceptance:!0,redirectUrl:"/terms"},{status:403});return await c.database.extensionSession.upsert({where:{userId_sessionId:{userId:o.id,sessionId:n}},update:{isActive:!0,lastActiveAt:new Date},create:{userId:o.id,sessionId:n,isActive:!0,lastActiveAt:new Date}}),await c.database.user.update({where:{id:o.id},data:{lastExtensionSync:new Date}}),p.NextResponse.json({success:!0,user:{id:o.id,name:o.name,email:o.email,picture:o.picture,subscriptionTier:o.subscriptionTier,subscriptionStatus:o.subscriptionStatus,extensionSettings:o.extensionSettings,preferences:o.preferences},extensionApiKey:o.extensionApiKey})}catch(e){return console.error("Extension auth error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let t=e.headers.get("authorization"),r=null;if(t?.startsWith("Bearer ")){let e=t.substring(7),s=await c.database.pendingLogin.findFirst({where:{token:e,expiresAt:{gt:new Date}}});if(!s)return p.NextResponse.json({error:"Invalid or expired token"},{status:401});r=s.userId}else r=(await (0,a.j)()).userId;if(!r)return p.NextResponse.json({error:"Unauthorized"},{status:401});let s=await c.database.user.findUnique({where:{clerkId:r},include:{extensionSessions:{where:{isActive:!0},orderBy:{lastActiveAt:"desc"}}}});if(!s)return p.NextResponse.json({error:"User not found"},{status:404});return p.NextResponse.json({user:{id:s.id,name:s.name,email:s.email,picture:s.picture,subscriptionTier:s.subscriptionTier,subscriptionStatus:s.subscriptionStatus,termsAccepted:s.termsAccepted,lastExtensionSync:s.lastExtensionSync},activeSessions:s.extensionSessions.length,isExtensionConnected:s.extensionSessions.length>0})}catch(e){return p.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/auth/route",pathname:"/api/extension/auth",filename:"route",bundlePath:"app/api/extension/auth/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\auth\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:w,serverHooks:v}=m;function f(){return(0,o.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:w})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,1359,864],()=>r(98209));module.exports=s})();