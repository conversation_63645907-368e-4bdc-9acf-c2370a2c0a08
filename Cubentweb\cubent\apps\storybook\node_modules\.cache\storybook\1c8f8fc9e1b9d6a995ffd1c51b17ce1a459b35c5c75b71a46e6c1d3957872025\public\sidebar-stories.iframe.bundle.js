"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["sidebar-stories"],{

/***/ "../../packages/design-system/components/ui/avatar.tsx":
/*!*************************************************************!*\
  !*** ../../packages/design-system/components/ui/avatar.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Avatar: () => (/* binding */ Avatar),
/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),
/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ "../../node_modules/.pnpm/@radix-ui+react-avatar@1.1._2102b0ade537f1e2a172941acff2e7b1/node_modules/@radix-ui/react-avatar/dist/index.mjs");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";




function Avatar({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {
        "data-slot": "avatar",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\avatar.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = Avatar;
function AvatarImage({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {
        "data-slot": "avatar-image",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("aspect-square size-full", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\avatar.tsx",
        lineNumber: 29,
        columnNumber: 5
    }, this);
}
_c1 = AvatarImage;
function AvatarFallback({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {
        "data-slot": "avatar-fallback",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-muted flex size-full items-center justify-center rounded-full", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\avatar.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
}
_c2 = AvatarFallback;

Avatar.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Avatar"
};
AvatarImage.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "AvatarImage"
};
AvatarFallback.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "AvatarFallback"
};
var _c, _c1, _c2;
__webpack_require__.$Refresh$.register(_c, "Avatar");
__webpack_require__.$Refresh$.register(_c1, "AvatarImage");
__webpack_require__.$Refresh$.register(_c2, "AvatarFallback");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/breadcrumb.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/design-system/components/ui/breadcrumb.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Breadcrumb: () => (/* binding */ Breadcrumb),
/* harmony export */   BreadcrumbEllipsis: () => (/* binding */ BreadcrumbEllipsis),
/* harmony export */   BreadcrumbItem: () => (/* binding */ BreadcrumbItem),
/* harmony export */   BreadcrumbLink: () => (/* binding */ BreadcrumbLink),
/* harmony export */   BreadcrumbList: () => (/* binding */ BreadcrumbList),
/* harmony export */   BreadcrumbPage: () => (/* binding */ BreadcrumbPage),
/* harmony export */   BreadcrumbSeparator: () => (/* binding */ BreadcrumbSeparator)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ "../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js");
/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");






function Breadcrumb({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("nav", {
        "aria-label": "breadcrumb",
        "data-slot": "breadcrumb",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
        lineNumber: 8,
        columnNumber: 10
    }, this);
}
_c = Breadcrumb;
function BreadcrumbList({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("ol", {
        "data-slot": "breadcrumb-list",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c1 = BreadcrumbList;
function BreadcrumbItem({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("li", {
        "data-slot": "breadcrumb-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("inline-flex items-center gap-1.5", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
_c2 = BreadcrumbItem;
function BreadcrumbLink({ asChild, className, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : "a";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "breadcrumb-link",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("hover:text-foreground transition-colors", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_c3 = BreadcrumbLink;
function BreadcrumbPage({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
        "data-slot": "breadcrumb-page",
        role: "link",
        "aria-disabled": "true",
        "aria-current": "page",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("text-foreground font-normal", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
        lineNumber: 54,
        columnNumber: 5
    }, this);
}
_c4 = BreadcrumbPage;
function BreadcrumbSeparator({ children, className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("li", {
        "data-slot": "breadcrumb-separator",
        role: "presentation",
        "aria-hidden": "true",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("[&>svg]:size-3.5", className),
        ...props,
        children: children !== null && children !== void 0 ? children : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {}, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
            lineNumber: 78,
            columnNumber: 20
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
_c5 = BreadcrumbSeparator;
function BreadcrumbEllipsis({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
        "data-slot": "breadcrumb-ellipsis",
        role: "presentation",
        "aria-hidden": "true",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("flex size-9 items-center justify-center", className),
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
                lineNumber: 95,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "sr-only",
                children: "More"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\breadcrumb.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
_c6 = BreadcrumbEllipsis;

Breadcrumb.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Breadcrumb"
};
BreadcrumbList.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "BreadcrumbList"
};
BreadcrumbItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "BreadcrumbItem"
};
BreadcrumbLink.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "BreadcrumbLink",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        }
    }
};
BreadcrumbPage.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "BreadcrumbPage"
};
BreadcrumbSeparator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "BreadcrumbSeparator"
};
BreadcrumbEllipsis.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "BreadcrumbEllipsis"
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__webpack_require__.$Refresh$.register(_c, "Breadcrumb");
__webpack_require__.$Refresh$.register(_c1, "BreadcrumbList");
__webpack_require__.$Refresh$.register(_c2, "BreadcrumbItem");
__webpack_require__.$Refresh$.register(_c3, "BreadcrumbLink");
__webpack_require__.$Refresh$.register(_c4, "BreadcrumbPage");
__webpack_require__.$Refresh$.register(_c5, "BreadcrumbSeparator");
__webpack_require__.$Refresh$.register(_c6, "BreadcrumbEllipsis");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/button.tsx":
/*!*************************************************************!*\
  !*** ../../packages/design-system/components/ui/button.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Button: () => (/* binding */ Button),
/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ "../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs");
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");






const buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : "button";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "button",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = Button;

Button.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Button",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Button");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/collapsible.tsx":
/*!******************************************************************!*\
  !*** ../../packages/design-system/components/ui/collapsible.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Collapsible: () => (/* binding */ Collapsible),
/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),
/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-collapsible */ "../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";


function Collapsible({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.Root, {
        "data-slot": "collapsible",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\collapsible.tsx",
        lineNumber: 8,
        columnNumber: 10
    }, this);
}
_c = Collapsible;
function CollapsibleTrigger({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.CollapsibleTrigger, {
        "data-slot": "collapsible-trigger",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\collapsible.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
_c1 = CollapsibleTrigger;
function CollapsibleContent({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.CollapsibleContent, {
        "data-slot": "collapsible-content",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\collapsible.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
_c2 = CollapsibleContent;

Collapsible.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Collapsible"
};
CollapsibleTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "CollapsibleTrigger"
};
CollapsibleContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "CollapsibleContent"
};
var _c, _c1, _c2;
__webpack_require__.$Refresh$.register(_c, "Collapsible");
__webpack_require__.$Refresh$.register(_c1, "CollapsibleTrigger");
__webpack_require__.$Refresh$.register(_c2, "CollapsibleContent");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/input.tsx":
/*!************************************************************!*\
  !*** ../../packages/design-system/components/ui/input.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Input: () => (/* binding */ Input)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");




function Input({ className, type, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("input", {
        type: type,
        "data-slot": "input",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]", "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Input;

Input.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Input"
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Input");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/separator.tsx":
/*!****************************************************************!*\
  !*** ../../packages/design-system/components/ui/separator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Separator: () => (/* binding */ Separator)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ "../../node_modules/.pnpm/@radix-ui+react-separator@1_2ce8aa190072d6fd618d2cdb4281497c/node_modules/@radix-ui/react-separator/dist/index.mjs");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";




function Separator({ className, orientation = "horizontal", decorative = true, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {
        "data-slot": "separator-root",
        decorative: decorative,
        orientation: orientation,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\separator.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
_c = Separator;

Separator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Separator",
    "props": {
        "orientation": {
            "defaultValue": {
                "value": "\"horizontal\"",
                "computed": false
            },
            "required": false
        },
        "decorative": {
            "defaultValue": {
                "value": "true",
                "computed": false
            },
            "required": false
        }
    }
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Separator");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/sidebar.tsx":
/*!**************************************************************!*\
  !*** ../../packages/design-system/components/ui/sidebar.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sidebar: () => (/* binding */ Sidebar),
/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent),
/* harmony export */   SidebarFooter: () => (/* binding */ SidebarFooter),
/* harmony export */   SidebarGroup: () => (/* binding */ SidebarGroup),
/* harmony export */   SidebarGroupAction: () => (/* binding */ SidebarGroupAction),
/* harmony export */   SidebarGroupContent: () => (/* binding */ SidebarGroupContent),
/* harmony export */   SidebarGroupLabel: () => (/* binding */ SidebarGroupLabel),
/* harmony export */   SidebarHeader: () => (/* binding */ SidebarHeader),
/* harmony export */   SidebarInput: () => (/* binding */ SidebarInput),
/* harmony export */   SidebarInset: () => (/* binding */ SidebarInset),
/* harmony export */   SidebarMenu: () => (/* binding */ SidebarMenu),
/* harmony export */   SidebarMenuAction: () => (/* binding */ SidebarMenuAction),
/* harmony export */   SidebarMenuBadge: () => (/* binding */ SidebarMenuBadge),
/* harmony export */   SidebarMenuButton: () => (/* binding */ SidebarMenuButton),
/* harmony export */   SidebarMenuItem: () => (/* binding */ SidebarMenuItem),
/* harmony export */   SidebarMenuSkeleton: () => (/* binding */ SidebarMenuSkeleton),
/* harmony export */   SidebarMenuSub: () => (/* binding */ SidebarMenuSub),
/* harmony export */   SidebarMenuSubButton: () => (/* binding */ SidebarMenuSubButton),
/* harmony export */   SidebarMenuSubItem: () => (/* binding */ SidebarMenuSubItem),
/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),
/* harmony export */   SidebarRail: () => (/* binding */ SidebarRail),
/* harmony export */   SidebarSeparator: () => (/* binding */ SidebarSeparator),
/* harmony export */   SidebarTrigger: () => (/* binding */ SidebarTrigger),
/* harmony export */   useSidebar: () => (/* binding */ useSidebar)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ "../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs");
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! class-variance-authority */ "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_PanelLeftIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=PanelLeftIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/panel-left.js");
/* harmony import */ var _repo_design_system_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/hooks/use-mobile */ "../../packages/design-system/hooks/use-mobile.ts");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* harmony import */ var _repo_design_system_components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @repo/design-system/components/ui/button */ "../../packages/design-system/components/ui/button.tsx");
/* harmony import */ var _repo_design_system_components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @repo/design-system/components/ui/input */ "../../packages/design-system/components/ui/input.tsx");
/* harmony import */ var _repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @repo/design-system/components/ui/separator */ "../../packages/design-system/components/ui/separator.tsx");
/* harmony import */ var _repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @repo/design-system/components/ui/sheet */ "../../packages/design-system/components/ui/sheet.tsx");
/* harmony import */ var _repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @repo/design-system/components/ui/skeleton */ "../../packages/design-system/components/ui/skeleton.tsx");
/* harmony import */ var _repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @repo/design-system/components/ui/tooltip */ "../../packages/design-system/components/ui/tooltip.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


var _s = __webpack_require__.$Refresh$.signature(), _s1 = __webpack_require__.$Refresh$.signature(), _s2 = __webpack_require__.$Refresh$.signature(), _s3 = __webpack_require__.$Refresh$.signature(), _s4 = __webpack_require__.$Refresh$.signature(), _s5 = __webpack_require__.$Refresh$.signature(), _s6 = __webpack_require__.$Refresh$.signature();
"use client";












const SIDEBAR_COOKIE_NAME = "sidebar_state";
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;
const SIDEBAR_WIDTH = "16rem";
const SIDEBAR_WIDTH_MOBILE = "18rem";
const SIDEBAR_WIDTH_ICON = "3rem";
const SIDEBAR_KEYBOARD_SHORTCUT = "b";
const SidebarContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);
function useSidebar() {
    _s();
    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SidebarContext);
    if (!context) {
        throw new Error("useSidebar must be used within a SidebarProvider.");
    }
    return context;
}
_s(useSidebar, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function SidebarProvider({ defaultOpen = true, open: openProp, onOpenChange: setOpenProp, className, style, children, ...props }) {
    _s1();
    const isMobile = (0,_repo_design_system_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__.useIsMobile)();
    const [openMobile, setOpenMobile] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);
    // This is the internal state of the sidebar.
    // We use openProp and setOpenProp for control from outside the component.
    const [_open, _setOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(defaultOpen);
    const open = openProp !== null && openProp !== void 0 ? openProp : _open;
    const setOpen = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({
        "SidebarProvider.useCallback[setOpen]": (value)=>{
            const openState = typeof value === "function" ? value(open) : value;
            if (setOpenProp) {
                setOpenProp(openState);
            } else {
                _setOpen(openState);
            }
            // This sets the cookie to keep the sidebar state.
            document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
        }
    }["SidebarProvider.useCallback[setOpen]"], [
        setOpenProp,
        open
    ]);
    // Helper to toggle the sidebar.
    const toggleSidebar = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({
        "SidebarProvider.useCallback[toggleSidebar]": ()=>{
            return isMobile ? setOpenMobile({
                "SidebarProvider.useCallback[toggleSidebar]": (open)=>!open
            }["SidebarProvider.useCallback[toggleSidebar]"]) : setOpen({
                "SidebarProvider.useCallback[toggleSidebar]": (open)=>!open
            }["SidebarProvider.useCallback[toggleSidebar]"]);
        }
    }["SidebarProvider.useCallback[toggleSidebar]"], [
        isMobile,
        setOpen,
        setOpenMobile
    ]);
    // Adds a keyboard shortcut to toggle the sidebar.
    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({
        "SidebarProvider.useEffect": ()=>{
            const handleKeyDown = {
                "SidebarProvider.useEffect.handleKeyDown": (event)=>{
                    if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
                        event.preventDefault();
                        toggleSidebar();
                    }
                }
            }["SidebarProvider.useEffect.handleKeyDown"];
            window.addEventListener("keydown", handleKeyDown);
            return ({
                "SidebarProvider.useEffect": ()=>window.removeEventListener("keydown", handleKeyDown)
            })["SidebarProvider.useEffect"];
        }
    }["SidebarProvider.useEffect"], [
        toggleSidebar
    ]);
    // We add a state so that we can do data-state="expanded" or "collapsed".
    // This makes it easier to style the sidebar with Tailwind classes.
    const state = open ? "expanded" : "collapsed";
    const contextValue = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({
        "SidebarProvider.useMemo[contextValue]": ()=>({
                state,
                open,
                setOpen,
                isMobile,
                openMobile,
                setOpenMobile,
                toggleSidebar
            })
    }["SidebarProvider.useMemo[contextValue]"], [
        state,
        open,
        setOpen,
        isMobile,
        openMobile,
        setOpenMobile,
        toggleSidebar
    ]);
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {
        value: contextValue,
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.TooltipProvider, {
            delayDuration: 0,
            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                "data-slot": "sidebar-wrapper",
                style: {
                    "--sidebar-width": SIDEBAR_WIDTH,
                    "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
                    ...style
                },
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full", className),
                ...props,
                children: children
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 132,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
            lineNumber: 131,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 130,
        columnNumber: 5
    }, this);
}
_s1(SidebarProvider, "QSOkjq1AvKFJW5+zwiK52jPX7zI=", false, function() {
    return [
        _repo_design_system_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__.useIsMobile
    ];
});
_c = SidebarProvider;
function Sidebar({ side = "left", variant = "sidebar", collapsible = "offcanvas", className, children, ...props }) {
    _s2();
    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();
    if (collapsible === "none") {
        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
            "data-slot": "sidebar",
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col", className),
            ...props,
            children: children
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
            lineNumber: 170,
            columnNumber: 7
        }, this);
    }
    if (isMobile) {
        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.Sheet, {
            open: openMobile,
            onOpenChange: setOpenMobile,
            ...props,
            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetContent, {
                "data-sidebar": "sidebar",
                "data-slot": "sidebar",
                "data-mobile": "true",
                className: "bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",
                style: {
                    "--sidebar-width": SIDEBAR_WIDTH_MOBILE
                },
                side: side,
                children: [
                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetHeader, {
                        className: "sr-only",
                        children: [
                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetTitle, {
                                children: "Sidebar"
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                                lineNumber: 199,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetDescription, {
                                children: "Displays the mobile sidebar."
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                                lineNumber: 200,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                        lineNumber: 198,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                        className: "flex h-full w-full flex-col",
                        children: children
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                        lineNumber: 202,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 186,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
            lineNumber: 185,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        className: "group peer text-sidebar-foreground hidden md:block",
        "data-state": state,
        "data-collapsible": state === "collapsed" ? collapsible : "",
        "data-variant": variant,
        "data-side": side,
        "data-slot": "sidebar",
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                "data-slot": "sidebar-gap",
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear", "group-data-[collapsible=offcanvas]:w-0", "group-data-[side=right]:rotate-180", variant === "floating" || variant === "inset" ? "group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]" : "group-data-[collapsible=icon]:w-(--sidebar-width-icon)")
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 218,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                "data-slot": "sidebar-container",
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex", side === "left" ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]" : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]", // Adjust the padding for floating and inset variants.
                variant === "floating" || variant === "inset" ? "p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]" : "group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l", className),
                ...props,
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                    "data-sidebar": "sidebar",
                    "data-slot": "sidebar-inner",
                    className: "bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",
                    children: children
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                    lineNumber: 244,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 229,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 209,
        columnNumber: 5
    }, this);
}
_s2(Sidebar, "hAL3+uRFwO9tnbDK50BUE5wZ71s=", false, function() {
    return [
        useSidebar
    ];
});
_c1 = Sidebar;
function SidebarTrigger({ className, onClick, ...props }) {
    _s3();
    const { toggleSidebar } = useSidebar();
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {
        "data-sidebar": "trigger",
        "data-slot": "sidebar-trigger",
        variant: "ghost",
        size: "icon",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("size-7", className),
        onClick: (event)=>{
            onClick === null || onClick === void 0 ? void 0 : onClick(event);
            toggleSidebar();
        },
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PanelLeftIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], {}, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 276,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "sr-only",
                children: "Toggle Sidebar"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 277,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 264,
        columnNumber: 5
    }, this);
}
_s3(SidebarTrigger, "dRnjPhQbCChcVGr4xvQkpNxnqyg=", false, function() {
    return [
        useSidebar
    ];
});
_c2 = SidebarTrigger;
function SidebarRail({ className, ...props }) {
    _s4();
    const { toggleSidebar } = useSidebar();
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("button", {
        "data-sidebar": "rail",
        "data-slot": "sidebar-rail",
        "aria-label": "Toggle Sidebar",
        tabIndex: -1,
        onClick: toggleSidebar,
        title: "Toggle Sidebar",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex", "in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize", "[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize", "hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full", "[[data-side=left][data-collapsible=offcanvas]_&]:-right-2", "[[data-side=right][data-collapsible=offcanvas]_&]:-left-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 286,
        columnNumber: 5
    }, this);
}
_s4(SidebarRail, "dRnjPhQbCChcVGr4xvQkpNxnqyg=", false, function() {
    return [
        useSidebar
    ];
});
_c3 = SidebarRail;
function SidebarInset({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("main", {
        "data-slot": "sidebar-inset",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-background relative flex w-full flex-1 flex-col", "md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 309,
        columnNumber: 5
    }, this);
}
_c4 = SidebarInset;
function SidebarInput({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {
        "data-slot": "sidebar-input",
        "data-sidebar": "input",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-background h-8 w-full shadow-none", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 326,
        columnNumber: 5
    }, this);
}
_c5 = SidebarInput;
function SidebarHeader({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-header",
        "data-sidebar": "header",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex flex-col gap-2 p-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 337,
        columnNumber: 5
    }, this);
}
_c6 = SidebarHeader;
function SidebarFooter({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-footer",
        "data-sidebar": "footer",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex flex-col gap-2 p-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 348,
        columnNumber: 5
    }, this);
}
_c7 = SidebarFooter;
function SidebarSeparator({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {
        "data-slot": "sidebar-separator",
        "data-sidebar": "separator",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-sidebar-border mx-2 w-auto", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 362,
        columnNumber: 5
    }, this);
}
_c8 = SidebarSeparator;
function SidebarContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-content",
        "data-sidebar": "content",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 373,
        columnNumber: 5
    }, this);
}
_c9 = SidebarContent;
function SidebarGroup({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-group",
        "data-sidebar": "group",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("relative flex w-full min-w-0 flex-col p-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 387,
        columnNumber: 5
    }, this);
}
_c10 = SidebarGroup;
function SidebarGroupLabel({ className, asChild = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "div";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-group-label",
        "data-sidebar": "group-label",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0", "group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 404,
        columnNumber: 5
    }, this);
}
_c11 = SidebarGroupLabel;
function SidebarGroupAction({ className, asChild = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "button";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-group-action",
        "data-sidebar": "group-action",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0", // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 md:after:hidden", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 425,
        columnNumber: 5
    }, this);
}
_c12 = SidebarGroupAction;
function SidebarGroupContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-group-content",
        "data-sidebar": "group-content",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("w-full text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 445,
        columnNumber: 5
    }, this);
}
_c13 = SidebarGroupContent;
function SidebarMenu({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("ul", {
        "data-slot": "sidebar-menu",
        "data-sidebar": "menu",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex w-full min-w-0 flex-col gap-1", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 456,
        columnNumber: 5
    }, this);
}
_c14 = SidebarMenu;
function SidebarMenuItem({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("li", {
        "data-slot": "sidebar-menu-item",
        "data-sidebar": "menu-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("group/menu-item relative", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 467,
        columnNumber: 5
    }, this);
}
_c15 = SidebarMenuItem;
const sidebarMenuButtonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_12__.cva)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0", {
    variants: {
        variant: {
            default: "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
            outline: "bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"
        },
        size: {
            default: "h-8 text-sm",
            sm: "h-7 text-xs",
            lg: "h-12 text-sm group-data-[collapsible=icon]:p-0!"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function SidebarMenuButton({ asChild = false, isActive = false, variant = "default", size = "default", tooltip, className, ...props }) {
    _s5();
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "button";
    const { isMobile, state } = useSidebar();
    const button = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-menu-button",
        "data-sidebar": "menu-button",
        "data-size": size,
        "data-active": isActive,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(sidebarMenuButtonVariants({
            variant,
            size
        }), className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 515,
        columnNumber: 5
    }, this);
    if (!tooltip) {
        return button;
    }
    if (typeof tooltip === "string") {
        tooltip = {
            children: tooltip
        };
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.TooltipTrigger, {
                asChild: true,
                children: button
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 537,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.TooltipContent, {
                side: "right",
                align: "center",
                hidden: state !== "collapsed" || isMobile,
                ...tooltip
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 538,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 536,
        columnNumber: 5
    }, this);
}
_s5(SidebarMenuButton, "DSCdbs8JtpmKVxCYgM7sPAZNgB0=", false, function() {
    return [
        useSidebar
    ];
});
_c16 = SidebarMenuButton;
function SidebarMenuAction({ className, asChild = false, showOnHover = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "button";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-menu-action",
        "data-sidebar": "menu-action",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0", // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 md:after:hidden", "peer-data-[size=sm]/menu-button:top-1", "peer-data-[size=default]/menu-button:top-1.5", "peer-data-[size=lg]/menu-button:top-2.5", "group-data-[collapsible=icon]:hidden", showOnHover && "peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 560,
        columnNumber: 5
    }, this);
}
_c17 = SidebarMenuAction;
function SidebarMenuBadge({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-menu-badge",
        "data-sidebar": "menu-badge",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none", "peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground", "peer-data-[size=sm]/menu-button:top-1", "peer-data-[size=default]/menu-button:top-1.5", "peer-data-[size=lg]/menu-button:top-2.5", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 585,
        columnNumber: 5
    }, this);
}
_c18 = SidebarMenuBadge;
function SidebarMenuSkeleton({ className, showIcon = false, ...props }) {
    _s6();
    // Random width between 50 to 90%.
    const width = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({
        "SidebarMenuSkeleton.useMemo[width]": ()=>{
            return `${Math.floor(Math.random() * 40) + 50}%`;
        }
    }["SidebarMenuSkeleton.useMemo[width]"], []);
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-menu-skeleton",
        "data-sidebar": "menu-skeleton",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex h-8 items-center gap-2 rounded-md px-2", className),
        ...props,
        children: [
            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {
                className: "size-4 rounded-md",
                "data-sidebar": "menu-skeleton-icon"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 622,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {
                className: "h-4 max-w-(--skeleton-width) flex-1",
                "data-sidebar": "menu-skeleton-text",
                style: {
                    "--skeleton-width": width
                }
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 627,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 615,
        columnNumber: 5
    }, this);
}
_s6(SidebarMenuSkeleton, "nKFjX4dxbYo91VAj5VdWQ1XUe3I=");
_c19 = SidebarMenuSkeleton;
function SidebarMenuSub({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("ul", {
        "data-slot": "sidebar-menu-sub",
        "data-sidebar": "menu-sub",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 642,
        columnNumber: 5
    }, this);
}
_c20 = SidebarMenuSub;
function SidebarMenuSubItem({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("li", {
        "data-slot": "sidebar-menu-sub-item",
        "data-sidebar": "menu-sub-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("group/menu-sub-item relative", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 660,
        columnNumber: 5
    }, this);
}
_c21 = SidebarMenuSubItem;
function SidebarMenuSubButton({ asChild = false, size = "md", isActive = false, className, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "a";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-menu-sub-button",
        "data-sidebar": "menu-sub-button",
        "data-size": size,
        "data-active": isActive,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0", "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground", size === "sm" && "text-xs", size === "md" && "text-sm", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 683,
        columnNumber: 5
    }, this);
}
_c22 = SidebarMenuSubButton;

Sidebar.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Sidebar",
    "props": {
        "side": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"left\" | \"right\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"left\""
                    },
                    {
                        "name": "literal",
                        "value": "\"right\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"left\"",
                "computed": false
            }
        },
        "variant": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"sidebar\" | \"floating\" | \"inset\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"sidebar\""
                    },
                    {
                        "name": "literal",
                        "value": "\"floating\""
                    },
                    {
                        "name": "literal",
                        "value": "\"inset\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"sidebar\"",
                "computed": false
            }
        },
        "collapsible": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"offcanvas\" | \"icon\" | \"none\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"offcanvas\""
                    },
                    {
                        "name": "literal",
                        "value": "\"icon\""
                    },
                    {
                        "name": "literal",
                        "value": "\"none\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"offcanvas\"",
                "computed": false
            }
        }
    }
};
SidebarContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarContent"
};
SidebarFooter.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarFooter"
};
SidebarGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroup"
};
SidebarGroupAction.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroupAction",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarGroupContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroupContent"
};
SidebarGroupLabel.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroupLabel",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarHeader.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarHeader"
};
SidebarInput.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarInput"
};
SidebarInset.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarInset"
};
SidebarMenu.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenu"
};
SidebarMenuAction.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuAction",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "showOnHover": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarMenuBadge.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuBadge"
};
SidebarMenuButton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuButton",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "isActive": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "tooltip": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "string | React.ComponentProps<typeof TooltipContent>",
                "elements": [
                    {
                        "name": "string"
                    },
                    {
                        "name": "ReactComponentProps",
                        "raw": "React.ComponentProps<typeof TooltipContent>",
                        "elements": [
                            {
                                "name": "TooltipContent"
                            }
                        ]
                    }
                ]
            },
            "description": ""
        },
        "variant": {
            "defaultValue": {
                "value": "\"default\"",
                "computed": false
            },
            "required": false
        },
        "size": {
            "defaultValue": {
                "value": "\"default\"",
                "computed": false
            },
            "required": false
        }
    }
};
SidebarMenuItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuItem"
};
SidebarMenuSkeleton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSkeleton",
    "props": {
        "showIcon": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarMenuSub.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSub"
};
SidebarMenuSubButton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSubButton",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "size": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"sm\" | \"md\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"sm\""
                    },
                    {
                        "name": "literal",
                        "value": "\"md\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"md\"",
                "computed": false
            }
        },
        "isActive": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarMenuSubItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSubItem"
};
SidebarProvider.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarProvider",
    "props": {
        "defaultOpen": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "true",
                "computed": false
            }
        },
        "open": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        },
        "onOpenChange": {
            "required": false,
            "tsType": {
                "name": "signature",
                "type": "function",
                "raw": "(open: boolean) => void",
                "signature": {
                    "arguments": [
                        {
                            "type": {
                                "name": "boolean"
                            },
                            "name": "open"
                        }
                    ],
                    "return": {
                        "name": "void"
                    }
                }
            },
            "description": ""
        }
    }
};
SidebarRail.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarRail"
};
SidebarSeparator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarSeparator"
};
SidebarTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarTrigger"
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22;
__webpack_require__.$Refresh$.register(_c, "SidebarProvider");
__webpack_require__.$Refresh$.register(_c1, "Sidebar");
__webpack_require__.$Refresh$.register(_c2, "SidebarTrigger");
__webpack_require__.$Refresh$.register(_c3, "SidebarRail");
__webpack_require__.$Refresh$.register(_c4, "SidebarInset");
__webpack_require__.$Refresh$.register(_c5, "SidebarInput");
__webpack_require__.$Refresh$.register(_c6, "SidebarHeader");
__webpack_require__.$Refresh$.register(_c7, "SidebarFooter");
__webpack_require__.$Refresh$.register(_c8, "SidebarSeparator");
__webpack_require__.$Refresh$.register(_c9, "SidebarContent");
__webpack_require__.$Refresh$.register(_c10, "SidebarGroup");
__webpack_require__.$Refresh$.register(_c11, "SidebarGroupLabel");
__webpack_require__.$Refresh$.register(_c12, "SidebarGroupAction");
__webpack_require__.$Refresh$.register(_c13, "SidebarGroupContent");
__webpack_require__.$Refresh$.register(_c14, "SidebarMenu");
__webpack_require__.$Refresh$.register(_c15, "SidebarMenuItem");
__webpack_require__.$Refresh$.register(_c16, "SidebarMenuButton");
__webpack_require__.$Refresh$.register(_c17, "SidebarMenuAction");
__webpack_require__.$Refresh$.register(_c18, "SidebarMenuBadge");
__webpack_require__.$Refresh$.register(_c19, "SidebarMenuSkeleton");
__webpack_require__.$Refresh$.register(_c20, "SidebarMenuSub");
__webpack_require__.$Refresh$.register(_c21, "SidebarMenuSubItem");
__webpack_require__.$Refresh$.register(_c22, "SidebarMenuSubButton");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/skeleton.tsx":
/*!***************************************************************!*\
  !*** ../../packages/design-system/components/ui/skeleton.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Skeleton: () => (/* binding */ Skeleton)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



function Skeleton({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "skeleton",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)("bg-accent animate-pulse rounded-md", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\skeleton.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = Skeleton;

Skeleton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Skeleton"
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Skeleton");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/hooks/use-mobile.ts":
/*!********************************************************!*\
  !*** ../../packages/design-system/hooks/use-mobile.ts ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useIsMobile: () => (/* binding */ useIsMobile)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


const MOBILE_BREAKPOINT = 768;
function useIsMobile() {
    const [isMobile, setIsMobile] = react__WEBPACK_IMPORTED_MODULE_0__.useState(undefined);
    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({
        "useIsMobile.useEffect": ()=>{
            const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
            const onChange = {
                "useIsMobile.useEffect.onChange": ()=>{
                    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
                }
            }["useIsMobile.useEffect.onChange"];
            mql.addEventListener("change", onChange);
            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
            return ({
                "useIsMobile.useEffect": ()=>mql.removeEventListener("change", onChange)
            })["useIsMobile.useEffect"];
        }
    }["useIsMobile.useEffect"], []);
    return !!isMobile;
}


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/sidebar.stories.tsx":
/*!*************************************!*\
  !*** ./stories/sidebar.stories.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Base: () => (/* binding */ Base),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/gallery-vertical-end.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/audio-waveform.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/command.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-terminal.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/book-open.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings-2.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/frame.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chart-pie.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/forward.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/sparkles.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/badge-check.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/credit-card.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bell.js");
/* harmony import */ var _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AudioWaveform,BadgeCheck,Bell,BookOpen,Bot,ChevronRight,ChevronsUpDown,Command,CreditCard,Folder,Forward,Frame,GalleryVerticalEnd,LogOut,Map,MoreHorizontal,PieChart,Plus,Settings2,Sparkles,SquareTerminal,Trash2!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js");
/* harmony import */ var _repo_design_system_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/avatar */ "../../packages/design-system/components/ui/avatar.tsx");
/* harmony import */ var _repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/components/ui/breadcrumb */ "../../packages/design-system/components/ui/breadcrumb.tsx");
/* harmony import */ var _repo_design_system_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @repo/design-system/components/ui/collapsible */ "../../packages/design-system/components/ui/collapsible.tsx");
/* harmony import */ var _repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @repo/design-system/components/ui/dropdown-menu */ "../../packages/design-system/components/ui/dropdown-menu.tsx");
/* harmony import */ var _repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @repo/design-system/components/ui/separator */ "../../packages/design-system/components/ui/separator.tsx");
/* harmony import */ var _repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @repo/design-system/components/ui/sidebar */ "../../packages/design-system/components/ui/sidebar.tsx");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


var _s = __webpack_require__.$Refresh$.signature();








const meta = {
  title: 'ui/Sidebar',
  component: _repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.Sidebar,
  tags: ['autodocs'],
  argTypes: {}
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
const data = {
  user: {
    name: 'shadcn',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg'
  },
  teams: [{
    name: 'Acme Inc',
    logo: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"],
    plan: 'Enterprise'
  }, {
    name: 'Acme Corp.',
    logo: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"],
    plan: 'Startup'
  }, {
    name: 'Evil Corp.',
    logo: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"],
    plan: 'Free'
  }],
  navMain: [{
    title: 'Playground',
    url: '#',
    icon: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"],
    isActive: true,
    items: [{
      title: 'History',
      url: '#'
    }, {
      title: 'Starred',
      url: '#'
    }, {
      title: 'Settings',
      url: '#'
    }]
  }, {
    title: 'Models',
    url: '#',
    icon: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"],
    items: [{
      title: 'Genesis',
      url: '#'
    }, {
      title: 'Explorer',
      url: '#'
    }, {
      title: 'Quantum',
      url: '#'
    }]
  }, {
    title: 'Documentation',
    url: '#',
    icon: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"],
    items: [{
      title: 'Introduction',
      url: '#'
    }, {
      title: 'Get Started',
      url: '#'
    }, {
      title: 'Tutorials',
      url: '#'
    }, {
      title: 'Changelog',
      url: '#'
    }]
  }, {
    title: 'Settings',
    url: '#',
    icon: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"],
    items: [{
      title: 'General',
      url: '#'
    }, {
      title: 'Team',
      url: '#'
    }, {
      title: 'Billing',
      url: '#'
    }, {
      title: 'Limits',
      url: '#'
    }]
  }],
  projects: [{
    name: 'Design Engineering',
    url: '#',
    icon: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"]
  }, {
    name: 'Sales & Marketing',
    url: '#',
    icon: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"]
  }, {
    name: 'Travel',
    url: '#',
    icon: _barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"]
  }]
};
const Base = {
  render: _s(() => {
    _s();
    const [activeTeam, setActiveTeam] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(data.teams[0]);
    return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.Sidebar, {
        collapsible: "icon",
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarHeader, {
          children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenu, {
            children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuItem, {
              children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {
                children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {
                  asChild: true,
                  children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuButton, {
                    size: "lg",
                    className: "data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                      className: "flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground",
                      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(activeTeam.logo, {
                        className: "size-4"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 234,
                        columnNumber: 25
                      }, undefined)
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 233,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                      className: "grid flex-1 text-left text-sm leading-tight",
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        className: "truncate font-semibold",
                        children: activeTeam.name
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 237,
                        columnNumber: 25
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        className: "truncate text-xs",
                        children: activeTeam.plan
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 240,
                        columnNumber: 25
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 236,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__["default"], {
                      className: "ml-auto"
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 244,
                      columnNumber: 23
                    }, undefined)]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 229,
                    columnNumber: 21
                  }, undefined)
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 228,
                  columnNumber: 19
                }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {
                  className: "w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg",
                  align: "start",
                  side: "bottom",
                  sideOffset: 4,
                  children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuLabel, {
                    className: "text-muted-foreground text-xs",
                    children: "Teams"
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 253,
                    columnNumber: 21
                  }, undefined), data.teams.map((team, index) => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                    onClick: () => setActiveTeam(team),
                    className: "gap-2 p-2",
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                      className: "flex size-6 items-center justify-center rounded-sm border",
                      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(team.logo, {
                        className: "size-4 shrink-0"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 263,
                        columnNumber: 27
                      }, undefined)
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 262,
                      columnNumber: 25
                    }, undefined), team.name, /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuShortcut, {
                      children: ["⌘", index + 1]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 266,
                      columnNumber: 25
                    }, undefined)]
                  }, team.name, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 257,
                    columnNumber: 23
                  }, undefined)), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 271,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                    className: "gap-2 p-2",
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                      className: "flex size-6 items-center justify-center rounded-md border bg-background",
                      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__["default"], {
                        className: "size-4"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 274,
                        columnNumber: 25
                      }, undefined)
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 273,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                      className: "font-medium text-muted-foreground",
                      children: "Add team"
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 276,
                      columnNumber: 23
                    }, undefined)]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 272,
                    columnNumber: 21
                  }, undefined)]
                }, void 0, true, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 247,
                  columnNumber: 19
                }, undefined)]
              }, void 0, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                lineNumber: 227,
                columnNumber: 17
              }, undefined)
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 226,
              columnNumber: 15
            }, undefined)
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
            lineNumber: 225,
            columnNumber: 13
          }, undefined)
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
          lineNumber: 224,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarContent, {
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarGroup, {
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarGroupLabel, {
              children: "Platform"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 287,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenu, {
              children: data.navMain.map(item => {
                var _item_items;
                return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {
                  asChild: true,
                  defaultOpen: item.isActive,
                  className: "group/collapsible",
                  children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuItem, {
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {
                      asChild: true,
                      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuButton, {
                        tooltip: item.title,
                        children: [item.icon && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {}, void 0, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 299,
                          columnNumber: 41
                        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                          children: item.title
                        }, void 0, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 300,
                          columnNumber: 27
                        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__["default"], {
                          className: "ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                        }, void 0, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 301,
                          columnNumber: 27
                        }, undefined)]
                      }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 298,
                        columnNumber: 25
                      }, undefined)
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 297,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {
                      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuSub, {
                        children: (_item_items = item.items) === null || _item_items === void 0 ? void 0 : _item_items.map(subItem => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuSubItem, {
                          children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuSubButton, {
                            asChild: true,
                            children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("a", {
                              href: subItem.url,
                              children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                                children: subItem.title
                              }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                                lineNumber: 310,
                                columnNumber: 35
                              }, undefined)
                            }, void 0, false, {
                              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                              lineNumber: 309,
                              columnNumber: 33
                            }, undefined)
                          }, void 0, false, {
                            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                            lineNumber: 308,
                            columnNumber: 31
                          }, undefined)
                        }, subItem.title, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 307,
                          columnNumber: 29
                        }, undefined))
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 305,
                        columnNumber: 25
                      }, undefined)
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 304,
                      columnNumber: 23
                    }, undefined)]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 296,
                    columnNumber: 21
                  }, undefined)
                }, item.title, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 290,
                  columnNumber: 19
                }, undefined);
              })
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 288,
              columnNumber: 15
            }, undefined)]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
            lineNumber: 286,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarGroup, {
            className: "group-data-[collapsible=icon]:hidden",
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarGroupLabel, {
              children: "Projects"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 323,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenu, {
              children: [data.projects.map(item => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuItem, {
                children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuButton, {
                  asChild: true,
                  children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("a", {
                    href: item.url,
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {}, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 329,
                      columnNumber: 25
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                      children: item.name
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 330,
                      columnNumber: 25
                    }, undefined)]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 328,
                    columnNumber: 23
                  }, undefined)
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 327,
                  columnNumber: 21
                }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {
                  children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {
                    asChild: true,
                    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuAction, {
                      showOnHover: true,
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__["default"], {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 336,
                        columnNumber: 27
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        className: "sr-only",
                        children: "More"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 337,
                        columnNumber: 27
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 335,
                      columnNumber: 25
                    }, undefined)
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 334,
                    columnNumber: 23
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {
                    className: "w-48 rounded-lg",
                    side: "bottom",
                    align: "end",
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__["default"], {
                        className: "text-muted-foreground"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 346,
                        columnNumber: 27
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        children: "View Project"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 347,
                        columnNumber: 27
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 345,
                      columnNumber: 25
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__["default"], {
                        className: "text-muted-foreground"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 350,
                        columnNumber: 27
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        children: "Share Project"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 351,
                        columnNumber: 27
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 349,
                      columnNumber: 25
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 353,
                      columnNumber: 25
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__["default"], {
                        className: "text-muted-foreground"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 355,
                        columnNumber: 27
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        children: "Delete Project"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 356,
                        columnNumber: 27
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 354,
                      columnNumber: 25
                    }, undefined)]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 340,
                    columnNumber: 23
                  }, undefined)]
                }, void 0, true, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 333,
                  columnNumber: 21
                }, undefined)]
              }, item.name, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                lineNumber: 326,
                columnNumber: 19
              }, undefined)), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuItem, {
                children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuButton, {
                  className: "text-sidebar-foreground/70",
                  children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__["default"], {
                    className: "text-sidebar-foreground/70"
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 364,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                    children: "More"
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 365,
                    columnNumber: 21
                  }, undefined)]
                }, void 0, true, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 363,
                  columnNumber: 19
                }, undefined)
              }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                lineNumber: 362,
                columnNumber: 17
              }, undefined)]
            }, void 0, true, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 324,
              columnNumber: 15
            }, undefined)]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
            lineNumber: 322,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
          lineNumber: 285,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarFooter, {
          children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenu, {
            children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuItem, {
              children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {
                children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {
                  asChild: true,
                  children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuButton, {
                    size: "lg",
                    className: "data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {
                      className: "h-8 w-8 rounded-lg",
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {
                        src: data.user.avatar,
                        alt: data.user.name
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 381,
                        columnNumber: 25
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {
                        className: "rounded-lg",
                        children: "CN"
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 385,
                        columnNumber: 25
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 380,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                      className: "grid flex-1 text-left text-sm leading-tight",
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        className: "truncate font-semibold",
                        children: data.user.name
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 390,
                        columnNumber: 25
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                        className: "truncate text-xs",
                        children: data.user.email
                      }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 393,
                        columnNumber: 25
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 389,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__["default"], {
                      className: "ml-auto size-4"
                    }, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 397,
                      columnNumber: 23
                    }, undefined)]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 376,
                    columnNumber: 21
                  }, undefined)
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 375,
                  columnNumber: 19
                }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {
                  className: "w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg",
                  side: "bottom",
                  align: "end",
                  sideOffset: 4,
                  children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuLabel, {
                    className: "p-0 font-normal",
                    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                      className: "flex items-center gap-2 px-1 py-1.5 text-left text-sm",
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {
                        className: "h-8 w-8 rounded-lg",
                        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {
                          src: data.user.avatar,
                          alt: data.user.name
                        }, void 0, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 409,
                          columnNumber: 27
                        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {
                          className: "rounded-lg",
                          children: "CN"
                        }, void 0, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 413,
                          columnNumber: 27
                        }, undefined)]
                      }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 408,
                        columnNumber: 25
                      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                        className: "grid flex-1 text-left text-sm leading-tight",
                        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                          className: "truncate font-semibold",
                          children: data.user.name
                        }, void 0, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 418,
                          columnNumber: 27
                        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                          className: "truncate text-xs",
                          children: data.user.email
                        }, void 0, false, {
                          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                          lineNumber: 421,
                          columnNumber: 27
                        }, undefined)]
                      }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 417,
                        columnNumber: 25
                      }, undefined)]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 407,
                      columnNumber: 23
                    }, undefined)
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 406,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 427,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuGroup, {
                    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__["default"], {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 430,
                        columnNumber: 25
                      }, undefined), "Upgrade to Pro"]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 429,
                      columnNumber: 23
                    }, undefined)
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 428,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 434,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuGroup, {
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__["default"], {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 437,
                        columnNumber: 25
                      }, undefined), "Account"]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 436,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__["default"], {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 441,
                        columnNumber: 25
                      }, undefined), "Billing"]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 440,
                      columnNumber: 23
                    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__["default"], {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                        lineNumber: 445,
                        columnNumber: 25
                      }, undefined), "Notifications"]
                    }, void 0, true, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 444,
                      columnNumber: 23
                    }, undefined)]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 435,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 449,
                    columnNumber: 21
                  }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {
                    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AudioWaveform_BadgeCheck_Bell_BookOpen_Bot_ChevronRight_ChevronsUpDown_Command_CreditCard_Folder_Forward_Frame_GalleryVerticalEnd_LogOut_Map_MoreHorizontal_PieChart_Plus_Settings2_Sparkles_SquareTerminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__["default"], {}, void 0, false, {
                      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                      lineNumber: 451,
                      columnNumber: 23
                    }, undefined), "Log out"]
                  }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 450,
                    columnNumber: 21
                  }, undefined)]
                }, void 0, true, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 400,
                  columnNumber: 19
                }, undefined)]
              }, void 0, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                lineNumber: 374,
                columnNumber: 17
              }, undefined)
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 373,
              columnNumber: 15
            }, undefined)
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
            lineNumber: 372,
            columnNumber: 13
          }, undefined)
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
          lineNumber: 371,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarRail, {}, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
          lineNumber: 459,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
        lineNumber: 223,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("header", {
          className: "flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",
          children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
            className: "flex items-center gap-2 px-4",
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {
              className: "-ml-1"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 464,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {
              orientation: "vertical",
              className: "mr-2 h-4"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 465,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.Breadcrumb, {
              children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbList, {
                children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbItem, {
                  className: "hidden md:block",
                  children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbLink, {
                    href: "#",
                    children: "Building Your Application"
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 469,
                    columnNumber: 21
                  }, undefined)
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 468,
                  columnNumber: 19
                }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbSeparator, {
                  className: "hidden md:block"
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 473,
                  columnNumber: 19
                }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbItem, {
                  children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbPage, {
                    children: "Data Fetching"
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                    lineNumber: 475,
                    columnNumber: 21
                  }, undefined)
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                  lineNumber: 474,
                  columnNumber: 19
                }, undefined)]
              }, void 0, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
                lineNumber: 467,
                columnNumber: 17
              }, undefined)
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 466,
              columnNumber: 15
            }, undefined)]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
            lineNumber: 463,
            columnNumber: 13
          }, undefined)
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
          lineNumber: 462,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
          className: "flex flex-1 flex-col gap-4 p-4 pt-0",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
            className: "grid auto-rows-min gap-4 md:grid-cols-3",
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
              className: "aspect-video rounded-xl bg-muted/50"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 483,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
              className: "aspect-video rounded-xl bg-muted/50"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 484,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
              className: "aspect-video rounded-xl bg-muted/50"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
              lineNumber: 485,
              columnNumber: 15
            }, undefined)]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
            lineNumber: 482,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
            className: "min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
            lineNumber: 487,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
          lineNumber: 481,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
        lineNumber: 461,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\sidebar.stories.tsx",
      lineNumber: 222,
      columnNumber: 7
    }, undefined);
  }, "myA9xo3rI+n4/yiHYF4VUFcZj/I="),
  args: {}
};
;
const __namedExportsOrder = ["Base"];
Base.parameters = {
  ...Base.parameters,
  docs: {
    ...Base.parameters?.docs,
    source: {
      originalSource: "{\n  render: () => {\n    const [activeTeam, setActiveTeam] = useState(data.teams[0]);\n    return <SidebarProvider>\r\n        <Sidebar collapsible=\"icon\">\r\n          <SidebarHeader>\r\n            <SidebarMenu>\r\n              <SidebarMenuItem>\r\n                <DropdownMenu>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <SidebarMenuButton size=\"lg\" className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\">\r\n                      <div className=\"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground\">\r\n                        <activeTeam.logo className=\"size-4\" />\r\n                      </div>\r\n                      <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                        <span className=\"truncate font-semibold\">\r\n                          {activeTeam.name}\r\n                        </span>\r\n                        <span className=\"truncate text-xs\">\r\n                          {activeTeam.plan}\r\n                        </span>\r\n                      </div>\r\n                      <ChevronsUpDown className=\"ml-auto\" />\r\n                    </SidebarMenuButton>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\" align=\"start\" side=\"bottom\" sideOffset={4}>\r\n                    <DropdownMenuLabel className=\"text-muted-foreground text-xs\">\r\n                      Teams\r\n                    </DropdownMenuLabel>\r\n                    {data.teams.map((team, index) => <DropdownMenuItem key={team.name} onClick={() => setActiveTeam(team)} className=\"gap-2 p-2\">\r\n                        <div className=\"flex size-6 items-center justify-center rounded-sm border\">\r\n                          <team.logo className=\"size-4 shrink-0\" />\r\n                        </div>\r\n                        {team.name}\r\n                        <DropdownMenuShortcut>\r\n                          \u2318{index + 1}\r\n                        </DropdownMenuShortcut>\r\n                      </DropdownMenuItem>)}\r\n                    <DropdownMenuSeparator />\r\n                    <DropdownMenuItem className=\"gap-2 p-2\">\r\n                      <div className=\"flex size-6 items-center justify-center rounded-md border bg-background\">\r\n                        <Plus className=\"size-4\" />\r\n                      </div>\r\n                      <div className=\"font-medium text-muted-foreground\">\r\n                        Add team\r\n                      </div>\r\n                    </DropdownMenuItem>\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              </SidebarMenuItem>\r\n            </SidebarMenu>\r\n          </SidebarHeader>\r\n          <SidebarContent>\r\n            <SidebarGroup>\r\n              <SidebarGroupLabel>Platform</SidebarGroupLabel>\r\n              <SidebarMenu>\r\n                {data.navMain.map(item => <Collapsible key={item.title} asChild defaultOpen={item.isActive} className=\"group/collapsible\">\r\n                    <SidebarMenuItem>\r\n                      <CollapsibleTrigger asChild>\r\n                        <SidebarMenuButton tooltip={item.title}>\r\n                          {item.icon && <item.icon />}\r\n                          <span>{item.title}</span>\r\n                          <ChevronRight className=\"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\" />\r\n                        </SidebarMenuButton>\r\n                      </CollapsibleTrigger>\r\n                      <CollapsibleContent>\r\n                        <SidebarMenuSub>\r\n                          {item.items?.map(subItem => <SidebarMenuSubItem key={subItem.title}>\r\n                              <SidebarMenuSubButton asChild>\r\n                                <a href={subItem.url}>\r\n                                  <span>{subItem.title}</span>\r\n                                </a>\r\n                              </SidebarMenuSubButton>\r\n                            </SidebarMenuSubItem>)}\r\n                        </SidebarMenuSub>\r\n                      </CollapsibleContent>\r\n                    </SidebarMenuItem>\r\n                  </Collapsible>)}\r\n              </SidebarMenu>\r\n            </SidebarGroup>\r\n            <SidebarGroup className=\"group-data-[collapsible=icon]:hidden\">\r\n              <SidebarGroupLabel>Projects</SidebarGroupLabel>\r\n              <SidebarMenu>\r\n                {data.projects.map(item => <SidebarMenuItem key={item.name}>\r\n                    <SidebarMenuButton asChild>\r\n                      <a href={item.url}>\r\n                        <item.icon />\r\n                        <span>{item.name}</span>\r\n                      </a>\r\n                    </SidebarMenuButton>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger asChild>\r\n                        <SidebarMenuAction showOnHover>\r\n                          <MoreHorizontal />\r\n                          <span className=\"sr-only\">More</span>\r\n                        </SidebarMenuAction>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent className=\"w-48 rounded-lg\" side=\"bottom\" align=\"end\">\r\n                        <DropdownMenuItem>\r\n                          <Folder className=\"text-muted-foreground\" />\r\n                          <span>View Project</span>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem>\r\n                          <Forward className=\"text-muted-foreground\" />\r\n                          <span>Share Project</span>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuSeparator />\r\n                        <DropdownMenuItem>\r\n                          <Trash2 className=\"text-muted-foreground\" />\r\n                          <span>Delete Project</span>\r\n                        </DropdownMenuItem>\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </SidebarMenuItem>)}\r\n                <SidebarMenuItem>\r\n                  <SidebarMenuButton className=\"text-sidebar-foreground/70\">\r\n                    <MoreHorizontal className=\"text-sidebar-foreground/70\" />\r\n                    <span>More</span>\r\n                  </SidebarMenuButton>\r\n                </SidebarMenuItem>\r\n              </SidebarMenu>\r\n            </SidebarGroup>\r\n          </SidebarContent>\r\n          <SidebarFooter>\r\n            <SidebarMenu>\r\n              <SidebarMenuItem>\r\n                <DropdownMenu>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <SidebarMenuButton size=\"lg\" className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\">\r\n                      <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                        <AvatarImage src={data.user.avatar} alt={data.user.name} />\r\n                        <AvatarFallback className=\"rounded-lg\">\r\n                          CN\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                        <span className=\"truncate font-semibold\">\r\n                          {data.user.name}\r\n                        </span>\r\n                        <span className=\"truncate text-xs\">\r\n                          {data.user.email}\r\n                        </span>\r\n                      </div>\r\n                      <ChevronsUpDown className=\"ml-auto size-4\" />\r\n                    </SidebarMenuButton>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\" side=\"bottom\" align=\"end\" sideOffset={4}>\r\n                    <DropdownMenuLabel className=\"p-0 font-normal\">\r\n                      <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                        <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                          <AvatarImage src={data.user.avatar} alt={data.user.name} />\r\n                          <AvatarFallback className=\"rounded-lg\">\r\n                            CN\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                          <span className=\"truncate font-semibold\">\r\n                            {data.user.name}\r\n                          </span>\r\n                          <span className=\"truncate text-xs\">\r\n                            {data.user.email}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </DropdownMenuLabel>\r\n                    <DropdownMenuSeparator />\r\n                    <DropdownMenuGroup>\r\n                      <DropdownMenuItem>\r\n                        <Sparkles />\r\n                        Upgrade to Pro\r\n                      </DropdownMenuItem>\r\n                    </DropdownMenuGroup>\r\n                    <DropdownMenuSeparator />\r\n                    <DropdownMenuGroup>\r\n                      <DropdownMenuItem>\r\n                        <BadgeCheck />\r\n                        Account\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem>\r\n                        <CreditCard />\r\n                        Billing\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem>\r\n                        <Bell />\r\n                        Notifications\r\n                      </DropdownMenuItem>\r\n                    </DropdownMenuGroup>\r\n                    <DropdownMenuSeparator />\r\n                    <DropdownMenuItem>\r\n                      <LogOut />\r\n                      Log out\r\n                    </DropdownMenuItem>\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              </SidebarMenuItem>\r\n            </SidebarMenu>\r\n          </SidebarFooter>\r\n          <SidebarRail />\r\n        </Sidebar>\r\n        <SidebarInset>\r\n          <header className=\"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12\">\r\n            <div className=\"flex items-center gap-2 px-4\">\r\n              <SidebarTrigger className=\"-ml-1\" />\r\n              <Separator orientation=\"vertical\" className=\"mr-2 h-4\" />\r\n              <Breadcrumb>\r\n                <BreadcrumbList>\r\n                  <BreadcrumbItem className=\"hidden md:block\">\r\n                    <BreadcrumbLink href=\"#\">\r\n                      Building Your Application\r\n                    </BreadcrumbLink>\r\n                  </BreadcrumbItem>\r\n                  <BreadcrumbSeparator className=\"hidden md:block\" />\r\n                  <BreadcrumbItem>\r\n                    <BreadcrumbPage>Data Fetching</BreadcrumbPage>\r\n                  </BreadcrumbItem>\r\n                </BreadcrumbList>\r\n              </Breadcrumb>\r\n            </div>\r\n          </header>\r\n          <div className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\r\n            <div className=\"grid auto-rows-min gap-4 md:grid-cols-3\">\r\n              <div className=\"aspect-video rounded-xl bg-muted/50\" />\r\n              <div className=\"aspect-video rounded-xl bg-muted/50\" />\r\n              <div className=\"aspect-video rounded-xl bg-muted/50\" />\r\n            </div>\r\n            <div className=\"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min\" />\r\n          </div>\r\n        </SidebarInset>\r\n      </SidebarProvider>;\n  },\n  args: {}\n}",
      ...Base.parameters?.docs?.source
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=sidebar-stories.iframe.bundle.js.map