"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["toggle-group-stories"],{

/***/ "../../packages/design-system/components/ui/toggle-group.tsx":
/*!*******************************************************************!*\
  !*** ../../packages/design-system/components/ui/toggle-group.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToggleGroup: () => (/* binding */ ToggleGroup),
/* harmony export */   ToggleGroupItem: () => (/* binding */ ToggleGroupItem)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_toggle_group__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toggle-group */ "../../node_modules/.pnpm/@radix-ui+react-toggle-grou_c5fb78d868888a277584f024110028c4/node_modules/@radix-ui/react-toggle-group/dist/index.mjs");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* harmony import */ var _repo_design_system_components_ui_toggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @repo/design-system/components/ui/toggle */ "../../packages/design-system/components/ui/toggle.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


var _s = __webpack_require__.$Refresh$.signature();
"use client";




const ToggleGroupContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({
    size: "default",
    variant: "default"
});
function ToggleGroup({ className, variant, size, children, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toggle_group__WEBPACK_IMPORTED_MODULE_4__.Root, {
        "data-slot": "toggle-group",
        "data-variant": variant,
        "data-size": size,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs", className),
        ...props,
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleGroupContext.Provider, {
            value: {
                variant,
                size
            },
            children: children
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\toggle-group.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\toggle-group.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
_c = ToggleGroup;
function ToggleGroupItem({ className, children, variant, size, ...props }) {
    _s();
    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(ToggleGroupContext);
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toggle_group__WEBPACK_IMPORTED_MODULE_4__.Item, {
        "data-slot": "toggle-group-item",
        "data-variant": context.variant || variant,
        "data-size": context.size || size,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)((0,_repo_design_system_components_ui_toggle__WEBPACK_IMPORTED_MODULE_3__.toggleVariants)({
            variant: context.variant || variant,
            size: context.size || size
        }), "min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l", className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\toggle-group.tsx",
        lineNumber: 54,
        columnNumber: 5
    }, this);
}
_s(ToggleGroupItem, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
_c1 = ToggleGroupItem;

ToggleGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ToggleGroup"
};
ToggleGroupItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ToggleGroupItem"
};
var _c, _c1;
__webpack_require__.$Refresh$.register(_c, "ToggleGroup");
__webpack_require__.$Refresh$.register(_c1, "ToggleGroupItem");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/toggle.tsx":
/*!*************************************************************!*\
  !*** ../../packages/design-system/components/ui/toggle.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toggle: () => (/* binding */ Toggle),
/* harmony export */   toggleVariants: () => (/* binding */ toggleVariants)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toggle */ "../../node_modules/.pnpm/@radix-ui+react-toggle@1.1._2d180a23b7a9fca0a7221385c1a5d1d5/node_modules/@radix-ui/react-toggle/dist/index.mjs");
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";





const toggleVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)("inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap", {
    variants: {
        variant: {
            default: "bg-transparent",
            outline: "border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground"
        },
        size: {
            default: "h-9 px-2 min-w-9",
            sm: "h-8 px-1.5 min-w-8",
            lg: "h-10 px-2.5 min-w-10"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Toggle({ className, variant, size, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toggle__WEBPACK_IMPORTED_MODULE_4__.Root, {
        "data-slot": "toggle",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(toggleVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\toggle.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
_c = Toggle;

Toggle.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Toggle"
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Toggle");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/toggle-group.stories.tsx":
/*!******************************************!*\
  !*** ./stories/toggle-group.stories.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   Disabled: () => (/* binding */ Disabled),
/* harmony export */   Large: () => (/* binding */ Large),
/* harmony export */   Outline: () => (/* binding */ Outline),
/* harmony export */   Single: () => (/* binding */ Single),
/* harmony export */   Small: () => (/* binding */ Small),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _barrel_optimize_names_Bold_Italic_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Italic,Underline!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bold.js");
/* harmony import */ var _barrel_optimize_names_Bold_Italic_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Italic,Underline!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/italic.js");
/* harmony import */ var _barrel_optimize_names_Bold_Italic_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Italic,Underline!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/underline.js");
/* harmony import */ var _repo_design_system_components_ui_toggle_group__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/toggle-group */ "../../packages/design-system/components/ui/toggle-group.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");




/**
 * A set of two-state buttons that can be toggled on or off.
 */
const meta = {
  title: 'ui/ToggleGroup',
  component: _repo_design_system_components_ui_toggle_group__WEBPACK_IMPORTED_MODULE_1__.ToggleGroup,
  tags: ['autodocs'],
  argTypes: {
    type: {
      options: ['multiple', 'single'],
      control: {
        type: 'radio'
      }
    }
  },
  args: {
    variant: 'default',
    size: 'default',
    type: 'multiple',
    disabled: false
  },
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_toggle_group__WEBPACK_IMPORTED_MODULE_1__.ToggleGroup, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_toggle_group__WEBPACK_IMPORTED_MODULE_1__.ToggleGroupItem, {
      value: "bold",
      "aria-label": "Toggle bold",
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Italic_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], {
        className: "h-4 w-4"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\toggle-group.stories.tsx",
        lineNumber: 31,
        columnNumber: 9
      }, undefined)
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\toggle-group.stories.tsx",
      lineNumber: 30,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_toggle_group__WEBPACK_IMPORTED_MODULE_1__.ToggleGroupItem, {
      value: "italic",
      "aria-label": "Toggle italic",
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Italic_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], {
        className: "h-4 w-4"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\toggle-group.stories.tsx",
        lineNumber: 34,
        columnNumber: 9
      }, undefined)
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\toggle-group.stories.tsx",
      lineNumber: 33,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_toggle_group__WEBPACK_IMPORTED_MODULE_1__.ToggleGroupItem, {
      value: "underline",
      "aria-label": "Toggle underline",
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Italic_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {
        className: "h-4 w-4"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\toggle-group.stories.tsx",
        lineNumber: 37,
        columnNumber: 9
      }, undefined)
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\toggle-group.stories.tsx",
      lineNumber: 36,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\toggle-group.stories.tsx",
    lineNumber: 29,
    columnNumber: 5
  }, undefined),
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: "A set of two-state buttons that can be toggled on or off."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the toggle group.
 */
const Default = {};
/**
 * Use the `outline` variant to emphasizing the individuality of each button
 * while keeping them visually cohesive.
 */
const Outline = {
  args: {
    variant: 'outline'
  }
};
/**
 * Use the `single` type to create exclusive selection within the button
 * group, allowing only one button to be active at a time.
 */
const Single = {
  args: {
    type: 'single'
  }
};
/**
 * Use the `sm` size for a compact version of the button group, featuring
 * smaller buttons for spaces with limited real estate.
 */
const Small = {
  args: {
    size: 'sm'
  }
};
/**
 * Use the `lg` size for a more prominent version of the button group, featuring
 * larger buttons for emphasis.
 */
const Large = {
  args: {
    size: 'lg'
  }
};
/**
 * Add the `disabled` prop to a button to prevent interactions.
 */
const Disabled = {
  args: {
    disabled: true
  }
};
;
const __namedExportsOrder = ["Default", "Outline", "Single", "Small", "Large", "Disabled"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the toggle group.",
      ...Default.parameters?.docs?.description
    }
  }
};
Outline.parameters = {
  ...Outline.parameters,
  docs: {
    ...Outline.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    variant: 'outline'\n  }\n}",
      ...Outline.parameters?.docs?.source
    },
    description: {
      story: "Use the `outline` variant to emphasizing the individuality of each button\r\nwhile keeping them visually cohesive.",
      ...Outline.parameters?.docs?.description
    }
  }
};
Single.parameters = {
  ...Single.parameters,
  docs: {
    ...Single.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    type: 'single'\n  }\n}",
      ...Single.parameters?.docs?.source
    },
    description: {
      story: "Use the `single` type to create exclusive selection within the button\r\ngroup, allowing only one button to be active at a time.",
      ...Single.parameters?.docs?.description
    }
  }
};
Small.parameters = {
  ...Small.parameters,
  docs: {
    ...Small.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    size: 'sm'\n  }\n}",
      ...Small.parameters?.docs?.source
    },
    description: {
      story: "Use the `sm` size for a compact version of the button group, featuring\r\nsmaller buttons for spaces with limited real estate.",
      ...Small.parameters?.docs?.description
    }
  }
};
Large.parameters = {
  ...Large.parameters,
  docs: {
    ...Large.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    size: 'lg'\n  }\n}",
      ...Large.parameters?.docs?.source
    },
    description: {
      story: "Use the `lg` size for a more prominent version of the button group, featuring\r\nlarger buttons for emphasis.",
      ...Large.parameters?.docs?.description
    }
  }
};
Disabled.parameters = {
  ...Disabled.parameters,
  docs: {
    ...Disabled.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    disabled: true\n  }\n}",
      ...Disabled.parameters?.docs?.source
    },
    description: {
      story: "Add the `disabled` prop to a button to prevent interactions.",
      ...Disabled.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=toggle-group-stories.iframe.bundle.js.map