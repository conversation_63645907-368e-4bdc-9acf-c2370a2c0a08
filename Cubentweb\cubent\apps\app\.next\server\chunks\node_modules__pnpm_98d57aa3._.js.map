{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/instance.ts"], "sourcesContent": ["/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n"], "names": [], "mappings": ";;;;AAGO,SAAS,UAAU,WAAA,EAA8B;IACtD,OACE,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,iBAAiB,KACtC,YAAY,QAAA,CAAS,oBAAoB;AAE7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/constants.ts"], "sourcesContent": ["export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAO,IAAM,+BAA+B;IAAC;IAAY;IAAiB,eAAe;CAAA;AAClF,IAAM,gCAAgC;IAAC;IAAiB;IAAsB,wBAAwB;CAAA;AACtG,IAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AACO,IAAM,qBAAqB;IAAC;IAAY;IAAgB;IAAiB,wBAAwB;CAAA;AACjG,IAAM,uBAAuB;IAAC,oBAAoB;CAAA;AAClD,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAMrB,SAAS,aAAa,EAAA,EAAY,SAAyB,KAAA,EAAe;IAC/E,OAAO,CAAA,6BAAA,EAAgC,EAAE,CAAA,CAAA,EAAI,MAAM,EAAA;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/url.ts"], "sourcesContent": ["import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n"], "names": ["url"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,kBAAkB,cAAc,EAAA,EAAqB;IACnE,IAAI,YAAY,UAAA,CAAW,GAAG,GAAG;QAC/B,cAAc,YAAY,KAAA,CAAM,CAAC;IACnC;IACA,OAAO,IAAI,gBAAgB,WAAW;AACxC;AAEO,SAAS,YAAY,MAAM,EAAA,EAAY;IAC5C,OAAA,CAAQ,OAAO,EAAA,EAAI,OAAA,CAAQ,YAAY,EAAE;AAC3C;AAEO,SAAS,eAAe,GAAA,EAAyB;IACtD,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI;IACJ,IAAI,IAAI,KAAA,CAAM,iBAAiB,GAAG;QAChC,QAAQ;IACV,OAAA,IAAW,IAAI,KAAA,CAAM,kBAAkB,GAAG;QACxC,OAAO;IACT,OAAO;QACL,QAAQ;IACV;IAEA,MAAM,WAAW,IAAI,OAAA,CAAQ,OAAO,EAAE;IACtC,OAAO,CAAA,MAAA,EAAS,QAAQ,EAAA;AAC1B;AAQO,IAAM,8BAA8B,CAAC,aAAqB,YAAqB;IACpF,IAAI,CAAC,WAAW,4RAAA,EAAU,WAAW,GAAG;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,OAAO,QAAQ,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK;AAClC;AAOO,IAAM,eAAe,CAAC,aAAqB,EAAE,cAAA,CAAe,CAAA,KAAmC;IACpG,MAAM,sBAAsB,YAAY,OAAA,CAAQ,iBAAiB,EAAE;IACnE,MAAM,QAAQ,4BAA4B,aAAa,cAAc;IACrE,OAAO,CAAA,QAAA,EAAW,mBAAmB,CAAA,qBAAA,EAAwB,kBAAkB,KAAK,CAAA,sBAAA,CAAA;AACtF;AAMO,SAAS,+BAA+B,IAAA,EAAuB;IACpE,mRAAO,+BAAA,CAA6B,IAAA,CAAK,CAAA,oBAAmB;QAC1D,OAAO,KAAK,UAAA,CAAW,WAAW,KAAK,KAAK,QAAA,CAAS,eAAe;IACtE,CAAC;AACH;AAQO,SAAS,gCAAgC,IAAA,EAAuB;IACrE,mRAAO,gCAAA,CAA8B,IAAA,CAAK,CAAA,qBAAoB;QAC5D,OAAO,KAAK,QAAA,CAAS,gBAAgB,KAAK,CAAC,KAAK,QAAA,CAAS,WAAW,gBAAgB;IACtF,CAAC;AACH;AAIA,IAAM,oBAAoB;AAEnB,SAAS,iBAAiB,QAAQ,EAAA,EAAI,uBAAA,EAA4C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG;IAC3B;IACA,OAAO,kBAAkB,IAAA,CAAK,KAAK;AACrC;AAEO,SAAS,kBAAkB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG,IAAI,QAAQ,QAAQ;IAC/C;IACA,IAAI,iBAAiB,OAAO,IAAI,GAAG;QACjC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;QACpC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;IACF;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAO,KAAK,MAAA,CAAO,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9D;AAEO,SAAS,qBAAqB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IAC1F,IAAI,CAAC,yBAAyB;QAC5B,OAAA,CAAQ,iBAAiB,KAAK,IAAI,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI,KAAA,KAAU;IACnE;IACA,IAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;QAClC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;IACtC;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAA,CAAQ,GAAG,KAAA,CAAM,GAAG,CAAA,CAAE,KAAK,GAAA,IAAA,CAAQ,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9E;AAEO,SAAS,gBAAgB,QAAQ,EAAA,EAAa;IACnD,OAAO,MAAM,UAAA,CAAW,GAAG;AAC7B;AAEO,SAAS,oBAAoB,QAAQ,EAAA,EAAY;IACtD,OAAA,CAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAA,CAAM,CAAC,IAAI,KAAA,KAAU;AAC9D;AAEO,SAAS,iBAAiB,QAAQ,EAAA,EAAY;IACnD,OAAO,gBAAgB,KAAK,IAAI,QAAQ,MAAM;AAChD;AAEO,SAAS,mBAAmB,QAAQ,EAAA,EAAY;IACrD,OAAO,MACJ,KAAA,CAAM,KAAK,EACX,GAAA,CAAI,CAAA,UAAW,QAAQ,OAAA,CAAQ,WAAW,GAAG,CAAC,EAC9C,IAAA,CAAK,KAAK;AACf;AAEO,SAAS,cAAc,GAAA,EAAa;IACzC,OAAO,OAAO,QAAQ;AACxB;AAEA,IAAM,wBAAwB;AAEvB,SAAS,QAAQ,IAAA,EAAA,GAAiB,KAAA,EAAyB;IAChE,IAAI,MAAM,QAAQ;IAElB,KAAA,MAAW,WAAW,MAAM,MAAA,CAAO,CAAAA,OAAO,cAAcA,IAAG,CAAC,EAAG;QAC7D,IAAI,KAAK;YAEP,MAAM,WAAW,QAAQ,OAAA,CAAQ,uBAAuB,EAAE;YAC1D,MAAM,kBAAkB,GAAG,IAAI;QACjC,OAAO;YACL,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAMA,IAAM,qBAAqB;AACpB,IAAM,gBAAgB,CAAC,MAAgB,mBAAmB,IAAA,CAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/retry.ts"], "sourcesContent": ["type Milliseconds = number;\n\ntype RetryOptions = Partial<{\n  /**\n   * The initial delay before the first retry.\n   * @default 125\n   */\n  initialDelay: Milliseconds;\n  /**\n   * The maximum delay between retries.\n   * The delay between retries will never exceed this value.\n   * If set to 0, the delay will increase indefinitely.\n   * @default 0\n   */\n  maxDelayBetweenRetries: Milliseconds;\n  /**\n   * The multiplier for the exponential backoff.\n   * @default 2\n   */\n  factor: number;\n  /**\n   * A function to determine if the operation should be retried.\n   * The callback accepts the error that was thrown and the number of iterations.\n   * The iterations variable references the number of retries AFTER attempt\n   * that caused the error and starts at 1 (as in, this is the 1st, 2nd, nth retry).\n   * @default (error, iterations) => iterations < 5\n   */\n  shouldRetry: (error: unknown, iterations: number) => boolean;\n  /**\n   * Controls whether the helper should retry the operation immediately once before applying exponential backoff.\n   * The delay for the immediate retry is 100ms.\n   * @default false\n   */\n  retryImmediately: boolean;\n  /**\n   * If true, the intervals will be multiplied by a factor in the range of [1,2].\n   * @default true\n   */\n  jitter: boolean;\n}>;\n\nconst defaultOptions: Required<RetryOptions> = {\n  initialDelay: 125,\n  maxDelayBetweenRetries: 0,\n  factor: 2,\n  shouldRetry: (_: unknown, iteration: number) => iteration < 5,\n  retryImmediately: false,\n  jitter: true,\n};\n\nconst RETRY_IMMEDIATELY_DELAY = 100;\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst applyJitter = (delay: Milliseconds, jitter: boolean) => {\n  return jitter ? delay * (1 + Math.random()) : delay;\n};\n\nconst createExponentialDelayAsyncFn = (\n  opts: Required<Pick<RetryOptions, 'initialDelay' | 'maxDelayBetweenRetries' | 'factor' | 'jitter'>>,\n) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.initialDelay;\n    const base = opts.factor;\n    let delay = constant * Math.pow(base, timesCalled);\n    delay = applyJitter(delay, opts.jitter);\n    return Math.min(opts.maxDelayBetweenRetries || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\n/**\n * Retries a callback until it succeeds or the shouldRetry function returns false.\n * See {@link RetryOptions} for the available options.\n */\nexport const retry = async <T>(callback: () => T | Promise<T>, options: RetryOptions = {}): Promise<T> => {\n  let iterations = 0;\n  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {\n    ...defaultOptions,\n    ...options,\n  };\n\n  const delay = createExponentialDelayAsyncFn({\n    initialDelay,\n    maxDelayBetweenRetries,\n    factor,\n    jitter,\n  });\n\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterations++;\n      if (!shouldRetry(e, iterations)) {\n        throw e;\n      }\n      if (retryImmediately && iterations === 1) {\n        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));\n      } else {\n        await delay();\n      }\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;AAyCA,IAAM,iBAAyC;IAC7C,cAAc;IACd,wBAAwB;IACxB,QAAQ;IACR,aAAa,CAAC,GAAY,YAAsB,YAAY;IAC5D,kBAAkB;IAClB,QAAQ;AACV;AAEA,IAAM,0BAA0B;AAEhC,IAAM,QAAQ,OAAO,KAAqB,IAAI,QAAQ,CAAA,IAAK,WAAW,GAAG,EAAE,CAAC;AAE5E,IAAM,cAAc,CAAC,OAAqB,WAAoB;IAC5D,OAAO,SAAS,QAAA,CAAS,IAAI,KAAK,MAAA,CAAO,CAAA,IAAK;AAChD;AAEA,IAAM,gCAAgC,CACpC,SACG;IACH,IAAI,cAAc;IAElB,MAAM,qBAAqB,MAAM;QAC/B,MAAM,WAAW,KAAK,YAAA;QACtB,MAAM,OAAO,KAAK,MAAA;QAClB,IAAI,QAAQ,WAAW,KAAK,GAAA,CAAI,MAAM,WAAW;QACjD,QAAQ,YAAY,OAAO,KAAK,MAAM;QACtC,OAAO,KAAK,GAAA,CAAI,KAAK,sBAAA,IAA0B,OAAO,KAAK;IAC7D;IAEA,OAAO,YAA2B;QAChC,MAAM,MAAM,mBAAmB,CAAC;QAChC;IACF;AACF;AAMO,IAAM,QAAQ,OAAU,UAAgC,UAAwB,CAAC,CAAA,KAAkB;IACxG,IAAI,aAAa;IACjB,MAAM,EAAE,WAAA,EAAa,YAAA,EAAc,sBAAA,EAAwB,MAAA,EAAQ,gBAAA,EAAkB,MAAA,CAAO,CAAA,GAAI;QAC9F,GAAG,cAAA;QACH,GAAG,OAAA;IACL;IAEA,MAAM,QAAQ,8BAA8B;QAC1C;QACA;QACA;QACA;IACF,CAAC;IAED,MAAO,KAAM;QACX,IAAI;YACF,OAAO,MAAM,SAAS;QACxB,EAAA,OAAS,GAAG;YACV;YACA,IAAI,CAAC,YAAY,GAAG,UAAU,GAAG;gBAC/B,MAAM;YACR;YACA,IAAI,oBAAoB,eAAe,GAAG;gBACxC,MAAM,MAAM,YAAY,yBAAyB,MAAM,CAAC;YAC1D,OAAO;gBACL,MAAM,MAAM;YACd;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/isomorphicAtob.ts"], "sourcesContent": ["/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;AAIO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,WAAW,eAAe,OAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,OAAO,MAAA,CAAO,MAAM,QAAQ,EAAE,QAAA,CAAS;IACpD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/isomorphicBtoa.ts"], "sourcesContent": ["export const isomorphicBtoa = (data: string) => {\n  if (typeof btoa !== 'undefined' && typeof btoa === 'function') {\n    return btoa(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data).toString('base64');\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,WAAW,eAAe,OAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,OAAO,MAAA,CAAO,IAAI,EAAE,QAAA,CAAS,QAAQ;IAClD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/keys.ts"], "sourcesContent": ["import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAaA,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAGpC,IAAM,qCAAqC;AAEpC,SAAS,oBAAoB,WAAA,EAA6B;IAC/D,MAAM,WACJ,mCAAmC,IAAA,CAAK,WAAW,KAClD,YAAY,UAAA,CAAW,QAAQ,iRAAK,+BAAA,CAA6B,IAAA,CAAK,CAAA,IAAK,YAAY,QAAA,CAAS,CAAC,CAAC;IACrG,MAAM,YAAY,WAAW,8BAA8B;IAC3D,OAAO,GAAG,SAAS,mRAAG,iBAAA,EAAe,GAAG,WAAW,CAAA,CAAA,CAAG,CAAC,EAAA;AACzD;AAUO,SAAS,oBACd,GAAA,EACA,UAA0F,CAAC,CAAA,EACpE;IACvB,MAAM,OAAO;IAEb,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;QAClC,IAAI,QAAQ,KAAA,IAAS,CAAC,KAAK;YACzB,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,QAAQ,KAAA,IAAS,CAAC,iBAAiB,GAAG,GAAG;YAC3C,MAAM,IAAI,MAAM,4BAA4B;QAC9C;QACA,OAAO;IACT;IAEA,MAAM,eAAe,IAAI,UAAA,CAAW,2BAA2B,IAAI,eAAe;IAElF,IAAI,8RAAc,iBAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IAGlD,cAAc,YAAY,KAAA,CAAM,GAAG,CAAA,CAAE;IAErC,IAAI,QAAQ,QAAA,EAAU;QACpB,cAAc,QAAQ,QAAA;IACxB,OAAA,IAAW,iBAAiB,iBAAiB,QAAQ,MAAA,IAAU,QAAQ,WAAA,EAAa;QAClF,cAAc,CAAA,MAAA,EAAS,QAAQ,MAAM,EAAA;IACvC;IAEA,OAAO;QACL;QACA;IACF;AACF;AAQO,SAAS,iBAAiB,MAAc,EAAA,EAAI;IACjD,IAAI;QACF,MAAM,iBAAiB,IAAI,UAAA,CAAW,2BAA2B,KAAK,IAAI,UAAA,CAAW,2BAA2B;QAEhH,MAAM,6SAA6B,iBAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK,EAAE,EAAE,QAAA,CAAS,GAAG;QAEvF,OAAO,kBAAkB;IAC3B,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,SAAS,6BAA6B;IAC3C,MAAM,uBAAuB,aAAA,GAAA,IAAI,IAAqB;IAEtD,OAAO;QACL,mBAAmB,CAAC,QAA+B;YACjD,IAAI,CAAC,KAAK;gBACR,OAAO;YACT;YAEA,MAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI,QAAA;YACrD,IAAI,MAAM,qBAAqB,GAAA,CAAI,QAAQ;YAC3C,IAAI,QAAQ,KAAA,GAAW;gBACrB,kRAAM,0BAAA,CAAwB,IAAA,CAAK,CAAA,IAAK,SAAS,QAAA,CAAS,CAAC,CAAC;gBAC5D,qBAAqB,GAAA,CAAI,UAAU,GAAG;YACxC;YACA,OAAO;QACT;IACF;AACF;AAEO,SAAS,gCAAgC,MAAA,EAAyB;IACvE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,+BAA+B,MAAA,EAAyB;IACtE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,2BAA2B,MAAA,EAAyB;IAClE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,0BAA0B,MAAA,EAAyB;IACjE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEA,eAAsB,gBACpB,cAAA,EACA,SAAuB,WAAW,MAAA,CAAO,MAAA,EACxB;IACjB,MAAM,OAAO,IAAI,YAAY,EAAE,MAAA,CAAO,cAAc;IACpD,MAAM,SAAS,MAAM,OAAO,MAAA,CAAO,SAAS,IAAI;IAChD,MAAM,eAAe,OAAO,YAAA,CAAa,GAAG,IAAI,WAAW,MAAM,CAAC;IAElE,uRAAO,iBAAA,EAAe,YAAY,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,SAAA,CAAU,GAAG,CAAC;AAC9F;AAEO,IAAM,wBAAwB,CAAC,YAAoB,iBAAiC;IACzF,OAAO,GAAG,UAAU,CAAA,CAAA,EAAI,YAAY,EAAA;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/runtimeEnvironment.ts"], "sourcesContent": ["export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n"], "names": [], "mappings": ";;;;;;AAAO,IAAM,2BAA2B,MAAe;IACrD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAIT,OAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;IAC9C,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;IACpD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/deprecated.ts"], "sourcesContent": ["import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;AAqBA,IAAM,oBAAoB,aAAA,GAAA,IAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;IACjF,MAAM,8RAAc,oBAAA,CAAkB,OAAK,ySAAA,CAAwB;IACnE,MAAM,YAAY,OAAO;IACzB,IAAI,kBAAkB,GAAA,CAAI,SAAS,KAAK,aAAa;QACnD;IACF;IACA,kBAAkB,GAAA,CAAI,SAAS;IAE/B,QAAQ,IAAA,CACN,CAAA,8BAAA,EAAiC,MAAM,CAAA;AAAA,EAAmE,OAAO,EAAA;AAErH;AAyBO,IAAM,qBAAqB,CAAC,KAAe,UAAkB,SAAiB,WAAW,KAAA,KAAgB;IAC9G,MAAM,SAAS,WAAW,MAAM,IAAI,SAAA;IAEpC,IAAI,QAAQ,MAAA,CAAO,QAAQ,CAAA;IAC3B,OAAO,cAAA,CAAe,QAAQ,UAAU;QACtC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG,IAAI,IAAI,CAAA,CAAA,EAAI,QAAQ,EAAE;YACvD,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH;AAYO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;IACT,IAAI,QAAQ,GAAA,CAAI,QAAQ,CAAA;IACxB,OAAO,cAAA,CAAe,KAAK,UAAU;QACnC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG;YACjC,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/error.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n"], "names": ["packageName", "customMessages"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,oBAAoB,CAAA,EAAiB;IACnD,MAAM,SAAS,GAAG;IAClB,MAAM,OAAO,GAAG,QAAA,CAAS,CAAC,CAAA,EAAG;IAC7B,OAAO,SAAS,4BAA4B,WAAW;AACzD;AAEO,SAAS,eAAe,CAAA,EAAmC;IAChE,OAAO;QAAC;QAAmB;QAAuB,uBAAuB;KAAA,CAAE,QAAA,CAAS,EAAE,MAAA,CAAO,CAAC,CAAA,CAAE,IAAI;AACtG;AAEO,SAAS,WAAW,CAAA,EAAiB;IAC1C,MAAM,SAAS,GAAG;IAClB,OAAO,CAAC,CAAC,UAAU,UAAU,OAAO,SAAS;AAC/C;AAEO,SAAS,eAAe,CAAA,EAAiB;IAE9C,MAAM,UAAA,CAAW,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,EAAA,IAAM,EAAA,EAAI,WAAA,CAAY,EAAE,OAAA,CAAQ,QAAQ,EAAE;IAChF,OAAO,QAAQ,QAAA,CAAS,cAAc;AACxC;AAiBO,SAAS,aAAa,KAAA,EAAgF;IAC3G,OAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;AAC9F;AAEO,SAAS,wBAAwB,GAAA,EAAwC;IAC9E,OAAO,gBAAgB;AACzB;AAkBO,SAAS,oBAAoB,GAAA,EAAoC;IACtE,OAAO,uBAAuB;AAChC;AAEO,SAAS,+BAA+B,GAAA,EAAU;IACvD,OAAO,oBAAoB,GAAG,KAAK,IAAI,IAAA,KAAS;AAClD;AAEO,SAAS,gBAAgB,GAAA,EAAgC;IAC9D,OAAO,UAAU,OAAO;QAAC;QAAM;QAAO,KAAK;KAAA,CAAE,QAAA,CAAS,IAAI,IAAI,KAAK,aAAa;AAClF;AAEO,SAAS,kBAAkB,GAAA,EAAU;IAC1C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAEO,SAAS,qBAAqB,GAAA,EAAU;IAC7C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAEO,SAAS,YAAY,OAA4B,CAAC,CAAA,EAAoB;IAC3E,OAAO,KAAK,MAAA,GAAS,IAAI,KAAK,GAAA,CAAI,UAAU,IAAI,CAAC,CAAA;AACnD;AAEO,SAAS,WAAW,KAAA,EAAyC;IAClE,OAAO;QACL,MAAM,MAAM,IAAA;QACZ,SAAS,MAAM,OAAA;QACf,aAAa,MAAM,YAAA;QACnB,MAAM;YACJ,WAAW,OAAO,MAAM;YACxB,WAAW,OAAO,MAAM;YACxB,gBAAgB,OAAO,MAAM;YAC7B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;QACvB;IACF;AACF;AAEO,SAAS,YAAY,KAAA,EAAgD;IAC1E,OAAO;QACL,MAAM,OAAO,QAAQ;QACrB,SAAS,OAAO,WAAW;QAC3B,cAAc,OAAO;QACrB,MAAM;YACJ,YAAY,OAAO,MAAM;YACzB,YAAY,OAAO,MAAM;YACzB,iBAAiB,OAAO,MAAM;YAC9B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;QACvB;IACF;AACF;AAEO,IAAM,wBAAN,MAAM,+BAA8B,MAAM;IAU/C,YAAY,OAAA,EAAiB,EAAE,IAAA,EAAM,MAAA,EAAQ,YAAA,EAAc,UAAA,CAAW,CAAA,CAA4B;QAChG,KAAA,CAAM,OAAO;QAYf,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,IAAI,UAAU,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,CAAA;OAAA,EAAY,IAAA,CAAK,MAAM,CAAA;mBAAA,EAAwB,IAAA,CAAK,MAAA,CAAO,GAAA,CAC9G,CAAA,IAAK,KAAK,SAAA,CAAU,CAAC,IACtB;YAED,IAAI,IAAA,CAAK,YAAA,EAAc;gBACrB,WAAW,CAAA;gBAAA,EAAqB,IAAA,CAAK,YAAY,EAAA;YACnD;YAEA,OAAO;QACT;QApBE,OAAO,cAAA,CAAe,IAAA,EAAM,uBAAsB,SAAS;QAE3D,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,MAAA,GAAS,YAAY,IAAI;IAChC;AAaF;AASO,IAAM,oBAAN,MAAM,2BAA0B,MAAM;IAiB3C,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqB;QACvD,MAAM,SAAS;QACf,MAAM,QAAQ,IAAI,OAAO,OAAO,OAAA,CAAQ,KAAK,MAAM,GAAG,GAAG;QACzD,MAAM,YAAY,QAAQ,OAAA,CAAQ,OAAO,EAAE;QAC3C,MAAM,WAAW,GAAG,MAAM,CAAA,CAAA,EAAI,UAAU,IAAA,CAAK,CAAC,CAAA;;OAAA,EAAc,IAAI,CAAA;AAAA,CAAA;QAChE,KAAA,CAAM,QAAQ;QAehB;;;;KAAA,GAAA,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,OAAO,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,EAAA;QAChD;QAfE,OAAO,cAAA,CAAe,IAAA,EAAM,mBAAkB,SAAS;QAEvD,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,iBAAA,GAAoB;QACzB,IAAA,CAAK,IAAA,GAAO;IACd;AAUF;AAEO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;IAGxC,YAAY,IAAA,CAAc;QACxB,KAAA,CAAM,IAAI;QACV,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,IAAA,GAAO;QACZ,OAAO,cAAA,CAAe,IAAA,EAAM,gBAAe,SAAS;IACtD;AACF;AAEO,SAAS,iBAAiB,GAAA,EAAmC;IAClE,OAAO,IAAI,IAAA,KAAS;AACtB;AAOO,IAAM,qBAAqB;IAChC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEO,IAAM,2BAA2B;IACtC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEA,IAAM,kBAAkB,OAAO,MAAA,CAAO;IACpC,6BAA6B,CAAA,gJAAA,CAAA;IAC7B,mCAAmC,CAAA,uJAAA,CAAA;IACnC,mCAAmC,CAAA,sGAAA,CAAA;IACnC,8BAA8B,CAAA,iGAAA,CAAA;IAC9B,sBAAsB,CAAA,gIAAA,CAAA;AACxB,CAAC;AA+BM,SAAS,kBAAkB,EAAE,WAAA,EAAa,cAAA,CAAe,CAAA,EAAsC;IACpG,IAAI,MAAM;IAEV,MAAM,WAAW;QACf,GAAG,eAAA;QACH,GAAG,cAAA;IACL;IAEA,SAAS,aAAa,UAAA,EAAoB,YAAA,EAAgD;QACxF,IAAI,CAAC,cAAc;YACjB,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,UAAU,EAAA;QAC9B;QAEA,IAAI,MAAM;QACV,MAAM,UAAU,WAAW,QAAA,CAAS,uBAAuB;QAE3D,KAAA,MAAW,SAAS,QAAS;YAC3B,MAAM,cAAA,CAAe,YAAA,CAAa,KAAA,CAAM,CAAC,CAAC,CAAA,IAAK,EAAA,EAAI,QAAA,CAAS;YAC5D,MAAM,IAAI,OAAA,CAAQ,CAAA,EAAA,EAAK,KAAA,CAAM,CAAC,CAAC,CAAA,EAAA,CAAA,EAAM,WAAW;QAClD;QAEA,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,GAAG,EAAA;IACvB;IAEA,OAAO;QACL,gBAAe,EAAE,aAAAA,YAAAA,CAAY,CAAA,EAAsC;YACjE,IAAI,OAAOA,iBAAgB,UAAU;gBACnC,MAAMA;YACR;YACA,OAAO,IAAA;QACT;QAEA,aAAY,EAAE,gBAAAC,eAAAA,CAAe,CAAA,EAAsC;YACjE,OAAO,MAAA,CAAO,UAAUA,mBAAkB,CAAC,CAAC;YAC5C,OAAO,IAAA;QACT;QAEA,iCAAgC,MAAA,EAAiC;YAC/D,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAA,EAAmC,MAAM,CAAC;QAClF;QAEA,sBAAqB,MAAA,EAAiC;YACpD,MAAM,IAAI,MAAM,aAAa,SAAS,2BAAA,EAA6B,MAAM,CAAC;QAC5E;QAEA,kCAAyC;YACvC,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;QAC1E;QAEA,6BAAoC;YAClC,MAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;QACrE;QAEA,gCAA+B,MAAA,EAAoC;YACjE,MAAM,IAAI,MAAM,aAAa,SAAS,oBAAA,EAAsB,MAAM,CAAC;QACrE;QAEA,OAAM,OAAA,EAAwB;YAC5B,MAAM,IAAI,MAAM,aAAa,OAAO,CAAC;QACvC;IACF;AACF;AAgBO,IAAM,qBAAN,cAAiC,kBAAkB;IAMxD,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqC;QACvE,KAAA,CAAM,SAAS;YAAE;QAAK,CAAC;QACvB,IAAA,CAAK,IAAA,GAAO;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/authorization.ts"], "sourcesContent": ["import type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n  ReverificationConfig,\n  SessionStatusClaim,\n  SessionVerificationLevel,\n  SessionVerificationTypes,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\n\ntype TypesToConfig = Record<SessionVerificationTypes, Exclude<ReverificationConfig, SessionVerificationTypes>>;\ntype AuthorizationOptions = {\n  userId: string | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: string | null | undefined;\n  orgPermissions: string[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n  features: string | null | undefined;\n  plans: string | null | undefined;\n};\n\ntype CheckOrgAuthorization = (\n  params: { role?: OrganizationCustomRoleKey; permission?: OrganizationCustomPermissionKey },\n  options: Pick<AuthorizationOptions, 'orgId' | 'orgRole' | 'orgPermissions'>,\n) => boolean | null;\n\ntype CheckBillingAuthorization = (\n  params: { feature?: string; plan?: string },\n  options: Pick<AuthorizationOptions, 'plans' | 'features'>,\n) => boolean | null;\n\ntype CheckReverificationAuthorization = (\n  params: {\n    reverification?: ReverificationConfig;\n  },\n  { factorVerificationAge }: AuthorizationOptions,\n) => boolean | null;\n\nconst TYPES_TO_OBJECTS: TypesToConfig = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: 'multi_factor',\n  },\n  strict: {\n    afterMinutes: 10,\n    level: 'second_factor',\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: 'second_factor',\n  },\n  lax: {\n    afterMinutes: 1_440,\n    level: 'second_factor',\n  },\n};\n\nconst ALLOWED_LEVELS = new Set<SessionVerificationLevel>(['first_factor', 'second_factor', 'multi_factor']);\n\nconst ALLOWED_TYPES = new Set<SessionVerificationTypes>(['strict_mfa', 'strict', 'moderate', 'lax']);\n\n// Helper functions\nconst isValidMaxAge = (maxAge: any) => typeof maxAge === 'number' && maxAge > 0;\nconst isValidLevel = (level: any) => ALLOWED_LEVELS.has(level);\nconst isValidVerificationType = (type: any) => ALLOWED_TYPES.has(type);\n\nconst prefixWithOrg = (value: string) => (value.startsWith('org:') ? value : `org:${value}`);\n\n/**\n * Checks if a user has the required organization-level authorization.\n * Verifies if the user has the specified role or permission within their organization.\n * @returns null, if unable to determine due to missing data or unspecified role/permission.\n */\nconst checkOrgAuthorization: CheckOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n\n  if (params.permission) {\n    return orgPermissions.includes(prefixWithOrg(params.permission));\n  }\n\n  if (params.role) {\n    return orgRole === prefixWithOrg(params.role);\n  }\n  return null;\n};\n\nconst checkForFeatureOrPlan = (claim: string, featureOrPlan: string) => {\n  const { org: orgFeatures, user: userFeatures } = splitByScope(claim);\n  const [scope, _id] = featureOrPlan.split(':');\n  const id = _id || scope;\n\n  if (scope === 'org') {\n    return orgFeatures.includes(id);\n  } else if (scope === 'user') {\n    return userFeatures.includes(id);\n  } else {\n    // Since org scoped features will not exist if there is not an active org, merging is safe.\n    return [...orgFeatures, ...userFeatures].includes(id);\n  }\n};\n\nconst checkBillingAuthorization: CheckBillingAuthorization = (params, options) => {\n  const { features, plans } = options;\n\n  if (params.feature && features) {\n    return checkForFeatureOrPlan(features, params.feature);\n  }\n\n  if (params.plan && plans) {\n    return checkForFeatureOrPlan(plans, params.plan);\n  }\n  return null;\n};\n\nconst splitByScope = (fea: string | null | undefined) => {\n  const features = fea ? fea.split(',').map(f => f.trim()) : [];\n\n  // TODO: make this more efficient\n  return {\n    org: features.filter(f => f.split(':')[0].includes('o')).map(f => f.split(':')[1]),\n    user: features.filter(f => f.split(':')[0].includes('u')).map(f => f.split(':')[1]),\n  };\n};\n\nconst validateReverificationConfig = (config: ReverificationConfig | undefined | null) => {\n  if (!config) {\n    return false;\n  }\n\n  const convertConfigToObject = (config: ReverificationConfig) => {\n    if (typeof config === 'string') {\n      return TYPES_TO_OBJECTS[config];\n    }\n    return config;\n  };\n\n  const isValidStringValue = typeof config === 'string' && isValidVerificationType(config);\n  const isValidObjectValue =\n    typeof config === 'object' && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n\n  return false;\n};\n\n/**\n * Evaluates if the user meets re-verification authentication requirements.\n * Compares the user's factor verification ages against the specified maxAge.\n * Handles different verification levels (first factor, second factor, multi-factor).\n * @returns null, if requirements or verification data are missing.\n */\nconst checkReverificationAuthorization: CheckReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n\n  // -1 indicates the factor group (1fa,2fa) is not enabled\n  // -1 for 1fa is not a valid scenario, but we need to make sure we handle it properly\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n\n  switch (level) {\n    case 'first_factor':\n      return isValidFactor1;\n    case 'second_factor':\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case 'multi_factor':\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\n\n/**\n * Creates a function for comprehensive user authorization checks.\n * Combines organization-level and reverification authentication checks.\n * The returned function authorizes if both checks pass, or if at least one passes\n * when the other is indeterminate. Fails if userId is missing.\n */\nconst createCheckAuthorization = (options: AuthorizationOptions): CheckAuthorizationWithCustomPermissions => {\n  return (params): boolean => {\n    if (!options.userId) {\n      return false;\n    }\n\n    const billingAuthorization = checkBillingAuthorization(params, options);\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n\n    if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === null)) {\n      return [billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === true);\n    }\n\n    return [billingAuthorization || orgAuthorization, reverificationAuthorization].every(a => a === true);\n  };\n};\n\ntype AuthStateOptions = {\n  authObject: {\n    userId?: string | null;\n    sessionId?: string | null;\n    sessionStatus?: SessionStatusClaim | null;\n    sessionClaims?: JwtPayload | null;\n    actor?: ActClaim | null;\n    orgId?: string | null;\n    orgRole?: OrganizationCustomRoleKey | null;\n    orgSlug?: string | null;\n    orgPermissions?: OrganizationCustomPermissionKey[] | null;\n    getToken: GetToken;\n    signOut: SignOut;\n    has: (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => boolean;\n  };\n  options: PendingSessionOptions;\n};\n\n/**\n * Shared utility function that centralizes auth state resolution logic,\n * preventing duplication across different packages\n * @internal\n */\nconst resolveAuthState = ({\n  authObject: {\n    sessionId,\n    sessionStatus,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    signOut,\n    getToken,\n    has,\n    sessionClaims,\n  },\n  options: { treatPendingAsSignedOut = true },\n}: AuthStateOptions): UseAuthReturn | undefined => {\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      sessionClaims: undefined,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (treatPendingAsSignedOut && sessionStatus === 'pending') {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId: null,\n      userId: null,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n};\n\nexport { createCheckAuthorization, validateReverificationConfig, resolveAuthState, splitByScope };\n"], "names": ["config"], "mappings": ";;;;;;;AA4CA,IAAM,mBAAkC;IACtC,YAAY;QACV,cAAc;QACd,OAAO;IACT;IACA,QAAQ;QACN,cAAc;QACd,OAAO;IACT;IACA,UAAU;QACR,cAAc;QACd,OAAO;IACT;IACA,KAAK;QACH,cAAc;QACd,OAAO;IACT;AACF;AAEA,IAAM,iBAAiB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAgB;IAAiB,cAAc;CAAC;AAE1G,IAAM,gBAAgB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAc;IAAU;IAAY,KAAK;CAAC;AAGnG,IAAM,gBAAgB,CAAC,SAAgB,OAAO,WAAW,YAAY,SAAS;AAC9E,IAAM,eAAe,CAAC,QAAe,eAAe,GAAA,CAAI,KAAK;AAC7D,IAAM,0BAA0B,CAAC,OAAc,cAAc,GAAA,CAAI,IAAI;AAErE,IAAM,gBAAgB,CAAC,QAAmB,MAAM,UAAA,CAAW,MAAM,IAAI,QAAQ,CAAA,IAAA,EAAO,KAAK,EAAA;AAOzF,IAAM,wBAA+C,CAAC,QAAQ,YAAY;IACxE,MAAM,EAAE,KAAA,EAAO,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI;IAC3C,IAAI,CAAC,OAAO,IAAA,IAAQ,CAAC,OAAO,UAAA,EAAY;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB;QACzC,OAAO;IACT;IAEA,IAAI,OAAO,UAAA,EAAY;QACrB,OAAO,eAAe,QAAA,CAAS,cAAc,OAAO,UAAU,CAAC;IACjE;IAEA,IAAI,OAAO,IAAA,EAAM;QACf,OAAO,YAAY,cAAc,OAAO,IAAI;IAC9C;IACA,OAAO;AACT;AAEA,IAAM,wBAAwB,CAAC,OAAe,kBAA0B;IACtE,MAAM,EAAE,KAAK,WAAA,EAAa,MAAM,YAAA,CAAa,CAAA,GAAI,aAAa,KAAK;IACnE,MAAM,CAAC,OAAO,GAAG,CAAA,GAAI,cAAc,KAAA,CAAM,GAAG;IAC5C,MAAM,KAAK,OAAO;IAElB,IAAI,UAAU,OAAO;QACnB,OAAO,YAAY,QAAA,CAAS,EAAE;IAChC,OAAA,IAAW,UAAU,QAAQ;QAC3B,OAAO,aAAa,QAAA,CAAS,EAAE;IACjC,OAAO;QAEL,OAAO,CAAC;eAAG,aAAa;eAAG,YAAY;SAAA,CAAE,QAAA,CAAS,EAAE;IACtD;AACF;AAEA,IAAM,4BAAuD,CAAC,QAAQ,YAAY;IAChF,MAAM,EAAE,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;IAE5B,IAAI,OAAO,OAAA,IAAW,UAAU;QAC9B,OAAO,sBAAsB,UAAU,OAAO,OAAO;IACvD;IAEA,IAAI,OAAO,IAAA,IAAQ,OAAO;QACxB,OAAO,sBAAsB,OAAO,OAAO,IAAI;IACjD;IACA,OAAO;AACT;AAEA,IAAM,eAAe,CAAC,QAAmC;IACvD,MAAM,WAAW,MAAM,IAAI,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,IAAA,CAAK,CAAC,IAAI,CAAC,CAAA;IAG5D,OAAO;QACL,KAAK,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;QACjF,MAAM,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IACpF;AACF;AAEA,IAAM,+BAA+B,CAAC,WAAoD;IACxF,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,wBAAwB,CAACA,YAAiC;QAC9D,IAAI,OAAOA,YAAW,UAAU;YAC9B,OAAO,gBAAA,CAAiBA,OAAM,CAAA;QAChC;QACA,OAAOA;IACT;IAEA,MAAM,qBAAqB,OAAO,WAAW,YAAY,wBAAwB,MAAM;IACvF,MAAM,qBACJ,OAAO,WAAW,YAAY,aAAa,OAAO,KAAK,KAAK,cAAc,OAAO,YAAY;IAE/F,IAAI,sBAAsB,oBAAoB;QAC5C,OAAO,sBAAsB,IAAA,CAAK,MAAM,MAAM;IAChD;IAEA,OAAO;AACT;AAQA,IAAM,mCAAqE,CAAC,QAAQ,EAAE,qBAAA,CAAsB,CAAA,KAAM;IAChH,IAAI,CAAC,OAAO,cAAA,IAAkB,CAAC,uBAAuB;QACpD,OAAO;IACT;IAEA,MAAM,wBAAwB,6BAA6B,OAAO,cAAc;IAChF,IAAI,CAAC,uBAAuB;QAC1B,OAAO;IACT;IAEA,MAAM,EAAE,KAAA,EAAO,YAAA,CAAa,CAAA,GAAI,sBAAsB;IACtD,MAAM,CAAC,YAAY,UAAU,CAAA,GAAI;IAIjC,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IACvE,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IAEvE,OAAQ,OAAO;QACb,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB;QAC9C,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB,kBAAkB;IAClE;AACF;AAQA,IAAM,2BAA2B,CAAC,YAA2E;IAC3G,OAAO,CAAC,WAAoB;QAC1B,IAAI,CAAC,QAAQ,MAAA,EAAQ;YACnB,OAAO;QACT;QAEA,MAAM,uBAAuB,0BAA0B,QAAQ,OAAO;QACtE,MAAM,mBAAmB,sBAAsB,QAAQ,OAAO;QAC9D,MAAM,8BAA8B,iCAAiC,QAAQ,OAAO;QAEpF,IAAI;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI,GAAG;YACjG,OAAO;gBAAC,wBAAwB;gBAAkB,2BAA2B;aAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI;QACrG;QAEA,OAAO;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,KAAA,CAAM,CAAA,IAAK,MAAM,IAAI;IACtG;AACF;AAyBA,IAAM,mBAAmB,CAAC,EACxB,YAAY,EACV,SAAA,EACA,aAAA,EACA,MAAA,EACA,KAAA,EACA,KAAA,EACA,OAAA,EACA,OAAA,EACA,OAAA,EACA,QAAA,EACA,GAAA,EACA,aAAA,EACF,EACA,SAAS,EAAE,0BAA0B,IAAA,CAAK,CAAA,EAC5C,KAAmD;IACjD,IAAI,cAAc,KAAA,KAAa,WAAW,KAAA,GAAW;QACnD,OAAO;YACL,UAAU;YACV,YAAY,KAAA;YACZ;YACA,eAAe,KAAA;YACf;YACA,OAAO,KAAA;YACP,OAAO,KAAA;YACP,SAAS,KAAA;YACT,SAAS,KAAA;YACT,KAAK,KAAA;YACL;YACA;QACF;IACF;IAEA,IAAI,cAAc,QAAQ,WAAW,MAAM;QACzC,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,2BAA2B,kBAAkB,WAAW;QAC1D,OAAO;YACL,UAAU;YACV,YAAY;YACZ,WAAW;YACX,QAAQ;YACR,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;QACtE,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB;YACA;YACA,SAAS,WAAW;YACpB;YACA;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO;QACxD,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB,OAAO;YACP,SAAS;YACT,SAAS;YACT;YACA;YACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/jwtPayloadParser.ts"], "sourcesContent": ["import type {\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  SharedSignedInAuthObjectProperties,\n} from '@clerk/types';\n\nimport { splitByScope } from './authorization';\n\nexport const parsePermissions = ({ per, fpm }: { per?: string; fpm?: string }) => {\n  if (!per || !fpm) {\n    return { permissions: [], featurePermissionMap: [] };\n  }\n\n  const permissions = per.split(',').map(p => p.trim());\n\n  // TODO: make this more efficient\n  const featurePermissionMap = fpm\n    .split(',')\n    .map(permission => Number.parseInt(permission.trim(), 10))\n    .map((permission: number) =>\n      permission\n        .toString(2)\n        .padStart(permissions.length, '0')\n        .split('')\n        .map(bit => Number.parseInt(bit, 10))\n        .reverse(),\n    )\n    .filter(Boolean);\n\n  return { permissions, featurePermissionMap };\n};\n\nfunction buildOrgPermissions({\n  features,\n  permissions,\n  featurePermissionMap,\n}: {\n  features?: string[];\n  permissions?: string[];\n  featurePermissionMap?: number[][];\n}) {\n  // Early return if any required input is missing\n  if (!features || !permissions || !featurePermissionMap) {\n    return [];\n  }\n\n  const orgPermissions: string[] = [];\n\n  // Process each feature and its permissions in a single loop\n  for (let featureIndex = 0; featureIndex < features.length; featureIndex++) {\n    const feature = features[featureIndex];\n\n    if (featureIndex >= featurePermissionMap.length) {\n      continue;\n    }\n\n    const permissionBits = featurePermissionMap[featureIndex];\n    if (!permissionBits) continue;\n\n    for (let permIndex = 0; permIndex < permissionBits.length; permIndex++) {\n      if (permissionBits[permIndex] === 1) {\n        orgPermissions.push(`org:${feature}:${permissions[permIndex]}`);\n      }\n    }\n  }\n\n  return orgPermissions;\n}\n\n/**\n * @experimental\n *\n * Resolves the signed-in auth state from JWT claims.\n */\nconst __experimental_JWTPayloadToAuthObjectProperties = (claims: JwtPayload): SharedSignedInAuthObjectProperties => {\n  let orgId: string | undefined;\n  let orgRole: OrganizationCustomRoleKey | undefined;\n  let orgSlug: string | undefined;\n  let orgPermissions: OrganizationCustomPermissionKey[] | undefined;\n\n  // fva can be undefined for instances that have not opt-in\n  const factorVerificationAge = claims.fva ?? null;\n\n  // sts can be undefined for instances that have not opt-in\n  const sessionStatus = claims.sts ?? null;\n\n  switch (claims.v) {\n    case 2: {\n      if (claims.o) {\n        orgId = claims.o?.id;\n        orgSlug = claims.o?.slg;\n\n        if (claims.o?.rol) {\n          orgRole = `org:${claims.o?.rol}`;\n        }\n        const { org } = splitByScope(claims.fea);\n        const { permissions, featurePermissionMap } = parsePermissions({\n          per: claims.o?.per,\n          fpm: claims.o?.fpm,\n        });\n        orgPermissions = buildOrgPermissions({\n          features: org,\n          featurePermissionMap: featurePermissionMap,\n          permissions: permissions,\n        });\n      }\n      break;\n    }\n    default:\n      orgId = claims.org_id;\n      orgRole = claims.org_role;\n      orgSlug = claims.org_slug;\n      orgPermissions = claims.org_permissions;\n      break;\n  }\n\n  return {\n    sessionClaims: claims,\n    sessionId: claims.sid,\n    sessionStatus,\n    actor: claims.act,\n    userId: claims.sub,\n    orgId: orgId,\n    orgRole: orgRole,\n    orgSlug: orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n  };\n};\n\nexport { __experimental_JWTPayloadToAuthObjectProperties };\n"], "names": [], "mappings": ";;;;;;;;;AASO,IAAM,mBAAmB,CAAC,EAAE,GAAA,EAAK,GAAA,CAAI,CAAA,KAAsC;IAChF,IAAI,CAAC,OAAO,CAAC,KAAK;QAChB,OAAO;YAAE,aAAa,CAAC,CAAA;YAAG,sBAAsB,CAAC,CAAA;QAAE;IACrD;IAEA,MAAM,cAAc,IAAI,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,IAAA,CAAK,CAAC;IAGpD,MAAM,uBAAuB,IAC1B,KAAA,CAAM,GAAG,EACT,GAAA,CAAI,CAAA,aAAc,OAAO,QAAA,CAAS,WAAW,IAAA,CAAK,GAAG,EAAE,CAAC,EACxD,GAAA,CAAI,CAAC,aACJ,WACG,QAAA,CAAS,CAAC,EACV,QAAA,CAAS,YAAY,MAAA,EAAQ,GAAG,EAChC,KAAA,CAAM,EAAE,EACR,GAAA,CAAI,CAAA,MAAO,OAAO,QAAA,CAAS,KAAK,EAAE,CAAC,EACnC,OAAA,CAAQ,GAEZ,MAAA,CAAO,OAAO;IAEjB,OAAO;QAAE;QAAa;IAAqB;AAC7C;AAEA,SAAS,oBAAoB,EAC3B,QAAA,EACA,WAAA,EACA,oBAAA,EACF,EAIG;IAED,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,sBAAsB;QACtD,OAAO,CAAC,CAAA;IACV;IAEA,MAAM,iBAA2B,CAAC,CAAA;IAGlC,IAAA,IAAS,eAAe,GAAG,eAAe,SAAS,MAAA,EAAQ,eAAgB;QACzE,MAAM,UAAU,QAAA,CAAS,YAAY,CAAA;QAErC,IAAI,gBAAgB,qBAAqB,MAAA,EAAQ;YAC/C;QACF;QAEA,MAAM,iBAAiB,oBAAA,CAAqB,YAAY,CAAA;QACxD,IAAI,CAAC,eAAgB,CAAA;QAErB,IAAA,IAAS,YAAY,GAAG,YAAY,eAAe,MAAA,EAAQ,YAAa;YACtE,IAAI,cAAA,CAAe,SAAS,CAAA,KAAM,GAAG;gBACnC,eAAe,IAAA,CAAK,CAAA,IAAA,EAAO,OAAO,CAAA,CAAA,EAAI,WAAA,CAAY,SAAS,CAAC,EAAE;YAChE;QACF;IACF;IAEA,OAAO;AACT;AAOA,IAAM,kDAAkD,CAAC,WAA2D;IAClH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IAGJ,MAAM,wBAAwB,OAAO,GAAA,IAAO;IAG5C,MAAM,gBAAgB,OAAO,GAAA,IAAO;IAEpC,OAAQ,OAAO,CAAA,EAAG;QAChB,KAAK;YAAG;gBACN,IAAI,OAAO,CAAA,EAAG;oBACZ,QAAQ,OAAO,CAAA,EAAG;oBAClB,UAAU,OAAO,CAAA,EAAG;oBAEpB,IAAI,OAAO,CAAA,EAAG,KAAK;wBACjB,UAAU,CAAA,IAAA,EAAO,OAAO,CAAA,EAAG,GAAG,EAAA;oBAChC;oBACA,MAAM,EAAE,GAAA,CAAI,CAAA,mRAAI,eAAA,EAAa,OAAO,GAAG;oBACvC,MAAM,EAAE,WAAA,EAAa,oBAAA,CAAqB,CAAA,GAAI,iBAAiB;wBAC7D,KAAK,OAAO,CAAA,EAAG;wBACf,KAAK,OAAO,CAAA,EAAG;oBACjB,CAAC;oBACD,iBAAiB,oBAAoB;wBACnC,UAAU;wBACV;wBACA;oBACF,CAAC;gBACH;gBACA;YACF;QACA;YACE,QAAQ,OAAO,MAAA;YACf,UAAU,OAAO,QAAA;YACjB,UAAU,OAAO,QAAA;YACjB,iBAAiB,OAAO,eAAA;YACxB;IACJ;IAEA,OAAO;QACL,eAAe;QACf,WAAW,OAAO,GAAA;QAClB;QACA,OAAO,OAAO,GAAA;QACd,QAAQ,OAAO,GAAA;QACf;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/compiled/path-to-regexp/index.js", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/pathToRegexp.ts"], "sourcesContent": ["/* eslint-disable no-redeclare, curly */\n\nfunction _(r) {\n  for (var n = [], e = 0; e < r.length; ) {\n    var a = r[e];\n    if (a === '*' || a === '+' || a === '?') {\n      n.push({\n        type: 'MODIFIER',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '\\\\') {\n      n.push({\n        type: 'ESCAPED_CHAR',\n        index: e++,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '{') {\n      n.push({\n        type: 'OPEN',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '}') {\n      n.push({\n        type: 'CLOSE',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === ':') {\n      for (var u = '', t = e + 1; t < r.length; ) {\n        var c = r.charCodeAt(t);\n        if ((c >= 48 && c <= 57) || (c >= 65 && c <= 90) || (c >= 97 && c <= 122) || c === 95) {\n          u += r[t++];\n          continue;\n        }\n        break;\n      }\n      if (!u) throw new TypeError('Missing parameter name at '.concat(e));\n      n.push({\n        type: 'NAME',\n        index: e,\n        value: u,\n      }),\n        (e = t);\n      continue;\n    }\n    if (a === '(') {\n      var o = 1,\n        m = '',\n        t = e + 1;\n      if (r[t] === '?') throw new TypeError('Pattern cannot start with \"?\" at '.concat(t));\n      for (; t < r.length; ) {\n        if (r[t] === '\\\\') {\n          m += r[t++] + r[t++];\n          continue;\n        }\n        if (r[t] === ')') {\n          if ((o--, o === 0)) {\n            t++;\n            break;\n          }\n        } else if (r[t] === '(' && (o++, r[t + 1] !== '?'))\n          throw new TypeError('Capturing groups are not allowed at '.concat(t));\n        m += r[t++];\n      }\n      if (o) throw new TypeError('Unbalanced pattern at '.concat(e));\n      if (!m) throw new TypeError('Missing pattern at '.concat(e));\n      n.push({\n        type: 'PATTERN',\n        index: e,\n        value: m,\n      }),\n        (e = t);\n      continue;\n    }\n    n.push({\n      type: 'CHAR',\n      index: e,\n      value: r[e++],\n    });\n  }\n  return (\n    n.push({\n      type: 'END',\n      index: e,\n      value: '',\n    }),\n    n\n  );\n}\n\nfunction F(r, n) {\n  n === void 0 && (n = {});\n  for (\n    var e = _(r),\n      a = n.prefixes,\n      u = a === void 0 ? './' : a,\n      t = n.delimiter,\n      c = t === void 0 ? '/#?' : t,\n      o = [],\n      m = 0,\n      h = 0,\n      p = '',\n      f = function (l) {\n        if (h < e.length && e[h].type === l) return e[h++].value;\n      },\n      w = function (l) {\n        var v = f(l);\n        if (v !== void 0) return v;\n        var E = e[h],\n          N = E.type,\n          S = E.index;\n        throw new TypeError('Unexpected '.concat(N, ' at ').concat(S, ', expected ').concat(l));\n      },\n      d = function () {\n        for (var l = '', v; (v = f('CHAR') || f('ESCAPED_CHAR')); ) l += v;\n        return l;\n      },\n      M = function (l) {\n        for (var v = 0, E = c; v < E.length; v++) {\n          var N = E[v];\n          if (l.indexOf(N) > -1) return !0;\n        }\n        return !1;\n      },\n      A = function (l) {\n        var v = o[o.length - 1],\n          E = l || (v && typeof v == 'string' ? v : '');\n        if (v && !E)\n          throw new TypeError('Must have text between two parameters, missing text after \"'.concat(v.name, '\"'));\n        return !E || M(E) ? '[^'.concat(s(c), ']+?') : '(?:(?!'.concat(s(E), ')[^').concat(s(c), '])+?');\n      };\n    h < e.length;\n\n  ) {\n    var T = f('CHAR'),\n      x = f('NAME'),\n      C = f('PATTERN');\n    if (x || C) {\n      var g = T || '';\n      u.indexOf(g) === -1 && ((p += g), (g = '')),\n        p && (o.push(p), (p = '')),\n        o.push({\n          name: x || m++,\n          prefix: g,\n          suffix: '',\n          pattern: C || A(g),\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    var i = T || f('ESCAPED_CHAR');\n    if (i) {\n      p += i;\n      continue;\n    }\n    p && (o.push(p), (p = ''));\n    var R = f('OPEN');\n    if (R) {\n      var g = d(),\n        y = f('NAME') || '',\n        O = f('PATTERN') || '',\n        b = d();\n      w('CLOSE'),\n        o.push({\n          name: y || (O ? m++ : ''),\n          pattern: y && !O ? A(g) : O,\n          prefix: g,\n          suffix: b,\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    w('END');\n  }\n  return o;\n}\n\nfunction H(r, n) {\n  var e = [],\n    a = P(r, e, n);\n  return I(a, e, n);\n}\n\nfunction I(r, n, e) {\n  e === void 0 && (e = {});\n  var a = e.decode,\n    u =\n      a === void 0\n        ? function (t) {\n            return t;\n          }\n        : a;\n  return function (t) {\n    var c = r.exec(t);\n    if (!c) return !1;\n    for (\n      var o = c[0],\n        m = c.index,\n        h = Object.create(null),\n        p = function (w) {\n          if (c[w] === void 0) return 'continue';\n          var d = n[w - 1];\n          d.modifier === '*' || d.modifier === '+'\n            ? (h[d.name] = c[w].split(d.prefix + d.suffix).map(function (M) {\n                return u(M, d);\n              }))\n            : (h[d.name] = u(c[w], d));\n        },\n        f = 1;\n      f < c.length;\n      f++\n    )\n      p(f);\n    return {\n      path: o,\n      index: m,\n      params: h,\n    };\n  };\n}\n\nfunction s(r) {\n  return r.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, '\\\\$1');\n}\n\nfunction D(r) {\n  return r && r.sensitive ? '' : 'i';\n}\n\nfunction $(r, n) {\n  if (!n) return r;\n  for (var e = /\\((?:\\?<(.*?)>)?(?!\\?)/g, a = 0, u = e.exec(r.source); u; )\n    n.push({\n      name: u[1] || a++,\n      prefix: '',\n      suffix: '',\n      modifier: '',\n      pattern: '',\n    }),\n      (u = e.exec(r.source));\n  return r;\n}\n\nfunction W(r, n, e) {\n  var a = r.map(function (u) {\n    return P(u, n, e).source;\n  });\n  return new RegExp('(?:'.concat(a.join('|'), ')'), D(e));\n}\n\nfunction L(r, n, e) {\n  return U(F(r, e), n, e);\n}\n\nfunction U(r, n, e) {\n  e === void 0 && (e = {});\n  for (\n    var a = e.strict,\n      u = a === void 0 ? !1 : a,\n      t = e.start,\n      c = t === void 0 ? !0 : t,\n      o = e.end,\n      m = o === void 0 ? !0 : o,\n      h = e.encode,\n      p =\n        h === void 0\n          ? function (v) {\n              return v;\n            }\n          : h,\n      f = e.delimiter,\n      w = f === void 0 ? '/#?' : f,\n      d = e.endsWith,\n      M = d === void 0 ? '' : d,\n      A = '['.concat(s(M), ']|$'),\n      T = '['.concat(s(w), ']'),\n      x = c ? '^' : '',\n      C = 0,\n      g = r;\n    C < g.length;\n    C++\n  ) {\n    var i = g[C];\n    if (typeof i == 'string') x += s(p(i));\n    else {\n      var R = s(p(i.prefix)),\n        y = s(p(i.suffix));\n      if (i.pattern)\n        if ((n && n.push(i), R || y))\n          if (i.modifier === '+' || i.modifier === '*') {\n            var O = i.modifier === '*' ? '?' : '';\n            x += '(?:'\n              .concat(R, '((?:')\n              .concat(i.pattern, ')(?:')\n              .concat(y)\n              .concat(R, '(?:')\n              .concat(i.pattern, '))*)')\n              .concat(y, ')')\n              .concat(O);\n          } else x += '(?:'.concat(R, '(').concat(i.pattern, ')').concat(y, ')').concat(i.modifier);\n        else {\n          if (i.modifier === '+' || i.modifier === '*')\n            throw new TypeError('Can not repeat \"'.concat(i.name, '\" without a prefix and suffix'));\n          x += '('.concat(i.pattern, ')').concat(i.modifier);\n        }\n      else x += '(?:'.concat(R).concat(y, ')').concat(i.modifier);\n    }\n  }\n  if (m) u || (x += ''.concat(T, '?')), (x += e.endsWith ? '(?='.concat(A, ')') : '$');\n  else {\n    var b = r[r.length - 1],\n      l = typeof b == 'string' ? T.indexOf(b[b.length - 1]) > -1 : b === void 0;\n    u || (x += '(?:'.concat(T, '(?=').concat(A, '))?')), l || (x += '(?='.concat(T, '|').concat(A, ')'));\n  }\n  return new RegExp(x, D(e));\n}\n\nfunction P(r, n, e) {\n  return r instanceof RegExp ? $(r, n) : Array.isArray(r) ? W(r, n, e) : L(r, n, e);\n}\nexport { H as match, P as pathToRegexp };\n", "import type {\n  Match,\n  MatchFunction,\n  ParseOptions,\n  Path,\n  RegexpToFunctionOptions,\n  TokensToRegexpOptions,\n} from './compiled/path-to-regexp';\nimport { match as matchBase, pathToRegexp as pathToRegexpBase } from './compiled/path-to-regexp';\n\nexport const pathToRegexp = (path: string) => {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return pathToRegexpBase(path);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path: ${path}.\\nConsult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n};\n\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions,\n): MatchFunction<P> {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return matchBase(str, options);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n}\n\nexport { type Match, type MatchFunction };\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,IAAA,IAAS,IAAI,CAAC,CAAA,EAAG,IAAI,GAAG,IAAI,EAAE,MAAA,EAAU;QACtC,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;QACX,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;YACvC,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,MAAM;YACd,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,IAAA,IAAS,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,MAAA,EAAU;gBAC1C,IAAI,IAAI,EAAE,UAAA,CAAW,CAAC;gBACtB,IAAK,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,OAAQ,MAAM,IAAI;oBACrF,KAAK,CAAA,CAAE,GAAG,CAAA;oBACV;gBACF;gBACA;YACF;YACA,IAAI,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,6BAA6B,MAAA,CAAO,CAAC,CAAC;YAClE,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT,CAAC,GACE,IAAI;YACP;QACF;QACA,IAAI,MAAM,KAAK;YACb,IAAI,IAAI,GACN,IAAI,IACJ,IAAI,IAAI;YACV,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,IAAK,CAAA,MAAM,IAAI,UAAU,oCAAoC,MAAA,CAAO,CAAC,CAAC;YACnF,MAAO,IAAI,EAAE,MAAA,EAAU;gBACrB,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,MAAM;oBACjB,KAAK,CAAA,CAAE,GAAG,CAAA,GAAI,CAAA,CAAE,GAAG,CAAA;oBACnB;gBACF;gBACA,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,KAAK;oBAChB,IAAK,KAAK,MAAM,GAAI;wBAClB;wBACA;oBACF;gBACF,OAAA,IAAW,CAAA,CAAE,CAAC,CAAA,KAAM,OAAA,CAAQ,KAAK,CAAA,CAAE,IAAI,CAAC,CAAA,KAAM,GAAA,GAC5C,MAAM,IAAI,UAAU,uCAAuC,MAAA,CAAO,CAAC,CAAC;gBACtE,KAAK,CAAA,CAAE,GAAG,CAAA;YACZ;YACA,IAAI,EAAG,CAAA,MAAM,IAAI,UAAU,yBAAyB,MAAA,CAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,sBAAsB,MAAA,CAAO,CAAC,CAAC;YAC3D,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT,CAAC,GACE,IAAI;YACP;QACF;QACA,EAAE,IAAA,CAAK;YACL,MAAM;YACN,OAAO;YACP,OAAO,CAAA,CAAE,GAAG,CAAA;QACd,CAAC;IACH;IACA,OACE,EAAE,IAAA,CAAK;QACL,MAAM;QACN,OAAO;QACP,OAAO;IACT,CAAC,GACD;AAEJ;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAA,IACM,IAAI,EAAE,CAAC,GACT,IAAI,EAAE,QAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAO,GAC1B,IAAI,EAAE,SAAA,EACN,IAAI,MAAM,KAAA,IAAS,QAAQ,GAC3B,IAAI,CAAC,CAAA,EACL,IAAI,GACJ,IAAI,GACJ,IAAI,IACJ,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,EAAE,MAAA,IAAU,CAAA,CAAE,CAAC,CAAA,CAAE,IAAA,KAAS,EAAG,CAAA,OAAO,CAAA,CAAE,GAAG,CAAA,CAAE,KAAA;IACrD,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,EAAE,CAAC;QACX,IAAI,MAAM,KAAA,EAAQ,CAAA,OAAO;QACzB,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA,EACT,IAAI,EAAE,IAAA,EACN,IAAI,EAAE,KAAA;QACR,MAAM,IAAI,UAAU,cAAc,MAAA,CAAO,GAAG,MAAM,EAAE,MAAA,CAAO,GAAG,aAAa,EAAE,MAAA,CAAO,CAAC,CAAC;IACxF,GACA,IAAI,WAAY;QACd,IAAA,IAAS,IAAI,IAAI,GAAI,IAAI,EAAE,MAAM,KAAK,EAAE,cAAc,GAAM,CAAA,IAAK;QACjE,OAAO;IACT,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,IAAK;YACxC,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;YACX,IAAI,EAAE,OAAA,CAAQ,CAAC,IAAI,CAAA,EAAI,CAAA,OAAO;QAChC;QACA,OAAO;IACT,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAA,EACpB,IAAI,KAAA,CAAM,KAAK,OAAO,KAAK,WAAW,IAAI,EAAA;QAC5C,IAAI,KAAK,CAAC,GACR,MAAM,IAAI,UAAU,8DAA8D,MAAA,CAAO,EAAE,IAAA,EAAM,GAAG,CAAC;QACvG,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,IAAI,SAAS,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,EAAE,MAAA,CAAO,EAAE,CAAC,GAAG,MAAM;IACjG,GACF,IAAI,EAAE,MAAA,EAEN;QACA,IAAI,IAAI,EAAE,MAAM,GACd,IAAI,EAAE,MAAM,GACZ,IAAI,EAAE,SAAS;QACjB,IAAI,KAAK,GAAG;YACV,IAAI,IAAI,KAAK;YACb,EAAE,OAAA,CAAQ,CAAC,MAAM,CAAA,KAAA,CAAQ,KAAK,GAAK,IAAI,EAAA,GACrC,KAAA,CAAM,EAAE,IAAA,CAAK,CAAC,GAAI,IAAI,EAAA,GACtB,EAAE,IAAA,CAAK;gBACL,MAAM,KAAK;gBACX,QAAQ;gBACR,QAAQ;gBACR,SAAS,KAAK,EAAE,CAAC;gBACjB,UAAU,EAAE,UAAU,KAAK;YAC7B,CAAC;YACH;QACF;QACA,IAAI,IAAI,KAAK,EAAE,cAAc;QAC7B,IAAI,GAAG;YACL,KAAK;YACL;QACF;QACA,KAAA,CAAM,EAAE,IAAA,CAAK,CAAC,GAAI,IAAI,EAAA;QACtB,IAAI,IAAI,EAAE,MAAM;QAChB,IAAI,GAAG;YACL,IAAI,IAAI,EAAE,GACR,IAAI,EAAE,MAAM,KAAK,IACjB,IAAI,EAAE,SAAS,KAAK,IACpB,IAAI,EAAE;YACR,EAAE,OAAO,GACP,EAAE,IAAA,CAAK;gBACL,MAAM,KAAA,CAAM,IAAI,MAAM,EAAA;gBACtB,SAAS,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI;gBAC1B,QAAQ;gBACR,QAAQ;gBACR,UAAU,EAAE,UAAU,KAAK;YAC7B,CAAC;YACH;QACF;QACA,EAAE,KAAK;IACT;IACA,OAAO;AACT;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,IAAI,IAAI,CAAC,CAAA,EACP,IAAI,EAAE,GAAG,GAAG,CAAC;IACf,OAAO,EAAE,GAAG,GAAG,CAAC;AAClB;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAI,IAAI,EAAE,MAAA,EACR,IACE,MAAM,KAAA,IACF,SAAU,CAAA,EAAG;QACX,OAAO;IACT,IACA;IACR,OAAO,SAAU,CAAA,EAAG;QAClB,IAAI,IAAI,EAAE,IAAA,CAAK,CAAC;QAChB,IAAI,CAAC,EAAG,CAAA,OAAO;QACf,IAAA,IACM,IAAI,CAAA,CAAE,CAAC,CAAA,EACT,IAAI,EAAE,KAAA,EACN,IAAI,aAAA,GAAA,OAAO,MAAA,CAAO,IAAI,GACtB,IAAI,SAAU,CAAA,EAAG;YACf,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,KAAA,EAAQ,CAAA,OAAO;YAC5B,IAAI,IAAI,CAAA,CAAE,IAAI,CAAC,CAAA;YACf,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,MAChC,CAAA,CAAE,EAAE,IAAI,CAAA,GAAI,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAM,EAAE,MAAA,GAAS,EAAE,MAAM,EAAE,GAAA,CAAI,SAAU,CAAA,EAAG;gBAC5D,OAAO,EAAE,GAAG,CAAC;YACf,CAAC,IACA,CAAA,CAAE,EAAE,IAAI,CAAA,GAAI,EAAE,CAAA,CAAE,CAAC,CAAA,EAAG,CAAC;QAC5B,GACA,IAAI,GACN,IAAI,EAAE,MAAA,EACN,IAEA,EAAE,CAAC;QACL,OAAO;YACL,MAAM;YACN,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,OAAO,EAAE,OAAA,CAAQ,6BAA6B,MAAM;AACtD;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,OAAO,KAAK,EAAE,SAAA,GAAY,KAAK;AACjC;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,IAAI,CAAC,EAAG,CAAA,OAAO;IACf,IAAA,IAAS,IAAI,2BAA2B,IAAI,GAAG,IAAI,EAAE,IAAA,CAAK,EAAE,MAAM,GAAG,GACnE,EAAE,IAAA,CAAK;QACL,MAAM,CAAA,CAAE,CAAC,CAAA,IAAK;QACd,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,SAAS;IACX,CAAC,GACE,IAAI,EAAE,IAAA,CAAK,EAAE,MAAM;IACxB,OAAO;AACT;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,IAAI,IAAI,EAAE,GAAA,CAAI,SAAU,CAAA,EAAG;QACzB,OAAO,EAAE,GAAG,GAAG,CAAC,EAAE,MAAA;IACpB,CAAC;IACD,OAAO,IAAI,OAAO,MAAM,MAAA,CAAO,EAAE,IAAA,CAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACxD;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,OAAO,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AACxB;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAA,IACM,IAAI,EAAE,MAAA,EACR,IAAI,MAAM,KAAA,IAAS,QAAK,GACxB,IAAI,EAAE,KAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAK,GACxB,IAAI,EAAE,GAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAK,GACxB,IAAI,EAAE,MAAA,EACN,IACE,MAAM,KAAA,IACF,SAAU,CAAA,EAAG;QACX,OAAO;IACT,IACA,GACN,IAAI,EAAE,SAAA,EACN,IAAI,MAAM,KAAA,IAAS,QAAQ,GAC3B,IAAI,EAAE,QAAA,EACN,IAAI,MAAM,KAAA,IAAS,KAAK,GACxB,IAAI,IAAI,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,GAC1B,IAAI,IAAI,MAAA,CAAO,EAAE,CAAC,GAAG,GAAG,GACxB,IAAI,IAAI,MAAM,IACd,IAAI,GACJ,IAAI,GACN,IAAI,EAAE,MAAA,EACN,IACA;QACA,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;QACX,IAAI,OAAO,KAAK,SAAU,CAAA,KAAK,EAAE,EAAE,CAAC,CAAC;aAChC;YACH,IAAI,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,GACnB,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC;YACnB,IAAI,EAAE,OAAA,EACJ,IAAK,KAAK,EAAE,IAAA,CAAK,CAAC,GAAG,KAAK,GACxB,IAAI,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,KAAK;gBAC5C,IAAI,IAAI,EAAE,QAAA,KAAa,MAAM,MAAM;gBACnC,KAAK,MACF,MAAA,CAAO,GAAG,MAAM,EAChB,MAAA,CAAO,EAAE,OAAA,EAAS,MAAM,EACxB,MAAA,CAAO,CAAC,EACR,MAAA,CAAO,GAAG,KAAK,EACf,MAAA,CAAO,EAAE,OAAA,EAAS,MAAM,EACxB,MAAA,CAAO,GAAG,GAAG,EACb,MAAA,CAAO,CAAC;YACb,MAAO,CAAA,KAAK,MAAM,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,OAAA,EAAS,GAAG,EAAE,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;iBACrF;gBACH,IAAI,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,KACvC,MAAM,IAAI,UAAU,mBAAmB,MAAA,CAAO,EAAE,IAAA,EAAM,+BAA+B,CAAC;gBACxF,KAAK,IAAI,MAAA,CAAO,EAAE,OAAA,EAAS,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;YACnD;iBACG,KAAK,MAAM,MAAA,CAAO,CAAC,EAAE,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;QAC5D;IACF;IACA,IAAI,EAAG,CAAA,KAAA,CAAM,KAAK,GAAG,MAAA,CAAO,GAAG,GAAG,CAAA,GAAK,KAAK,EAAE,QAAA,GAAW,MAAM,MAAA,CAAO,GAAG,GAAG,IAAI;SAC3E;QACH,IAAI,IAAI,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAA,EACpB,IAAI,OAAO,KAAK,WAAW,EAAE,OAAA,CAAQ,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAC,IAAI,CAAA,IAAK,MAAM,KAAA;QACrE,KAAA,CAAM,KAAK,MAAM,MAAA,CAAO,GAAG,KAAK,EAAE,MAAA,CAAO,GAAG,KAAK,CAAA,GAAI,KAAA,CAAM,KAAK,MAAM,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,GAAG,GAAG,CAAA;IACpG;IACA,OAAO,IAAI,OAAO,GAAG,EAAE,CAAC,CAAC;AAC3B;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,OAAO,aAAa,SAAS,EAAE,GAAG,CAAC,IAAI,MAAM,OAAA,CAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC;AAClF;;AC/TO,IAAM,eAAe,CAAC,SAAiB;IAC5C,IAAI;QAEF,OAAO,EAAiB,IAAI;IAC9B,EAAA,OAAS,GAAQ;QACf,MAAM,IAAI,MACR,CAAA,cAAA,EAAiB,IAAI,CAAA;;AAAA,EAA6G,EAAE,OAAO,EAAA;IAE/I;AACF;AAEO,SAAS,MACd,GAAA,EACA,OAAA,EACkB;IAClB,IAAI;QAEF,OAAO,EAAU,KAAK,OAAO;IAC/B,EAAA,OAAS,GAAQ;QACf,MAAM,IAAI,MACR,CAAA;AAAA,EAAoI,EAAE,OAAO,EAAA;IAEjJ;AACF", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/buildAccountsBaseUrl.ts"], "sourcesContent": ["/**\n * Builds a full origin string pointing to the Account Portal for the given frontend API.\n */\nexport function buildAccountsBaseUrl(frontendApi?: string): string {\n  if (!frontendApi) {\n    return '';\n  }\n\n  // convert url from FAPI to accounts for Kima and legacy (prod & dev) instances\n  const accountsBaseUrl = frontendApi\n    // staging accounts\n    .replace(/clerk\\.accountsstage\\./, 'accountsstage.')\n    .replace(/clerk\\.accounts\\.|clerk\\./, 'accounts.');\n  return `https://${accountsBaseUrl}`;\n}\n"], "names": [], "mappings": ";;;;;;AAGO,SAAS,qBAAqB,WAAA,EAA8B;IACjE,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAGA,MAAM,kBAAkB,YAErB,OAAA,CAAQ,0BAA0B,gBAAgB,EAClD,OAAA,CAAQ,6BAA6B,WAAW;IACnD,OAAO,CAAA,QAAA,EAAW,eAAe,EAAA;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/authorization-errors.ts"], "sourcesContent": ["import type { ReverificationConfig } from '@clerk/types';\n\ntype ClerkError<T> = {\n  clerk_error: T;\n};\n\nconst REVERIFICATION_REASON = 'reverification-error';\n\ntype ReverificationError<M extends { metadata?: any } = { metadata: unknown }> = ClerkError<\n  {\n    type: 'forbidden';\n    reason: typeof REVERIFICATION_REASON;\n  } & M\n>;\n\nconst reverificationError = <MC extends ReverificationConfig>(\n  missingConfig?: MC,\n): ReverificationError<{\n  metadata?: {\n    reverification?: MC;\n  };\n}> => ({\n  clerk_error: {\n    type: 'forbidden',\n    reason: REVERIFICATION_REASON,\n    metadata: {\n      reverification: missingConfig,\n    },\n  },\n});\n\nconst reverificationErrorResponse = (...args: Parameters<typeof reverificationError>) =>\n  new Response(JSON.stringify(reverificationError(...args)), {\n    status: 403,\n  });\n\nconst isReverificationHint = (result: any): result is ReturnType<typeof reverificationError> => {\n  return (\n    result &&\n    typeof result === 'object' &&\n    'clerk_error' in result &&\n    result.clerk_error?.type === 'forbidden' &&\n    result.clerk_error?.reason === REVERIFICATION_REASON\n  );\n};\n\nexport { reverificationError, reverificationErrorResponse, isReverificationHint };\n"], "names": [], "mappings": ";;;;;;AAMA,IAAM,wBAAwB;AAS9B,IAAM,sBAAsB,CAC1B,gBAAA,CAKK;QACL,aAAa;YACX,MAAM;YACN,QAAQ;YACR,UAAU;gBACR,gBAAgB;YAClB;QACF;IACF,CAAA;AAEA,IAAM,8BAA8B,CAAA,GAAI,OACtC,IAAI,SAAS,KAAK,SAAA,CAAU,oBAAoB,GAAG,IAAI,CAAC,GAAG;QACzD,QAAQ;IACV,CAAC;AAEH,IAAM,uBAAuB,CAAC,WAAkE;IAC9F,OACE,UACA,OAAO,WAAW,YAClB,iBAAiB,UACjB,OAAO,WAAA,EAAa,SAAS,eAC7B,OAAO,WAAA,EAAa,WAAW;AAEnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/apiUrlFromPublishableKey.ts"], "sourcesContent": ["import {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  LOCAL_API_URL,\n  LOCAL_ENV_SUFFIXES,\n  PROD_API_URL,\n  STAGING_API_URL,\n  STAGING_ENV_SUFFIXES,\n} from './constants';\nimport { parsePublishableKey } from './keys';\n\n/**\n * Get the correct API url based on the publishable key.\n *\n * @param publishableKey - The publishable key to parse.\n * @returns One of Clerk's API URLs.\n */\nexport const apiUrlFromPublishableKey = (publishableKey: string) => {\n  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n\n  if (frontendApi?.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return PROD_API_URL;\n  }\n\n  if (LOCAL_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return LOCAL_API_URL;\n  }\n  if (STAGING_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return STAGING_API_URL;\n  }\n  return PROD_API_URL;\n};\n"], "names": [], "mappings": ";;;;;;;;AAgBO,IAAM,2BAA2B,CAAC,mBAA2B;IAClE,MAAM,8RAAc,sBAAA,EAAoB,cAAc,GAAG;IAEzD,IAAI,aAAa,WAAW,QAAQ,iRAAK,+BAAA,CAA6B,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACnH,mRAAO,eAAA;IACT;IAEA,+QAAI,sBAAA,CAAmB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACpE,mRAAO,gBAAA;IACT;IACA,IAAI,mSAAA,CAAqB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACtE,mRAAO,kBAAA;IACT;IACA,mRAAO,eAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1718, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/underscore.ts"], "sourcesContent": ["/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAMO,IAAM,aAAa,CAAC,UAA4B;IAErD,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO;IACT;IACA,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO,KAAA,CAAM,CAAC,CAAA;IAChB;IACA,IAAI,WAAW,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,IAAI;IAC3C,YAAY,CAAA,KAAA,EAAQ,MAAM,KAAA,CAAM,CAAA,CAAE,CAAC,EAAA;IACnC,OAAO;AACT;AAEA,IAAM,sBACJ;AAOK,SAAS,cAAc,GAAA,EAAyC;IACrE,OAAO,oBAAoB,IAAA,CAAK,OAAO,EAAE;AAC3C;AAaO,SAAS,SAAS,GAAA,EAAwC;IAC/D,MAAM,IAAI,OAAO;IACjB,OAAO,EAAE,MAAA,CAAO,CAAC,EAAE,WAAA,CAAY,IAAI,EAAE,KAAA,CAAM,CAAC;AAC9C;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,gBAAgB,CAAA,QAAS,MAAM,WAAA,CAAY,EAAE,OAAA,CAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,UAAU,CAAA,SAAU,CAAA,CAAA,EAAI,OAAO,WAAA,CAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;IACtD,MAAM,gBAAgB,CAAC,QAAkB;QACvC,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QAEA,IAAI,MAAM,OAAA,CAAQ,GAAG,GAAG;YACtB,OAAO,IAAI,GAAA,CAAI,CAAA,OAAM;gBACnB,IAAI,OAAO,OAAO,YAAY,MAAM,OAAA,CAAQ,EAAE,GAAG;oBAC/C,OAAO,cAAc,EAAE;gBACzB;gBACA,OAAO;YACT,CAAC;QACH;QAEA,MAAM,OAAO;YAAE,GAAG,GAAA;QAAI;QACtB,MAAM,OAAO,OAAO,IAAA,CAAK,IAAI;QAC7B,KAAA,MAAW,WAAW,KAAM;YAC1B,MAAM,UAAU,UAAU,QAAQ,QAAA,CAAS,CAAC;YAC5C,IAAI,YAAY,SAAS;gBACvB,IAAA,CAAK,OAAO,CAAA,GAAI,IAAA,CAAK,OAAO,CAAA;gBAC5B,OAAO,IAAA,CAAK,OAAO,CAAA;YACrB;YACA,IAAI,OAAO,IAAA,CAAK,OAAO,CAAA,KAAM,UAAU;gBACrC,IAAA,CAAK,OAAO,CAAA,GAAI,cAAc,IAAA,CAAK,OAAO,CAAC;YAC7C;QACF;QACA,OAAO;IACT;IAEA,OAAO;AACT;AASO,IAAM,mBAAmB,4BAA4B,YAAY;AASjE,IAAM,mBAAmB,4BAA4B,YAAY;AAOjE,SAAS,SAAS,KAAA,EAAyB;IAEhD,IAAI,OAAO,UAAU,CAAA,OAAA,CAAA,EAAW;QAC9B,OAAO;IACT;IAGA,IAAI,UAAU,KAAA,KAAa,UAAU,MAAM;QACzC,OAAO;IACT;IAGA,IAAI,OAAO,UAAU,CAAA,MAAA,CAAA,EAAU;QAC7B,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,IAAA,CAAA,EAAQ;YAClC,OAAO;QACT;QAEA,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,KAAA,CAAA,EAAS;YACnC,OAAO;QACT;IACF;IAGA,MAAM,SAAS,SAAS,OAAiB,EAAE;IAC3C,IAAI,MAAM,MAAM,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,SAAS,GAAG;QACd,OAAO;IACT;IAGA,OAAO;AACT;AAKO,SAAS,sBAAwC,GAAA,EAAoB;IAC1E,OAAO,OAAO,OAAA,CAAQ,GAAG,EAAE,MAAA,CAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAA,KAAM;QACvD,IAAI,UAAU,KAAA,GAAW;YACvB,GAAA,CAAI,GAAc,CAAA,GAAI;QACxB;QACA,OAAO;IACT,GAAG,CAAC,CAAe;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1863, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/logger.ts"], "sourcesContent": ["const loggedMessages: Set<string> = new Set();\n\nexport const logger = {\n  /**\n   * A custom logger that ensures messages are logged only once.\n   * Reduces noise and duplicated messages when logs are in a hot codepath.\n   */\n  warnOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    loggedMessages.add(msg);\n    console.warn(msg);\n  },\n  logOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    console.log(msg);\n    loggedMessages.add(msg);\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA,IAAM,iBAA8B,aAAA,GAAA,IAAI,IAAI;AAErC,IAAM,SAAS;IAAA;;;GAAA,GAKpB,UAAU,CAAC,QAAgB;QACzB,IAAI,eAAe,GAAA,CAAI,GAAG,GAAG;YAC3B;QACF;QAEA,eAAe,GAAA,CAAI,GAAG;QACtB,QAAQ,IAAA,CAAK,GAAG;IAClB;IACA,SAAS,CAAC,QAAgB;QACxB,IAAI,eAAe,GAAA,CAAI,GAAG,GAAG;YAC3B;QACF;QAEA,QAAQ,GAAA,CAAI,GAAG;QACf,eAAe,GAAA,CAAI,GAAG;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/proxy.ts"], "sourcesContent": ["export function isValidProxyUrl(key: string | undefined) {\n  if (!key) {\n    return true;\n  }\n\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\n\nexport function isHttpOrHttps(key: string | undefined) {\n  return /^http(s)?:\\/\\//.test(key || '');\n}\n\nexport function isProxyUrlRelative(key: string) {\n  return key.startsWith('/');\n}\n\nexport function proxyUrlToAbsoluteURL(url: string | undefined): string {\n  if (!url) {\n    return '';\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\n"], "names": [], "mappings": ";;;;;;;AAAO,SAAS,gBAAgB,GAAA,EAAyB;IACvD,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IAEA,OAAO,cAAc,GAAG,KAAK,mBAAmB,GAAG;AACrD;AAEO,SAAS,cAAc,GAAA,EAAyB;IACrD,OAAO,iBAAiB,IAAA,CAAK,OAAO,EAAE;AACxC;AAEO,SAAS,mBAAmB,GAAA,EAAa;IAC9C,OAAO,IAAI,UAAA,CAAW,GAAG;AAC3B;AAEO,SAAS,sBAAsB,GAAA,EAAiC;IACrE,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,OAAO,mBAAmB,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,QAAA,CAAS,MAAM,EAAE,QAAA,CAAS,IAAI;AACrF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1974, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/allSettled.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/logErrorInDevMode.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/fastDeepMerge.ts"], "sourcesContent": ["/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIO,SAAS,WACd,QAAA,EACsF;IACtF,MAAM,WAAW,MAAM,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,CAAA,IACxC,EAAE,IAAA,CACA,CAAA,QAAA,CAAU;gBAAE,QAAQ;gBAAa;YAAM,CAAA,GACvC,CAAA,SAAA,CAAW;gBAAE,QAAQ;gBAAY;YAAO,CAAA;IAG5C,OAAO,QAAQ,GAAA,CAAI,QAAQ;AAC7B;;ACZO,IAAM,oBAAoB,CAAC,YAAoB;IACpD,oRAAI,2BAAA,CAAyB,IAAG;QAC9B,QAAQ,KAAA,CAAM,CAAA,OAAA,EAAU,OAAO,EAAE;IACnC;AACF;;ACDO,IAAM,0BAA0B,CACrC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,wBAAwB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAClD,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,GAAG;YAC5D,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF;AAEO,IAAM,uBAAuB,CAClC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,qBAAqB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAC/C,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;YACzF,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/noop.ts"], "sourcesContent": ["export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAM,OAAO,CAAA,GAAI,SAExB,CAF+C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/createDeferredPromise.ts"], "sourcesContent": ["import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n"], "names": [], "mappings": ";;;;;;AAUO,IAAM,wBAAwB,MAAM;IACzC,IAAI,sRAAoB,OAAA;IACxB,IAAI,qRAAmB,OAAA;IACvB,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;QACxC,UAAU;QACV,SAAS;IACX,CAAC;IACD,OAAO;QAAE;QAAS;QAAS;IAAO;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/handleValueOrFn.ts"], "sourcesContent": ["type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;AAGO,SAAS,gBAAmB,KAAA,EAAyB,GAAA,EAAU,YAAA,EAAiC;IACrG,IAAI,OAAO,UAAU,YAAY;QAC/B,OAAQ,MAAwB,GAAG;IACrC;IAEA,IAAI,OAAO,UAAU,aAAa;QAChC,OAAO;IACT;IAEA,IAAI,OAAO,iBAAiB,aAAa;QACvC,OAAO;IACT;IAEA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/map-obj%404.3.0/node_modules/map-obj/index.js"], "sourcesContent": ["'use strict';\n\nconst isObject = value => typeof value === 'object' && value !== null;\nconst mapObjectSkip = Symbol('skip');\n\n// Customized for this use-case\nconst isObjectCustom = value =>\n\tisObject(value) &&\n\t!(value instanceof RegExp) &&\n\t!(value instanceof Error) &&\n\t!(value instanceof Date);\n\nconst mapObject = (object, mapper, options, isSeen = new WeakMap()) => {\n\toptions = {\n\t\tdeep: false,\n\t\ttarget: {},\n\t\t...options\n\t};\n\n\tif (isSeen.has(object)) {\n\t\treturn isSeen.get(object);\n\t}\n\n\tisSeen.set(object, options.target);\n\n\tconst {target} = options;\n\tdelete options.target;\n\n\tconst mapArray = array => array.map(element => isObjectCustom(element) ? mapObject(element, mapper, options, isSeen) : element);\n\tif (Array.isArray(object)) {\n\t\treturn mapArray(object);\n\t}\n\n\tfor (const [key, value] of Object.entries(object)) {\n\t\tconst mapResult = mapper(key, value, object);\n\n\t\tif (mapResult === mapObjectSkip) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet [newKey, newValue, {shouldRecurse = true} = {}] = mapResult;\n\n\t\t// Drop `__proto__` keys.\n\t\tif (newKey === '__proto__') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (options.deep && shouldRecurse && isObjectCustom(newValue)) {\n\t\t\tnewValue = Array.isArray(newValue) ?\n\t\t\t\tmapArray(newValue) :\n\t\t\t\tmapObject(newValue, mapper, options, isSeen);\n\t\t}\n\n\t\ttarget[newKey] = newValue;\n\t}\n\n\treturn target;\n};\n\nmodule.exports = (object, mapper, options) => {\n\tif (!isObject(object)) {\n\t\tthrow new TypeError(`Expected an object, got \\`${object}\\` (${typeof object})`);\n\t}\n\n\treturn mapObject(object, mapper, options);\n};\n\nmodule.exports.mapObjectSkip = mapObjectSkip;\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,WAAW,CAAA,QAAS,OAAO,UAAU,YAAY,UAAU;AACjE,MAAM,gBAAgB,OAAO;AAE7B,+BAA+B;AAC/B,MAAM,iBAAiB,CAAA,QACtB,SAAS,UACT,CAAC,CAAC,iBAAiB,MAAM,KACzB,CAAC,CAAC,iBAAiB,KAAK,KACxB,CAAC,CAAC,iBAAiB,IAAI;AAExB,MAAM,YAAY,CAAC,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS;IACjE,UAAU;QACT,MAAM;QACN,QAAQ,CAAC;QACT,GAAG,OAAO;IACX;IAEA,IAAI,OAAO,GAAG,CAAC,SAAS;QACvB,OAAO,OAAO,GAAG,CAAC;IACnB;IAEA,OAAO,GAAG,CAAC,QAAQ,QAAQ,MAAM;IAEjC,MAAM,EAAC,MAAM,EAAC,GAAG;IACjB,OAAO,QAAQ,MAAM;IAErB,MAAM,WAAW,CAAA,QAAS,MAAM,GAAG,CAAC,CAAA,UAAW,eAAe,WAAW,UAAU,SAAS,QAAQ,SAAS,UAAU;IACvH,IAAI,MAAM,OAAO,CAAC,SAAS;QAC1B,OAAO,SAAS;IACjB;IAEA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QAClD,MAAM,YAAY,OAAO,KAAK,OAAO;QAErC,IAAI,cAAc,eAAe;YAChC;QACD;QAEA,IAAI,CAAC,QAAQ,UAAU,EAAC,gBAAgB,IAAI,EAAC,GAAG,CAAC,CAAC,CAAC,GAAG;QAEtD,yBAAyB;QACzB,IAAI,WAAW,aAAa;YAC3B;QACD;QAEA,IAAI,QAAQ,IAAI,IAAI,iBAAiB,eAAe,WAAW;YAC9D,WAAW,MAAM,OAAO,CAAC,YACxB,SAAS,YACT,UAAU,UAAU,QAAQ,SAAS;QACvC;QAEA,MAAM,CAAC,OAAO,GAAG;IAClB;IAEA,OAAO;AACR;AAEA,OAAO,OAAO,GAAG,CAAC,QAAQ,QAAQ;IACjC,IAAI,CAAC,SAAS,SAAS;QACtB,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,OAAO,IAAI,EAAE,OAAO,OAAO,CAAC,CAAC;IAC/E;IAEA,OAAO,UAAU,QAAQ,QAAQ;AAClC;AAEA,OAAO,OAAO,CAAC,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/tslib%402.8.1/node_modules/tslib/tslib.es6.mjs"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;8EAa8E,GAC9E,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC;IAC/B,gBAAgB,OAAO,cAAc,IAChC,CAAA;QAAE,WAAW,EAAE;IAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;IAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;QAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IACpG,OAAO,cAAc,GAAG;AAC1B;AAEO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC5B,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;IAC7D,cAAc,GAAG;IACjB,SAAS;QAAO,IAAI,CAAC,WAAW,GAAG;IAAG;IACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;AACrF;AAEO,IAAI,WAAW;IACpB,WAAW,OAAO,MAAM,IAAI,SAAS,SAAS,CAAC;QAC3C,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACT;AAEO,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;IACtD,IAAI,IAAI,UAAU,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,wBAAwB,CAAC,QAAQ,OAAO,MAAM;IAC3H,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,KAAK;SACpH,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK,IAAI,IAAI,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,IAAI,KAAK;IAChJ,OAAO,IAAI,KAAK,KAAK,OAAO,cAAc,CAAC,QAAQ,KAAK,IAAI;AAC9D;AAEO,SAAS,QAAQ,UAAU,EAAE,SAAS;IAC3C,OAAO,SAAU,MAAM,EAAE,GAAG;QAAI,UAAU,QAAQ,KAAK;IAAa;AACtE;AAEO,SAAS,aAAa,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB;IACrG,SAAS,OAAO,CAAC;QAAI,IAAI,MAAM,KAAK,KAAK,OAAO,MAAM,YAAY,MAAM,IAAI,UAAU;QAAsB,OAAO;IAAG;IACtH,IAAI,OAAO,UAAU,IAAI,EAAE,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;IACzF,IAAI,SAAS,CAAC,gBAAgB,OAAO,SAAS,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG;IACnF,IAAI,aAAa,gBAAgB,CAAC,SAAS,OAAO,wBAAwB,CAAC,QAAQ,UAAU,IAAI,IAAI,CAAC,CAAC;IACvG,IAAI,GAAG,OAAO;IACd,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC7C,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,KAAK,UAAW,OAAO,CAAC,EAAE,GAAG,MAAM,WAAW,CAAC,IAAI,SAAS,CAAC,EAAE;QACxE,IAAK,IAAI,KAAK,UAAU,MAAM,CAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,EAAE;QACvE,QAAQ,cAAc,GAAG,SAAU,CAAC;YAAI,IAAI,MAAM,MAAM,IAAI,UAAU;YAA2D,kBAAkB,IAAI,CAAC,OAAO,KAAK;QAAQ;QAC5K,IAAI,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,SAAS,aAAa;YAAE,KAAK,WAAW,GAAG;YAAE,KAAK,WAAW,GAAG;QAAC,IAAI,UAAU,CAAC,IAAI,EAAE;QACtH,IAAI,SAAS,YAAY;YACrB,IAAI,WAAW,KAAK,GAAG;YACvB,IAAI,WAAW,QAAQ,OAAO,WAAW,UAAU,MAAM,IAAI,UAAU;YACvE,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,IAAI,GAAG,aAAa,OAAO,CAAC;QACtD,OACK,IAAI,IAAI,OAAO,SAAS;YACzB,IAAI,SAAS,SAAS,aAAa,OAAO,CAAC;iBACtC,UAAU,CAAC,IAAI,GAAG;QAC3B;IACJ;IACA,IAAI,QAAQ,OAAO,cAAc,CAAC,QAAQ,UAAU,IAAI,EAAE;IAC1D,OAAO;AACT;;AAEO,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,KAAK;IAC5D,IAAI,WAAW,UAAU,MAAM,GAAG;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC1C,QAAQ,WAAW,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;IACnF;IACA,OAAO,WAAW,QAAQ,KAAK;AACjC;;AAEO,SAAS,UAAU,CAAC;IACzB,OAAO,OAAO,MAAM,WAAW,IAAI,GAAG,MAAM,CAAC;AAC/C;;AAEO,SAAS,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,WAAW,EAAE,OAAO;IAC5F,OAAO,OAAO,cAAc,CAAC,GAAG,QAAQ;QAAE,cAAc;QAAM,OAAO,SAAS,GAAG,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAAK;AACpH;;AAEO,SAAS,WAAW,WAAW,EAAE,aAAa;IACnD,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,OAAO,QAAQ,QAAQ,CAAC,aAAa;AAClH;AAEO,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACzD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACF;AAEO,SAAS,YAAY,OAAO,EAAE,IAAI;IACvC,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,aAAa,aAAa,WAAW,MAAM,EAAE,SAAS;IAC/L,OAAO,EAAE,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IAC1J,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACF;AAEO,IAAI,kBAAkB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAChE,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QAC/E,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAChE;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AAC/B,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACxB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AACd;AAEO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC/B,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,gBAAgB,GAAG,GAAG;AAC7G;AAEO,SAAS,SAAS,CAAC;IACxB,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACtD;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,KAAK,EAAE,EAAE,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAC3C,KAAK,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE;IACtC,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,MAAM,EAAE,IAAI,IAAI,IAAK,KAAK,SAAS,CAAC,EAAE,CAAC,MAAM;IACnF,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IACzC,IAAK,IAAI,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAC1D,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI;IAC1C,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACpD;AAEO,SAAS,QAAQ,CAAC;IACvB,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AACpE;AAEO,SAAS,iBAAiB,OAAO,EAAE,UAAU,EAAE,SAAS;IAC7D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,kBAAkB,aAAa,gBAAgB,MAAM,EAAE,SAAS,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACtN,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACnF;AAEO,SAAS,iBAAiB,CAAC;IAChC,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACvI;AAEO,SAAS,cAAc,CAAC;IAC7B,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC7H;AAEO,SAAS,qBAAqB,MAAM,EAAE,GAAG;IAC9C,IAAI,OAAO,cAAc,EAAE;QAAE,OAAO,cAAc,CAAC,QAAQ,OAAO;YAAE,OAAO;QAAI;IAAI,OAAO;QAAE,OAAO,GAAG,GAAG;IAAK;IAC9G,OAAO;AACT;;AAEA,IAAI,qBAAqB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC;IACrD,OAAO,cAAc,CAAC,GAAG,WAAW;QAAE,YAAY;QAAM,OAAO;IAAE;AACnE,IAAK,SAAS,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,UAAU,GAAG;AACjB;AAEA,IAAI,UAAU,SAAS,CAAC;IACtB,UAAU,OAAO,mBAAmB,IAAI,SAAU,CAAC;QACjD,IAAI,KAAK,EAAE;QACX,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG;QACjF,OAAO;IACT;IACA,OAAO,QAAQ;AACjB;AAEO,SAAS,aAAa,GAAG;IAC9B,IAAI,OAAO,IAAI,UAAU,EAAE,OAAO;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,MAAM;QAAA,IAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,KAAK,WAAW,gBAAgB,QAAQ,KAAK,CAAC,CAAC,EAAE;IAAC;IAChI,mBAAmB,QAAQ;IAC3B,OAAO;AACT;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,SAAS;IAAI;AACxD;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC7D,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACtF;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpE,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACtG;AAEO,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IACnD,IAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa,YAAa,MAAM,IAAI,UAAU;IAC/G,OAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,GAAG,CAAC;AACtE;AAEO,SAAS,wBAAwB,GAAG,EAAE,KAAK,EAAE,KAAK;IACvD,IAAI,UAAU,QAAQ,UAAU,KAAK,GAAG;QACtC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,MAAM,IAAI,UAAU;QAClF,IAAI,SAAS;QACb,IAAI,OAAO;YACT,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,IAAI,UAAU;YAC9C,UAAU,KAAK,CAAC,OAAO,YAAY,CAAC;QACtC;QACA,IAAI,YAAY,KAAK,GAAG;YACtB,IAAI,CAAC,OAAO,OAAO,EAAE,MAAM,IAAI,UAAU;YACzC,UAAU,KAAK,CAAC,OAAO,OAAO,CAAC;YAC/B,IAAI,OAAO,QAAQ;QACrB;QACA,IAAI,OAAO,YAAY,YAAY,MAAM,IAAI,UAAU;QACvD,IAAI,OAAO,UAAU;YAAa,IAAI;gBAAE,MAAM,IAAI,CAAC,IAAI;YAAG,EAAE,OAAO,GAAG;gBAAE,OAAO,QAAQ,MAAM,CAAC;YAAI;QAAE;QACpG,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;YAAO,SAAS;YAAS,OAAO;QAAM;IAChE,OACK,IAAI,OAAO;QACd,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;QAAK;IAC/B;IACA,OAAO;AACT;AAEA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IACnH,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AAEO,SAAS,mBAAmB,GAAG;IACpC,SAAS,KAAK,CAAC;QACb,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,IAAI,iBAAiB,GAAG,IAAI,KAAK,EAAE,8CAA8C;QAC5G,IAAI,QAAQ,GAAG;IACjB;IACA,IAAI,GAAG,IAAI;IACX,SAAS;QACP,MAAO,IAAI,IAAI,KAAK,CAAC,GAAG,GAAI;YAC1B,IAAI;gBACF,IAAI,CAAC,EAAE,KAAK,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,OAAO,GAAG,IAAI,CAAC;gBACjF,IAAI,EAAE,OAAO,EAAE;oBACb,IAAI,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK;oBACnC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC;wBAAI,KAAK;wBAAI,OAAO;oBAAQ;gBACvG,OACK,KAAK;YACZ,EACA,OAAO,GAAG;gBACR,KAAK;YACP;QACF;QACA,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,GAAG,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI,QAAQ,OAAO;QAC9E,IAAI,IAAI,QAAQ,EAAE,MAAM,IAAI,KAAK;IACnC;IACA,OAAO;AACT;AAEO,SAAS,iCAAiC,IAAI,EAAE,WAAW;IAChE,IAAI,OAAO,SAAS,YAAY,WAAW,IAAI,CAAC,OAAO;QACnD,OAAO,KAAK,OAAO,CAAC,oDAAoD,SAAU,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YAChG,OAAO,MAAM,cAAc,SAAS,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,MAAM,GAAG,WAAW,KAAK;QAC7G;IACJ;IACA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2813, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/lower-case%402.0.2/node_modules/lower-case/src/index.ts"], "sourcesContent": ["/**\n * Locale character mapping rules.\n */\ninterface Locale {\n  regexp: RegExp;\n  map: Record<string, string>;\n}\n\n/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nconst SUPPORTED_LOCALE: Record<string, Locale> = {\n  tr: {\n    regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  az: {\n    regexp: /\\u0130/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  lt: {\n    regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n    map: {\n      I: \"\\u0069\\u0307\",\n      J: \"\\u006A\\u0307\",\n      Į: \"\\u012F\\u0307\",\n      Ì: \"\\u0069\\u0307\\u0300\",\n      Í: \"\\u0069\\u0307\\u0301\",\n      Ĩ: \"\\u0069\\u0307\\u0303\",\n    },\n  },\n};\n\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str: string, locale: string) {\n  const lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return lowerCase(str.replace(lang.regexp, (m) => lang.map[m]));\n  return lowerCase(str);\n}\n\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str: string) {\n  return str.toLowerCase();\n}\n"], "names": [], "mappings": "AAQA;;GAEG;;;;AACH,IAAM,gBAAgB,GAA2B;IAC/C,EAAE,EAAE;QACF,MAAM,EAAE,6BAA6B;QACrC,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,SAAS;QACjB,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,4CAA4C;QACpD,GAAG,EAAE;YACH,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;SACxB;KACF;CACF,CAAC;AAKI,SAAU,eAAe,CAAC,GAAW,EAAE,MAAc;IACzD,IAAM,IAAI,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACpD,IAAI,IAAI,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,SAAC,CAAC;QAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAAX,CAAW,CAAC,CAAC,CAAC;IACzE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AAKK,SAAU,SAAS,CAAC,GAAW;IACnC,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;AAC3B,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2864, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/no-case%403.0.4/node_modules/no-case/src/index.ts"], "sourcesContent": ["import { lowerCase } from \"lower-case\";\n\nexport interface Options {\n  splitRegexp?: RegExp | RegExp[];\n  stripRegexp?: RegExp | RegExp[];\n  delimiter?: string;\n  transform?: (part: string, index: number, parts: string[]) => string;\n}\n\n// Support camel case (\"camelCase\" -> \"camel Case\" and \"CAMELCase\" -> \"CAMEL Case\").\nconst DEFAULT_SPLIT_REGEXP = [/([a-z0-9])([A-Z])/g, /([A-Z])([A-Z][a-z])/g];\n\n// Remove all non-word characters.\nconst DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;\n\n/**\n * Normalize the string into something other libraries can manipulate easier.\n */\nexport function noCase(input: string, options: Options = {}) {\n  const {\n    splitRegexp = DEFAULT_SPLIT_REGEXP,\n    stripRegexp = DEFAULT_STRIP_REGEXP,\n    transform = lowerCase,\n    delimiter = \" \",\n  } = options;\n\n  let result = replace(\n    replace(input, splitRegexp, \"$1\\0$2\"),\n    stripRegexp,\n    \"\\0\"\n  );\n  let start = 0;\n  let end = result.length;\n\n  // Trim the delimiter from around the output string.\n  while (result.charAt(start) === \"\\0\") start++;\n  while (result.charAt(end - 1) === \"\\0\") end--;\n\n  // Transform each token independently.\n  return result.slice(start, end).split(\"\\0\").map(transform).join(delimiter);\n}\n\n/**\n * Replace `re` in the input string with the replacement value.\n */\nfunction replace(input: string, re: RegExp | RegExp[], value: string) {\n  if (re instanceof RegExp) return input.replace(re, value);\n  return re.reduce((input, re) => input.replace(re, value), input);\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;;AASvC,oFAAoF;AACpF,IAAM,oBAAoB,GAAG;IAAC,oBAAoB;IAAE,sBAAsB;CAAC,CAAC;AAE5E,kCAAkC;AAClC,IAAM,oBAAoB,GAAG,cAAc,CAAC;AAKtC,SAAU,MAAM,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAEvD,IAAA,KAIE,OAAO,CAAA,WAJyB,EAAlC,WAAW,GAAA,OAAA,KAAA,IAAG,oBAAoB,GAAA,EAAA,EAClC,KAGE,OAAO,CAAA,WAHyB,EAAlC,WAAW,GAAA,OAAA,KAAA,IAAG,oBAAoB,GAAA,EAAA,EAClC,KAEE,OAAO,CAAA,SAFY,EAArB,SAAS,GAAA,OAAA,KAAA,yNAAG,YAAS,GAAA,EAAA,EACrB,KACE,OAAO,CAAA,SADM,EAAf,SAAS,GAAA,OAAA,KAAA,IAAG,GAAG,GAAA,EAAA,CACL;IAEZ,IAAI,MAAM,GAAG,OAAO,CAClB,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EACrC,WAAW,EACX,IAAI,CACL,CAAC;IACF,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAExB,oDAAoD;IACpD,MAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,CAAE,KAAK,EAAE,CAAC;IAC9C,MAAO,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,CAAE,GAAG,EAAE,CAAC;IAE9C,sCAAsC;IACtC,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7E,CAAC;AAED;;GAEG,CACH,SAAS,OAAO,CAAC,KAAa,EAAE,EAAqB,EAAE,KAAa;IAClE,IAAI,EAAE,YAAY,MAAM,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC1D,OAAO,EAAE,CAAC,MAAM,CAAC,SAAC,KAAK,EAAE,EAAE;QAAK,OAAA,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC;IAAxB,CAAwB,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2904, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/dot-case%403.0.4/node_modules/dot-case/src/index.ts"], "sourcesContent": ["import { noCase, Options } from \"no-case\";\n\nexport { Options };\n\nexport function dotCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \".\",\n    ...options,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,MAAM,EAAW,MAAM,SAAS,CAAC;;;AAIpC,SAAU,OAAO,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAC1D,0NAAO,SAAA,AAAM,EAAC,KAAK,EAAA,CAAA,GAAA,0LAAA,CAAA,WAAA,EAAA;QACjB,SAAS,EAAE,GAAG;IAAA,GACX,OAAO,EACV,CAAC;AACL,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2925, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/snake-case%403.0.4/node_modules/snake-case/src/index.ts"], "sourcesContent": ["import { dotCase, Options } from \"dot-case\";\n\nexport { Options };\n\nexport function snakeCase(input: string, options: Options = {}) {\n  return dotCase(input, {\n    delimiter: \"_\",\n    ...options,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,OAAO,EAAW,MAAM,UAAU,CAAC;;;AAItC,SAAU,SAAS,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAC5D,4NAAO,UAAA,AAAO,EAAC,KAAK,EAAA,CAAA,GAAA,0LAAA,CAAA,WAAA,EAAA;QAClB,SAAS,EAAE,GAAG;IAAA,GACX,OAAO,EACV,CAAC;AACL,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/snakecase-keys%408.0.1/node_modules/snakecase-keys/index.js"], "sourcesContent": ["'use strict'\n\nconst map = require('map-obj')\nconst { snakeCase } = require('snake-case')\n\nconst PlainObjectConstructor = {}.constructor\n\nmodule.exports = function (obj, options) {\n  if (Array.isArray(obj)) {\n    if (obj.some(item => item.constructor !== PlainObjectConstructor)) {\n      throw new Error('obj must be array of plain objects')\n    }\n  } else {\n    if (obj.constructor !== PlainObjectConstructor) {\n      throw new Error('obj must be an plain object')\n    }\n  }\n\n  options = Object.assign({ deep: true, exclude: [], parsingOptions: {} }, options)\n\n  return map(obj, function (key, val) {\n    return [\n      matches(options.exclude, key) ? key : snakeCase(key, options.parsingOptions),\n      val,\n      mapperOptions(key, val, options)\n    ]\n  }, options)\n}\n\nfunction matches (patterns, value) {\n  return patterns.some(function (pattern) {\n    return typeof pattern === 'string'\n      ? pattern === value\n      : pattern.test(value)\n  })\n}\n\nfunction mapperOptions (key, val, options) {\n  return options.shouldRecurse\n    ? { shouldRecurse: options.shouldRecurse(key, val) }\n    : undefined\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,EAAE,SAAS,EAAE;AAEnB,MAAM,yBAAyB,CAAC,EAAE,WAAW;AAE7C,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,IAAI,MAAM,OAAO,CAAC,MAAM;QACtB,IAAI,IAAI,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,yBAAyB;YACjE,MAAM,IAAI,MAAM;QAClB;IACF,OAAO;QACL,IAAI,IAAI,WAAW,KAAK,wBAAwB;YAC9C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,UAAU,OAAO,MAAM,CAAC;QAAE,MAAM;QAAM,SAAS,EAAE;QAAE,gBAAgB,CAAC;IAAE,GAAG;IAEzE,OAAO,IAAI,KAAK,SAAU,GAAG,EAAE,GAAG;QAChC,OAAO;YACL,QAAQ,QAAQ,OAAO,EAAE,OAAO,MAAM,UAAU,KAAK,QAAQ,cAAc;YAC3E;YACA,cAAc,KAAK,KAAK;SACzB;IACH,GAAG;AACL;AAEA,SAAS,QAAS,QAAQ,EAAE,KAAK;IAC/B,OAAO,SAAS,IAAI,CAAC,SAAU,OAAO;QACpC,OAAO,OAAO,YAAY,WACtB,YAAY,QACZ,QAAQ,IAAI,CAAC;IACnB;AACF;AAEA,SAAS,cAAe,GAAG,EAAE,GAAG,EAAE,OAAO;IACvC,OAAO,QAAQ,aAAa,GACxB;QAAE,eAAe,QAAQ,aAAa,CAAC,KAAK;IAAK,IACjD;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/cookie%401.0.2/node_modules/cookie/src/index.ts"], "sourcesContent": ["/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp =\n  /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\nconst __toString = Object.prototype.toString;\n\nconst NullObject = /* @__PURE__ */ (() => {\n  const C = function () {};\n  C.prototype = Object.create(null);\n  return C;\n})() as unknown as { new (): any };\n\n/**\n * Parse options.\n */\nexport interface ParseOptions {\n  /**\n   * Specifies a function that will be used to decode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since the value of a cookie has a limited character set (and must be a simple string), this function can be used to decode\n   * a previously-encoded cookie value into a JavaScript string.\n   *\n   * The default function is the global `decodeURIComponent`, wrapped in a `try..catch`. If an error\n   * is thrown it will return the cookie's original value. If you provide your own encode/decode\n   * scheme you must ensure errors are appropriately handled.\n   *\n   * @default decode\n   */\n  decode?: (str: string) => string | undefined;\n}\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nexport function parse(\n  str: string,\n  options?: ParseOptions,\n): Record<string, string | undefined> {\n  const obj: Record<string, string | undefined> = new NullObject();\n  const len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  const dec = options?.decode || decode;\n  let index = 0;\n\n  do {\n    const eqIdx = str.indexOf(\"=\", index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    const colonIdx = str.indexOf(\";\", index);\n    const endIdx = colonIdx === -1 ? len : colonIdx;\n\n    if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n      continue;\n    }\n\n    const keyStartIdx = startIndex(str, index, eqIdx);\n    const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    const key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (obj[key] === undefined) {\n      let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      let valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      const value = dec(str.slice(valStartIdx, valEndIdx));\n      obj[key] = value;\n    }\n\n    index = endIdx + 1;\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str: string, index: number, max: number) {\n  do {\n    const code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str: string, index: number, min: number) {\n  while (index > min) {\n    const code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize options.\n */\nexport interface SerializeOptions {\n  /**\n   * Specifies a function that will be used to encode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since value of a cookie has a limited character set (and must be a simple string), this function can be used to encode\n   * a value into a string suited for a cookie's value, and should mirror `decode` when parsing.\n   *\n   * @default encodeURIComponent\n   */\n  encode?: (str: string) => string;\n  /**\n   * Specifies the `number` (in seconds) to be the value for the [`Max-Age` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.2).\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  maxAge?: number;\n  /**\n   * Specifies the `Date` object to be the value for the [`Expires` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.1).\n   * When no expiration is set clients consider this a \"non-persistent cookie\" and delete it the current session is over.\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  expires?: Date;\n  /**\n   * Specifies the value for the [`Domain` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.3).\n   * When no domain is set clients consider the cookie to apply to the current domain only.\n   */\n  domain?: string;\n  /**\n   * Specifies the value for the [`Path` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.4).\n   * When no path is set, the path is considered the [\"default path\"](https://tools.ietf.org/html/rfc6265#section-5.1.4).\n   */\n  path?: string;\n  /**\n   * Enables the [`HttpOnly` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.6).\n   * When enabled, clients will not allow client-side JavaScript to see the cookie in `document.cookie`.\n   */\n  httpOnly?: boolean;\n  /**\n   * Enables the [`Secure` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.5).\n   * When enabled, clients will only send the cookie back if the browser has a HTTPS connection.\n   */\n  secure?: boolean;\n  /**\n   * Enables the [`Partitioned` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-cutler-httpbis-partitioned-cookies/).\n   * When enabled, clients will only send the cookie back when the current domain _and_ top-level domain matches.\n   *\n   * This is an attribute that has not yet been fully standardized, and may change in the future.\n   * This also means clients may ignore this attribute until they understand it. More information\n   * about can be found in [the proposal](https://github.com/privacycg/CHIPS).\n   */\n  partitioned?: boolean;\n  /**\n   * Specifies the value for the [`Priority` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   *\n   * - `'low'` will set the `Priority` attribute to `Low`.\n   * - `'medium'` will set the `Priority` attribute to `Medium`, the default priority when not set.\n   * - `'high'` will set the `Priority` attribute to `High`.\n   *\n   * More information about priority levels can be found in [the specification](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   */\n  priority?: \"low\" | \"medium\" | \"high\";\n  /**\n   * Specifies the value for the [`SameSite` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   *\n   * - `true` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   * - `'lax'` will set the `SameSite` attribute to `Lax` for lax same site enforcement.\n   * - `'none'` will set the `SameSite` attribute to `None` for an explicit cross-site cookie.\n   * - `'strict'` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   *\n   * More information about enforcement levels can be found in [the specification](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   */\n  sameSite?: boolean | \"lax\" | \"strict\" | \"none\";\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nexport function serialize(\n  name: string,\n  val: string,\n  options?: SerializeOptions,\n): string {\n  const enc = options?.encode || encodeURIComponent;\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError(`argument name is invalid: ${name}`);\n  }\n\n  const value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError(`argument val is invalid: ${val}`);\n  }\n\n  let str = name + \"=\" + value;\n  if (!options) return str;\n\n  if (options.maxAge !== undefined) {\n    if (!Number.isInteger(options.maxAge)) {\n      throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n    }\n\n    str += \"; Max-Age=\" + options.maxAge;\n  }\n\n  if (options.domain) {\n    if (!domainValueRegExp.test(options.domain)) {\n      throw new TypeError(`option domain is invalid: ${options.domain}`);\n    }\n\n    str += \"; Domain=\" + options.domain;\n  }\n\n  if (options.path) {\n    if (!pathValueRegExp.test(options.path)) {\n      throw new TypeError(`option path is invalid: ${options.path}`);\n    }\n\n    str += \"; Path=\" + options.path;\n  }\n\n  if (options.expires) {\n    if (\n      !isDate(options.expires) ||\n      !Number.isFinite(options.expires.valueOf())\n    ) {\n      throw new TypeError(`option expires is invalid: ${options.expires}`);\n    }\n\n    str += \"; Expires=\" + options.expires.toUTCString();\n  }\n\n  if (options.httpOnly) {\n    str += \"; HttpOnly\";\n  }\n\n  if (options.secure) {\n    str += \"; Secure\";\n  }\n\n  if (options.partitioned) {\n    str += \"; Partitioned\";\n  }\n\n  if (options.priority) {\n    const priority =\n      typeof options.priority === \"string\"\n        ? options.priority.toLowerCase()\n        : undefined;\n    switch (priority) {\n      case \"low\":\n        str += \"; Priority=Low\";\n        break;\n      case \"medium\":\n        str += \"; Priority=Medium\";\n        break;\n      case \"high\":\n        str += \"; Priority=High\";\n        break;\n      default:\n        throw new TypeError(`option priority is invalid: ${options.priority}`);\n    }\n  }\n\n  if (options.sameSite) {\n    const sameSite =\n      typeof options.sameSite === \"string\"\n        ? options.sameSite.toLowerCase()\n        : options.sameSite;\n    switch (sameSite) {\n      case true:\n      case \"strict\":\n        str += \"; SameSite=Strict\";\n        break;\n      case \"lax\":\n        str += \"; SameSite=Lax\";\n        break;\n      case \"none\":\n        str += \"; SameSite=None\";\n        break;\n      default:\n        throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str: string): string {\n  if (str.indexOf(\"%\") === -1) return str;\n\n  try {\n    return decodeURIComponent(str);\n  } catch (e) {\n    return str;\n  }\n}\n\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val: any): val is Date {\n  return __toString.call(val) === \"[object Date]\";\n}\n"], "names": [], "mappings": ";;;;AAiGA,QAAA,KAAA,GAAA,MA0CC;AA4GD,QAAA,SAAA,GAAA,UA6GC;AApWD;;;;;;;;;;;;;GAaG,CACH,MAAM,gBAAgB,GAAG,uCAAuC,CAAC;AAEjE;;;;;;;;;;;GAWG,CACH,MAAM,iBAAiB,GAAG,iCAAiC,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;;;;GAsBG,CACH,MAAM,iBAAiB,GACrB,qFAAqF,CAAC;AAExF;;;;;;GAMG,CACH,MAAM,eAAe,GAAG,iCAAiC,CAAC;AAE1D,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAE7C,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,CAAC,GAAG,EAAE;IACvC,MAAM,CAAC,GAAG,YAAa,CAAC,CAAC;IACzB,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClC,OAAO,CAAC,CAAC;AACX,CAAC,CAAC,EAAgC,CAAC;AAoBnC;;;;;GAKG,CACH,SAAgB,KAAK,CACnB,GAAW,EACX,OAAsB;IAEtB,MAAM,GAAG,GAAuC,IAAI,UAAU,EAAE,CAAC;IACjE,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,iGAAiG;IACjG,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC;IAExB,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,MAAM,CAAC;IACtC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,GAAG,CAAC;QACF,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,wBAAwB;QAEjD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;QAEhD,IAAI,KAAK,GAAG,MAAM,EAAE,CAAC;YACnB,+BAA+B;YAC/B,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5C,SAAS;QACX,CAAC;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAE9C,mBAAmB;QACnB,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAEnD,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YACrD,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;IACrB,CAAC,OAAQ,KAAK,GAAG,GAAG,CAAE;IAEtB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACzD,GAAG,CAAC;QACF,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,CAAC;IACpE,CAAC,OAAQ,EAAE,KAAK,GAAG,GAAG,CAAE;IACxB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACvD,MAAO,KAAK,GAAG,GAAG,CAAE,CAAC;QACnB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,GAAG,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAmFD;;;;;;;;GAQG,CACH,SAAgB,SAAS,CACvB,IAAY,EACZ,GAAW,EACX,OAA0B;IAE1B,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,kBAAkB,CAAC;IAElD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAEvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,SAAS,CAAC,CAAA,yBAAA,EAA4B,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;IAC7B,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC;IAEzB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;IACtC,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,SAAS,CAAC,CAAA,wBAAA,EAA2B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,GAAG,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAClC,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,IACE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IACxB,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAC3C,CAAC;YACD,MAAM,IAAI,SAAS,CAAC,CAAA,2BAAA,EAA8B,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,GAAG,IAAI,YAAY,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,GAAG,IAAI,UAAU,CAAC;IACpB,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,GAAG,IAAI,eAAe,CAAC;IACzB,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,SAAS,CAAC;QAChB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,OAAO,CAAC,QAAQ,CAAC;QACvB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC;YACV,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAW;IACzB,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC;IAExC,IAAI,CAAC;QACH,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAQ;IACtB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,eAAe,CAAC;AAClD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/constants.ts"], "sourcesContent": ["import { apiUrlFromPublishableKey } from '@clerk/shared/apiUrlFromPublishableKey';\nimport { isTruthy } from '@clerk/shared/underscore';\n\nexport const CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || '';\nexport const CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || '';\nexport const API_VERSION = process.env.CLERK_API_VERSION || 'v1';\nexport const SECRET_KEY = process.env.CLERK_SECRET_KEY || '';\nexport const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';\nexport const ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || '';\nexport const API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nexport const DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || '';\nexport const PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '';\nexport const IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nexport const SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '';\nexport const SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '';\nexport const SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport const TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nexport const TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\n\nexport const KEYLESS_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED) || false;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,SAAS,gCAAgC;;AACzC,SAAS,gBAAgB;;;;;AAElB,MAAM,mBAAmB,QAAQ,GAAA,CAAI,4BAAA,IAAgC;AACrE,MAAM,eAAe,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AAC7D,MAAM,cAAc,QAAQ,GAAA,CAAI,iBAAA,IAAqB;AACrD,MAAM,aAAa,QAAQ,GAAA,CAAI,gBAAA,IAAoB;AACnD,MAAM,kBAAkB,QAAQ,IAAI,2DAAqC;AACzE,MAAM,iBAAiB,QAAQ,GAAA,CAAI,oBAAA,IAAwB;AAC3D,MAAM,UAAU,QAAQ,GAAA,CAAI,aAAA,oRAAiB,2BAAA,EAAyB,eAAe;AACrF,MAAM,SAAS,QAAQ,IAAI,sCAA4B;AACvD,MAAM,YAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;AAC7D,MAAM,+RAAe,WAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B,KAAK;AAC7E,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,eAAe;IAC1B,MAAM;IACN,SAAS;IACT,WAAA,EAAa,QAAQ,IAAI;AAC3B;AAEO,MAAM,qSAAqB,WAAA,EAAS,QAAQ,GAAA,CAAI,oCAAoC;AACpF,MAAM,kSAAkB,WAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;AAE9E,MAAM,mSAAmB,WAAA,EAAS,QAAQ,GAAA,CAAI,kCAAkC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/logFormatter.ts"], "sourcesContent": ["import type { LogEntry } from './debugLogger';\n\n// Move to shared once clerk/shared is used in clerk/nextjs\nconst maskSecretKey = (str: any) => {\n  if (!str || typeof str !== 'string') {\n    return str;\n  }\n\n  try {\n    return (str || '').replace(/^(sk_(live|test)_)(.+?)(.{3})$/, '$1*********$4');\n  } catch {\n    return '';\n  }\n};\n\nexport const logFormatter = (entry: LogEntry) => {\n  return (Array.isArray(entry) ? entry : [entry])\n    .map(entry => {\n      if (typeof entry === 'string') {\n        return maskSecretKey(entry);\n      }\n\n      const masked = Object.fromEntries(Object.entries(entry).map(([k, v]) => [k, maskSecretKey(v)]));\n      return JSON.stringify(masked, null, 2);\n    })\n    .join(', ');\n};\n"], "names": ["entry"], "mappings": ";;;;AAGA,MAAM,gBAAgB,CAAC,QAAa;IAClC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,IAAI;QACF,OAAA,CAAQ,OAAO,EAAA,EAAI,OAAA,CAAQ,kCAAkC,eAAe;IAC9E,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,MAAM,eAAe,CAAC,UAAoB;IAC/C,OAAA,CAAQ,MAAM,OAAA,CAAQ,KAAK,IAAI,QAAQ;QAAC,KAAK;KAAA,EAC1C,GAAA,CAAI,CAAAA,WAAS;QACZ,IAAI,OAAOA,WAAU,UAAU;YAC7B,OAAO,cAAcA,MAAK;QAC5B;QAEA,MAAM,SAAS,OAAO,WAAA,CAAY,OAAO,OAAA,CAAQA,MAAK,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAC,CAAA,GAAM;gBAAC;gBAAG,cAAc,CAAC,CAAC;aAAC,CAAC;QAC9F,OAAO,KAAK,SAAA,CAAU,QAAQ,MAAM,CAAC;IACvC,CAAC,EACA,IAAA,CAAK,IAAI;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/debugLogger.ts"], "sourcesContent": ["// TODO: Replace with a more sophisticated logging solution\n\nimport nextPkg from 'next/package.json';\n\nimport { logFormatter } from './logFormatter';\n\nexport type Log = string | Record<string, unknown>;\nexport type LogEntry = Log | Log[];\nexport type Logger<L = Log> = {\n  commit: () => void;\n  debug: (...args: Array<L | (() => L)>) => void;\n  enable: () => void;\n};\nexport type LoggerNoCommit<L = Logger> = Omit<L, 'commit'>;\n\nexport const createDebugLogger = (name: string, formatter: (val: LogEntry) => string) => (): Logger => {\n  const entries: LogEntry[] = [];\n  let isEnabled = false;\n\n  return {\n    enable: () => {\n      isEnabled = true;\n    },\n    debug: (...args) => {\n      if (isEnabled) {\n        entries.push(args.map(arg => (typeof arg === 'function' ? arg() : arg)));\n      }\n    },\n    commit: () => {\n      if (isEnabled) {\n        console.log(debugLogHeader(name));\n\n        /**\n         * We buffer each collected log entry so we can format them and log them all at once.\n         * Individual console.log calls are used to ensure we don't hit platform-specific log limits (Vercel and Netlify are 4kb).\n         */\n        for (const log of entries) {\n          let output = formatter(log);\n\n          output = output\n            .split('\\n')\n            .map(l => `  ${l}`)\n            .join('\\n');\n\n          // Vercel errors if the output is > 4kb, so we truncate it\n          if (process.env.VERCEL) {\n            output = truncate(output, 4096);\n          }\n\n          console.log(output);\n        }\n\n        console.log(debugLogFooter(name));\n      }\n    },\n  };\n};\n\ntype WithLogger = <L extends Logger, H extends (...args: any[]) => any>(\n  loggerFactoryOrName: string | (() => L),\n  handlerCtor: (logger: LoggerNoCommit<L>) => H,\n) => H;\n\nexport const withLogger: WithLogger = (loggerFactoryOrName, handlerCtor) => {\n  return ((...args: any) => {\n    const factory =\n      typeof loggerFactoryOrName === 'string'\n        ? createDebugLogger(loggerFactoryOrName, logFormatter)\n        : loggerFactoryOrName;\n\n    const logger = factory();\n    const handler = handlerCtor(logger as any);\n\n    try {\n      const res = handler(...args);\n      if (typeof res === 'object' && 'then' in res && typeof res.then === 'function') {\n        return res\n          .then((val: any) => {\n            logger.commit();\n            return val;\n          })\n          .catch((err: any) => {\n            logger.commit();\n            throw err;\n          });\n      }\n      // handle sync methods\n      logger.commit();\n      return res;\n    } catch (err: any) {\n      logger.commit();\n      throw err;\n    }\n  }) as ReturnType<typeof handlerCtor>;\n};\n\nfunction debugLogHeader(name: string) {\n  return `[clerk debug start: ${name}]`;\n}\n\nfunction debugLogFooter(name: string) {\n  return `[clerk debug end: ${name}] (@clerk/nextjs=${PACKAGE_VERSION},next=${nextPkg.version},timestamp=${Math.round(new Date().getTime() / 1_000)})`;\n}\n\n// ref: https://stackoverflow.com/questions/57769465/javascript-truncate-text-by-bytes-length\nfunction truncate(str: string, maxLength: number) {\n  const encoder = new TextEncoder();\n  const decoder = new TextDecoder('utf-8');\n\n  const encodedString = encoder.encode(str);\n  const truncatedString = encodedString.slice(0, maxLength);\n\n  // return the truncated string, removing any replacement characters that result from partially truncated characters\n  return decoder.decode(truncatedString).replace(/\\uFFFD/g, '');\n}\n"], "names": [], "mappings": ";;;;AAEA,OAAO,aAAa;AAEpB,SAAS,oBAAoB;;;;AAWtB,MAAM,oBAAoB,CAAC,MAAc,YAAyC,MAAc;QACrG,MAAM,UAAsB,CAAC,CAAA;QAC7B,IAAI,YAAY;QAEhB,OAAO;YACL,QAAQ,MAAM;gBACZ,YAAY;YACd;YACA,OAAO,CAAA,GAAI,SAAS;gBAClB,IAAI,WAAW;oBACb,QAAQ,IAAA,CAAK,KAAK,GAAA,CAAI,CAAA,MAAQ,OAAO,QAAQ,aAAa,IAAI,IAAI,GAAI,CAAC;gBACzE;YACF;YACA,QAAQ,MAAM;gBACZ,IAAI,WAAW;oBACb,QAAQ,GAAA,CAAI,eAAe,IAAI,CAAC;oBAMhC,KAAA,MAAW,OAAO,QAAS;wBACzB,IAAI,SAAS,UAAU,GAAG;wBAE1B,SAAS,OACN,KAAA,CAAM,IAAI,EACV,GAAA,CAAI,CAAA,IAAK,CAAA,EAAA,EAAK,CAAC,EAAE,EACjB,IAAA,CAAK,IAAI;wBAGZ,IAAI,QAAQ,GAAA,CAAI,MAAA,EAAQ;4BACtB,SAAS,SAAS,QAAQ,IAAI;wBAChC;wBAEA,QAAQ,GAAA,CAAI,MAAM;oBACpB;oBAEA,QAAQ,GAAA,CAAI,eAAe,IAAI,CAAC;gBAClC;YACF;QACF;IACF;AAOO,MAAM,aAAyB,CAAC,qBAAqB,gBAAgB;IAC1E,OAAQ,CAAA,GAAI,SAAc;QACxB,MAAM,UACJ,OAAO,wBAAwB,WAC3B,kBAAkB,2SAAqB,eAAY,IACnD;QAEN,MAAM,SAAS,QAAQ;QACvB,MAAM,UAAU,YAAY,MAAa;QAEzC,IAAI;YACF,MAAM,MAAM,QAAQ,GAAG,IAAI;YAC3B,IAAI,OAAO,QAAQ,YAAY,UAAU,OAAO,OAAO,IAAI,IAAA,KAAS,YAAY;gBAC9E,OAAO,IACJ,IAAA,CAAK,CAAC,QAAa;oBAClB,OAAO,MAAA,CAAO;oBACd,OAAO;gBACT,CAAC,EACA,KAAA,CAAM,CAAC,QAAa;oBACnB,OAAO,MAAA,CAAO;oBACd,MAAM;gBACR,CAAC;YACL;YAEA,OAAO,MAAA,CAAO;YACd,OAAO;QACT,EAAA,OAAS,KAAU;YACjB,OAAO,MAAA,CAAO;YACd,MAAM;QACR;IACF;AACF;AAEA,SAAS,eAAe,IAAA,EAAc;IACpC,OAAO,CAAA,oBAAA,EAAuB,IAAI,CAAA,CAAA,CAAA;AACpC;AAEA,SAAS,eAAe,IAAA,EAAc;IACpC,OAAO,CAAA,kBAAA,EAAqB,IAAI,CAAA,iBAAA,EAAoB,QAAe,CAAA,MAAA,yNAAS,UAAA,CAAQ,OAAO,CAAA,WAAA,EAAc,KAAK,KAAA,CAAA,AAAM,aAAA,GAAA,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAI,GAAK,CAAC,CAAA,CAAA,CAAA;AACnJ;AAGA,SAAS,SAAS,GAAA,EAAa,SAAA,EAAmB;IAChD,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,UAAU,IAAI,YAAY,OAAO;IAEvC,MAAM,gBAAgB,QAAQ,MAAA,CAAO,GAAG;IACxC,MAAM,kBAAkB,cAAc,KAAA,CAAM,GAAG,SAAS;IAGxD,OAAO,QAAQ,MAAA,CAAO,eAAe,EAAE,OAAA,CAAQ,WAAW,EAAE;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/sdk-versions.ts"], "sourcesContent": ["import nextPkg from 'next/package.json';\n\nconst isNext13 = nextPkg.version.startsWith('13.');\n\n/**\n * Those versions are affected by a bundling issue that will break the application if `node:fs` is used inside a server function.\n * The affected versions are >=next@13.5.4 and <=next@14.0.4\n */\nconst isNextWithUnstableServerActions = isNext13 || nextPkg.version.startsWith('14.0');\n\nexport { isNext13, isNextWithUnstableServerActions };\n"], "names": [], "mappings": ";;;;AAAA,OAAO,aAAa;;;AAEpB,MAAM,kOAAW,UAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,KAAK;AAMjD,MAAM,kCAAkC,mOAAY,UAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/headers-utils.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\nimport type { NextRequest } from 'next/server';\n\nimport type { RequestLike } from './types';\n\nexport function getCustomAttributeFromRequest(req: RequestLike, key: string): string | null | undefined {\n  // @ts-expect-error - TS doesn't like indexing into RequestLike\n  return key in req ? req[key] : undefined;\n}\n\nexport function getAuthKeyFromRequest(\n  req: RequestLike,\n  key: keyof typeof constants.Attributes,\n): string | null | undefined {\n  return getCustomAttributeFromRequest(req, constants.Attributes[key]) || getHeader(req, constants.Headers[key]);\n}\n\nexport function getHeader(req: RequestLike, name: string): string | null | undefined {\n  if (isNextRequest(req) || isRequestWebAPI(req)) {\n    return req.headers.get(name);\n  }\n\n  // If no header has been determined for IncomingMessage case, check if available within private `socket` headers\n  // When deployed to vercel, req.headers for API routes is a `IncomingHttpHeaders` key-val object which does not follow\n  // the Headers spec so the name is no longer case-insensitive.\n  return req.headers[name] || req.headers[name.toLowerCase()] || (req.socket as any)?._httpMessage?.getHeader(name);\n}\n\nexport function detectClerkMiddleware(req: RequestLike): boolean {\n  return Boolean(getAuthKeyFromRequest(req, 'AuthStatus'));\n}\n\nexport function isNextRequest(val: unknown): val is NextRequest {\n  try {\n    const { headers, nextUrl, cookies } = (val || {}) as NextRequest;\n    return (\n      typeof headers?.get === 'function' &&\n      typeof nextUrl?.searchParams.get === 'function' &&\n      typeof cookies?.get === 'function'\n    );\n  } catch {\n    return false;\n  }\n}\n\nexport function isRequestWebAPI(val: unknown): val is Request {\n  try {\n    const { headers } = (val || {}) as Request;\n    return typeof headers?.get === 'function';\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,SAAS,iBAAiB;;;AAKnB,SAAS,8BAA8B,GAAA,EAAkB,GAAA,EAAwC;IAEtG,OAAO,OAAO,MAAM,GAAA,CAAI,GAAG,CAAA,GAAI,KAAA;AACjC;AAEO,SAAS,sBACd,GAAA,EACA,GAAA,EAC2B;IAC3B,OAAO,8BAA8B,+QAAK,YAAA,CAAU,UAAA,CAAW,GAAG,CAAC,KAAK,UAAU,+QAAK,YAAA,CAAU,OAAA,CAAQ,GAAG,CAAC;AAC/G;AAEO,SAAS,UAAU,GAAA,EAAkB,IAAA,EAAyC;IAjBrF,IAAA,IAAA;IAkBE,IAAI,cAAc,GAAG,KAAK,gBAAgB,GAAG,GAAG;QAC9C,OAAO,IAAI,OAAA,CAAQ,GAAA,CAAI,IAAI;IAC7B;IAKA,OAAO,IAAI,OAAA,CAAQ,IAAI,CAAA,IAAK,IAAI,OAAA,CAAQ,KAAK,WAAA,CAAY,CAAC,CAAA,IAAA,CAAA,CAAM,KAAA,CAAA,KAAA,IAAI,MAAA,KAAJ,OAAA,KAAA,IAAA,GAAoB,YAAA,KAApB,OAAA,KAAA,IAAA,GAAkC,SAAA,CAAU,KAAA;AAC9G;AAEO,SAAS,sBAAsB,GAAA,EAA2B;IAC/D,OAAO,QAAQ,sBAAsB,KAAK,YAAY,CAAC;AACzD;AAEO,SAAS,cAAc,GAAA,EAAkC;IAC9D,IAAI;QACF,MAAM,EAAE,OAAA,EAAS,OAAA,EAAS,OAAA,CAAQ,CAAA,GAAK,OAAO,CAAC;QAC/C,OACE,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ,cACxB,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,YAAA,CAAa,GAAA,MAAQ,cACrC,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ;IAE5B,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,GAAA,EAA8B;IAC5D,IAAI;QACF,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAK,OAAO,CAAC;QAC7B,OAAO,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ;IACjC,EAAA,OAAQ;QACN,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/constants.ts"], "sourcesContent": ["const Headers = {\n  NextRewrite: 'x-middleware-rewrite',\n  NextResume: 'x-middleware-next',\n  NextRedirect: 'Location',\n  // Used by next to identify internal navigation for app router\n  NextUrl: 'next-url',\n  NextAction: 'next-action',\n  // Used by next to identify internal navigation for pages router\n  NextjsData: 'x-nextjs-data',\n} as const;\n\nexport const constants = {\n  Headers,\n} as const;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,UAAU;IACd,aAAa;IACb,YAAY;IACZ,cAAc;IAAA,8DAAA;IAEd,SAAS;IACT,YAAY;IAAA,gEAAA;IAEZ,YAAY;AACd;AAEO,MAAM,YAAY;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/feature-flags.ts"], "sourcesContent": ["import { isDevelopmentEnvironment } from '@clerk/shared/utils';\n\nimport { KEYLESS_DISABLED } from '../server/constants';\nimport { isNextWithUnstableServerActions } from './sdk-versions';\n\nconst canUseKeyless =\n  !isNextWithUnstableServerActions &&\n  // Next.js will inline the value of 'development' or 'production' on the client bundle, so this is client-safe.\n  isDevelopmentEnvironment() &&\n  !KEYLESS_DISABLED;\n\nexport { canUseKeyless };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gCAAgC;AAEzC,SAAS,wBAAwB;AACjC,SAAS,uCAAuC;;;;;AAEhD,MAAM,gBACJ,0RAAC,kCAAA,IAAA,+GAAA;gRAED,2BAAA,CAAyB,MACzB,qRAAC,mBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3499, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/dist/esm/vendor/crypto-es.js"], "sourcesContent": ["var kt=Object.defineProperty;var bt=(c,t,s)=>t in c?kt(c,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):c[t]=s;var it=(c,t,s)=>bt(c,typeof t!=\"symbol\"?t+\"\":t,s);var lt,ht,dt,pt,xt,_t,at=((lt=typeof globalThis!=\"undefined\"?globalThis:void 0)==null?void 0:lt.crypto)||((ht=typeof global!=\"undefined\"?global:void 0)==null?void 0:ht.crypto)||((dt=typeof window!=\"undefined\"?window:void 0)==null?void 0:dt.crypto)||((pt=typeof self!=\"undefined\"?self:void 0)==null?void 0:pt.crypto)||((_t=(xt=typeof frames!=\"undefined\"?frames:void 0)==null?void 0:xt[0])==null?void 0:_t.crypto),Z;at?Z=c=>{let t=[];for(let s=0,e;s<c;s+=4)t.push(at.getRandomValues(new Uint32Array(1))[0]);return new u(t,c)}:Z=c=>{let t=[],s=e=>{let r=e,o=987654321,n=4294967295;return()=>{o=36969*(o&65535)+(o>>16)&n,r=18e3*(r&65535)+(r>>16)&n;let h=(o<<16)+r&n;return h/=4294967296,h+=.5,h*(Math.random()>.5?1:-1)}};for(let e=0,r;e<c;e+=4){let o=s((r||Math.random())*4294967296);r=o()*987654071,t.push(o()*4294967296|0)}return new u(t,c)};var m=class{static create(...t){return new this(...t)}mixIn(t){return Object.assign(this,t)}clone(){let t=new this.constructor;return Object.assign(t,this),t}},u=class extends m{constructor(t=[],s=t.length*4){super();let e=t;if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){let r=e.byteLength,o=[];for(let n=0;n<r;n+=1)o[n>>>2]|=e[n]<<24-n%4*8;this.words=o,this.sigBytes=r}else this.words=t,this.sigBytes=s}toString(t=Mt){return t.stringify(this)}concat(t){let s=this.words,e=t.words,r=this.sigBytes,o=t.sigBytes;if(this.clamp(),r%4)for(let n=0;n<o;n+=1){let h=e[n>>>2]>>>24-n%4*8&255;s[r+n>>>2]|=h<<24-(r+n)%4*8}else for(let n=0;n<o;n+=4)s[r+n>>>2]=e[n>>>2];return this.sigBytes+=o,this}clamp(){let{words:t,sigBytes:s}=this;t[s>>>2]&=4294967295<<32-s%4*8,t.length=Math.ceil(s/4)}clone(){let t=super.clone.call(this);return t.words=this.words.slice(0),t}};it(u,\"random\",Z);var Mt={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push((o>>>4).toString(16)),e.push((o&15).toString(16))}return e.join(\"\")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=2)s[e>>>3]|=parseInt(c.substr(e,2),16)<<24-e%8*4;return new u(s,t/2)}},ft={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push(String.fromCharCode(o))}return e.join(\"\")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=1)s[e>>>2]|=(c.charCodeAt(e)&255)<<24-e%4*8;return new u(s,t)}},X={stringify(c){try{return decodeURIComponent(escape(ft.stringify(c)))}catch{throw new Error(\"Malformed UTF-8 data\")}},parse(c){return ft.parse(unescape(encodeURIComponent(c)))}},N=class extends m{constructor(){super(),this._minBufferSize=0}reset(){this._data=new u,this._nDataBytes=0}_append(t){let s=t;typeof s==\"string\"&&(s=X.parse(s)),this._data.concat(s),this._nDataBytes+=s.sigBytes}_process(t){let s,{_data:e,blockSize:r}=this,o=e.words,n=e.sigBytes,h=r*4,x=n/h;t?x=Math.ceil(x):x=Math.max((x|0)-this._minBufferSize,0);let p=x*r,_=Math.min(p*4,n);if(p){for(let y=0;y<p;y+=r)this._doProcessBlock(o,y);s=o.splice(0,p),e.sigBytes-=_}return new u(s,_)}clone(){let t=super.clone.call(this);return t._data=this._data.clone(),t}},H=class extends N{constructor(t){super(),this.blockSize=512/32,this.cfg=Object.assign(new m,t),this.reset()}static _createHelper(t){return(s,e)=>new t(e).finalize(s)}static _createHmacHelper(t){return(s,e)=>new $(t,e).finalize(s)}reset(){super.reset.call(this),this._doReset()}update(t){return this._append(t),this._process(),this}finalize(t){return t&&this._append(t),this._doFinalize()}},$=class extends m{constructor(t,s){super();let e=new t;this._hasher=e;let r=s;typeof r==\"string\"&&(r=X.parse(r));let o=e.blockSize,n=o*4;r.sigBytes>n&&(r=e.finalize(s)),r.clamp();let h=r.clone();this._oKey=h;let x=r.clone();this._iKey=x;let p=h.words,_=x.words;for(let y=0;y<o;y+=1)p[y]^=1549556828,_[y]^=909522486;h.sigBytes=n,x.sigBytes=n,this.reset()}reset(){let t=this._hasher;t.reset(),t.update(this._iKey)}update(t){return this._hasher.update(t),this}finalize(t){let s=this._hasher,e=s.finalize(t);return s.reset(),s.finalize(this._oKey.clone().concat(e))}};var zt=(c,t,s)=>{let e=[],r=0;for(let o=0;o<t;o+=1)if(o%4){let n=s[c.charCodeAt(o-1)]<<o%4*2,h=s[c.charCodeAt(o)]>>>6-o%4*2,x=n|h;e[r>>>2]|=x<<24-r%4*8,r+=1}return u.create(e,r)},tt={stringify(c){let{words:t,sigBytes:s}=c,e=this._map;c.clamp();let r=[];for(let n=0;n<s;n+=3){let h=t[n>>>2]>>>24-n%4*8&255,x=t[n+1>>>2]>>>24-(n+1)%4*8&255,p=t[n+2>>>2]>>>24-(n+2)%4*8&255,_=h<<16|x<<8|p;for(let y=0;y<4&&n+y*.75<s;y+=1)r.push(e.charAt(_>>>6*(3-y)&63))}let o=e.charAt(64);if(o)for(;r.length%4;)r.push(o);return r.join(\"\")},parse(c){let t=c.length,s=this._map,e=this._reverseMap;if(!e){this._reverseMap=[],e=this._reverseMap;for(let o=0;o<s.length;o+=1)e[s.charCodeAt(o)]=o}let r=s.charAt(64);if(r){let o=c.indexOf(r);o!==-1&&(t=o)}return zt(c,t,e)},_map:\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\"};var d=[];for(let c=0;c<64;c+=1)d[c]=Math.abs(Math.sin(c+1))*4294967296|0;var w=(c,t,s,e,r,o,n)=>{let h=c+(t&s|~t&e)+r+n;return(h<<o|h>>>32-o)+t},B=(c,t,s,e,r,o,n)=>{let h=c+(t&e|s&~e)+r+n;return(h<<o|h>>>32-o)+t},k=(c,t,s,e,r,o,n)=>{let h=c+(t^s^e)+r+n;return(h<<o|h>>>32-o)+t},b=(c,t,s,e,r,o,n)=>{let h=c+(s^(t|~e))+r+n;return(h<<o|h>>>32-o)+t},L=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878])}_doProcessBlock(t,s){let e=t;for(let Y=0;Y<16;Y+=1){let ct=s+Y,G=t[ct];e[ct]=(G<<8|G>>>24)&16711935|(G<<24|G>>>8)&4278255360}let r=this._hash.words,o=e[s+0],n=e[s+1],h=e[s+2],x=e[s+3],p=e[s+4],_=e[s+5],y=e[s+6],M=e[s+7],z=e[s+8],v=e[s+9],g=e[s+10],O=e[s+11],S=e[s+12],P=e[s+13],I=e[s+14],W=e[s+15],i=r[0],a=r[1],f=r[2],l=r[3];i=w(i,a,f,l,o,7,d[0]),l=w(l,i,a,f,n,12,d[1]),f=w(f,l,i,a,h,17,d[2]),a=w(a,f,l,i,x,22,d[3]),i=w(i,a,f,l,p,7,d[4]),l=w(l,i,a,f,_,12,d[5]),f=w(f,l,i,a,y,17,d[6]),a=w(a,f,l,i,M,22,d[7]),i=w(i,a,f,l,z,7,d[8]),l=w(l,i,a,f,v,12,d[9]),f=w(f,l,i,a,g,17,d[10]),a=w(a,f,l,i,O,22,d[11]),i=w(i,a,f,l,S,7,d[12]),l=w(l,i,a,f,P,12,d[13]),f=w(f,l,i,a,I,17,d[14]),a=w(a,f,l,i,W,22,d[15]),i=B(i,a,f,l,n,5,d[16]),l=B(l,i,a,f,y,9,d[17]),f=B(f,l,i,a,O,14,d[18]),a=B(a,f,l,i,o,20,d[19]),i=B(i,a,f,l,_,5,d[20]),l=B(l,i,a,f,g,9,d[21]),f=B(f,l,i,a,W,14,d[22]),a=B(a,f,l,i,p,20,d[23]),i=B(i,a,f,l,v,5,d[24]),l=B(l,i,a,f,I,9,d[25]),f=B(f,l,i,a,x,14,d[26]),a=B(a,f,l,i,z,20,d[27]),i=B(i,a,f,l,P,5,d[28]),l=B(l,i,a,f,h,9,d[29]),f=B(f,l,i,a,M,14,d[30]),a=B(a,f,l,i,S,20,d[31]),i=k(i,a,f,l,_,4,d[32]),l=k(l,i,a,f,z,11,d[33]),f=k(f,l,i,a,O,16,d[34]),a=k(a,f,l,i,I,23,d[35]),i=k(i,a,f,l,n,4,d[36]),l=k(l,i,a,f,p,11,d[37]),f=k(f,l,i,a,M,16,d[38]),a=k(a,f,l,i,g,23,d[39]),i=k(i,a,f,l,P,4,d[40]),l=k(l,i,a,f,o,11,d[41]),f=k(f,l,i,a,x,16,d[42]),a=k(a,f,l,i,y,23,d[43]),i=k(i,a,f,l,v,4,d[44]),l=k(l,i,a,f,S,11,d[45]),f=k(f,l,i,a,W,16,d[46]),a=k(a,f,l,i,h,23,d[47]),i=b(i,a,f,l,o,6,d[48]),l=b(l,i,a,f,M,10,d[49]),f=b(f,l,i,a,I,15,d[50]),a=b(a,f,l,i,_,21,d[51]),i=b(i,a,f,l,S,6,d[52]),l=b(l,i,a,f,x,10,d[53]),f=b(f,l,i,a,g,15,d[54]),a=b(a,f,l,i,n,21,d[55]),i=b(i,a,f,l,z,6,d[56]),l=b(l,i,a,f,W,10,d[57]),f=b(f,l,i,a,y,15,d[58]),a=b(a,f,l,i,P,21,d[59]),i=b(i,a,f,l,p,6,d[60]),l=b(l,i,a,f,O,10,d[61]),f=b(f,l,i,a,h,15,d[62]),a=b(a,f,l,i,v,21,d[63]),r[0]=r[0]+i|0,r[1]=r[1]+a|0,r[2]=r[2]+f|0,r[3]=r[3]+l|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;s[r>>>5]|=128<<24-r%32;let o=Math.floor(e/4294967296),n=e;s[(r+64>>>9<<4)+15]=(o<<8|o>>>24)&16711935|(o<<24|o>>>8)&4278255360,s[(r+64>>>9<<4)+14]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,t.sigBytes=(s.length+1)*4,this._process();let h=this._hash,x=h.words;for(let p=0;p<4;p+=1){let _=x[p];x[p]=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360}return h}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},St=H._createHelper(L),Pt=H._createHmacHelper(L);var T=class extends m{constructor(t){super(),this.cfg=Object.assign(new m,{keySize:128/32,hasher:L,iterations:1},t)}compute(t,s){let e,{cfg:r}=this,o=r.hasher.create(),n=u.create(),h=n.words,{keySize:x,iterations:p}=r;for(;h.length<x;){e&&o.update(e),e=o.update(t).finalize(s),o.reset();for(let _=1;_<p;_+=1)e=o.finalize(e),o.reset();n.concat(e)}return n.sigBytes=x*4,n}};var C=class extends N{constructor(t,s,e){super(),this.cfg=Object.assign(new m,e),this._xformMode=t,this._key=s,this.reset()}static createEncryptor(t,s){return this.create(this._ENC_XFORM_MODE,t,s)}static createDecryptor(t,s){return this.create(this._DEC_XFORM_MODE,t,s)}static _createHelper(t){let s=e=>typeof e==\"string\"?q:E;return{encrypt(e,r,o){return s(r).encrypt(t,e,r,o)},decrypt(e,r,o){return s(r).decrypt(t,e,r,o)}}}reset(){super.reset.call(this),this._doReset()}process(t){return this._append(t),this._process()}finalize(t){return t&&this._append(t),this._doFinalize()}};C._ENC_XFORM_MODE=1;C._DEC_XFORM_MODE=2;C.keySize=128/32;C.ivSize=128/32;var et=class extends m{constructor(t,s){super(),this._cipher=t,this._iv=s}static createEncryptor(t,s){return this.Encryptor.create(t,s)}static createDecryptor(t,s){return this.Decryptor.create(t,s)}};function yt(c,t,s){let e=c,r,o=this._iv;o?(r=o,this._iv=void 0):r=this._prevBlock;for(let n=0;n<s;n+=1)e[t+n]^=r[n]}var j=class extends et{};j.Encryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s;yt.call(this,c,t,e),s.encryptBlock(c,t),this._prevBlock=c.slice(t,t+e)}};j.Decryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s,r=c.slice(t,t+e);s.decryptBlock(c,t),yt.call(this,c,t,e),this._prevBlock=r}};var vt={pad(c,t){let s=t*4,e=s-c.sigBytes%s,r=e<<24|e<<16|e<<8|e,o=[];for(let h=0;h<e;h+=4)o.push(r);let n=u.create(o,e);c.concat(n)},unpad(c){let t=c,s=t.words[t.sigBytes-1>>>2]&255;t.sigBytes-=s}},U=class extends C{constructor(t,s,e){super(t,s,Object.assign({mode:j,padding:vt},e)),this.blockSize=128/32}reset(){let t;super.reset.call(this);let{cfg:s}=this,{iv:e,mode:r}=s;this._xformMode===this.constructor._ENC_XFORM_MODE?t=r.createEncryptor:(t=r.createDecryptor,this._minBufferSize=1),this._mode=t.call(r,this,e&&e.words),this._mode.__creator=t}_doProcessBlock(t,s){this._mode.processBlock(t,s)}_doFinalize(){let t,{padding:s}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(s.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),s.unpad(t)),t}},V=class extends m{constructor(t){super(),this.mixIn(t)}toString(t){return(t||this.formatter).stringify(this)}},Rt={stringify(c){let t,{ciphertext:s,salt:e}=c;return e?t=u.create([1398893684,1701076831]).concat(e).concat(s):t=s,t.toString(tt)},parse(c){let t,s=tt.parse(c),e=s.words;return e[0]===1398893684&&e[1]===1701076831&&(t=u.create(e.slice(2,4)),e.splice(0,4),s.sigBytes-=16),V.create({ciphertext:s,salt:t})}},E=class extends m{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=t.createEncryptor(e,o),h=n.finalize(s),x=n.cfg;return V.create({ciphertext:h,key:e,iv:x.iv,algorithm:t,mode:x.mode,padding:x.padding,blockSize:n.blockSize,formatter:o.format})}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);return o=this._parse(o,n.format),t.createDecryptor(e,n).finalize(o.ciphertext)}static _parse(t,s){return typeof t==\"string\"?s.parse(t,this):t}};E.cfg=Object.assign(new m,{format:Rt});var Ft={execute(c,t,s,e,r){let o=e;o||(o=u.random(64/8));let n;r?n=T.create({keySize:t+s,hasher:r}).compute(c,o):n=T.create({keySize:t+s}).compute(c,o);let h=u.create(n.words.slice(t),s*4);return n.sigBytes=t*4,V.create({key:n,iv:h,salt:o})}},q=class extends E{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=o.kdf.execute(e,t.keySize,t.ivSize,o.salt,o.hasher);o.iv=n.iv;let h=E.encrypt.call(this,t,s,n.key,o);return h.mixIn(n),h}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);o=this._parse(o,n.format);let h=n.kdf.execute(e,t.keySize,t.ivSize,o.salt,n.hasher);return n.iv=h.iv,E.decrypt.call(this,t,o,h.key,n)}};q.cfg=Object.assign(E.cfg,{kdf:Ft});var R=[],ut=[],gt=[],mt=[],wt=[],Bt=[],st=[],rt=[],ot=[],nt=[],A=[];for(let c=0;c<256;c+=1)c<128?A[c]=c<<1:A[c]=c<<1^283;var F=0,D=0;for(let c=0;c<256;c+=1){let t=D^D<<1^D<<2^D<<3^D<<4;t=t>>>8^t&255^99,R[F]=t,ut[t]=F;let s=A[F],e=A[s],r=A[e],o=A[t]*257^t*16843008;gt[F]=o<<24|o>>>8,mt[F]=o<<16|o>>>16,wt[F]=o<<8|o>>>24,Bt[F]=o,o=r*16843009^e*65537^s*257^F*16843008,st[t]=o<<24|o>>>8,rt[t]=o<<16|o>>>16,ot[t]=o<<8|o>>>24,nt[t]=o,F?(F=s^A[A[A[r^s]]],D^=A[A[D]]):(D=1,F=D)}var At=[0,1,2,4,8,16,32,64,128,27,54],J=class extends U{_doReset(){let t;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let s=this._keyPriorReset,e=s.words,r=s.sigBytes/4;this._nRounds=r+6;let n=(this._nRounds+1)*4;this._keySchedule=[];let h=this._keySchedule;for(let p=0;p<n;p+=1)p<r?h[p]=e[p]:(t=h[p-1],p%r?r>6&&p%r===4&&(t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255]):(t=t<<8|t>>>24,t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255],t^=At[p/r|0]<<24),h[p]=h[p-r]^t);this._invKeySchedule=[];let x=this._invKeySchedule;for(let p=0;p<n;p+=1){let _=n-p;p%4?t=h[_]:t=h[_-4],p<4||_<=4?x[p]=t:x[p]=st[R[t>>>24]]^rt[R[t>>>16&255]]^ot[R[t>>>8&255]]^nt[R[t&255]]}}encryptBlock(t,s){this._doCryptBlock(t,s,this._keySchedule,gt,mt,wt,Bt,R)}decryptBlock(t,s){let e=t,r=e[s+1];e[s+1]=e[s+3],e[s+3]=r,this._doCryptBlock(e,s,this._invKeySchedule,st,rt,ot,nt,ut),r=e[s+1],e[s+1]=e[s+3],e[s+3]=r}_doCryptBlock(t,s,e,r,o,n,h,x){let p=t,_=this._nRounds,y=p[s]^e[0],M=p[s+1]^e[1],z=p[s+2]^e[2],v=p[s+3]^e[3],g=4;for(let W=1;W<_;W+=1){let i=r[y>>>24]^o[M>>>16&255]^n[z>>>8&255]^h[v&255]^e[g];g+=1;let a=r[M>>>24]^o[z>>>16&255]^n[v>>>8&255]^h[y&255]^e[g];g+=1;let f=r[z>>>24]^o[v>>>16&255]^n[y>>>8&255]^h[M&255]^e[g];g+=1;let l=r[v>>>24]^o[y>>>16&255]^n[M>>>8&255]^h[z&255]^e[g];g+=1,y=i,M=a,z=f,v=l}let O=(x[y>>>24]<<24|x[M>>>16&255]<<16|x[z>>>8&255]<<8|x[v&255])^e[g];g+=1;let S=(x[M>>>24]<<24|x[z>>>16&255]<<16|x[v>>>8&255]<<8|x[y&255])^e[g];g+=1;let P=(x[z>>>24]<<24|x[v>>>16&255]<<16|x[y>>>8&255]<<8|x[M&255])^e[g];g+=1;let I=(x[v>>>24]<<24|x[y>>>16&255]<<16|x[M>>>8&255]<<8|x[z&255])^e[g];g+=1,p[s]=O,p[s+1]=S,p[s+2]=P,p[s+3]=I}};J.keySize=256/32;var Ht=U._createHelper(J);var K=[],Q=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878,3285377520])}_doProcessBlock(t,s){let e=this._hash.words,r=e[0],o=e[1],n=e[2],h=e[3],x=e[4];for(let p=0;p<80;p+=1){if(p<16)K[p]=t[s+p]|0;else{let y=K[p-3]^K[p-8]^K[p-14]^K[p-16];K[p]=y<<1|y>>>31}let _=(r<<5|r>>>27)+x+K[p];p<20?_+=(o&n|~o&h)+1518500249:p<40?_+=(o^n^h)+1859775393:p<60?_+=(o&n|o&h|n&h)-1894007588:_+=(o^n^h)-899497514,x=h,h=n,n=o<<30|o>>>2,o=r,r=_}e[0]=e[0]+r|0,e[1]=e[1]+o|0,e[2]=e[2]+n|0,e[3]=e[3]+h|0,e[4]=e[4]+x|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;return s[r>>>5]|=128<<24-r%32,s[(r+64>>>9<<4)+14]=Math.floor(e/4294967296),s[(r+64>>>9<<4)+15]=e,t.sigBytes=s.length*4,this._process(),this._hash}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},Xt=H._createHelper(Q),Dt=H._createHmacHelper(Q);export{Ht as AES,Dt as HmacSHA1,X as Utf8};\n"], "names": [], "mappings": ";;;;;AAAA,IAAI,KAAG,OAAO,cAAc;AAAC,IAAI,KAAG,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,GAAG,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,KAAG,CAAC,GAAE,GAAE,IAAI,GAAG,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE;AAAG,IAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAG,CAAC,CAAC,KAAG,OAAO,cAAY,cAAY,aAAW,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,OAAO,UAAQ,cAAY,SAAO,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,6EAAkC,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,OAAO,QAAM,cAAY,OAAK,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,CAAC,KAAG,OAAO,UAAQ,cAAY,SAAO,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,GAAE;AAAE,KAAG,IAAE,CAAA;IAAI,IAAI,IAAE,EAAE;IAAC,IAAI,IAAI,IAAE,GAAE,GAAE,IAAE,GAAE,KAAG,EAAE,EAAE,IAAI,CAAC,GAAG,eAAe,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;IAAE,OAAO,IAAI,EAAE,GAAE;AAAE,IAAE,IAAE,CAAA;IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,CAAA;QAAI,IAAI,IAAE,GAAE,IAAE,WAAU,IAAE;QAAW,OAAM;YAAK,IAAE,QAAM,CAAC,IAAE,KAAK,IAAE,CAAC,KAAG,EAAE,IAAE,GAAE,IAAE,OAAK,CAAC,IAAE,KAAK,IAAE,CAAC,KAAG,EAAE,IAAE;YAAE,IAAI,IAAE,CAAC,KAAG,EAAE,IAAE,IAAE;YAAE,OAAO,KAAG,YAAW,KAAG,IAAG,IAAE,CAAC,KAAK,MAAM,KAAG,KAAG,IAAE,CAAC,CAAC;QAAC;IAAC;IAAE,IAAI,IAAI,IAAE,GAAE,GAAE,IAAE,GAAE,KAAG,EAAE;QAAC,IAAI,IAAE,EAAE,CAAC,KAAG,KAAK,MAAM,EAAE,IAAE;QAAY,IAAE,MAAI,WAAU,EAAE,IAAI,CAAC,MAAI,aAAW;IAAE;IAAC,OAAO,IAAI,EAAE,GAAE;AAAE;AAAE,IAAI,IAAE;IAAM,OAAO,OAAO,GAAG,CAAC,EAAC;QAAC,OAAO,IAAI,IAAI,IAAI;IAAE;IAAC,MAAM,CAAC,EAAC;QAAC,OAAO,OAAO,MAAM,CAAC,IAAI,EAAC;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,IAAI,IAAI,CAAC,WAAW;QAAC,OAAO,OAAO,MAAM,CAAC,GAAE,IAAI,GAAE;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,IAAE,EAAE,EAAC,IAAE,EAAE,MAAM,GAAC,CAAC,CAAC;QAAC,KAAK;QAAG,IAAI,IAAE;QAAE,IAAG,aAAa,eAAa,CAAC,IAAE,IAAI,WAAW,EAAE,GAAE,CAAC,aAAa,aAAW,aAAa,qBAAmB,aAAa,cAAY,aAAa,eAAa,aAAa,cAAY,aAAa,eAAa,aAAa,gBAAc,aAAa,YAAY,KAAG,CAAC,IAAE,IAAI,WAAW,EAAE,MAAM,EAAC,EAAE,UAAU,EAAC,EAAE,UAAU,CAAC,GAAE,aAAa,YAAW;YAAC,IAAI,IAAE,EAAE,UAAU,EAAC,IAAE,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,MAAI,EAAE,IAAE,CAAC,CAAC,EAAE,IAAE,KAAG,IAAE,IAAE;YAAE,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,QAAQ,GAAC;QAAC,OAAM,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,QAAQ,GAAC;IAAC;IAAC,SAAS,IAAE,EAAE,EAAC;QAAC,OAAO,EAAE,SAAS,CAAC,IAAI;IAAC;IAAC,OAAO,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,IAAI,CAAC,QAAQ,EAAC,IAAE,EAAE,QAAQ;QAAC,IAAG,IAAI,CAAC,KAAK,IAAG,IAAE,GAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE;YAAI,CAAC,CAAC,IAAE,MAAI,EAAE,IAAE,KAAG,KAAG,CAAC,IAAE,CAAC,IAAE,IAAE;QAAC;aAAM,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,IAAE,MAAI,EAAE,GAAC,CAAC,CAAC,MAAI,EAAE;QAAC,OAAO,IAAI,CAAC,QAAQ,IAAE,GAAE,IAAI;IAAA;IAAC,QAAO;QAAC,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,IAAI;QAAC,CAAC,CAAC,MAAI,EAAE,IAAE,cAAY,KAAG,IAAE,IAAE,GAAE,EAAE,MAAM,GAAC,KAAK,IAAI,CAAC,IAAE;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAG;IAAC;AAAC;AAAE,GAAG,GAAE,UAAS;AAAG,IAAI,KAAG;IAAC,WAAU,CAAC;QAAE,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,GAAE,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE;YAAI,EAAE,IAAI,CAAC,CAAC,MAAI,CAAC,EAAE,QAAQ,CAAC,MAAK,EAAE,IAAI,CAAC,CAAC,IAAE,EAAE,EAAE,QAAQ,CAAC;QAAI;QAAC,OAAO,EAAE,IAAI,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,MAAI,EAAE,IAAE,SAAS,EAAE,MAAM,CAAC,GAAE,IAAG,OAAK,KAAG,IAAE,IAAE;QAAE,OAAO,IAAI,EAAE,GAAE,IAAE;IAAE;AAAC,GAAE,KAAG;IAAC,WAAU,CAAC;QAAE,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,GAAE,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE;YAAI,EAAE,IAAI,CAAC,OAAO,YAAY,CAAC;QAAG;QAAC,OAAO,EAAE,IAAI,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,MAAI,EAAE,IAAE,CAAC,EAAE,UAAU,CAAC,KAAG,GAAG,KAAG,KAAG,IAAE,IAAE;QAAE,OAAO,IAAI,EAAE,GAAE;IAAE;AAAC,GAAE,IAAE;IAAC,WAAU,CAAC;QAAE,IAAG;YAAC,OAAO,mBAAmB,OAAO,GAAG,SAAS,CAAC;QAAI,EAAC,OAAK;YAAC,MAAM,IAAI,MAAM;QAAuB;IAAC;IAAE,OAAM,CAAC;QAAE,OAAO,GAAG,KAAK,CAAC,SAAS,mBAAmB;IAAI;AAAC,GAAE,IAAE,cAAc;IAAE,aAAa;QAAC,KAAK,IAAG,IAAI,CAAC,cAAc,GAAC;IAAC;IAAC,QAAO;QAAC,IAAI,CAAC,KAAK,GAAC,IAAI,GAAE,IAAI,CAAC,WAAW,GAAC;IAAC;IAAC,QAAQ,CAAC,EAAC;QAAC,IAAI,IAAE;QAAE,OAAO,KAAG,YAAU,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAG,IAAI,CAAC,WAAW,IAAE,EAAE,QAAQ;IAAA;IAAC,SAAS,CAAC,EAAC;QAAC,IAAI,GAAE,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,GAAC,IAAI,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,QAAQ,EAAC,IAAE,IAAE,GAAE,IAAE,IAAE;QAAE,IAAE,IAAE,KAAK,IAAI,CAAC,KAAG,IAAE,KAAK,GAAG,CAAC,CAAC,IAAE,CAAC,IAAE,IAAI,CAAC,cAAc,EAAC;QAAG,IAAI,IAAE,IAAE,GAAE,IAAE,KAAK,GAAG,CAAC,IAAE,GAAE;QAAG,IAAG,GAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE;YAAG,IAAE,EAAE,MAAM,CAAC,GAAE,IAAG,EAAE,QAAQ,IAAE;QAAC;QAAC,OAAO,IAAI,EAAE,GAAE;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAG;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,SAAS,GAAC,MAAI,IAAG,IAAI,CAAC,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE,IAAG,IAAI,CAAC,KAAK;IAAE;IAAC,OAAO,cAAc,CAAC,EAAC;QAAC,OAAM,CAAC,GAAE,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC;IAAE;IAAC,OAAO,kBAAkB,CAAC,EAAC;QAAC,OAAM,CAAC,GAAE,IAAI,IAAI,EAAE,GAAE,GAAG,QAAQ,CAAC;IAAE;IAAC,QAAO;QAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAE,IAAI,CAAC,QAAQ;IAAE;IAAC,OAAO,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,QAAQ,IAAG,IAAI;IAAA;IAAC,SAAS,CAAC,EAAC;QAAC,OAAO,KAAG,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,WAAW;IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK;QAAG,IAAI,IAAE,IAAI;QAAE,IAAI,CAAC,OAAO,GAAC;QAAE,IAAI,IAAE;QAAE,OAAO,KAAG,YAAU,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE;QAAE,IAAI,IAAE,EAAE,SAAS,EAAC,IAAE,IAAE;QAAE,EAAE,QAAQ,GAAC,KAAG,CAAC,IAAE,EAAE,QAAQ,CAAC,EAAE,GAAE,EAAE,KAAK;QAAG,IAAI,IAAE,EAAE,KAAK;QAAG,IAAI,CAAC,KAAK,GAAC;QAAE,IAAI,IAAE,EAAE,KAAK;QAAG,IAAI,CAAC,KAAK,GAAC;QAAE,IAAI,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,KAAK;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,EAAE,IAAE,YAAW,CAAC,CAAC,EAAE,IAAE;QAAU,EAAE,QAAQ,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAI,CAAC,KAAK;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO;QAAC,EAAE,KAAK,IAAG,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK;IAAC;IAAC,OAAO,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAG,IAAI;IAAA;IAAC,SAAS,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO,EAAC,IAAE,EAAE,QAAQ,CAAC;QAAG,OAAO,EAAE,KAAK,IAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;IAAG;AAAC;AAAE,IAAI,KAAG,CAAC,GAAE,GAAE;IAAK,IAAI,IAAE,EAAE,EAAC,IAAE;IAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAG,IAAE,GAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,UAAU,CAAC,IAAE,GAAG,IAAE,IAAE,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,KAAG,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE;QAAE,CAAC,CAAC,MAAI,EAAE,IAAE,KAAG,KAAG,IAAE,IAAE,GAAE,KAAG;IAAC;IAAC,OAAO,EAAE,MAAM,CAAC,GAAE;AAAE,GAAE,KAAG;IAAC,WAAU,CAAC;QAAE,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,GAAE,IAAE,IAAI,CAAC,IAAI;QAAC,EAAE,KAAK;QAAG,IAAI,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE,KAAI,IAAE,CAAC,CAAC,IAAE,MAAI,EAAE,KAAG,KAAG,CAAC,IAAE,CAAC,IAAE,IAAE,IAAE,KAAI,IAAE,CAAC,CAAC,IAAE,MAAI,EAAE,KAAG,KAAG,CAAC,IAAE,CAAC,IAAE,IAAE,IAAE,KAAI,IAAE,KAAG,KAAG,KAAG,IAAE;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,KAAG,IAAE,IAAE,MAAI,GAAE,KAAG,EAAE,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,MAAI,IAAE,CAAC,IAAE,CAAC,IAAE;QAAI;QAAC,IAAI,IAAE,EAAE,MAAM,CAAC;QAAI,IAAG,GAAE,MAAK,EAAE,MAAM,GAAC,GAAG,EAAE,IAAI,CAAC;QAAG,OAAO,EAAE,IAAI,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,IAAI,CAAC,IAAI,EAAC,IAAE,IAAI,CAAC,WAAW;QAAC,IAAG,CAAC,GAAE;YAAC,IAAI,CAAC,WAAW,GAAC,EAAE,EAAC,IAAE,IAAI,CAAC,WAAW;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,GAAC;QAAC;QAAC,IAAI,IAAE,EAAE,MAAM,CAAC;QAAI,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,OAAO,CAAC;YAAG,MAAI,CAAC,KAAG,CAAC,IAAE,CAAC;QAAC;QAAC,OAAO,GAAG,GAAE,GAAE;IAAE;IAAE,MAAK;AAAmE;AAAE,IAAI,IAAE,EAAE;AAAC,IAAI,IAAI,IAAE,GAAE,IAAE,IAAG,KAAG,EAAE,CAAC,CAAC,EAAE,GAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAE,MAAI,aAAW;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,IAAE,IAAE,CAAC,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,IAAE,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,CAAC,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,WAAU;QAAC,IAAI,CAAC,KAAK,GAAC,IAAI,EAAE;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;IAAC,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE;QAAE,IAAI,IAAI,IAAE,GAAE,IAAE,IAAG,KAAG,EAAE;YAAC,IAAI,KAAG,IAAE,GAAE,IAAE,CAAC,CAAC,GAAG;YAAC,CAAC,CAAC,GAAG,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE;QAAU;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;QAAC,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE;IAAC;IAAC,cAAa;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,IAAI,CAAC,WAAW,GAAC,GAAE,IAAE,EAAE,QAAQ,GAAC;QAAE,CAAC,CAAC,MAAI,EAAE,IAAE,OAAK,KAAG,IAAE;QAAG,IAAI,IAAE,KAAK,KAAK,CAAC,IAAE,aAAY,IAAE;QAAE,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE,YAAW,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE,YAAW,EAAE,QAAQ,GAAC,CAAC,EAAE,MAAM,GAAC,CAAC,IAAE,GAAE,IAAI,CAAC,QAAQ;QAAG,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,CAAC,CAAC,EAAE,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE;QAAU;QAAC,OAAO;IAAC;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAG;IAAC;AAAC,GAAE,KAAG,EAAE,aAAa,CAAC,IAAG,KAAG,EAAE,iBAAiB,CAAC;AAAG,IAAI,IAAE,cAAc;IAAE,YAAY,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE;YAAC,SAAQ,MAAI;YAAG,QAAO;YAAE,YAAW;QAAC,GAAE;IAAE;IAAC,QAAQ,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,GAAE,EAAC,KAAI,CAAC,EAAC,GAAC,IAAI,EAAC,IAAE,EAAE,MAAM,CAAC,MAAM,IAAG,IAAE,EAAE,MAAM,IAAG,IAAE,EAAE,KAAK,EAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC;QAAE,MAAK,EAAE,MAAM,GAAC,GAAG;YAAC,KAAG,EAAE,MAAM,CAAC,IAAG,IAAE,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAG,EAAE,KAAK;YAAG,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAE,EAAE,QAAQ,CAAC,IAAG,EAAE,KAAK;YAAG,EAAE,MAAM,CAAC;QAAE;QAAC,OAAO,EAAE,QAAQ,GAAC,IAAE,GAAE;IAAC;AAAC;AAAE,IAAI,IAAE,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE,IAAG,IAAI,CAAC,UAAU,GAAC,GAAE,IAAI,CAAC,IAAI,GAAC,GAAE,IAAI,CAAC,KAAK;IAAE;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAC,GAAE;IAAE;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAC,GAAE;IAAE;IAAC,OAAO,cAAc,CAAC,EAAC;QAAC,IAAI,IAAE,CAAA,IAAG,OAAO,KAAG,WAAS,IAAE;QAAE,OAAM;YAAC,SAAQ,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,GAAG,OAAO,CAAC,GAAE,GAAE,GAAE;YAAE;YAAE,SAAQ,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,GAAG,OAAO,CAAC,GAAE,GAAE,GAAE;YAAE;QAAC;IAAC;IAAC,QAAO;QAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAE,IAAI,CAAC,QAAQ;IAAE;IAAC,QAAQ,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,QAAQ;IAAE;IAAC,SAAS,CAAC,EAAC;QAAC,OAAO,KAAG,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,WAAW;IAAE;AAAC;AAAE,EAAE,eAAe,GAAC;AAAE,EAAE,eAAe,GAAC;AAAE,EAAE,OAAO,GAAC,MAAI;AAAG,EAAE,MAAM,GAAC,MAAI;AAAG,IAAI,KAAG,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,OAAO,GAAC,GAAE,IAAI,CAAC,GAAG,GAAC;IAAC;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAE;IAAE;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAE;IAAE;AAAC;AAAE,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,GAAE,GAAE,IAAE,IAAI,CAAC,GAAG;IAAC,IAAE,CAAC,IAAE,GAAE,IAAI,CAAC,GAAG,GAAC,KAAK,CAAC,IAAE,IAAE,IAAI,CAAC,UAAU;IAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,IAAE,EAAE,IAAE,CAAC,CAAC,EAAE;AAAA;AAAC,IAAI,IAAE,cAAc;AAAG;AAAE,EAAE,SAAS,GAAC,cAAc;IAAE,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO,EAAC,EAAC,WAAU,CAAC,EAAC,GAAC;QAAE,GAAG,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,IAAG,EAAE,YAAY,CAAC,GAAE,IAAG,IAAI,CAAC,UAAU,GAAC,EAAE,KAAK,CAAC,GAAE,IAAE;IAAE;AAAC;AAAE,EAAE,SAAS,GAAC,cAAc;IAAE,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO,EAAC,EAAC,WAAU,CAAC,EAAC,GAAC,GAAE,IAAE,EAAE,KAAK,CAAC,GAAE,IAAE;QAAG,EAAE,YAAY,CAAC,GAAE,IAAG,GAAG,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,IAAG,IAAI,CAAC,UAAU,GAAC;IAAC;AAAC;AAAE,IAAI,KAAG;IAAC,KAAI,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,IAAE,GAAE,IAAE,IAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,KAAG,KAAG,KAAG,KAAG,KAAG,IAAE,GAAE,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,EAAE,IAAI,CAAC;QAAG,IAAI,IAAE,EAAE,MAAM,CAAC,GAAE;QAAG,EAAE,MAAM,CAAC;IAAE;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAC,MAAI,EAAE,GAAC;QAAI,EAAE,QAAQ,IAAE;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK,CAAC,GAAE,GAAE,OAAO,MAAM,CAAC;YAAC,MAAK;YAAE,SAAQ;QAAE,GAAE,KAAI,IAAI,CAAC,SAAS,GAAC,MAAI;IAAE;IAAC,QAAO;QAAC,IAAI;QAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,IAAG,EAAC,KAAI,CAAC,EAAC,GAAC,IAAI,EAAC,EAAC,IAAG,CAAC,EAAC,MAAK,CAAC,EAAC,GAAC;QAAE,IAAI,CAAC,UAAU,KAAG,IAAI,CAAC,WAAW,CAAC,eAAe,GAAC,IAAE,EAAE,eAAe,GAAC,CAAC,IAAE,EAAE,eAAe,EAAC,IAAI,CAAC,cAAc,GAAC,CAAC,GAAE,IAAI,CAAC,KAAK,GAAC,EAAE,IAAI,CAAC,GAAE,IAAI,EAAC,KAAG,EAAE,KAAK,GAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAC;IAAC;IAAC,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAE;IAAE;IAAC,cAAa;QAAC,IAAI,GAAE,EAAC,SAAQ,CAAC,EAAC,GAAC,IAAI,CAAC,GAAG;QAAC,OAAO,IAAI,CAAC,UAAU,KAAG,IAAI,CAAC,WAAW,CAAC,eAAe,GAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,EAAC,IAAI,CAAC,SAAS,GAAE,IAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAG,EAAE,KAAK,CAAC,EAAE,GAAE;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,KAAK,CAAC;IAAE;IAAC,SAAS,CAAC,EAAC;QAAC,OAAM,CAAC,KAAG,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI;IAAC;AAAC,GAAE,KAAG;IAAC,WAAU,CAAC;QAAE,IAAI,GAAE,EAAC,YAAW,CAAC,EAAC,MAAK,CAAC,EAAC,GAAC;QAAE,OAAO,IAAE,IAAE,EAAE,MAAM,CAAC;YAAC;YAAW;SAAW,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,KAAG,IAAE,GAAE,EAAE,QAAQ,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,GAAE,IAAE,GAAG,KAAK,CAAC,IAAG,IAAE,EAAE,KAAK;QAAC,OAAO,CAAC,CAAC,EAAE,KAAG,cAAY,CAAC,CAAC,EAAE,KAAG,cAAY,CAAC,IAAE,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,GAAE,KAAI,EAAE,MAAM,CAAC,GAAE,IAAG,EAAE,QAAQ,IAAE,EAAE,GAAE,EAAE,MAAM,CAAC;YAAC,YAAW;YAAE,MAAK;QAAC;IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC,IAAG,IAAE,EAAE,eAAe,CAAC,GAAE,IAAG,IAAE,EAAE,QAAQ,CAAC,IAAG,IAAE,EAAE,GAAG;QAAC,OAAO,EAAE,MAAM,CAAC;YAAC,YAAW;YAAE,KAAI;YAAE,IAAG,EAAE,EAAE;YAAC,WAAU;YAAE,MAAK,EAAE,IAAI;YAAC,SAAQ,EAAE,OAAO;YAAC,WAAU,EAAE,SAAS;YAAC,WAAU,EAAE,MAAM;QAAA;IAAE;IAAC,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC;QAAG,OAAO,IAAE,IAAI,CAAC,MAAM,CAAC,GAAE,EAAE,MAAM,GAAE,EAAE,eAAe,CAAC,GAAE,GAAG,QAAQ,CAAC,EAAE,UAAU;IAAC;IAAC,OAAO,OAAO,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,OAAO,KAAG,WAAS,EAAE,KAAK,CAAC,GAAE,IAAI,IAAE;IAAC;AAAC;AAAE,EAAE,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE;IAAC,QAAO;AAAE;AAAG,IAAI,KAAG;IAAC,SAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE;QAAE,KAAG,CAAC,IAAE,EAAE,MAAM,CAAC,KAAG,EAAE;QAAE,IAAI;QAAE,IAAE,IAAE,EAAE,MAAM,CAAC;YAAC,SAAQ,IAAE;YAAE,QAAO;QAAC,GAAG,OAAO,CAAC,GAAE,KAAG,IAAE,EAAE,MAAM,CAAC;YAAC,SAAQ,IAAE;QAAC,GAAG,OAAO,CAAC,GAAE;QAAG,IAAI,IAAE,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,IAAG,IAAE;QAAG,OAAO,EAAE,QAAQ,GAAC,IAAE,GAAE,EAAE,MAAM,CAAC;YAAC,KAAI;YAAE,IAAG;YAAE,MAAK;QAAC;IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC,IAAG,IAAE,EAAE,GAAG,CAAC,OAAO,CAAC,GAAE,EAAE,OAAO,EAAC,EAAE,MAAM,EAAC,EAAE,IAAI,EAAC,EAAE,MAAM;QAAE,EAAE,EAAE,GAAC,EAAE,EAAE;QAAC,IAAI,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,EAAE,GAAG,EAAC;QAAG,OAAO,EAAE,KAAK,CAAC,IAAG;IAAC;IAAC,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC;QAAG,IAAE,IAAI,CAAC,MAAM,CAAC,GAAE,EAAE,MAAM;QAAE,IAAI,IAAE,EAAE,GAAG,CAAC,OAAO,CAAC,GAAE,EAAE,OAAO,EAAC,EAAE,MAAM,EAAC,EAAE,IAAI,EAAC,EAAE,MAAM;QAAE,OAAO,EAAE,EAAE,GAAC,EAAE,EAAE,EAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,EAAE,GAAG,EAAC;IAAE;AAAC;AAAE,EAAE,GAAG,GAAC,OAAO,MAAM,CAAC,EAAE,GAAG,EAAC;IAAC,KAAI;AAAE;AAAG,IAAI,IAAE,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,IAAE,EAAE;AAAC,IAAI,IAAI,IAAE,GAAE,IAAE,KAAI,KAAG,EAAE,IAAE,MAAI,CAAC,CAAC,EAAE,GAAC,KAAG,IAAE,CAAC,CAAC,EAAE,GAAC,KAAG,IAAE;AAAI,IAAI,IAAE,GAAE,IAAE;AAAE,IAAI,IAAI,IAAE,GAAE,IAAE,KAAI,KAAG,EAAE;IAAC,IAAI,IAAE,IAAE,KAAG,IAAE,KAAG,IAAE,KAAG,IAAE,KAAG;IAAE,IAAE,MAAI,IAAE,IAAE,MAAI,IAAG,CAAC,CAAC,EAAE,GAAC,GAAE,EAAE,CAAC,EAAE,GAAC;IAAE,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,GAAC,MAAI,IAAE;IAAS,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,GAAE,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,KAAG,IAAE,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,WAAS,IAAE,QAAM,IAAE,MAAI,IAAE,UAAS,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,GAAE,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,KAAG,IAAE,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,GAAE,IAAE,CAAC,IAAE,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAE,EAAE,CAAC,CAAC,EAAC,KAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,IAAE,GAAE,IAAE,CAAC;AAAC;AAAC,IAAI,KAAG;IAAC;IAAE;IAAE;IAAE;IAAE;IAAE;IAAG;IAAG;IAAG;IAAI;IAAG;CAAG,EAAC,IAAE,cAAc;IAAE,WAAU;QAAC,IAAI;QAAE,IAAG,IAAI,CAAC,QAAQ,IAAE,IAAI,CAAC,cAAc,KAAG,IAAI,CAAC,IAAI,EAAC;QAAO,IAAI,CAAC,cAAc,GAAC,IAAI,CAAC,IAAI;QAAC,IAAI,IAAE,IAAI,CAAC,cAAc,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,QAAQ,GAAC;QAAE,IAAI,CAAC,QAAQ,GAAC,IAAE;QAAE,IAAI,IAAE,CAAC,IAAI,CAAC,QAAQ,GAAC,CAAC,IAAE;QAAE,IAAI,CAAC,YAAY,GAAC,EAAE;QAAC,IAAI,IAAE,IAAI,CAAC,YAAY;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,IAAE,IAAE,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAG,IAAE,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,EAAC,KAAG,EAAE,CAAC,IAAE,IAAE,EAAE,IAAE,EAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC;QAAE,IAAI,CAAC,eAAe,GAAC,EAAE;QAAC,IAAI,IAAE,IAAI,CAAC,eAAe;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,IAAE;YAAE,IAAE,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,KAAG,KAAG,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC,CAAC,EAAE,GAAC,EAAE,CAAC,CAAC,CAAC,MAAI,GAAG,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,MAAI,KAAG,IAAI,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,MAAI,IAAE,IAAI,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,IAAE,IAAI,CAAC;QAAA;IAAC;IAAC,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,aAAa,CAAC,GAAE,GAAE,IAAI,CAAC,YAAY,EAAC,IAAG,IAAG,IAAG,IAAG;IAAE;IAAC,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,CAAC,CAAC,IAAE,EAAE;QAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,GAAE,IAAI,CAAC,aAAa,CAAC,GAAE,GAAE,IAAI,CAAC,eAAe,EAAC,IAAG,IAAG,IAAG,IAAG,KAAI,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC;IAAC;IAAC,cAAc,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,IAAI,CAAC,QAAQ,EAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE;QAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG;YAAE,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG;YAAE,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG;YAAE,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE;QAAC;QAAC,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG;QAAE,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG;QAAE,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG;QAAE,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG,GAAE,CAAC,CAAC,EAAE,GAAC,GAAE,CAAC,CAAC,IAAE,EAAE,GAAC,GAAE,CAAC,CAAC,IAAE,EAAE,GAAC,GAAE,CAAC,CAAC,IAAE,EAAE,GAAC;IAAC;AAAC;AAAE,EAAE,OAAO,GAAC,MAAI;AAAG,IAAI,KAAG,EAAE,aAAa,CAAC;AAAG,IAAI,IAAE,EAAE,EAAC,IAAE,cAAc;IAAE,WAAU;QAAC,IAAI,CAAC,KAAK,GAAC,IAAI,EAAE;YAAC;YAAW;YAAW;YAAW;YAAU;SAAW;IAAC;IAAC,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,IAAG,KAAG,EAAE;YAAC,IAAG,IAAE,IAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC;iBAAM;gBAAC,IAAI,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,GAAG,GAAC,CAAC,CAAC,IAAE,GAAG;gBAAC,CAAC,CAAC,EAAE,GAAC,KAAG,IAAE,MAAI;YAAE;YAAC,IAAI,IAAE,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,IAAE,CAAC,CAAC,EAAE;YAAC,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,aAAW,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,aAAW,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,IAAE,IAAE,IAAE,CAAC,IAAE,aAAW,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,WAAU,IAAE,GAAE,IAAE,GAAE,IAAE,KAAG,KAAG,MAAI,GAAE,IAAE,GAAE,IAAE;QAAC;QAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE;IAAC;IAAC,cAAa;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,IAAI,CAAC,WAAW,GAAC,GAAE,IAAE,EAAE,QAAQ,GAAC;QAAE,OAAO,CAAC,CAAC,MAAI,EAAE,IAAE,OAAK,KAAG,IAAE,IAAG,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,KAAK,KAAK,CAAC,IAAE,aAAY,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,GAAE,EAAE,QAAQ,GAAC,EAAE,MAAM,GAAC,GAAE,IAAI,CAAC,QAAQ,IAAG,IAAI,CAAC,KAAK;IAAA;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAG;IAAC;AAAC,GAAE,KAAG,EAAE,aAAa,CAAC,IAAG,KAAG,EAAE,iBAAiB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/errors.ts"], "sourcesContent": ["export const missingDomainAndProxy = `\nMissing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.\n\n1) With middleware\n   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'\n   `;\n\nexport const missingSignInUrlInDev = `\nInvalid signInUrl. A satellite application requires a signInUrl for development instances.\nCheck if signInUrl is missing from your configuration or if it is not an absolute URL\n\n1) With middleware\n   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`;\n\nexport const getAuthAuthHeaderMissing = () => authAuthHeaderMissing('getAuth');\n\nexport const authAuthHeaderMissing = (helperName = 'auth', prefixSteps?: string[]) =>\n  `Clerk: ${helperName}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:\n- ${prefixSteps ? [...prefixSteps, ''].join('\\n- ') : ' '}clerkMiddleware() is used in your Next.js Middleware.\n- Your Middleware matcher is configured to match this route or page.\n- If you are using the src directory, make sure the Middleware file is inside of it.\n\nFor more details, see https://clerk.com/docs/quickstarts/nextjs\n`;\n\nexport const authSignatureInvalid = `Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)`;\n\nexport const encryptionKeyInvalid = `Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\n\nexport const encryptionKeyInvalidDev = `Clerk: Unable to decrypt request data.\\n\\nRefresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.\\n\\nFor more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\n"], "names": [], "mappings": ";;;;;;;;;;AAAO,MAAM,wBAAwB,CAAA;;;;;;;;GAAA,CAAA;AAU9B,MAAM,wBAAwB,CAAA;;;;;;;;wCAAA,CAAA;AAU9B,MAAM,2BAA2B,IAAM,sBAAsB,SAAS;AAEtE,MAAM,wBAAwB,CAAC,aAAa,MAAA,EAAQ,cACzD,CAAA,OAAA,EAAU,UAAU,CAAA;EAAA,EAClB,cAAc,CAAC;WAAG;QAAa,EAAE;KAAA,CAAE,IAAA,CAAK,MAAM,IAAI,GAAG,CAAA;;;;;AAAA,CAAA;AAOlD,MAAM,uBAAuB,CAAA,yRAAA,CAAA;AAE7B,MAAM,uBAAuB,CAAA,oQAAA,CAAA;AAE7B,MAAM,0BAA0B,CAAA;;;;gIAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/errorThrower.ts"], "sourcesContent": ["import { buildErrorThrower } from '@clerk/shared/error';\n\nexport const errorThrower = buildErrorThrower({ packageName: '@clerk/nextjs' });\n"], "names": [], "mappings": ";;;;AAAA,SAAS,yBAAyB;;;AAE3B,MAAM,+RAAe,oBAAA,EAAkB;IAAE,aAAa;AAAgB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/utils.ts"], "sourcesContent": ["import type { AuthenticateRequestOptions, ClerkRequest, RequestState } from '@clerk/backend/internal';\nimport { constants } from '@clerk/backend/internal';\nimport { isDevelopmentFromSecretKey } from '@clerk/shared/keys';\nimport { logger } from '@clerk/shared/logger';\nimport { isHttpOrHttps } from '@clerk/shared/proxy';\nimport { handleValueOrFn, isProductionEnvironment } from '@clerk/shared/utils';\nimport { NextResponse } from 'next/server';\n\nimport { constants as nextConstants } from '../constants';\nimport { canUseKeyless } from '../utils/feature-flags';\nimport { AES, HmacSHA1, Utf8 } from '../vendor/crypto-es';\nimport { DOMAIN, ENCRYPTION_KEY, IS_SATELLITE, PROXY_URL, SECRET_KEY, SIGN_IN_URL } from './constants';\nimport {\n  authSignatureInvalid,\n  encryptionKeyInvalid,\n  encryptionKeyInvalidDev,\n  missingDomainAndProxy,\n  missingSignInUrlInDev,\n} from './errors';\nimport { errorThrower } from './errorThrower';\nimport { detectClerkMiddleware } from './headers-utils';\nimport type { RequestLike } from './types';\n\nconst OVERRIDE_HEADERS = 'x-middleware-override-headers';\nconst MIDDLEWARE_HEADER_PREFIX = 'x-middleware-request' as string;\n\nexport const setRequestHeadersOnNextResponse = (\n  res: NextResponse | Response,\n  req: Request,\n  newHeaders: Record<string, string>,\n) => {\n  if (!res.headers.get(OVERRIDE_HEADERS)) {\n    // Emulate a user setting overrides by explicitly adding the required nextjs headers\n    // https://github.com/vercel/next.js/pull/41380\n    // @ts-expect-error -- property keys does not exist on type Headers\n    res.headers.set(OVERRIDE_HEADERS, [...req.headers.keys()]);\n    req.headers.forEach((val, key) => {\n      res.headers.set(`${MIDDLEWARE_HEADER_PREFIX}-${key}`, val);\n    });\n  }\n\n  // Now that we have normalised res to include overrides, just append the new header\n  Object.entries(newHeaders).forEach(([key, val]) => {\n    res.headers.set(OVERRIDE_HEADERS, `${res.headers.get(OVERRIDE_HEADERS)},${key}`);\n    res.headers.set(`${MIDDLEWARE_HEADER_PREFIX}-${key}`, val);\n  });\n};\n\n// Auth result will be set as both a query param & header when applicable\nexport function decorateRequest(\n  req: ClerkRequest,\n  res: Response,\n  requestState: RequestState,\n  requestData: AuthenticateRequestOptions,\n  keylessMode: Pick<AuthenticateRequestOptions, 'publishableKey' | 'secretKey'>,\n): Response {\n  const { reason, message, status, token } = requestState;\n  // pass-through case, convert to next()\n  if (!res) {\n    res = NextResponse.next();\n  }\n\n  // redirect() case, return early\n  if (res.headers.get(nextConstants.Headers.NextRedirect)) {\n    return res;\n  }\n\n  let rewriteURL;\n\n  // next() case, convert to a rewrite\n  if (res.headers.get(nextConstants.Headers.NextResume) === '1') {\n    res.headers.delete(nextConstants.Headers.NextResume);\n    rewriteURL = new URL(req.url);\n  }\n\n  // rewrite() case, set auth result only if origin remains the same\n  const rewriteURLHeader = res.headers.get(nextConstants.Headers.NextRewrite);\n\n  if (rewriteURLHeader) {\n    const reqURL = new URL(req.url);\n    rewriteURL = new URL(rewriteURLHeader);\n\n    // if the origin has changed, return early\n    if (rewriteURL.origin !== reqURL.origin) {\n      return res;\n    }\n  }\n\n  if (rewriteURL) {\n    const clerkRequestData = encryptClerkRequestData(requestData, keylessMode);\n\n    setRequestHeadersOnNextResponse(res, req, {\n      [constants.Headers.AuthStatus]: status,\n      [constants.Headers.AuthToken]: token || '',\n      [constants.Headers.AuthSignature]: token\n        ? createTokenSignature(token, requestData?.secretKey || SECRET_KEY || keylessMode.secretKey || '')\n        : '',\n      [constants.Headers.AuthMessage]: message || '',\n      [constants.Headers.AuthReason]: reason || '',\n      [constants.Headers.ClerkUrl]: req.clerkUrl.toString(),\n      ...(clerkRequestData ? { [constants.Headers.ClerkRequestData]: clerkRequestData } : {}),\n    });\n    res.headers.set(nextConstants.Headers.NextRewrite, rewriteURL.href);\n  }\n\n  return res;\n}\n\nexport const handleMultiDomainAndProxy = (clerkRequest: ClerkRequest, opts: AuthenticateRequestOptions) => {\n  const relativeOrAbsoluteProxyUrl = handleValueOrFn(opts?.proxyUrl, clerkRequest.clerkUrl, PROXY_URL);\n\n  let proxyUrl;\n  if (!!relativeOrAbsoluteProxyUrl && !isHttpOrHttps(relativeOrAbsoluteProxyUrl)) {\n    proxyUrl = new URL(relativeOrAbsoluteProxyUrl, clerkRequest.clerkUrl).toString();\n  } else {\n    proxyUrl = relativeOrAbsoluteProxyUrl;\n  }\n\n  const isSatellite = handleValueOrFn(opts.isSatellite, new URL(clerkRequest.url), IS_SATELLITE);\n  const domain = handleValueOrFn(opts.domain, new URL(clerkRequest.url), DOMAIN);\n  const signInUrl = opts?.signInUrl || SIGN_IN_URL;\n\n  if (isSatellite && !proxyUrl && !domain) {\n    throw new Error(missingDomainAndProxy);\n  }\n\n  if (isSatellite && !isHttpOrHttps(signInUrl) && isDevelopmentFromSecretKey(opts.secretKey || SECRET_KEY)) {\n    throw new Error(missingSignInUrlInDev);\n  }\n\n  return {\n    proxyUrl,\n    isSatellite,\n    domain,\n    signInUrl,\n  };\n};\n\nexport const redirectAdapter = (url: string | URL) => {\n  return NextResponse.redirect(url, { headers: { [constants.Headers.ClerkRedirectTo]: 'true' } });\n};\n\nexport function assertAuthStatus(req: RequestLike, error: string) {\n  if (!detectClerkMiddleware(req)) {\n    throw new Error(error);\n  }\n}\n\nexport function assertKey(key: string | undefined, onError: () => never): string {\n  if (!key) {\n    onError();\n  }\n\n  return key;\n}\n\n/**\n * Compute a cryptographic signature from a session token and provided secret key. Used to validate that the token has not been modified when transferring between middleware and the Next.js origin.\n */\nfunction createTokenSignature(token: string, key: string): string {\n  return HmacSHA1(token, key).toString();\n}\n\n/**\n * Assert that the provided token generates a matching signature.\n */\nexport function assertTokenSignature(token: string, key: string, signature?: string | null) {\n  if (!signature) {\n    throw new Error(authSignatureInvalid);\n  }\n\n  const expectedSignature = createTokenSignature(token, key);\n  if (expectedSignature !== signature) {\n    throw new Error(authSignatureInvalid);\n  }\n}\n\nconst KEYLESS_ENCRYPTION_KEY = 'clerk_keyless_dummy_key';\n\n/**\n * Encrypt request data propagated between server requests.\n * @internal\n **/\nexport function encryptClerkRequestData(\n  requestData: Partial<AuthenticateRequestOptions>,\n  keylessModeKeys: Pick<AuthenticateRequestOptions, 'publishableKey' | 'secretKey'>,\n) {\n  const isEmpty = (obj: Record<string, any> | undefined) => {\n    if (!obj) {\n      return true;\n    }\n    return !Object.values(obj).some(v => v !== undefined);\n  };\n\n  if (isEmpty(requestData) && isEmpty(keylessModeKeys)) {\n    return;\n  }\n\n  if (requestData.secretKey && !ENCRYPTION_KEY) {\n    // TODO SDK-1833: change this to an error in the next major version of `@clerk/nextjs`\n    logger.warnOnce(\n      'Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys',\n    );\n\n    return;\n  }\n\n  const maybeKeylessEncryptionKey = isProductionEnvironment()\n    ? ENCRYPTION_KEY || assertKey(SECRET_KEY, () => errorThrower.throwMissingSecretKeyError())\n    : ENCRYPTION_KEY || SECRET_KEY || KEYLESS_ENCRYPTION_KEY;\n\n  return AES.encrypt(JSON.stringify({ ...keylessModeKeys, ...requestData }), maybeKeylessEncryptionKey).toString();\n}\n\n/**\n * Decrypt request data propagated between server requests.\n * @internal\n */\nexport function decryptClerkRequestData(\n  encryptedRequestData?: string | undefined | null,\n): Partial<AuthenticateRequestOptions> {\n  if (!encryptedRequestData) {\n    return {};\n  }\n\n  const maybeKeylessEncryptionKey = isProductionEnvironment()\n    ? ENCRYPTION_KEY || SECRET_KEY\n    : ENCRYPTION_KEY || SECRET_KEY || KEYLESS_ENCRYPTION_KEY;\n\n  try {\n    return decryptData(encryptedRequestData, maybeKeylessEncryptionKey);\n  } catch {\n    /**\n     * There is a great chance when running in Keyless mode that the above fails,\n     * because the keys hot-swapped and the Next.js dev server has not yet fully rebuilt middleware and routes.\n     *\n     * Attempt one more time with the default dummy value.\n     */\n    if (canUseKeyless) {\n      try {\n        return decryptData(encryptedRequestData, KEYLESS_ENCRYPTION_KEY);\n      } catch {\n        throwInvalidEncryptionKey();\n      }\n    }\n    throwInvalidEncryptionKey();\n  }\n}\n\nfunction throwInvalidEncryptionKey(): never {\n  if (isProductionEnvironment()) {\n    throw new Error(encryptionKeyInvalid);\n  }\n  throw new Error(encryptionKeyInvalidDev);\n}\n\nfunction decryptData(data: string, key: string) {\n  const decryptedBytes = AES.decrypt(data, key);\n  const encoded = decryptedBytes.toString(Utf8);\n  return JSON.parse(encoded);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AACA,SAAS,iBAAiB;;AAC1B,SAAS,kCAAkC;;AAC3C,SAAS,cAAc;;;AACvB,SAAS,qBAAqB;;;AAC9B,SAAS,iBAAiB,+BAA+B;AACzD,SAAS,oBAAoB;AAE7B,SAAS,aAAa,qBAAqB;AAC3C,SAAS,qBAAqB;AAC9B,SAAS,KAAK,UAAU,YAAY;AACpC,SAAS,QAAQ,gBAAgB,cAAc,WAAW,YAAY,mBAAmB;AACzF;AAOA,SAAS,oBAAoB;AAC7B,SAAS,6BAA6B;;;;;;;;;;;;;;;AAGtC,MAAM,mBAAmB;AACzB,MAAM,2BAA2B;AAE1B,MAAM,kCAAkC,CAC7C,KACA,KACA,eACG;IACH,IAAI,CAAC,IAAI,OAAA,CAAQ,GAAA,CAAI,gBAAgB,GAAG;QAItC,IAAI,OAAA,CAAQ,GAAA,CAAI,kBAAkB,CAAC;eAAG,IAAI,OAAA,CAAQ,IAAA,CAAK,CAAC;SAAC;QACzD,IAAI,OAAA,CAAQ,OAAA,CAAQ,CAAC,KAAK,QAAQ;YAChC,IAAI,OAAA,CAAQ,GAAA,CAAI,GAAG,wBAAwB,CAAA,CAAA,EAAI,GAAG,EAAA,EAAI,GAAG;QAC3D,CAAC;IACH;IAGA,OAAO,OAAA,CAAQ,UAAU,EAAE,OAAA,CAAQ,CAAC,CAAC,KAAK,GAAG,CAAA,KAAM;QACjD,IAAI,OAAA,CAAQ,GAAA,CAAI,kBAAkB,GAAG,IAAI,OAAA,CAAQ,GAAA,CAAI,gBAAgB,CAAC,CAAA,CAAA,EAAI,GAAG,EAAE;QAC/E,IAAI,OAAA,CAAQ,GAAA,CAAI,GAAG,wBAAwB,CAAA,CAAA,EAAI,GAAG,EAAA,EAAI,GAAG;IAC3D,CAAC;AACH;AAGO,SAAS,gBACd,GAAA,EACA,GAAA,EACA,YAAA,EACA,WAAA,EACA,WAAA,EACU;IACV,MAAM,EAAE,MAAA,EAAQ,OAAA,EAAS,MAAA,EAAQ,KAAA,CAAM,CAAA,GAAI;IAE3C,IAAI,CAAC,KAAK;QACR,sPAAM,eAAA,CAAa,IAAA,CAAK;IAC1B;IAGA,IAAI,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,YAAY,GAAG;QACvD,OAAO;IACT;IAEA,IAAI;IAGJ,IAAI,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,UAAU,MAAM,KAAK;QAC7D,IAAI,OAAA,CAAQ,MAAA,0QAAO,aAAA,CAAc,OAAA,CAAQ,UAAU;QACnD,aAAa,IAAI,IAAI,IAAI,GAAG;IAC9B;IAGA,MAAM,mBAAmB,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,WAAW;IAE1E,IAAI,kBAAkB;QACpB,MAAM,SAAS,IAAI,IAAI,IAAI,GAAG;QAC9B,aAAa,IAAI,IAAI,gBAAgB;QAGrC,IAAI,WAAW,MAAA,KAAW,OAAO,MAAA,EAAQ;YACvC,OAAO;QACT;IACF;IAEA,IAAI,YAAY;QACd,MAAM,mBAAmB,wBAAwB,aAAa,WAAW;QAEzE,gCAAgC,KAAK,KAAK;YACxC,2QAAC,YAAA,CAAU,OAAA,CAAQ,UAAU,CAAA,EAAG;YAChC,2QAAC,YAAA,CAAU,OAAA,CAAQ,SAAS,CAAA,EAAG,SAAS;YACxC,2QAAC,YAAA,CAAU,OAAA,CAAQ,aAAa,CAAA,EAAG,QAC/B,qBAAqB,OAAA,CAAO,eAAA,OAAA,KAAA,IAAA,YAAa,SAAA,yRAAa,aAAA,IAAc,YAAY,SAAA,IAAa,EAAE,IAC/F;YACJ,CAAC,sRAAA,CAAU,OAAA,CAAQ,WAAW,CAAA,EAAG,WAAW;YAC5C,2QAAC,YAAA,CAAU,OAAA,CAAQ,UAAU,CAAA,EAAG,UAAU;YAC1C,CAAC,sRAAA,CAAU,OAAA,CAAQ,QAAQ,CAAA,EAAG,IAAI,QAAA,CAAS,QAAA,CAAS;YACpD,GAAI,mBAAmB;gBAAE,2QAAC,YAAA,CAAU,OAAA,CAAQ,gBAAgB,CAAA,EAAG;YAAiB,IAAI,CAAC,CAAA;QACvF,CAAC;QACD,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,WAAA,EAAa,WAAW,IAAI;IACpE;IAEA,OAAO;AACT;AAEO,MAAM,4BAA4B,CAAC,cAA4B,SAAqC;IACzG,MAAM,6SAA6B,kBAAA,EAAgB,QAAA,OAAA,KAAA,IAAA,KAAM,QAAA,EAAU,aAAa,QAAA,sRAAU,YAAS;IAEnG,IAAI;IACJ,IAAI,CAAC,CAAC,8BAA8B,iRAAC,gBAAA,EAAc,0BAA0B,GAAG;QAC9E,WAAW,IAAI,IAAI,4BAA4B,aAAa,QAAQ,EAAE,QAAA,CAAS;IACjF,OAAO;QACL,WAAW;IACb;IAEA,MAAM,8RAAc,kBAAA,EAAgB,KAAK,WAAA,EAAa,IAAI,IAAI,aAAa,GAAG,uRAAG,eAAY;IAC7F,MAAM,UAAS,iSAAA,EAAgB,KAAK,MAAA,EAAQ,IAAI,IAAI,aAAa,GAAG,uRAAG,SAAM;IAC7E,MAAM,YAAA,CAAY,QAAA,OAAA,KAAA,IAAA,KAAM,SAAA,yRAAa,cAAA;IAErC,IAAI,eAAe,CAAC,YAAY,CAAC,QAAQ;QACvC,MAAM,IAAI,uRAAM,wBAAqB;IACvC;IAEA,IAAI,eAAe,CAAC,gSAAA,EAAc,SAAS,qRAAK,6BAAA,EAA2B,KAAK,SAAA,IAAa,iSAAU,GAAG;QACxG,MAAM,IAAI,uRAAM,wBAAqB;IACvC;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,kBAAkB,CAAC,QAAsB;IACpD,uPAAO,eAAA,CAAa,QAAA,CAAS,KAAK;QAAE,SAAS;YAAE,2QAAC,YAAA,CAAU,OAAA,CAAQ,eAAe,CAAA,EAAG;QAAO;IAAE,CAAC;AAChG;AAEO,SAAS,iBAAiB,GAAA,EAAkB,KAAA,EAAe;IAChE,IAAI,gSAAC,wBAAA,EAAsB,GAAG,GAAG;QAC/B,MAAM,IAAI,MAAM,KAAK;IACvB;AACF;AAEO,SAAS,UAAU,GAAA,EAAyB,OAAA,EAA8B;IAC/E,IAAI,CAAC,KAAK;QACR,QAAQ;IACV;IAEA,OAAO;AACT;AAKA,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAqB;IAChE,kSAAO,WAAA,EAAS,OAAO,GAAG,EAAE,QAAA,CAAS;AACvC;AAKO,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAa,SAAA,EAA2B;IAC1F,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,uRAAM,uBAAoB;IACtC;IAEA,MAAM,oBAAoB,qBAAqB,OAAO,GAAG;IACzD,IAAI,sBAAsB,WAAW;QACnC,MAAM,IAAI,uRAAM,uBAAoB;IACtC;AACF;AAEA,MAAM,yBAAyB;AAMxB,SAAS,wBACd,WAAA,EACA,eAAA,EACA;IACA,MAAM,UAAU,CAAC,QAAyC;QACxD,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QACA,OAAO,CAAC,OAAO,MAAA,CAAO,GAAG,EAAE,IAAA,CAAK,CAAA,IAAK,MAAM,KAAA,CAAS;IACtD;IAEA,IAAI,QAAQ,WAAW,KAAK,QAAQ,eAAe,GAAG;QACpD;IACF;IAEA,IAAI,YAAY,SAAA,IAAa,qRAAC,iBAAA,EAAgB;QAE5C,2QAAA,CAAA,SAAA,CAAO,QAAA,CACL;QAGF;IACF;IAEA,MAAM,6BAA4B,ySAAA,CAAwB,yRACtD,iBAAA,IAAkB,8RAAU,aAAA,EAAY,2RAAM,eAAA,CAAa,0BAAA,CAA2B,CAAC,wRACvF,iBAAA,wRAAkB,aAAA,IAAc;IAEpC,8RAAO,MAAA,CAAI,OAAA,CAAQ,KAAK,SAAA,CAAU;QAAE,GAAG,eAAA;QAAiB,GAAG,WAAA;IAAY,CAAC,GAAG,yBAAyB,EAAE,QAAA,CAAS;AACjH;AAMO,SAAS,wBACd,oBAAA,EACqC;IACrC,IAAI,CAAC,sBAAsB;QACzB,OAAO,CAAC;IACV;IAEA,MAAM,4SAA4B,0BAAA,CAAwB,KACtD,qSAAA,wRAAkB,aAAA,uRAClB,iBAAA,wRAAkB,aAAA,IAAc;IAEpC,IAAI;QACF,OAAO,YAAY,sBAAsB,yBAAyB;IACpE,EAAA,OAAQ;QAON,8RAAI,gBAAA,EAAe;YACjB,IAAI;gBACF,OAAO,YAAY,sBAAsB,sBAAsB;YACjE,EAAA,OAAQ;gBACN,0BAA0B;YAC5B;QACF;QACA,0BAA0B;IAC5B;AACF;AAEA,SAAS,4BAAmC;IAC1C,oRAAI,0BAAA,CAAwB,IAAG;QAC7B,MAAM,IAAI,uRAAM,uBAAoB;IACtC;IACA,MAAM,IAAI,uRAAM,0BAAuB;AACzC;AAEA,SAAS,YAAY,IAAA,EAAc,GAAA,EAAa;IAC9C,MAAM,iBAAiB,6RAAA,CAAI,OAAA,CAAQ,MAAM,GAAG;IAC5C,MAAM,UAAU,eAAe,QAAA,wRAAS,OAAI;IAC5C,OAAO,KAAK,KAAA,CAAM,OAAO;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/data/getAuthDataFromRequest.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport { AuthStatus, constants, signedInAuthObject, signedOutAuthObject } from '@clerk/backend/internal';\nimport { decodeJwt } from '@clerk/backend/jwt';\nimport type { PendingSessionOptions } from '@clerk/types';\n\nimport type { LoggerNoCommit } from '../../utils/debugLogger';\nimport { API_URL, API_VERSION, PUBLISHABLE_KEY, SECRET_KEY } from '../constants';\nimport { getAuthKeyFromRequest, getHeader } from '../headers-utils';\nimport type { RequestLike } from '../types';\nimport { assertTokenSignature, decryptClerkRequestData } from '../utils';\n\n/**\n * Given a request object, builds an auth object from the request data. Used in server-side environments to get access\n * to auth data for a given request.\n */\nexport function getAuthDataFromRequest(\n  req: RequestLike,\n  {\n    treatPendingAsSignedOut = true,\n    ...opts\n  }: { secretKey?: string; logger?: LoggerNoCommit } & PendingSessionOptions = {},\n): AuthObject {\n  const authStatus = getAuthKeyFromRequest(req, 'AuthStatus');\n  const authToken = getAuthKeyFromRequest(req, 'AuthToken');\n  const authMessage = getAuthKeyFromRequest(req, 'AuthMessage');\n  const authReason = getAuthKeyFromRequest(req, 'AuthReason');\n  const authSignature = getAuthKeyFromRequest(req, 'AuthSignature');\n\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n\n  const options = {\n    secretKey: opts?.secretKey || decryptedRequestData.secretKey || SECRET_KEY,\n    publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n    apiUrl: API_URL,\n    apiVersion: API_VERSION,\n    authStatus,\n    authMessage,\n    authReason,\n    treatPendingAsSignedOut,\n  };\n\n  opts.logger?.debug('auth options', options);\n\n  let authObject;\n  if (!authStatus || authStatus !== AuthStatus.SignedIn) {\n    authObject = signedOutAuthObject(options);\n  } else {\n    assertTokenSignature(authToken as string, options.secretKey, authSignature);\n\n    const jwt = decodeJwt(authToken as string);\n\n    opts.logger?.debug('jwt', jwt.raw);\n\n    // @ts-expect-error -- Restrict parameter type of options to only list what's needed\n    authObject = signedInAuthObject(options, jwt.raw.text, jwt.payload);\n  }\n\n  if (treatPendingAsSignedOut && authObject.sessionStatus === 'pending') {\n    authObject = signedOutAuthObject(options, authObject.sessionStatus);\n  }\n\n  return authObject;\n}\n"], "names": [], "mappings": ";;;AACA,SAAS,YAAY,WAAW,oBAAoB,2BAA2B;;AAC/E,SAAS,iBAAiB;AAI1B,SAAS,SAAS,aAAa,iBAAiB,kBAAkB;AAClE,SAAS,uBAAuB,iBAAiB;AAEjD,SAAS,sBAAsB,+BAA+B;;;;;;;AAMvD,SAAS,uBACd,GAAA,EACA,EACE,0BAA0B,IAAA,EAC1B,GAAG,MACL,GAA6E,CAAC,CAAA,EAClE;IArBd,IAAA,IAAA,IAAA;IAsBE,MAAM,aAAa,uTAAA,EAAsB,KAAK,YAAY;IAC1D,MAAM,2SAAY,wBAAA,EAAsB,KAAK,WAAW;IACxD,MAAM,cAAc,uTAAA,EAAsB,KAAK,aAAa;IAC5D,MAAM,4SAAa,wBAAA,EAAsB,KAAK,YAAY;IAC1D,MAAM,+SAAgB,wBAAA,EAAsB,KAAK,eAAe;IAEhE,CAAA,KAAA,KAAK,MAAA,KAAL,OAAA,KAAA,IAAA,GAAa,KAAA,CAAM,WAAW;QAAE;QAAY;QAAa;IAAW;IAEpE,MAAM,sTAAuB,YAAA,EAAU,+QAAK,YAAA,CAAU,OAAA,CAAQ,gBAAgB;IAC9E,MAAM,2SAAuB,0BAAA,EAAwB,oBAAoB;IAEzE,MAAM,UAAU;QACd,WAAA,CAAW,QAAA,OAAA,KAAA,IAAA,KAAM,SAAA,KAAa,qBAAqB,SAAA,wRAAa,aAAA;QAChE,gBAAgB,qBAAqB,cAAA,wRAAkB,kBAAA;QACvD,4RAAQ,UAAA;QACR,YAAY,kSAAA;QACZ;QACA;QACA;QACA;IACF;IAEA,CAAA,KAAA,KAAK,MAAA,KAAL,OAAA,KAAA,IAAA,GAAa,KAAA,CAAM,gBAAgB;IAEnC,IAAI;IACJ,IAAI,CAAC,cAAc,eAAe,uRAAA,CAAW,QAAA,EAAU;QACrD,2RAAa,sBAAA,EAAoB,OAAO;IAC1C,OAAO;QACL,CAAA,GAAA,+QAAA,CAAA,uBAAA,EAAqB,WAAqB,QAAQ,SAAA,EAAW,aAAa;QAE1E,MAAM,OAAM,oRAAA,EAAU,SAAmB;QAEzC,CAAA,KAAA,KAAK,MAAA,KAAL,OAAA,KAAA,IAAA,GAAa,KAAA,CAAM,OAAO,IAAI,GAAA;QAG9B,2RAAa,qBAAA,EAAmB,SAAS,IAAI,GAAA,CAAI,IAAA,EAAM,IAAI,OAAO;IACpE;IAEA,IAAI,2BAA2B,WAAW,aAAA,KAAkB,WAAW;QACrE,2RAAa,sBAAA,EAAoB,SAAS,WAAW,aAAa;IACpE;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/createGetAuth.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport { constants } from '@clerk/backend/internal';\nimport { isTruthy } from '@clerk/shared/underscore';\nimport type { PendingSessionOptions } from '@clerk/types';\n\nimport { withLogger } from '../utils/debugLogger';\nimport { isNextWithUnstableServerActions } from '../utils/sdk-versions';\nimport { getAuthDataFromRequest } from './data/getAuthDataFromRequest';\nimport { getAuthAuthHeaderMissing } from './errors';\nimport { detectClerkMiddleware, getHeader } from './headers-utils';\nimport type { RequestLike } from './types';\nimport { assertAuthStatus } from './utils';\n\n/**\n * The async variant of our old `createGetAuth` allows for asynchronous code inside its callback.\n * Should be used with function like `auth()` that are already asynchronous.\n */\nexport const createAsyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage,\n}: {\n  debugLoggerName: string;\n  noAuthStatusMessage: string;\n}) =>\n  withLogger(debugLoggerName, logger => {\n    return async (req: RequestLike, opts?: { secretKey?: string } & PendingSessionOptions): Promise<AuthObject> => {\n      if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n        logger.enable();\n      }\n\n      if (!detectClerkMiddleware(req)) {\n        // Keep the same behaviour for versions that may have issues with bundling `node:fs`\n        if (isNextWithUnstableServerActions) {\n          assertAuthStatus(req, noAuthStatusMessage);\n        }\n\n        const missConfiguredMiddlewareLocation = await import('./fs/middleware-location.js')\n          .then(m => m.suggestMiddlewareLocation())\n          .catch(() => undefined);\n\n        if (missConfiguredMiddlewareLocation) {\n          throw new Error(missConfiguredMiddlewareLocation);\n        }\n\n        // still throw there is no suggested move location\n        assertAuthStatus(req, noAuthStatusMessage);\n      }\n\n      return getAuthDataFromRequest(req, { ...opts, logger });\n    };\n  });\n\n/**\n * Previous known as `createGetAuth`. We needed to create a sync and async variant in order to allow for improvements\n * that required dynamic imports (using `require` would not work).\n * It powers the synchronous top-level api `getAuth()`.\n */\nexport const createSyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage,\n}: {\n  debugLoggerName: string;\n  noAuthStatusMessage: string;\n}) =>\n  withLogger(debugLoggerName, logger => {\n    return (req: RequestLike, opts?: { secretKey?: string } & PendingSessionOptions): AuthObject => {\n      if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n        logger.enable();\n      }\n\n      assertAuthStatus(req, noAuthStatusMessage);\n      return getAuthDataFromRequest(req, { ...opts, logger });\n    };\n  });\n\n/**\n * The `getAuth()` helper retrieves authentication state from the request object.\n *\n * > [!NOTE]\n * > If you are using App Router, use the [`auth()` helper](https://clerk.com/docs/references/nextjs/auth) instead.\n *\n * @param req - The Next.js request object.\n * @param [options] - An optional object that can be used to configure the behavior of the `getAuth()` function.\n * @param [options.secretKey] - A string that represents the Secret Key used to sign the session token. If not provided, the Secret Key is retrieved from the environment variable `CLERK_SECRET_KEY`.\n * @returns The `Auth` object. See the [Auth reference](https://clerk.com/docs/references/backend/types/auth-object) for more information.\n *\n * @example\n * ### Protect API routes\n *\n * The following example demonstrates how to protect an API route by checking if the `userId` is present in the `getAuth()` response.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { userId } = getAuth(req)\n *\n *   if (!userId) {\n *     return res.status(401).json({ error: 'Not authenticated' })\n *   }\n *\n *   // Add logic that retrieves the data for the API route\n *\n *   return res.status(200).json({ userId: userId })\n * }\n * ```\n *\n * @example\n * ### Usage with `getToken()`\n *\n * `getAuth()` returns [`getToken()`](https://clerk.com/docs/references/backend/types/auth-object#get-token), which is a method that returns the current user's session token or a custom JWT template.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { getToken } = getAuth(req)\n *\n *   const token = await getToken({ template: 'supabase' })\n *\n *   // Add logic that retrieves the data\n *   // from your database using the token\n *\n *   return res.status(200).json({})\n * }\n * ```\n *\n * @example\n * ### Usage with `clerkClient`\n *\n * `clerkClient` is used to access the [Backend SDK](https://clerk.com/docs/references/backend/overview), which exposes Clerk's Backend API resources. You can use `getAuth()` to pass authentication information that many of the Backend SDK methods require, like the user's ID.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { clerkClient, getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { userId } = getAuth(req)\n *\n *   const client = await clerkClient()\n *\n *   const user = userId ? await client.users.getUser(userId) : null\n *\n *   return res.status(200).json({})\n * }\n * ```\n */\nexport const getAuth = createSyncGetAuth({\n  debugLoggerName: 'getAuth()',\n  noAuthStatusMessage: getAuthAuthHeaderMissing(),\n});\n"], "names": [], "mappings": ";;;;;AACA,SAAS,iBAAiB;;AAC1B,SAAS,gBAAgB;;AAGzB,SAAS,kBAAkB;AAC3B,SAAS,uCAAuC;AAChD,SAAS,8BAA8B;AACvC,SAAS,gCAAgC;AACzC,SAAS,uBAAuB,iBAAiB;AAEjD,SAAS,wBAAwB;;;;;;;;;;AAM1B,MAAM,qBAAqB,CAAC,EACjC,eAAA,EACA,mBAAA,EACF,2RAIE,cAAA,EAAW,iBAAiB,CAAA,WAAU;QACpC,OAAO,OAAO,KAAkB,SAA+E;YAC7G,QAAI,uRAAA,iSAAS,YAAA,EAAU,+QAAK,YAAA,CAAU,OAAA,CAAQ,WAAW,CAAC,GAAG;gBAC3D,OAAO,MAAA,CAAO;YAChB;YAEA,IAAI,EAAC,sTAAA,EAAsB,GAAG,GAAG;gBAE/B,6RAAI,kCAAA,EAAiC;oBACnC,CAAA,GAAA,+QAAA,CAAA,mBAAA,EAAiB,KAAK,mBAAmB;gBAC3C;gBAEA,MAAM,mCAAmC,MAAM,OAAO,6BAA6B,sNAChF,IAAA,CAAK,CAAA,IAAK,EAAE,yBAAA,CAA0B,CAAC,EACvC,KAAA,CAAM,IAAM,KAAA,CAAS;gBAExB,IAAI,kCAAkC;oBACpC,MAAM,IAAI,MAAM,gCAAgC;gBAClD;gBAGA,CAAA,GAAA,+QAAA,CAAA,mBAAA,EAAiB,KAAK,mBAAmB;YAC3C;YAEA,QAAO,qUAAA,EAAuB,KAAK;gBAAE,GAAG,IAAA;gBAAM;YAAO,CAAC;QACxD;IACF,CAAC;AAOI,MAAM,oBAAoB,CAAC,EAChC,eAAA,EACA,mBAAA,EACF,GAIE,sSAAA,EAAW,iBAAiB,CAAA,WAAU;QACpC,OAAO,CAAC,KAAkB,SAAsE;YAC9F,oRAAI,WAAA,iSAAS,YAAA,EAAU,8QAAK,aAAA,CAAU,OAAA,CAAQ,WAAW,CAAC,GAAG;gBAC3D,OAAO,MAAA,CAAO;YAChB;YAEA,CAAA,GAAA,+QAAA,CAAA,mBAAA,EAAiB,KAAK,mBAAmB;YACzC,oTAAO,yBAAA,EAAuB,KAAK;gBAAE,GAAG,IAAA;gBAAM;YAAO,CAAC;QACxD;IACF,CAAC;AA4EI,MAAM,UAAU,kBAAkB;IACvC,iBAAiB;IACjB,0SAAqB,2BAAA,CAAyB;AAChD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/nextFetcher.ts"], "sourcesContent": ["type Fetcher = typeof globalThis.fetch;\n\n/**\n * Based on nextjs internal implementation https://github.com/vercel/next.js/blob/6185444e0a944a82e7719ac37dad8becfed86acd/packages/next/src/server/lib/patch-fetch.ts#L23\n */\ntype NextFetcher = Fetcher & {\n  readonly __nextPatched: true;\n  readonly __nextGetStaticStore: () => { getStore: () => StaticGenerationAsyncStorage | undefined };\n};\n\n/**\n * Full type can be found https://github.com/vercel/next.js/blob/6185444e0a944a82e7719ac37dad8becfed86acd/packages/next/src/client/components/static-generation-async-storage.external.ts#L4\n */\ninterface StaticGenerationAsyncStorage {\n  /**\n   * Available for Next 14\n   */\n  readonly pagePath?: string;\n  /**\n   * Available for Next 15\n   */\n  readonly page?: string;\n}\n\nfunction isNextFetcher(fetch: Fetcher | NextFetcher): fetch is NextFetcher {\n  return '__nextPatched' in fetch && fetch.__nextPatched === true;\n}\n\nexport { isNextFetcher };\n"], "names": [], "mappings": ";;;;AAwBA,SAAS,cAAc,KAAA,EAAoD;IACzE,OAAO,mBAAmB,SAAS,MAAM,aAAA,KAAkB;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4504, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/protect.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport type { RedirectFun, SignedInAuthObject } from '@clerk/backend/internal';\nimport { constants } from '@clerk/backend/internal';\nimport type {\n  CheckAuthorizationFromSessionClaims,\n  CheckAuthorizationParamsFromSessionClaims,\n  CheckAuthorizationParamsWithCustomPermissions,\n  CheckAuthorizationWithCustomPermissions,\n  OrganizationCustomPermissionKey,\n} from '@clerk/types';\n\nimport { constants as nextConstants } from '../constants';\nimport { isNextFetcher } from './nextFetcher';\n\ntype AuthProtectOptions = {\n  /**\n   * The URL to redirect the user to if they are not authorized.\n   */\n  unauthorizedUrl?: string;\n  /**\n   * The URL to redirect the user to if they are not authenticated.\n   */\n  unauthenticatedUrl?: string;\n};\n\n/**\n * Throws a Nextjs notFound error if user is not authenticated or authorized.\n */\nexport interface AuthProtect {\n  <P extends OrganizationCustomPermissionKey>(\n    params?: CheckAuthorizationParamsFromSessionClaims<P>,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  (\n    params?: (has: CheckAuthorizationFromSessionClaims) => boolean,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  (options?: AuthProtectOptions): Promise<SignedInAuthObject>;\n}\n\nexport function createProtect(opts: {\n  request: Request;\n  authObject: AuthObject;\n  /**\n   * middleware and pages throw a notFound error if signed out\n   * but the middleware needs to throw an error it can catch\n   * use this callback to customise the behavior\n   */\n  notFound: () => never;\n  /**\n   * see {@link notFound} above\n   */\n  redirect: (url: string) => void;\n  /**\n   * protect() in middleware redirects to signInUrl if signed out\n   * protect() in pages throws a notFound error if signed out\n   * use this callback to customise the behavior\n   */\n  redirectToSignIn: RedirectFun<unknown>;\n}): AuthProtect {\n  const { redirectToSignIn, authObject, redirect, notFound, request } = opts;\n\n  return (async (...args: any[]) => {\n    const optionValuesAsParam = args[0]?.unauthenticatedUrl || args[0]?.unauthorizedUrl;\n    const paramsOrFunction = optionValuesAsParam\n      ? undefined\n      : (args[0] as\n          | CheckAuthorizationParamsWithCustomPermissions\n          | ((has: CheckAuthorizationWithCustomPermissions) => boolean));\n    const unauthenticatedUrl = (args[0]?.unauthenticatedUrl || args[1]?.unauthenticatedUrl) as string | undefined;\n    const unauthorizedUrl = (args[0]?.unauthorizedUrl || args[1]?.unauthorizedUrl) as string | undefined;\n\n    const handleUnauthenticated = () => {\n      if (unauthenticatedUrl) {\n        return redirect(unauthenticatedUrl);\n      }\n      if (isPageRequest(request)) {\n        // TODO: Handle runtime values. What happens if runtime values are set in middleware and in ClerkProvider as well?\n        return redirectToSignIn();\n      }\n      return notFound();\n    };\n\n    const handleUnauthorized = () => {\n      if (unauthorizedUrl) {\n        return redirect(unauthorizedUrl);\n      }\n      return notFound();\n    };\n\n    /**\n     * Redirects the user back to the tasks URL if their session status is pending\n     */\n    if (authObject.sessionStatus === 'pending') {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is not authenticated\n     */\n    if (!authObject.userId) {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is authenticated\n     */\n    if (!paramsOrFunction) {\n      return authObject;\n    }\n\n    /**\n     * if a function is passed and returns false then throw not found\n     */\n    if (typeof paramsOrFunction === 'function') {\n      if (paramsOrFunction(authObject.has)) {\n        return authObject;\n      }\n      return handleUnauthorized();\n    }\n\n    /**\n     * Checking if user is authorized when permission or role is passed\n     */\n    if (authObject.has(paramsOrFunction)) {\n      return authObject;\n    }\n\n    return handleUnauthorized();\n  }) as AuthProtect;\n}\n\nconst isServerActionRequest = (req: Request) => {\n  return (\n    !!req.headers.get(nextConstants.Headers.NextUrl) &&\n    (req.headers.get(constants.Headers.Accept)?.includes('text/x-component') ||\n      req.headers.get(constants.Headers.ContentType)?.includes('multipart/form-data') ||\n      !!req.headers.get(nextConstants.Headers.NextAction))\n  );\n};\n\nconst isPageRequest = (req: Request): boolean => {\n  return (\n    req.headers.get(constants.Headers.SecFetchDest) === 'document' ||\n    req.headers.get(constants.Headers.SecFetchDest) === 'iframe' ||\n    req.headers.get(constants.Headers.Accept)?.includes('text/html') ||\n    isAppRouterInternalNavigation(req) ||\n    isPagesRouterInternalNavigation(req)\n  );\n};\n\nconst isAppRouterInternalNavigation = (req: Request) =>\n  (!!req.headers.get(nextConstants.Headers.NextUrl) && !isServerActionRequest(req)) || isPagePathAvailable();\n\nconst isPagePathAvailable = () => {\n  const __fetch = globalThis.fetch;\n\n  if (!isNextFetcher(__fetch)) {\n    return false;\n  }\n\n  const { page, pagePath } = __fetch.__nextGetStaticStore().getStore() || {};\n\n  return Boolean(\n    // available on next@14\n    pagePath ||\n      // available on next@15\n      page,\n  );\n};\n\nconst isPagesRouterInternalNavigation = (req: Request) => !!req.headers.get(nextConstants.Headers.NextjsData);\n\n// /**\n//  * In case we want to handle router handlers and server actions differently in the future\n//  */\n// const isApiRouteRequest = (req: Request) => {\n//   return !isPageRequest(req) && !isServerActionRequest(req);\n// };\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB;;AAS1B,SAAS,aAAa,qBAAqB;AAC3C,SAAS,qBAAqB;;;;;AA8BvB,SAAS,cAAc,IAAA,EAmBd;IACd,MAAM,EAAE,gBAAA,EAAkB,UAAA,EAAY,QAAA,EAAU,QAAA,EAAU,OAAA,CAAQ,CAAA,GAAI;IAEtE,OAAQ,OAAA,GAAU,SAAgB;QAhEpC,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;QAiEI,MAAM,sBAAA,CAAA,CAAsB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,kBAAA,KAAA,CAAA,CAAsB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,eAAA;QACpE,MAAM,mBAAmB,sBACrB,KAAA,IACC,IAAA,CAAK,CAAC,CAAA;QAGX,MAAM,qBAAA,CAAA,CAAsB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,kBAAA,KAAA,CAAA,CAAsB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,kBAAA;QACpE,MAAM,kBAAA,CAAA,CAAmB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,eAAA,KAAA,CAAA,CAAmB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,eAAA;QAE9D,MAAM,wBAAwB,MAAM;YAClC,IAAI,oBAAoB;gBACtB,OAAO,SAAS,kBAAkB;YACpC;YACA,IAAI,cAAc,OAAO,GAAG;gBAE1B,OAAO,iBAAiB;YAC1B;YACA,OAAO,SAAS;QAClB;QAEA,MAAM,qBAAqB,MAAM;YAC/B,IAAI,iBAAiB;gBACnB,OAAO,SAAS,eAAe;YACjC;YACA,OAAO,SAAS;QAClB;QAKA,IAAI,WAAW,aAAA,KAAkB,WAAW;YAC1C,OAAO,sBAAsB;QAC/B;QAKA,IAAI,CAAC,WAAW,MAAA,EAAQ;YACtB,OAAO,sBAAsB;QAC/B;QAKA,IAAI,CAAC,kBAAkB;YACrB,OAAO;QACT;QAKA,IAAI,OAAO,qBAAqB,YAAY;YAC1C,IAAI,iBAAiB,WAAW,GAAG,GAAG;gBACpC,OAAO;YACT;YACA,OAAO,mBAAmB;QAC5B;QAKA,IAAI,WAAW,GAAA,CAAI,gBAAgB,GAAG;YACpC,OAAO;QACT;QAEA,OAAO,mBAAmB;IAC5B;AACF;AAEA,MAAM,wBAAwB,CAAC,QAAiB;IAtIhD,IAAA,IAAA;IAuIE,OACE,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,0QAAI,aAAA,CAAc,OAAA,CAAQ,OAAO,KAAA,CAAA,CAAA,CAC9C,KAAA,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,MAAM,CAAA,KAAxC,OAAA,KAAA,IAAA,GAA2C,QAAA,CAAS,mBAAA,KAAA,CAAA,CACnD,KAAA,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,WAAW,CAAA,KAA7C,OAAA,KAAA,IAAA,GAAgD,QAAA,CAAS,sBAAA,KACzD,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,UAAU,CAAA;AAExD;AAEA,MAAM,gBAAgB,CAAC,QAA0B;IA/IjD,IAAA;IAgJE,OACE,IAAI,OAAA,CAAQ,GAAA,CAAI,sRAAA,CAAU,OAAA,CAAQ,YAAY,MAAM,cACpD,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,YAAY,MAAM,YAAA,CAAA,CACpD,KAAA,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,MAAM,CAAA,KAAxC,OAAA,KAAA,IAAA,GAA2C,QAAA,CAAS,YAAA,KACpD,8BAA8B,GAAG,KACjC,gCAAgC,GAAG;AAEvC;AAEA,MAAM,gCAAgC,CAAC,MACpC,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,OAAO,KAAK,CAAC,sBAAsB,GAAG,KAAM,oBAAoB;AAE3G,MAAM,sBAAsB,MAAM;IAChC,MAAM,UAAU,WAAW,KAAA;IAE3B,IAAI,2RAAC,gBAAA,EAAc,OAAO,GAAG;QAC3B,OAAO;IACT;IAEA,MAAM,EAAE,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,QAAQ,oBAAA,CAAqB,EAAE,QAAA,CAAS,KAAK,CAAC;IAEzE,OAAO,QAAA,uBAAA;IAEL,YAAA,uBAAA;IAEE;AAEN;AAEA,MAAM,kCAAkC,CAAC,MAAiB,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4587, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/app-router/server/utils.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\n\nexport const isPrerenderingBailout = (e: unknown) => {\n  if (!(e instanceof Error) || !('message' in e)) {\n    return false;\n  }\n\n  const { message } = e;\n\n  const lowerCaseInput = message.toLowerCase();\n  const dynamicServerUsage = lowerCaseInput.includes('dynamic server usage');\n  const bailOutPrerendering = lowerCaseInput.includes('this page needs to bail out of prerendering');\n\n  // note: new error message syntax introduced in next@14.1.1-canary.21\n  // but we still want to support older versions.\n  // https://github.com/vercel/next.js/pull/61332 (dynamic-rendering.ts:153)\n  const routeRegex = /Route .*? needs to bail out of prerendering at this point because it used .*?./;\n\n  return routeRegex.test(message) || dynamicServerUsage || bailOutPrerendering;\n};\n\nexport async function buildRequestLike(): Promise<NextRequest> {\n  try {\n    // Dynamically import next/headers, otherwise Next12 apps will break\n    // @ts-expect-error: Cannot find module 'next/headers' or its corresponding type declarations.ts(2307)\n    const { headers } = await import('next/headers');\n    const resolvedHeaders = await headers();\n    return new NextRequest('https://placeholder.com', { headers: resolvedHeaders });\n  } catch (e: any) {\n    // rethrow the error when react throws a prerendering bailout\n    // https://nextjs.org/docs/messages/ppr-caught-error\n    if (e && isPrerenderingBailout(e)) {\n      throw e;\n    }\n\n    throw new Error(\n      `Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).\\nIf you're using /pages, try getAuth() instead.\\nOriginal error: ${e}`,\n    );\n  }\n}\n\n// Original source: https://github.com/vercel/next.js/blob/canary/packages/next/src/server/app-render/get-script-nonce-from-header.tsx\nexport function getScriptNonceFromHeader(cspHeaderValue: string): string | undefined {\n  const directives = cspHeaderValue\n    // Directives are split by ';'.\n    .split(';')\n    .map(directive => directive.trim());\n\n  // First try to find the directive for the 'script-src', otherwise try to\n  // fallback to the 'default-src'.\n  const directive =\n    directives.find(dir => dir.startsWith('script-src')) || directives.find(dir => dir.startsWith('default-src'));\n\n  // If no directive could be found, then we're done.\n  if (!directive) {\n    return;\n  }\n\n  // Extract the nonce from the directive\n  const nonce = directive\n    .split(' ')\n    // Remove the 'strict-src'/'default-src' string, this can't be the nonce.\n    .slice(1)\n    .map(source => source.trim())\n    // Find the first source with the 'nonce-' prefix.\n    .find(source => source.startsWith(\"'nonce-\") && source.length > 8 && source.endsWith(\"'\"))\n    // Grab the nonce by trimming the 'nonce-' prefix.\n    ?.slice(7, -1);\n\n  // If we couldn't find the nonce, then we're done.\n  if (!nonce) {\n    return;\n  }\n\n  // Don't accept the nonce value if it contains HTML escape characters.\n  // Technically, the spec requires a base64'd value, but this is just an\n  // extra layer.\n  if (/[&><\\u2028\\u2029]/g.test(nonce)) {\n    throw new Error(\n      'Nonce value from Content-Security-Policy contained invalid HTML escape characters, which is disallowed for security reasons. Make sure that your nonce value does not contain the following characters: `<`, `>`, `&`',\n    );\n  }\n\n  return nonce;\n}\n"], "names": ["directive"], "mappings": ";;;;;AAAA,SAAS,mBAAmB;;;AAErB,MAAM,wBAAwB,CAAC,MAAe;IACnD,IAAI,CAAA,CAAE,aAAa,KAAA,KAAU,CAAA,CAAE,aAAa,CAAA,GAAI;QAC9C,OAAO;IACT;IAEA,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI;IAEpB,MAAM,iBAAiB,QAAQ,WAAA,CAAY;IAC3C,MAAM,qBAAqB,eAAe,QAAA,CAAS,sBAAsB;IACzE,MAAM,sBAAsB,eAAe,QAAA,CAAS,6CAA6C;IAKjG,MAAM,aAAa;IAEnB,OAAO,WAAW,IAAA,CAAK,OAAO,KAAK,sBAAsB;AAC3D;AAEA,eAAsB,mBAAyC;IAC7D,IAAI;QAGF,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,MAAM,OAAO,cAAc;QAC/C,MAAM,kBAAkB,MAAM,QAAQ;QACtC,OAAO,oPAAI,cAAA,CAAY,2BAA2B;YAAE,SAAS;QAAgB,CAAC;IAChF,EAAA,OAAS,GAAQ;QAGf,IAAI,KAAK,sBAAsB,CAAC,GAAG;YACjC,MAAM;QACR;QAEA,MAAM,IAAI,MACR,CAAA;;gBAAA,EAAuK,CAAC,EAAA;IAE5K;AACF;AAGO,SAAS,yBAAyB,cAAA,EAA4C;IA1CrF,IAAA;IA2CE,MAAM,aAAa,eAEhB,KAAA,CAAM,GAAG,EACT,GAAA,CAAI,CAAAA,aAAaA,WAAU,IAAA,CAAK,CAAC;IAIpC,MAAM,YACJ,WAAW,IAAA,CAAK,CAAA,MAAO,IAAI,UAAA,CAAW,YAAY,CAAC,KAAK,WAAW,IAAA,CAAK,CAAA,MAAO,IAAI,UAAA,CAAW,aAAa,CAAC;IAG9G,IAAI,CAAC,WAAW;QACd;IACF;IAGA,MAAM,QAAA,CAAQ,KAAA,UACX,KAAA,CAAM,GAAG,EAET,KAAA,CAAM,CAAC,EACP,GAAA,CAAI,CAAA,SAAU,OAAO,IAAA,CAAK,CAAC,EAE3B,IAAA,CAAK,CAAA,SAAU,OAAO,UAAA,CAAW,SAAS,KAAK,OAAO,MAAA,GAAS,KAAK,OAAO,QAAA,CAAS,GAAG,CAAC,CAAA,KAN7E,OAAA,KAAA,IAAA,GAQV,KAAA,CAAM,GAAG,CAAA;IAGb,IAAI,CAAC,OAAO;QACV;IACF;IAKA,IAAI,qBAAqB,IAAA,CAAK,KAAK,GAAG;QACpC,MAAM,IAAI,MACR;IAEJ;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/app-router/server/auth.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport { constants, createClerkRequest, createRedirect, type RedirectFun } from '@clerk/backend/internal';\nimport type { PendingSessionOptions } from '@clerk/types';\nimport { notFound, redirect } from 'next/navigation';\n\nimport { PUBLISHABLE_KEY, SIGN_IN_URL, SIGN_UP_URL } from '../../server/constants';\nimport { createAsyncGetAuth } from '../../server/createGetAuth';\nimport { authAuthHeaderMissing } from '../../server/errors';\nimport { getAuthKeyFromRequest, getHeader } from '../../server/headers-utils';\nimport type { AuthProtect } from '../../server/protect';\nimport { createProtect } from '../../server/protect';\nimport { decryptClerkRequestData } from '../../server/utils';\nimport { isNextWithUnstableServerActions } from '../../utils/sdk-versions';\nimport { buildRequestLike } from './utils';\n\n/**\n * `Auth` object of the currently active user and the `redirectToSignIn()` method.\n */\ntype Auth = AuthObject & {\n  /**\n   * The `auth()` helper returns the `redirectToSignIn()` method, which you can use to redirect the user to the sign-in page.\n   *\n   * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign in.\n   *\n   * > [!NOTE]\n   * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).\n   */\n  redirectToSignIn: RedirectFun<ReturnType<typeof redirect>>;\n\n  /**\n   * The `auth()` helper returns the `redirectToSignUp()` method, which you can use to redirect the user to the sign-up page.\n   *\n   * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign up.\n   *\n   * > [!NOTE]\n   * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).\n   */\n  redirectToSignUp: RedirectFun<ReturnType<typeof redirect>>;\n};\n\nexport interface AuthFn {\n  (options?: PendingSessionOptions): Promise<Auth>;\n\n  /**\n   * `auth` includes a single property, the `protect()` method, which you can use in two ways:\n   * - to check if a user is authenticated (signed in)\n   * - to check if a user is authorized (has the correct roles or permissions) to access something, such as a component or a route handler\n   *\n   * The following table describes how auth.protect() behaves based on user authentication or authorization status:\n   *\n   * | Authenticated | Authorized | `auth.protect()` will |\n   * | - | - | - |\n   * | Yes | Yes | Return the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object. |\n   * | Yes | No | Return a `404` error. |\n   * | No | No | Redirect the user to the sign-in page\\*. |\n   *\n   * > [!IMPORTANT]\n   * > \\*For non-document requests, such as API requests, `auth.protect()` returns a `404` error to users who aren't authenticated.\n   *\n   * `auth.protect()` can be used to check if a user is authenticated or authorized to access certain parts of your application or even entire routes. See detailed examples in the [dedicated guide](https://clerk.com/docs/organizations/verify-user-permissions).\n   */\n  protect: AuthProtect;\n}\n\n/**\n * The `auth()` helper returns the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object of the currently active user, as well as the [`redirectToSignIn()`](https://clerk.com/docs/references/nextjs/auth#redirect-to-sign-in) method.\n *\n * - Only available for App Router.\n * - Only works on the server-side, such as in Server Components, Route Handlers, and Server Actions.\n * - Requires [`clerkMiddleware()`](https://clerk.com/docs/references/nextjs/clerk-middleware) to be configured.\n */\nexport const auth: AuthFn = async ({ treatPendingAsSignedOut } = {}) => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const request = await buildRequestLike();\n\n  const stepsBasedOnSrcDirectory = async () => {\n    if (isNextWithUnstableServerActions) {\n      return [];\n    }\n\n    try {\n      const isSrcAppDir = await import('../../server/fs/middleware-location.js').then(m => m.hasSrcAppDir());\n      return [`Your Middleware exists at ./${isSrcAppDir ? 'src/' : ''}middleware.(ts|js)`];\n    } catch {\n      return [];\n    }\n  };\n  const authObject = await createAsyncGetAuth({\n    debugLoggerName: 'auth()',\n    noAuthStatusMessage: authAuthHeaderMissing('auth', await stepsBasedOnSrcDirectory()),\n  })(request, { treatPendingAsSignedOut });\n\n  const clerkUrl = getAuthKeyFromRequest(request, 'ClerkUrl');\n\n  const createRedirectForRequest = (...args: Parameters<RedirectFun<never>>) => {\n    const { returnBackUrl } = args[0] || {};\n    const clerkRequest = createClerkRequest(request);\n    const devBrowserToken =\n      clerkRequest.clerkUrl.searchParams.get(constants.QueryParameters.DevBrowser) ||\n      clerkRequest.cookies.get(constants.Cookies.DevBrowser);\n\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n    return [\n      createRedirect({\n        redirectAdapter: redirect,\n        devBrowserToken: devBrowserToken,\n        baseUrl: clerkRequest.clerkUrl.toString(),\n        publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n        signInUrl: decryptedRequestData.signInUrl || SIGN_IN_URL,\n        signUpUrl: decryptedRequestData.signUpUrl || SIGN_UP_URL,\n        sessionStatus: authObject.sessionStatus,\n      }),\n      returnBackUrl === null ? '' : returnBackUrl || clerkUrl?.toString(),\n    ] as const;\n  };\n\n  const redirectToSignIn: RedirectFun<never> = (opts = {}) => {\n    const [r, returnBackUrl] = createRedirectForRequest(opts);\n    return r.redirectToSignIn({\n      returnBackUrl,\n    });\n  };\n\n  const redirectToSignUp: RedirectFun<never> = (opts = {}) => {\n    const [r, returnBackUrl] = createRedirectForRequest(opts);\n    return r.redirectToSignUp({\n      returnBackUrl,\n    });\n  };\n\n  return Object.assign(authObject, { redirectToSignIn, redirectToSignUp });\n};\n\nauth.protect = async (...args: any[]) => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const request = await buildRequestLike();\n  const authObject = await auth();\n\n  const protect = createProtect({\n    request,\n    authObject,\n    redirectToSignIn: authObject.redirectToSignIn,\n    notFound,\n    redirect,\n  });\n\n  return protect(...args);\n};\n"], "names": [], "mappings": ";;;AACA,SAAS,WAAW,oBAAoB,sBAAwC;;;AAEhF,SAAS,UAAU,gBAAgB;;AAEnC,SAAS,iBAAiB,aAAa,mBAAmB;AAC1D,SAAS,0BAA0B;AACnC,SAAS,6BAA6B;AACtC,SAAS,uBAAuB,iBAAiB;AAEjD,SAAS,qBAAqB;AAC9B,SAAS,+BAA+B;AACxC,SAAS,uCAAuC;AAChD,SAAS,wBAAwB;;;;;;;;;;;;AA0D1B,MAAM,OAAe,OAAO,EAAE,uBAAA,CAAwB,CAAA,GAAI,CAAC,CAAA,KAAM;;IAItE,MAAM,UAAU,2SAAM,mBAAA,CAAiB;IAEvC,MAAM,2BAA2B,YAAY;QAC3C,6RAAI,kCAAA,EAAiC;YACnC,OAAO,CAAC,CAAA;QACV;QAEA,IAAI;YACF,MAAM,cAAc,MAAM,OAAO,wCAAwC,2MAAE,IAAA,CAAK,CAAA,IAAK,EAAE,YAAA,CAAa,CAAC;YACrG,OAAO;gBAAC,CAAA,4BAAA,EAA+B,cAAc,SAAS,EAAE,CAAA,kBAAA,CAAoB;aAAA;QACtF,EAAA,OAAQ;YACN,OAAO,CAAC,CAAA;QACV;IACF;IACA,MAAM,aAAa,UAAM,6SAAA,EAAmB;QAC1C,iBAAiB;QACjB,0SAAqB,wBAAA,EAAsB,QAAQ,MAAM,yBAAyB,CAAC;IACrF,CAAC,EAAE,SAAS;QAAE;IAAwB,CAAC;IAEvC,MAAM,YAAW,sTAAA,EAAsB,SAAS,UAAU;IAE1D,MAAM,2BAA2B,CAAA,GAAI,SAAyC;QAC5E,MAAM,EAAE,aAAA,CAAc,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA,IAAK,CAAC;QACtC,MAAM,mBAAe,+RAAA,EAAmB,OAAO;QAC/C,MAAM,kBACJ,aAAa,QAAA,CAAS,YAAA,CAAa,GAAA,2QAAI,YAAA,CAAU,eAAA,CAAgB,UAAU,KAC3E,aAAa,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,UAAU;QAEvD,MAAM,sTAAuB,YAAA,EAAU,mRAAS,YAAA,CAAU,OAAA,CAAQ,gBAAgB;QAClF,MAAM,2SAAuB,0BAAA,EAAwB,oBAAoB;QACzE,OAAO;aACL,qSAAA,EAAe;gBACb,wTAAiB,WAAA;gBACjB;gBACA,SAAS,aAAa,QAAA,CAAS,QAAA,CAAS;gBACxC,gBAAgB,qBAAqB,cAAA,wRAAkB,kBAAA;gBACvD,WAAW,qBAAqB,SAAA,wRAAa,cAAA;gBAC7C,WAAW,qBAAqB,SAAA,wRAAa,cAAA;gBAC7C,eAAe,WAAW,aAAA;YAC5B,CAAC;YACD,kBAAkB,OAAO,KAAK,iBAAA,CAAiB,YAAA,OAAA,KAAA,IAAA,SAAU,QAAA,EAAA;SAC3D;IACF;IAEA,MAAM,mBAAuC,CAAC,OAAO,CAAC,CAAA,KAAM;QAC1D,MAAM,CAAC,GAAG,aAAa,CAAA,GAAI,yBAAyB,IAAI;QACxD,OAAO,EAAE,gBAAA,CAAiB;YACxB;QACF,CAAC;IACH;IAEA,MAAM,mBAAuC,CAAC,OAAO,CAAC,CAAA,KAAM;QAC1D,MAAM,CAAC,GAAG,aAAa,CAAA,GAAI,yBAAyB,IAAI;QACxD,OAAO,EAAE,gBAAA,CAAiB;YACxB;QACF,CAAC;IACH;IAEA,OAAO,OAAO,MAAA,CAAO,YAAY;QAAE;QAAkB;IAAiB,CAAC;AACzE;AAEA,KAAK,OAAA,GAAU,OAAA,GAAU,SAAgB;;IAIvC,MAAM,UAAU,2SAAM,mBAAA,CAAiB;IACvC,MAAM,aAAa,MAAM,KAAK;IAE9B,MAAM,gSAAU,gBAAA,EAAc;QAC5B;QACA;QACA,kBAAkB,WAAW,gBAAA;yTAC7B,WAAA;yTACA,WAAA;IACF,CAAC;IAED,OAAO,QAAQ,GAAG,IAAI;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/node-gyp-build%404.8.4/node_modules/node-gyp-build/node-gyp-build.js"], "sourcesContent": ["var fs = require('fs')\nvar path = require('path')\nvar os = require('os')\n\n// Workaround to fix webpack's build warnings: 'the request of a dependency is an expression'\nvar runtimeRequire = typeof __webpack_require__ === 'function' ? __non_webpack_require__ : require // eslint-disable-line\n\nvar vars = (process.config && process.config.variables) || {}\nvar prebuildsOnly = !!process.env.PREBUILDS_ONLY\nvar abi = process.versions.modules // TODO: support old node where this is undef\nvar runtime = isElectron() ? 'electron' : (isNwjs() ? 'node-webkit' : 'node')\n\nvar arch = process.env.npm_config_arch || os.arch()\nvar platform = process.env.npm_config_platform || os.platform()\nvar libc = process.env.LIBC || (isAlpine(platform) ? 'musl' : 'glibc')\nvar armv = process.env.ARM_VERSION || (arch === 'arm64' ? '8' : vars.arm_version) || ''\nvar uv = (process.versions.uv || '').split('.')[0]\n\nmodule.exports = load\n\nfunction load (dir) {\n  return runtimeRequire(load.resolve(dir))\n}\n\nload.resolve = load.path = function (dir) {\n  dir = path.resolve(dir || '.')\n\n  try {\n    var name = runtimeRequire(path.join(dir, 'package.json')).name.toUpperCase().replace(/-/g, '_')\n    if (process.env[name + '_PREBUILD']) dir = process.env[name + '_PREBUILD']\n  } catch (err) {}\n\n  if (!prebuildsOnly) {\n    var release = getFirst(path.join(dir, 'build/Release'), matchBuild)\n    if (release) return release\n\n    var debug = getFirst(path.join(dir, 'build/Debug'), matchBuild)\n    if (debug) return debug\n  }\n\n  var prebuild = resolve(dir)\n  if (prebuild) return prebuild\n\n  var nearby = resolve(path.dirname(process.execPath))\n  if (nearby) return nearby\n\n  var target = [\n    'platform=' + platform,\n    'arch=' + arch,\n    'runtime=' + runtime,\n    'abi=' + abi,\n    'uv=' + uv,\n    armv ? 'armv=' + armv : '',\n    'libc=' + libc,\n    'node=' + process.versions.node,\n    process.versions.electron ? 'electron=' + process.versions.electron : '',\n    typeof __webpack_require__ === 'function' ? 'webpack=true' : '' // eslint-disable-line\n  ].filter(Boolean).join(' ')\n\n  throw new Error('No native build was found for ' + target + '\\n    loaded from: ' + dir + '\\n')\n\n  function resolve (dir) {\n    // Find matching \"prebuilds/<platform>-<arch>\" directory\n    var tuples = readdirSync(path.join(dir, 'prebuilds')).map(parseTuple)\n    var tuple = tuples.filter(matchTuple(platform, arch)).sort(compareTuples)[0]\n    if (!tuple) return\n\n    // Find most specific flavor first\n    var prebuilds = path.join(dir, 'prebuilds', tuple.name)\n    var parsed = readdirSync(prebuilds).map(parseTags)\n    var candidates = parsed.filter(matchTags(runtime, abi))\n    var winner = candidates.sort(compareTags(runtime))[0]\n    if (winner) return path.join(prebuilds, winner.file)\n  }\n}\n\nfunction readdirSync (dir) {\n  try {\n    return fs.readdirSync(dir)\n  } catch (err) {\n    return []\n  }\n}\n\nfunction getFirst (dir, filter) {\n  var files = readdirSync(dir).filter(filter)\n  return files[0] && path.join(dir, files[0])\n}\n\nfunction matchBuild (name) {\n  return /\\.node$/.test(name)\n}\n\nfunction parseTuple (name) {\n  // Example: darwin-x64+arm64\n  var arr = name.split('-')\n  if (arr.length !== 2) return\n\n  var platform = arr[0]\n  var architectures = arr[1].split('+')\n\n  if (!platform) return\n  if (!architectures.length) return\n  if (!architectures.every(Boolean)) return\n\n  return { name, platform, architectures }\n}\n\nfunction matchTuple (platform, arch) {\n  return function (tuple) {\n    if (tuple == null) return false\n    if (tuple.platform !== platform) return false\n    return tuple.architectures.includes(arch)\n  }\n}\n\nfunction compareTuples (a, b) {\n  // Prefer single-arch prebuilds over multi-arch\n  return a.architectures.length - b.architectures.length\n}\n\nfunction parseTags (file) {\n  var arr = file.split('.')\n  var extension = arr.pop()\n  var tags = { file: file, specificity: 0 }\n\n  if (extension !== 'node') return\n\n  for (var i = 0; i < arr.length; i++) {\n    var tag = arr[i]\n\n    if (tag === 'node' || tag === 'electron' || tag === 'node-webkit') {\n      tags.runtime = tag\n    } else if (tag === 'napi') {\n      tags.napi = true\n    } else if (tag.slice(0, 3) === 'abi') {\n      tags.abi = tag.slice(3)\n    } else if (tag.slice(0, 2) === 'uv') {\n      tags.uv = tag.slice(2)\n    } else if (tag.slice(0, 4) === 'armv') {\n      tags.armv = tag.slice(4)\n    } else if (tag === 'glibc' || tag === 'musl') {\n      tags.libc = tag\n    } else {\n      continue\n    }\n\n    tags.specificity++\n  }\n\n  return tags\n}\n\nfunction matchTags (runtime, abi) {\n  return function (tags) {\n    if (tags == null) return false\n    if (tags.runtime && tags.runtime !== runtime && !runtimeAgnostic(tags)) return false\n    if (tags.abi && tags.abi !== abi && !tags.napi) return false\n    if (tags.uv && tags.uv !== uv) return false\n    if (tags.armv && tags.armv !== armv) return false\n    if (tags.libc && tags.libc !== libc) return false\n\n    return true\n  }\n}\n\nfunction runtimeAgnostic (tags) {\n  return tags.runtime === 'node' && tags.napi\n}\n\nfunction compareTags (runtime) {\n  // Precedence: non-agnostic runtime, abi over napi, then by specificity.\n  return function (a, b) {\n    if (a.runtime !== b.runtime) {\n      return a.runtime === runtime ? -1 : 1\n    } else if (a.abi !== b.abi) {\n      return a.abi ? -1 : 1\n    } else if (a.specificity !== b.specificity) {\n      return a.specificity > b.specificity ? -1 : 1\n    } else {\n      return 0\n    }\n  }\n}\n\nfunction isNwjs () {\n  return !!(process.versions && process.versions.nw)\n}\n\nfunction isElectron () {\n  if (process.versions && process.versions.electron) return true\n  if (process.env.ELECTRON_RUN_AS_NODE) return true\n  return typeof window !== 'undefined' && window.process && window.process.type === 'renderer'\n}\n\nfunction isAlpine (platform) {\n  return platform === 'linux' && fs.existsSync('/etc/alpine-release')\n}\n\n// Exposed for unit tests\n// TODO: move to lib\nload.parseTags = parseTags\nload.matchTags = matchTags\nload.compareTags = compareTags\nload.parseTuple = parseTuple\nload.matchTuple = matchTuple\nload.compareTuples = compareTuples\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,6FAA6F;AAC7F,IAAI,iBAAiB,OAAO,wBAAwB,aAAa,oFAAkC,sBAAsB;;AAEzH,IAAI,OAAO,AAAC,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,SAAS,IAAK,CAAC;AAC5D,IAAI,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;AAChD,IAAI,MAAM,QAAQ,QAAQ,CAAC,OAAO,CAAC,6CAA6C;;AAChF,IAAI,UAAU,eAAe,aAAc,WAAW,gBAAgB;AAEtE,IAAI,OAAO,QAAQ,GAAG,CAAC,eAAe,IAAI,GAAG,IAAI;AACjD,IAAI,WAAW,QAAQ,GAAG,CAAC,mBAAmB,IAAI,GAAG,QAAQ;AAC7D,IAAI,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,YAAY,SAAS,OAAO;AACrE,IAAI,OAAO,QAAQ,GAAG,CAAC,WAAW,IAAI,CAAC,SAAS,UAAU,MAAM,KAAK,WAAW,KAAK;AACrF,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;AAElD,OAAO,OAAO,GAAG;AAEjB,SAAS,KAAM,GAAG;IAChB,OAAO,eAAe,KAAK,OAAO,CAAC;AACrC;AAEA,KAAK,OAAO,GAAG,KAAK,IAAI,GAAG,SAAU,GAAG;IACtC,MAAM,KAAK,OAAO,CAAC,OAAO;IAE1B,IAAI;QACF,IAAI,OAAO,eAAe,KAAK,IAAI,CAAC,KAAK,iBAAiB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM;QAC3F,IAAI,QAAQ,GAAG,CAAC,OAAO,YAAY,EAAE,MAAM,QAAQ,GAAG,CAAC,OAAO,YAAY;IAC5E,EAAE,OAAO,KAAK,CAAC;IAEf,IAAI,CAAC,eAAe;QAClB,IAAI,UAAU,SAAS,KAAK,IAAI,CAAC,KAAK,kBAAkB;QACxD,IAAI,SAAS,OAAO;QAEpB,IAAI,QAAQ,SAAS,KAAK,IAAI,CAAC,KAAK,gBAAgB;QACpD,IAAI,OAAO,OAAO;IACpB;IAEA,IAAI,WAAW,QAAQ;IACvB,IAAI,UAAU,OAAO;IAErB,IAAI,SAAS,QAAQ,KAAK,OAAO,CAAC,QAAQ,QAAQ;IAClD,IAAI,QAAQ,OAAO;IAEnB,IAAI,SAAS;QACX,cAAc;QACd,UAAU;QACV,aAAa;QACb,SAAS;QACT,QAAQ;QACR,OAAO,UAAU,OAAO;QACxB,UAAU;QACV,UAAU,QAAQ,QAAQ,CAAC,IAAI;QAC/B,QAAQ,QAAQ,CAAC,QAAQ,GAAG,cAAc,QAAQ,QAAQ,CAAC,QAAQ,GAAG;QACtE,OAAO,wBAAwB,aAAa,iBAAiB,GAAG,sBAAsB;KACvF,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,MAAM,IAAI,MAAM,mCAAmC,SAAS,wBAAwB,MAAM;IAE1F,SAAS,QAAS,GAAG;QACnB,wDAAwD;QACxD,IAAI,SAAS,YAAY,KAAK,IAAI,CAAC,KAAK,cAAc,GAAG,CAAC;QAC1D,IAAI,QAAQ,OAAO,MAAM,CAAC,WAAW,UAAU,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE;QAC5E,IAAI,CAAC,OAAO;QAEZ,kCAAkC;QAClC,IAAI,YAAY,KAAK,IAAI,CAAC,KAAK,aAAa,MAAM,IAAI;QACtD,IAAI,SAAS,YAAY,WAAW,GAAG,CAAC;QACxC,IAAI,aAAa,OAAO,MAAM,CAAC,UAAU,SAAS;QAClD,IAAI,SAAS,WAAW,IAAI,CAAC,YAAY,SAAS,CAAC,EAAE;QACrD,IAAI,QAAQ,OAAO,KAAK,IAAI,CAAC,WAAW,OAAO,IAAI;IACrD;AACF;AAEA,SAAS,YAAa,GAAG;IACvB,IAAI;QACF,OAAO,GAAG,WAAW,CAAC;IACxB,EAAE,OAAO,KAAK;QACZ,OAAO,EAAE;IACX;AACF;AAEA,SAAS,SAAU,GAAG,EAAE,MAAM;IAC5B,IAAI,QAAQ,YAAY,KAAK,MAAM,CAAC;IACpC,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;AAC5C;AAEA,SAAS,WAAY,IAAI;IACvB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,SAAS,WAAY,IAAI;IACvB,4BAA4B;IAC5B,IAAI,MAAM,KAAK,KAAK,CAAC;IACrB,IAAI,IAAI,MAAM,KAAK,GAAG;IAEtB,IAAI,WAAW,GAAG,CAAC,EAAE;IACrB,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC;IAEjC,IAAI,CAAC,UAAU;IACf,IAAI,CAAC,cAAc,MAAM,EAAE;IAC3B,IAAI,CAAC,cAAc,KAAK,CAAC,UAAU;IAEnC,OAAO;QAAE;QAAM;QAAU;IAAc;AACzC;AAEA,SAAS,WAAY,QAAQ,EAAE,IAAI;IACjC,OAAO,SAAU,KAAK;QACpB,IAAI,SAAS,MAAM,OAAO;QAC1B,IAAI,MAAM,QAAQ,KAAK,UAAU,OAAO;QACxC,OAAO,MAAM,aAAa,CAAC,QAAQ,CAAC;IACtC;AACF;AAEA,SAAS,cAAe,CAAC,EAAE,CAAC;IAC1B,+CAA+C;IAC/C,OAAO,EAAE,aAAa,CAAC,MAAM,GAAG,EAAE,aAAa,CAAC,MAAM;AACxD;AAEA,SAAS,UAAW,IAAI;IACtB,IAAI,MAAM,KAAK,KAAK,CAAC;IACrB,IAAI,YAAY,IAAI,GAAG;IACvB,IAAI,OAAO;QAAE,MAAM;QAAM,aAAa;IAAE;IAExC,IAAI,cAAc,QAAQ;IAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,MAAM,GAAG,CAAC,EAAE;QAEhB,IAAI,QAAQ,UAAU,QAAQ,cAAc,QAAQ,eAAe;YACjE,KAAK,OAAO,GAAG;QACjB,OAAO,IAAI,QAAQ,QAAQ;YACzB,KAAK,IAAI,GAAG;QACd,OAAO,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,OAAO;YACpC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC;QACvB,OAAO,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,MAAM;YACnC,KAAK,EAAE,GAAG,IAAI,KAAK,CAAC;QACtB,OAAO,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,QAAQ;YACrC,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC;QACxB,OAAO,IAAI,QAAQ,WAAW,QAAQ,QAAQ;YAC5C,KAAK,IAAI,GAAG;QACd,OAAO;YACL;QACF;QAEA,KAAK,WAAW;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,UAAW,OAAO,EAAE,GAAG;IAC9B,OAAO,SAAU,IAAI;QACnB,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,KAAK,WAAW,CAAC,gBAAgB,OAAO,OAAO;QAC/E,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK,OAAO,CAAC,KAAK,IAAI,EAAE,OAAO;QACvD,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,KAAK,IAAI,OAAO;QACtC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,OAAO;QAC5C,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,OAAO;QAE5C,OAAO;IACT;AACF;AAEA,SAAS,gBAAiB,IAAI;IAC5B,OAAO,KAAK,OAAO,KAAK,UAAU,KAAK,IAAI;AAC7C;AAEA,SAAS,YAAa,OAAO;IAC3B,wEAAwE;IACxE,OAAO,SAAU,CAAC,EAAE,CAAC;QACnB,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE;YAC3B,OAAO,EAAE,OAAO,KAAK,UAAU,CAAC,IAAI;QACtC,OAAO,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE;YAC1B,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI;QACtB,OAAO,IAAI,EAAE,WAAW,KAAK,EAAE,WAAW,EAAE;YAC1C,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI;QAC9C,OAAO;YACL,OAAO;QACT;IACF;AACF;AAEA,SAAS;IACP,OAAO,CAAC,CAAC,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,EAAE;AACnD;AAEA,SAAS;IACP,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,QAAQ,EAAE,OAAO;IAC1D,IAAI,QAAQ,GAAG,CAAC,oBAAoB,EAAE,OAAO;IAC7C,OAAO,gBAAkB,eAAe,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK;AACpF;AAEA,SAAS,SAAU,QAAQ;IACzB,OAAO,aAAa,WAAW,GAAG,UAAU,CAAC;AAC/C;AAEA,yBAAyB;AACzB,oBAAoB;AACpB,KAAK,SAAS,GAAG;AACjB,KAAK,SAAS,GAAG;AACjB,KAAK,WAAW,GAAG;AACnB,KAAK,UAAU,GAAG;AAClB,KAAK,UAAU,GAAG;AAClB,KAAK,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/node-gyp-build%404.8.4/node_modules/node-gyp-build/index.js"], "sourcesContent": ["const runtimeRequire = typeof __webpack_require__ === 'function' ? __non_webpack_require__ : require // eslint-disable-line\nif (typeof runtimeRequire.addon === 'function') { // if the platform supports native resolving prefer that\n  module.exports = runtimeRequire.addon.bind(runtimeRequire)\n} else { // else use the runtime version here\n  module.exports = require('./node-gyp-build.js')\n}\n"], "names": [], "mappings": "AAAA,MAAM,iBAAiB,OAAO,wBAAwB,aAAa,oFAAkC,sBAAsB;;AAC3H,IAAI,OAAO,eAAe,KAAK,KAAK,YAAY;IAC9C,OAAO,OAAO,GAAG,eAAe,KAAK,CAAC,IAAI,CAAC;AAC7C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/bufferutil%404.0.9/node_modules/bufferutil/fallback.js"], "sourcesContent": ["'use strict';\n\n/**\n * Masks a buffer using the given mask.\n *\n * @param {<PERSON><PERSON><PERSON>} source The buffer to mask\n * @param {<PERSON><PERSON><PERSON>} mask The mask to use\n * @param {<PERSON><PERSON><PERSON>} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */\nconst mask = (source, mask, output, offset, length) => {\n  for (var i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask[i & 3];\n  }\n};\n\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {<PERSON><PERSON>er} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */\nconst unmask = (buffer, mask) => {\n  // Required until https://github.com/nodejs/node/issues/9006 is resolved.\n  const length = buffer.length;\n  for (var i = 0; i < length; i++) {\n    buffer[i] ^= mask[i & 3];\n  }\n};\n\nmodule.exports = { mask, unmask };\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;;;;CASC,GACD,MAAM,OAAO,CAAC,QAAQ,MAAM,QAAQ,QAAQ;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;IAC9C;AACF;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,CAAC,QAAQ;IACtB,yEAAyE;IACzE,MAAM,SAAS,OAAO,MAAM;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;IAC1B;AACF;AAEA,OAAO,OAAO,GAAG;IAAE;IAAM;AAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/bufferutil%404.0.9/node_modules/bufferutil/index.js"], "sourcesContent": ["'use strict';\n\ntry {\n  module.exports = require('node-gyp-build')(__dirname);\n} catch (e) {\n  module.exports = require('./fallback');\n}\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;IACF,OAAO,OAAO,GAAG,2IAA0B;AAC7C,EAAE,OAAO,GAAG;IACV,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40t3-oss%2Benv-core%400.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/%40t3-oss/env-core/dist/src-Cq4nGjdj.js"], "sourcesContent": ["//#region src/standard.ts\nfunction ensureSynchronous(value, message) {\n\tif (value instanceof Promise) throw new Error(message);\n}\nfunction parseWithDictionary(dictionary, value) {\n\tconst result = {};\n\tconst issues = [];\n\tfor (const key in dictionary) {\n\t\tconst propResult = dictionary[key][\"~standard\"].validate(value[key]);\n\t\tensureSynchronous(propResult, `Validation must be synchronous, but ${key} returned a Promise.`);\n\t\tif (propResult.issues) {\n\t\t\tissues.push(...propResult.issues.map((issue) => ({\n\t\t\t\t...issue,\n\t\t\t\tpath: [key, ...issue.path ?? []]\n\t\t\t})));\n\t\t\tcontinue;\n\t\t}\n\t\tresult[key] = propResult.value;\n\t}\n\tif (issues.length) return { issues };\n\treturn { value: result };\n}\n\n//#endregion\n//#region src/index.ts\n/**\n* Create a new environment variable schema.\n*/\nfunction createEnv(opts) {\n\tconst runtimeEnv = opts.runtimeEnvStrict ?? opts.runtimeEnv ?? process.env;\n\tconst emptyStringAsUndefined = opts.emptyStringAsUndefined ?? false;\n\tif (emptyStringAsUndefined) {\n\t\tfor (const [key, value] of Object.entries(runtimeEnv)) if (value === \"\") delete runtimeEnv[key];\n\t}\n\tconst skip = !!opts.skipValidation;\n\tif (skip) return runtimeEnv;\n\tconst _client = typeof opts.client === \"object\" ? opts.client : {};\n\tconst _server = typeof opts.server === \"object\" ? opts.server : {};\n\tconst _shared = typeof opts.shared === \"object\" ? opts.shared : {};\n\tconst isServer = opts.isServer ?? (typeof window === \"undefined\" || \"Deno\" in window);\n\tconst finalSchemaShape = isServer ? {\n\t\t..._server,\n\t\t..._shared,\n\t\t..._client\n\t} : {\n\t\t..._client,\n\t\t..._shared\n\t};\n\tconst parsed = opts.createFinalSchema?.(finalSchemaShape, isServer)[\"~standard\"].validate(runtimeEnv) ?? parseWithDictionary(finalSchemaShape, runtimeEnv);\n\tensureSynchronous(parsed, \"Validation must be synchronous\");\n\tconst onValidationError = opts.onValidationError ?? ((issues) => {\n\t\tconsole.error(\"❌ Invalid environment variables:\", issues);\n\t\tthrow new Error(\"Invalid environment variables\");\n\t});\n\tconst onInvalidAccess = opts.onInvalidAccess ?? (() => {\n\t\tthrow new Error(\"❌ Attempted to access a server-side environment variable on the client\");\n\t});\n\tif (parsed.issues) return onValidationError(parsed.issues);\n\tconst isServerAccess = (prop) => {\n\t\tif (!opts.clientPrefix) return true;\n\t\treturn !prop.startsWith(opts.clientPrefix) && !(prop in _shared);\n\t};\n\tconst isValidServerAccess = (prop) => {\n\t\treturn isServer || !isServerAccess(prop);\n\t};\n\tconst ignoreProp = (prop) => {\n\t\treturn prop === \"__esModule\" || prop === \"$$typeof\";\n\t};\n\tconst extendedObj = (opts.extends ?? []).reduce((acc, curr) => {\n\t\treturn Object.assign(acc, curr);\n\t}, {});\n\tconst fullObj = Object.assign(extendedObj, parsed.value);\n\tconst env = new Proxy(fullObj, { get(target, prop) {\n\t\tif (typeof prop !== \"string\") return void 0;\n\t\tif (ignoreProp(prop)) return void 0;\n\t\tif (!isValidServerAccess(prop)) return onInvalidAccess(prop);\n\t\treturn Reflect.get(target, prop);\n\t} });\n\treturn env;\n}\n\n//#endregion\nexport { createEnv };"], "names": [], "mappings": "AAAA,yBAAyB;;;;AACzB,SAAS,kBAAkB,KAAK,EAAE,OAAO;IACxC,IAAI,iBAAiB,SAAS,MAAM,IAAI,MAAM;AAC/C;AACA,SAAS,oBAAoB,UAAU,EAAE,KAAK;IAC7C,MAAM,SAAS,CAAC;IAChB,MAAM,SAAS,EAAE;IACjB,IAAK,MAAM,OAAO,WAAY;QAC7B,MAAM,aAAa,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI;QACnE,kBAAkB,YAAY,CAAC,oCAAoC,EAAE,IAAI,oBAAoB,CAAC;QAC9F,IAAI,WAAW,MAAM,EAAE;YACtB,OAAO,IAAI,IAAI,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,QAAU,CAAC;oBAChD,GAAG,KAAK;oBACR,MAAM;wBAAC;2BAAQ,MAAM,IAAI,IAAI,EAAE;qBAAC;gBACjC,CAAC;YACD;QACD;QACA,MAAM,CAAC,IAAI,GAAG,WAAW,KAAK;IAC/B;IACA,IAAI,OAAO,MAAM,EAAE,OAAO;QAAE;IAAO;IACnC,OAAO;QAAE,OAAO;IAAO;AACxB;AAEA,YAAY;AACZ,sBAAsB;AACtB;;AAEA,GACA,SAAS,UAAU,IAAI;IACtB,MAAM,aAAa,KAAK,gBAAgB,IAAI,KAAK,UAAU,IAAI,QAAQ,GAAG;IAC1E,MAAM,yBAAyB,KAAK,sBAAsB,IAAI;IAC9D,IAAI,wBAAwB;QAC3B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,YAAa,IAAI,UAAU,IAAI,OAAO,UAAU,CAAC,IAAI;IAChG;IACA,MAAM,OAAO,CAAC,CAAC,KAAK,cAAc;IAClC,IAAI,MAAM,OAAO;IACjB,MAAM,UAAU,OAAO,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,GAAG,CAAC;IACjE,MAAM,UAAU,OAAO,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,GAAG,CAAC;IACjE,MAAM,UAAU,OAAO,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,GAAG,CAAC;IACjE,MAAM,WAAW,KAAK,QAAQ,IAAI,CAAC,gBAAkB,eAAe,UAAU,MAAM;IACpF,MAAM,mBAAmB,WAAW;QACnC,GAAG,OAAO;QACV,GAAG,OAAO;QACV,GAAG,OAAO;IACX,IAAI;QACH,GAAG,OAAO;QACV,GAAG,OAAO;IACX;IACA,MAAM,SAAS,KAAK,iBAAiB,GAAG,kBAAkB,SAAS,CAAC,YAAY,CAAC,SAAS,eAAe,oBAAoB,kBAAkB;IAC/I,kBAAkB,QAAQ;IAC1B,MAAM,oBAAoB,KAAK,iBAAiB,IAAI,CAAC,CAAC;QACrD,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IACjB,CAAC;IACD,MAAM,kBAAkB,KAAK,eAAe,IAAI,CAAC;QAChD,MAAM,IAAI,MAAM;IACjB,CAAC;IACD,IAAI,OAAO,MAAM,EAAE,OAAO,kBAAkB,OAAO,MAAM;IACzD,MAAM,iBAAiB,CAAC;QACvB,IAAI,CAAC,KAAK,YAAY,EAAE,OAAO;QAC/B,OAAO,CAAC,KAAK,UAAU,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,QAAQ,OAAO;IAChE;IACA,MAAM,sBAAsB,CAAC;QAC5B,OAAO,YAAY,CAAC,eAAe;IACpC;IACA,MAAM,aAAa,CAAC;QACnB,OAAO,SAAS,gBAAgB,SAAS;IAC1C;IACA,MAAM,cAAc,CAAC,KAAK,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,KAAK;QACrD,OAAO,OAAO,MAAM,CAAC,KAAK;IAC3B,GAAG,CAAC;IACJ,MAAM,UAAU,OAAO,MAAM,CAAC,aAAa,OAAO,KAAK;IACvD,MAAM,MAAM,IAAI,MAAM,SAAS;QAAE,KAAI,MAAM,EAAE,IAAI;YAChD,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK;YAC1C,IAAI,WAAW,OAAO,OAAO,KAAK;YAClC,IAAI,CAAC,oBAAoB,OAAO,OAAO,gBAAgB;YACvD,OAAO,QAAQ,GAAG,CAAC,QAAQ;QAC5B;IAAE;IACF,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40t3-oss%2Benv-core%400.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/%40t3-oss/env-core/dist/index.js"], "sourcesContent": ["import { createEnv } from \"./src-Cq4nGjdj.js\";\n\nexport { createEnv };"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40t3-oss%2Benv-nextjs%400.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/%40t3-oss/env-nextjs/dist/index.js"], "sourcesContent": ["import { createEnv as createEnv$1 } from \"@t3-oss/env-core\";\n\n//#region src/index.ts\nconst CLIENT_PREFIX = \"NEXT_PUBLIC_\";\n/**\n* Create a new environment variable schema.\n*/\nfunction createEnv(opts) {\n\tconst client = typeof opts.client === \"object\" ? opts.client : {};\n\tconst server = typeof opts.server === \"object\" ? opts.server : {};\n\tconst shared = opts.shared;\n\tconst runtimeEnv = opts.runtimeEnv ? opts.runtimeEnv : {\n\t\t...process.env,\n\t\t...opts.experimental__runtimeEnv\n\t};\n\treturn createEnv$1({\n\t\t...opts,\n\t\tshared,\n\t\tclient,\n\t\tserver,\n\t\tclientPrefix: CLIENT_PREFIX,\n\t\truntimeEnv\n\t});\n}\n\n//#endregion\nexport { createEnv };"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,sBAAsB;AACtB,MAAM,gBAAgB;AACtB;;AAEA,GACA,SAAS,UAAU,IAAI;IACtB,MAAM,SAAS,OAAO,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,GAAG,CAAC;IAChE,MAAM,SAAS,OAAO,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,GAAG,CAAC;IAChE,MAAM,SAAS,KAAK,MAAM;IAC1B,MAAM,aAAa,KAAK,UAAU,GAAG,KAAK,UAAU,GAAG;QACtD,GAAG,QAAQ,GAAG;QACd,GAAG,KAAK,wBAAwB;IACjC;IACA,OAAO,CAAA,GAAA,oRAAA,CAAA,YAAW,AAAD,EAAE;QAClB,GAAG,IAAI;QACP;QACA;QACA;QACA,cAAc;QACd;IACD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40prisma%2Bdebug%406.4.1/node_modules/%40prisma/debug/dist/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// ../../node_modules/.pnpm/kleur@4.1.5/node_modules/kleur/colors.mjs\nvar colors_exports = {};\n__export(colors_exports, {\n  $: () => $,\n  bgBlack: () => bgBlack,\n  bgBlue: () => bgBlue,\n  bgCyan: () => bgCyan,\n  bgGreen: () => bgGreen,\n  bgMagenta: () => bgMagenta,\n  bgRed: () => bgRed,\n  bgWhite: () => bgWhite,\n  bgYellow: () => bgYellow,\n  black: () => black,\n  blue: () => blue,\n  bold: () => bold,\n  cyan: () => cyan,\n  dim: () => dim,\n  gray: () => gray,\n  green: () => green,\n  grey: () => grey,\n  hidden: () => hidden,\n  inverse: () => inverse,\n  italic: () => italic,\n  magenta: () => magenta,\n  red: () => red,\n  reset: () => reset,\n  strikethrough: () => strikethrough,\n  underline: () => underline,\n  white: () => white,\n  yellow: () => yellow\n});\nvar FORCE_COLOR;\nvar NODE_DISABLE_COLORS;\nvar NO_COLOR;\nvar TERM;\nvar isTTY = true;\nif (typeof process !== \"undefined\") {\n  ({ FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM } = process.env || {});\n  isTTY = process.stdout && process.stdout.isTTY;\n}\nvar $ = {\n  enabled: !NODE_DISABLE_COLORS && NO_COLOR == null && TERM !== \"dumb\" && (FORCE_COLOR != null && FORCE_COLOR !== \"0\" || isTTY)\n};\nfunction init(x, y) {\n  let rgx = new RegExp(`\\\\x1b\\\\[${y}m`, \"g\");\n  let open = `\\x1B[${x}m`, close = `\\x1B[${y}m`;\n  return function(txt) {\n    if (!$.enabled || txt == null) return txt;\n    return open + (!!~(\"\" + txt).indexOf(close) ? txt.replace(rgx, close + open) : txt) + close;\n  };\n}\nvar reset = init(0, 0);\nvar bold = init(1, 22);\nvar dim = init(2, 22);\nvar italic = init(3, 23);\nvar underline = init(4, 24);\nvar inverse = init(7, 27);\nvar hidden = init(8, 28);\nvar strikethrough = init(9, 29);\nvar black = init(30, 39);\nvar red = init(31, 39);\nvar green = init(32, 39);\nvar yellow = init(33, 39);\nvar blue = init(34, 39);\nvar magenta = init(35, 39);\nvar cyan = init(36, 39);\nvar white = init(37, 39);\nvar gray = init(90, 39);\nvar grey = init(90, 39);\nvar bgBlack = init(40, 49);\nvar bgRed = init(41, 49);\nvar bgGreen = init(42, 49);\nvar bgYellow = init(43, 49);\nvar bgBlue = init(44, 49);\nvar bgMagenta = init(45, 49);\nvar bgCyan = init(46, 49);\nvar bgWhite = init(47, 49);\n\n// src/index.ts\nvar MAX_ARGS_HISTORY = 100;\nvar COLORS = [\"green\", \"yellow\", \"blue\", \"magenta\", \"cyan\", \"red\"];\nvar argsHistory = [];\nvar lastTimestamp = Date.now();\nvar lastColor = 0;\nvar processEnv = typeof process !== \"undefined\" ? process.env : {};\nglobalThis.DEBUG ??= processEnv.DEBUG ?? \"\";\nglobalThis.DEBUG_COLORS ??= processEnv.DEBUG_COLORS ? processEnv.DEBUG_COLORS === \"true\" : true;\nvar topProps = {\n  enable(namespace) {\n    if (typeof namespace === \"string\") {\n      globalThis.DEBUG = namespace;\n    }\n  },\n  disable() {\n    const prev = globalThis.DEBUG;\n    globalThis.DEBUG = \"\";\n    return prev;\n  },\n  // this is the core logic to check if logging should happen or not\n  enabled(namespace) {\n    const listenedNamespaces = globalThis.DEBUG.split(\",\").map((s) => {\n      return s.replace(/[.+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    });\n    const isListened = listenedNamespaces.some((listenedNamespace) => {\n      if (listenedNamespace === \"\" || listenedNamespace[0] === \"-\") return false;\n      return namespace.match(RegExp(listenedNamespace.split(\"*\").join(\".*\") + \"$\"));\n    });\n    const isExcluded = listenedNamespaces.some((listenedNamespace) => {\n      if (listenedNamespace === \"\" || listenedNamespace[0] !== \"-\") return false;\n      return namespace.match(RegExp(listenedNamespace.slice(1).split(\"*\").join(\".*\") + \"$\"));\n    });\n    return isListened && !isExcluded;\n  },\n  log: (...args) => {\n    const [namespace, format, ...rest] = args;\n    const logWithFormatting = console.warn ?? console.log;\n    logWithFormatting(`${namespace} ${format}`, ...rest);\n  },\n  formatters: {}\n  // not implemented\n};\nfunction debugCreate(namespace) {\n  const instanceProps = {\n    color: COLORS[lastColor++ % COLORS.length],\n    enabled: topProps.enabled(namespace),\n    namespace,\n    log: topProps.log,\n    extend: () => {\n    }\n    // not implemented\n  };\n  const debugCall = (...args) => {\n    const { enabled, namespace: namespace2, color, log } = instanceProps;\n    if (args.length !== 0) {\n      argsHistory.push([namespace2, ...args]);\n    }\n    if (argsHistory.length > MAX_ARGS_HISTORY) {\n      argsHistory.shift();\n    }\n    if (topProps.enabled(namespace2) || enabled) {\n      const stringArgs = args.map((arg) => {\n        if (typeof arg === \"string\") {\n          return arg;\n        }\n        return safeStringify(arg);\n      });\n      const ms = `+${Date.now() - lastTimestamp}ms`;\n      lastTimestamp = Date.now();\n      if (globalThis.DEBUG_COLORS) {\n        log(colors_exports[color](bold(namespace2)), ...stringArgs, colors_exports[color](ms));\n      } else {\n        log(namespace2, ...stringArgs, ms);\n      }\n    }\n  };\n  return new Proxy(debugCall, {\n    get: (_, prop) => instanceProps[prop],\n    set: (_, prop, value) => instanceProps[prop] = value\n  });\n}\nvar Debug = new Proxy(debugCreate, {\n  get: (_, prop) => topProps[prop],\n  set: (_, prop, value) => topProps[prop] = value\n});\nfunction safeStringify(value, indent = 2) {\n  const cache = /* @__PURE__ */ new Set();\n  return JSON.stringify(\n    value,\n    (key, value2) => {\n      if (typeof value2 === \"object\" && value2 !== null) {\n        if (cache.has(value2)) {\n          return `[Circular *]`;\n        }\n        cache.add(value2);\n      } else if (typeof value2 === \"bigint\") {\n        return value2.toString();\n      }\n      return value2;\n    },\n    indent\n  );\n}\nfunction getLogs(numChars = 7500) {\n  const logs = argsHistory.map(([namespace, ...args]) => {\n    return `${namespace} ${args.map((arg) => {\n      if (typeof arg === \"string\") {\n        return arg;\n      } else {\n        return JSON.stringify(arg);\n      }\n    }).join(\" \")}`;\n  }).join(\"\\n\");\n  if (logs.length < numChars) {\n    return logs;\n  }\n  return logs.slice(-numChars);\n}\nfunction clearLogs() {\n  argsHistory.length = 0;\n}\nvar index_default = Debug;\nexport {\n  Debug,\n  clearLogs,\n  index_default as default,\n  getLogs\n};\n"], "names": [], "mappings": ";;;;;;AAAA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AAEA,qEAAqE;AACrE,IAAI,iBAAiB,CAAC;AACtB,SAAS,gBAAgB;IACvB,GAAG,IAAM;IACT,SAAS,IAAM;IACf,QAAQ,IAAM;IACd,QAAQ,IAAM;IACd,SAAS,IAAM;IACf,WAAW,IAAM;IACjB,OAAO,IAAM;IACb,SAAS,IAAM;IACf,UAAU,IAAM;IAChB,OAAO,IAAM;IACb,MAAM,IAAM;IACZ,MAAM,IAAM;IACZ,MAAM,IAAM;IACZ,KAAK,IAAM;IACX,MAAM,IAAM;IACZ,OAAO,IAAM;IACb,MAAM,IAAM;IACZ,QAAQ,IAAM;IACd,SAAS,IAAM;IACf,QAAQ,IAAM;IACd,SAAS,IAAM;IACf,KAAK,IAAM;IACX,OAAO,IAAM;IACb,eAAe,IAAM;IACrB,WAAW,IAAM;IACjB,OAAO,IAAM;IACb,QAAQ,IAAM;AAChB;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,QAAQ;AACZ,IAAI,OAAO,YAAY,aAAa;IAClC,CAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;IACzE,QAAQ,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,KAAK;AAChD;AACA,IAAI,IAAI;IACN,SAAS,CAAC,uBAAuB,YAAY,QAAQ,SAAS,UAAU,CAAC,eAAe,QAAQ,gBAAgB,OAAO,KAAK;AAC9H;AACA,SAAS,KAAK,CAAC,EAAE,CAAC;IAChB,IAAI,MAAM,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE;IACtC,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC7C,OAAO,SAAS,GAAG;QACjB,IAAI,CAAC,EAAE,OAAO,IAAI,OAAO,MAAM,OAAO;QACtC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,KAAK,QAAQ,QAAQ,GAAG,IAAI;IACxF;AACF;AACA,IAAI,QAAQ,KAAK,GAAG;AACpB,IAAI,OAAO,KAAK,GAAG;AACnB,IAAI,MAAM,KAAK,GAAG;AAClB,IAAI,SAAS,KAAK,GAAG;AACrB,IAAI,YAAY,KAAK,GAAG;AACxB,IAAI,UAAU,KAAK,GAAG;AACtB,IAAI,SAAS,KAAK,GAAG;AACrB,IAAI,gBAAgB,KAAK,GAAG;AAC5B,IAAI,QAAQ,KAAK,IAAI;AACrB,IAAI,MAAM,KAAK,IAAI;AACnB,IAAI,QAAQ,KAAK,IAAI;AACrB,IAAI,SAAS,KAAK,IAAI;AACtB,IAAI,OAAO,KAAK,IAAI;AACpB,IAAI,UAAU,KAAK,IAAI;AACvB,IAAI,OAAO,KAAK,IAAI;AACpB,IAAI,QAAQ,KAAK,IAAI;AACrB,IAAI,OAAO,KAAK,IAAI;AACpB,IAAI,OAAO,KAAK,IAAI;AACpB,IAAI,UAAU,KAAK,IAAI;AACvB,IAAI,QAAQ,KAAK,IAAI;AACrB,IAAI,UAAU,KAAK,IAAI;AACvB,IAAI,WAAW,KAAK,IAAI;AACxB,IAAI,SAAS,KAAK,IAAI;AACtB,IAAI,YAAY,KAAK,IAAI;AACzB,IAAI,SAAS,KAAK,IAAI;AACtB,IAAI,UAAU,KAAK,IAAI;AAEvB,eAAe;AACf,IAAI,mBAAmB;AACvB,IAAI,SAAS;IAAC;IAAS;IAAU;IAAQ;IAAW;IAAQ;CAAM;AAClE,IAAI,cAAc,EAAE;AACpB,IAAI,gBAAgB,KAAK,GAAG;AAC5B,IAAI,YAAY;AAChB,IAAI,aAAa,OAAO,YAAY,cAAc,QAAQ,GAAG,GAAG,CAAC;AACjE,WAAW,KAAK,KAAK,WAAW,KAAK,IAAI;AACzC,WAAW,YAAY,KAAK,WAAW,YAAY,GAAG,WAAW,YAAY,KAAK,SAAS;AAC3F,IAAI,WAAW;IACb,QAAO,SAAS;QACd,IAAI,OAAO,cAAc,UAAU;YACjC,WAAW,KAAK,GAAG;QACrB;IACF;IACA;QACE,MAAM,OAAO,WAAW,KAAK;QAC7B,WAAW,KAAK,GAAG;QACnB,OAAO;IACT;IACA,kEAAkE;IAClE,SAAQ,SAAS;QACf,MAAM,qBAAqB,WAAW,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;YAC1D,OAAO,EAAE,OAAO,CAAC,sBAAsB;QACzC;QACA,MAAM,aAAa,mBAAmB,IAAI,CAAC,CAAC;YAC1C,IAAI,sBAAsB,MAAM,iBAAiB,CAAC,EAAE,KAAK,KAAK,OAAO;YACrE,OAAO,UAAU,KAAK,CAAC,OAAO,kBAAkB,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ;QAC1E;QACA,MAAM,aAAa,mBAAmB,IAAI,CAAC,CAAC;YAC1C,IAAI,sBAAsB,MAAM,iBAAiB,CAAC,EAAE,KAAK,KAAK,OAAO;YACrE,OAAO,UAAU,KAAK,CAAC,OAAO,kBAAkB,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ;QACnF;QACA,OAAO,cAAc,CAAC;IACxB;IACA,KAAK,CAAC,GAAG;QACP,MAAM,CAAC,WAAW,QAAQ,GAAG,KAAK,GAAG;QACrC,MAAM,oBAAoB,QAAQ,IAAI,IAAI,QAAQ,GAAG;QACrD,kBAAkB,GAAG,UAAU,CAAC,EAAE,QAAQ,KAAK;IACjD;IACA,YAAY,CAAC;AAEf;AACA,SAAS,YAAY,SAAS;IAC5B,MAAM,gBAAgB;QACpB,OAAO,MAAM,CAAC,cAAc,OAAO,MAAM,CAAC;QAC1C,SAAS,SAAS,OAAO,CAAC;QAC1B;QACA,KAAK,SAAS,GAAG;QACjB,QAAQ,KACR;IAEF;IACA,MAAM,YAAY,CAAC,GAAG;QACpB,MAAM,EAAE,OAAO,EAAE,WAAW,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;QACvD,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,YAAY,IAAI,CAAC;gBAAC;mBAAe;aAAK;QACxC;QACA,IAAI,YAAY,MAAM,GAAG,kBAAkB;YACzC,YAAY,KAAK;QACnB;QACA,IAAI,SAAS,OAAO,CAAC,eAAe,SAAS;YAC3C,MAAM,aAAa,KAAK,GAAG,CAAC,CAAC;gBAC3B,IAAI,OAAO,QAAQ,UAAU;oBAC3B,OAAO;gBACT;gBACA,OAAO,cAAc;YACvB;YACA,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,cAAc,EAAE,CAAC;YAC7C,gBAAgB,KAAK,GAAG;YACxB,IAAI,WAAW,YAAY,EAAE;gBAC3B,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,iBAAiB,YAAY,cAAc,CAAC,MAAM,CAAC;YACpF,OAAO;gBACL,IAAI,eAAe,YAAY;YACjC;QACF;IACF;IACA,OAAO,IAAI,MAAM,WAAW;QAC1B,KAAK,CAAC,GAAG,OAAS,aAAa,CAAC,KAAK;QACrC,KAAK,CAAC,GAAG,MAAM,QAAU,aAAa,CAAC,KAAK,GAAG;IACjD;AACF;AACA,IAAI,QAAQ,IAAI,MAAM,aAAa;IACjC,KAAK,CAAC,GAAG,OAAS,QAAQ,CAAC,KAAK;IAChC,KAAK,CAAC,GAAG,MAAM,QAAU,QAAQ,CAAC,KAAK,GAAG;AAC5C;AACA,SAAS,cAAc,KAAK,EAAE,SAAS,CAAC;IACtC,MAAM,QAAQ,aAAa,GAAG,IAAI;IAClC,OAAO,KAAK,SAAS,CACnB,OACA,CAAC,KAAK;QACJ,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;YACjD,IAAI,MAAM,GAAG,CAAC,SAAS;gBACrB,OAAO,CAAC,YAAY,CAAC;YACvB;YACA,MAAM,GAAG,CAAC;QACZ,OAAO,IAAI,OAAO,WAAW,UAAU;YACrC,OAAO,OAAO,QAAQ;QACxB;QACA,OAAO;IACT,GACA;AAEJ;AACA,SAAS,QAAQ,WAAW,IAAI;IAC9B,MAAM,OAAO,YAAY,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK;QAChD,OAAO,GAAG,UAAU,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;YAC/B,IAAI,OAAO,QAAQ,UAAU;gBAC3B,OAAO;YACT,OAAO;gBACL,OAAO,KAAK,SAAS,CAAC;YACxB;QACF,GAAG,IAAI,CAAC,MAAM;IAChB,GAAG,IAAI,CAAC;IACR,IAAI,KAAK,MAAM,GAAG,UAAU;QAC1B,OAAO;IACT;IACA,OAAO,KAAK,KAAK,CAAC,CAAC;AACrB;AACA,SAAS;IACP,YAAY,MAAM,GAAG;AACvB;AACA,IAAI,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40prisma%2Bdriver-adapter-utils%406.4.1/node_modules/%40prisma/driver-adapter-utils/dist/index.mjs"], "sourcesContent": ["// src/result.ts\nfunction ok(value) {\n  return {\n    ok: true,\n    value,\n    map(fn) {\n      return ok(fn(value));\n    },\n    flatMap(fn) {\n      return fn(value);\n    }\n  };\n}\nfunction err(error) {\n  return {\n    ok: false,\n    error,\n    map() {\n      return err(error);\n    },\n    flatMap() {\n      return err(error);\n    }\n  };\n}\n\n// src/binder.ts\nvar ErrorRegistryInternal = class {\n  constructor() {\n    this.registeredErrors = [];\n  }\n  consumeError(id) {\n    return this.registeredErrors[id];\n  }\n  registerNewError(error) {\n    let i = 0;\n    while (this.registeredErrors[i] !== void 0) {\n      i++;\n    }\n    this.registeredErrors[i] = { error };\n    return i;\n  }\n};\nvar bindAdapter = (adapter) => {\n  const errorRegistry = new ErrorRegistryInternal();\n  const createTransactionContext = wrapAsync(errorRegistry, adapter.transactionContext.bind(adapter));\n  const boundAdapter = {\n    adapterName: adapter.adapterName,\n    errorRegistry,\n    queryRaw: wrapAsync(errorRegistry, adapter.queryRaw.bind(adapter)),\n    executeRaw: wrapAsync(errorRegistry, adapter.executeRaw.bind(adapter)),\n    provider: adapter.provider,\n    transactionContext: async (...args) => {\n      const ctx = await createTransactionContext(...args);\n      return ctx.map((tx) => bindTransactionContext(errorRegistry, tx));\n    }\n  };\n  if (adapter.getConnectionInfo) {\n    boundAdapter.getConnectionInfo = wrapSync(errorRegistry, adapter.getConnectionInfo.bind(adapter));\n  }\n  return boundAdapter;\n};\nvar bindTransactionContext = (errorRegistry, ctx) => {\n  const startTransaction = wrapAsync(errorRegistry, ctx.startTransaction.bind(ctx));\n  return {\n    adapterName: ctx.adapterName,\n    provider: ctx.provider,\n    queryRaw: wrapAsync(errorRegistry, ctx.queryRaw.bind(ctx)),\n    executeRaw: wrapAsync(errorRegistry, ctx.executeRaw.bind(ctx)),\n    startTransaction: async (...args) => {\n      const result = await startTransaction(...args);\n      return result.map((tx) => bindTransaction(errorRegistry, tx));\n    }\n  };\n};\nvar bindTransaction = (errorRegistry, transaction) => {\n  return {\n    adapterName: transaction.adapterName,\n    provider: transaction.provider,\n    options: transaction.options,\n    queryRaw: wrapAsync(errorRegistry, transaction.queryRaw.bind(transaction)),\n    executeRaw: wrapAsync(errorRegistry, transaction.executeRaw.bind(transaction)),\n    commit: wrapAsync(errorRegistry, transaction.commit.bind(transaction)),\n    rollback: wrapAsync(errorRegistry, transaction.rollback.bind(transaction))\n  };\n};\nfunction wrapAsync(registry, fn) {\n  return async (...args) => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      const id = registry.registerNewError(error);\n      return err({ kind: \"GenericJs\", id });\n    }\n  };\n}\nfunction wrapSync(registry, fn) {\n  return (...args) => {\n    try {\n      return fn(...args);\n    } catch (error) {\n      const id = registry.registerNewError(error);\n      return err({ kind: \"GenericJs\", id });\n    }\n  };\n}\n\n// src/const.ts\nvar ColumnTypeEnum = {\n  // Scalars\n  Int32: 0,\n  Int64: 1,\n  Float: 2,\n  Double: 3,\n  Numeric: 4,\n  Boolean: 5,\n  Character: 6,\n  Text: 7,\n  Date: 8,\n  Time: 9,\n  DateTime: 10,\n  Json: 11,\n  Enum: 12,\n  Bytes: 13,\n  Set: 14,\n  Uuid: 15,\n  // Arrays\n  Int32Array: 64,\n  Int64Array: 65,\n  FloatArray: 66,\n  DoubleArray: 67,\n  NumericArray: 68,\n  BooleanArray: 69,\n  CharacterArray: 70,\n  TextArray: 71,\n  DateArray: 72,\n  TimeArray: 73,\n  DateTimeArray: 74,\n  JsonArray: 75,\n  EnumArray: 76,\n  BytesArray: 77,\n  UuidArray: 78,\n  // Custom\n  UnknownNumber: 128\n};\n\n// src/debug.ts\nimport { Debug } from \"@prisma/debug\";\nexport {\n  ColumnTypeEnum,\n  Debug,\n  bindAdapter,\n  err,\n  ok\n};\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;AAChB,SAAS,GAAG,KAAK;IACf,OAAO;QACL,IAAI;QACJ;QACA,KAAI,EAAE;YACJ,OAAO,GAAG,GAAG;QACf;QACA,SAAQ,EAAE;YACR,OAAO,GAAG;QACZ;IACF;AACF;AACA,SAAS,IAAI,KAAK;IAChB,OAAO;QACL,IAAI;QACJ;QACA;YACE,OAAO,IAAI;QACb;QACA;YACE,OAAO,IAAI;QACb;IACF;AACF;AAEA,gBAAgB;AAChB,IAAI,wBAAwB;IAC1B,aAAc;QACZ,IAAI,CAAC,gBAAgB,GAAG,EAAE;IAC5B;IACA,aAAa,EAAE,EAAE;QACf,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG;IAClC;IACA,iBAAiB,KAAK,EAAE;QACtB,IAAI,IAAI;QACR,MAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,KAAK,KAAK,EAAG;YAC1C;QACF;QACA,IAAI,CAAC,gBAAgB,CAAC,EAAE,GAAG;YAAE;QAAM;QACnC,OAAO;IACT;AACF;AACA,IAAI,cAAc,CAAC;IACjB,MAAM,gBAAgB,IAAI;IAC1B,MAAM,2BAA2B,UAAU,eAAe,QAAQ,kBAAkB,CAAC,IAAI,CAAC;IAC1F,MAAM,eAAe;QACnB,aAAa,QAAQ,WAAW;QAChC;QACA,UAAU,UAAU,eAAe,QAAQ,QAAQ,CAAC,IAAI,CAAC;QACzD,YAAY,UAAU,eAAe,QAAQ,UAAU,CAAC,IAAI,CAAC;QAC7D,UAAU,QAAQ,QAAQ;QAC1B,oBAAoB,OAAO,GAAG;YAC5B,MAAM,MAAM,MAAM,4BAA4B;YAC9C,OAAO,IAAI,GAAG,CAAC,CAAC,KAAO,uBAAuB,eAAe;QAC/D;IACF;IACA,IAAI,QAAQ,iBAAiB,EAAE;QAC7B,aAAa,iBAAiB,GAAG,SAAS,eAAe,QAAQ,iBAAiB,CAAC,IAAI,CAAC;IAC1F;IACA,OAAO;AACT;AACA,IAAI,yBAAyB,CAAC,eAAe;IAC3C,MAAM,mBAAmB,UAAU,eAAe,IAAI,gBAAgB,CAAC,IAAI,CAAC;IAC5E,OAAO;QACL,aAAa,IAAI,WAAW;QAC5B,UAAU,IAAI,QAAQ;QACtB,UAAU,UAAU,eAAe,IAAI,QAAQ,CAAC,IAAI,CAAC;QACrD,YAAY,UAAU,eAAe,IAAI,UAAU,CAAC,IAAI,CAAC;QACzD,kBAAkB,OAAO,GAAG;YAC1B,MAAM,SAAS,MAAM,oBAAoB;YACzC,OAAO,OAAO,GAAG,CAAC,CAAC,KAAO,gBAAgB,eAAe;QAC3D;IACF;AACF;AACA,IAAI,kBAAkB,CAAC,eAAe;IACpC,OAAO;QACL,aAAa,YAAY,WAAW;QACpC,UAAU,YAAY,QAAQ;QAC9B,SAAS,YAAY,OAAO;QAC5B,UAAU,UAAU,eAAe,YAAY,QAAQ,CAAC,IAAI,CAAC;QAC7D,YAAY,UAAU,eAAe,YAAY,UAAU,CAAC,IAAI,CAAC;QACjE,QAAQ,UAAU,eAAe,YAAY,MAAM,CAAC,IAAI,CAAC;QACzD,UAAU,UAAU,eAAe,YAAY,QAAQ,CAAC,IAAI,CAAC;IAC/D;AACF;AACA,SAAS,UAAU,QAAQ,EAAE,EAAE;IAC7B,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,MAAM,KAAK,SAAS,gBAAgB,CAAC;YACrC,OAAO,IAAI;gBAAE,MAAM;gBAAa;YAAG;QACrC;IACF;AACF;AACA,SAAS,SAAS,QAAQ,EAAE,EAAE;IAC5B,OAAO,CAAC,GAAG;QACT,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,MAAM,KAAK,SAAS,gBAAgB,CAAC;YACrC,OAAO,IAAI;gBAAE,MAAM;gBAAa;YAAG;QACrC;IACF;AACF;AAEA,eAAe;AACf,IAAI,iBAAiB;IACnB,UAAU;IACV,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,SAAS;IACT,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,WAAW;IACX,eAAe;IACf,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,SAAS;IACT,eAAe;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/postgres-array%403.0.2/node_modules/postgres-array/index.js"], "sourcesContent": ["'use strict'\n\nexports.parse = function (source, transform) {\n  return parsePostgresArray(source, transform)\n}\n\nfunction parsePostgresArray (source, transform, nested = false) {\n  let character = ''\n  let quote = false\n  let position = 0\n  let dimension = 0\n  const entries = []\n  let recorded = ''\n\n  const newEntry = function (includeEmpty) {\n    let entry = recorded\n\n    if (entry.length > 0 || includeEmpty) {\n      if (entry === 'NULL' && !includeEmpty) {\n        entry = null\n      }\n\n      if (entry !== null && transform) {\n        entry = transform(entry)\n      }\n\n      entries.push(entry)\n      recorded = ''\n    }\n  }\n\n  if (source[0] === '[') {\n    while (position < source.length) {\n      const char = source[position++]\n\n      if (char === '=') { break }\n    }\n  }\n\n  while (position < source.length) {\n    let escaped = false\n    character = source[position++]\n\n    if (character === '\\\\') {\n      character = source[position++]\n      escaped = true\n    }\n\n    if (character === '{' && !quote) {\n      dimension++\n\n      if (dimension > 1) {\n        const parser = parsePostgresArray(source.substr(position - 1), transform, true)\n\n        entries.push(parser.entries)\n        position += parser.position - 2\n      }\n    } else if (character === '}' && !quote) {\n      dimension--\n\n      if (!dimension) {\n        newEntry()\n\n        if (nested) {\n          return {\n            entries,\n            position\n          }\n        }\n      }\n    } else if (character === '\"' && !escaped) {\n      if (quote) {\n        newEntry(true)\n      }\n\n      quote = !quote\n    } else if (character === ',' && !quote) {\n      newEntry()\n    } else {\n      recorded += character\n    }\n  }\n\n  if (dimension !== 0) {\n    throw new Error('array dimension not balanced')\n  }\n\n  return entries\n}\n"], "names": [], "mappings": "AAAA;AAEA,QAAQ,KAAK,GAAG,SAAU,MAAM,EAAE,SAAS;IACzC,OAAO,mBAAmB,QAAQ;AACpC;AAEA,SAAS,mBAAoB,MAAM,EAAE,SAAS,EAAE,SAAS,KAAK;IAC5D,IAAI,YAAY;IAChB,IAAI,QAAQ;IACZ,IAAI,WAAW;IACf,IAAI,YAAY;IAChB,MAAM,UAAU,EAAE;IAClB,IAAI,WAAW;IAEf,MAAM,WAAW,SAAU,YAAY;QACrC,IAAI,QAAQ;QAEZ,IAAI,MAAM,MAAM,GAAG,KAAK,cAAc;YACpC,IAAI,UAAU,UAAU,CAAC,cAAc;gBACrC,QAAQ;YACV;YAEA,IAAI,UAAU,QAAQ,WAAW;gBAC/B,QAAQ,UAAU;YACpB;YAEA,QAAQ,IAAI,CAAC;YACb,WAAW;QACb;IACF;IAEA,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;QACrB,MAAO,WAAW,OAAO,MAAM,CAAE;YAC/B,MAAM,OAAO,MAAM,CAAC,WAAW;YAE/B,IAAI,SAAS,KAAK;gBAAE;YAAM;QAC5B;IACF;IAEA,MAAO,WAAW,OAAO,MAAM,CAAE;QAC/B,IAAI,UAAU;QACd,YAAY,MAAM,CAAC,WAAW;QAE9B,IAAI,cAAc,MAAM;YACtB,YAAY,MAAM,CAAC,WAAW;YAC9B,UAAU;QACZ;QAEA,IAAI,cAAc,OAAO,CAAC,OAAO;YAC/B;YAEA,IAAI,YAAY,GAAG;gBACjB,MAAM,SAAS,mBAAmB,OAAO,MAAM,CAAC,WAAW,IAAI,WAAW;gBAE1E,QAAQ,IAAI,CAAC,OAAO,OAAO;gBAC3B,YAAY,OAAO,QAAQ,GAAG;YAChC;QACF,OAAO,IAAI,cAAc,OAAO,CAAC,OAAO;YACtC;YAEA,IAAI,CAAC,WAAW;gBACd;gBAEA,IAAI,QAAQ;oBACV,OAAO;wBACL;wBACA;oBACF;gBACF;YACF;QACF,OAAO,IAAI,cAAc,OAAO,CAAC,SAAS;YACxC,IAAI,OAAO;gBACT,SAAS;YACX;YAEA,QAAQ,CAAC;QACX,OAAO,IAAI,cAAc,OAAO,CAAC,OAAO;YACtC;QACF,OAAO;YACL,YAAY;QACd;IACF;IAEA,IAAI,cAAc,GAAG;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40prisma%2Badapter-neon%406.4.1_%40neondatabase%2Bserverless%401.0.0/node_modules/%40prisma/adapter-neon/dist/index.mjs"], "sourcesContent": ["// src/neon.ts\nimport * as neon from \"@neondatabase/serverless\";\nimport { Debug, err, ok } from \"@prisma/driver-adapter-utils\";\n\n// package.json\nvar name = \"@prisma/adapter-neon\";\n\n// src/conversion.ts\nimport { types } from \"@neondatabase/serverless\";\nimport { ColumnTypeEnum } from \"@prisma/driver-adapter-utils\";\nimport { parse as parseArray } from \"postgres-array\";\nvar { builtins: ScalarColumnType, getTypeParser } = types;\nvar ArrayColumnType = {\n  BIT_ARRAY: 1561,\n  BOOL_ARRAY: 1e3,\n  BYTEA_ARRAY: 1001,\n  BPCHAR_ARRAY: 1014,\n  CHAR_ARRAY: 1002,\n  CIDR_ARRAY: 651,\n  DATE_ARRAY: 1182,\n  FLOAT4_ARRAY: 1021,\n  FLOAT8_ARRAY: 1022,\n  INET_ARRAY: 1041,\n  INT2_ARRAY: 1005,\n  INT4_ARRAY: 1007,\n  INT8_ARRAY: 1016,\n  J<PERSON><PERSON><PERSON>_ARRAY: 3807,\n  JSON_ARRAY: 199,\n  MONEY_ARRAY: 791,\n  NUMERIC_ARRAY: 1231,\n  OID_ARRAY: 1028,\n  TEXT_ARRAY: 1009,\n  TIMESTAMP_ARRAY: 1115,\n  TIME_ARRAY: 1183,\n  UUID_ARRAY: 2951,\n  VARBIT_ARRAY: 1563,\n  VARCHAR_ARRAY: 1015,\n  XML_ARRAY: 143\n};\nvar _UnsupportedNativeDataType = class _UnsupportedNativeDataType extends Error {\n  constructor(code) {\n    super();\n    this.type = _UnsupportedNativeDataType.typeNames[code] || \"Unknown\";\n    this.message = `Unsupported column type ${this.type}`;\n  }\n};\n// map of type codes to type names\n_UnsupportedNativeDataType.typeNames = {\n  16: \"bool\",\n  17: \"bytea\",\n  18: \"char\",\n  19: \"name\",\n  20: \"int8\",\n  21: \"int2\",\n  22: \"int2vector\",\n  23: \"int4\",\n  24: \"regproc\",\n  25: \"text\",\n  26: \"oid\",\n  27: \"tid\",\n  28: \"xid\",\n  29: \"cid\",\n  30: \"oidvector\",\n  32: \"pg_ddl_command\",\n  71: \"pg_type\",\n  75: \"pg_attribute\",\n  81: \"pg_proc\",\n  83: \"pg_class\",\n  114: \"json\",\n  142: \"xml\",\n  194: \"pg_node_tree\",\n  269: \"table_am_handler\",\n  325: \"index_am_handler\",\n  600: \"point\",\n  601: \"lseg\",\n  602: \"path\",\n  603: \"box\",\n  604: \"polygon\",\n  628: \"line\",\n  650: \"cidr\",\n  700: \"float4\",\n  701: \"float8\",\n  705: \"unknown\",\n  718: \"circle\",\n  774: \"macaddr8\",\n  790: \"money\",\n  829: \"macaddr\",\n  869: \"inet\",\n  1033: \"aclitem\",\n  1042: \"bpchar\",\n  1043: \"varchar\",\n  1082: \"date\",\n  1083: \"time\",\n  1114: \"timestamp\",\n  1184: \"timestamptz\",\n  1186: \"interval\",\n  1266: \"timetz\",\n  1560: \"bit\",\n  1562: \"varbit\",\n  1700: \"numeric\",\n  1790: \"refcursor\",\n  2202: \"regprocedure\",\n  2203: \"regoper\",\n  2204: \"regoperator\",\n  2205: \"regclass\",\n  2206: \"regtype\",\n  2249: \"record\",\n  2275: \"cstring\",\n  2276: \"any\",\n  2277: \"anyarray\",\n  2278: \"void\",\n  2279: \"trigger\",\n  2280: \"language_handler\",\n  2281: \"internal\",\n  2283: \"anyelement\",\n  2287: \"_record\",\n  2776: \"anynonarray\",\n  2950: \"uuid\",\n  2970: \"txid_snapshot\",\n  3115: \"fdw_handler\",\n  3220: \"pg_lsn\",\n  3310: \"tsm_handler\",\n  3361: \"pg_ndistinct\",\n  3402: \"pg_dependencies\",\n  3500: \"anyenum\",\n  3614: \"tsvector\",\n  3615: \"tsquery\",\n  3642: \"gtsvector\",\n  3734: \"regconfig\",\n  3769: \"regdictionary\",\n  3802: \"jsonb\",\n  3831: \"anyrange\",\n  3838: \"event_trigger\",\n  3904: \"int4range\",\n  3906: \"numrange\",\n  3908: \"tsrange\",\n  3910: \"tstzrange\",\n  3912: \"daterange\",\n  3926: \"int8range\",\n  4072: \"jsonpath\",\n  4089: \"regnamespace\",\n  4096: \"regrole\",\n  4191: \"regcollation\",\n  4451: \"int4multirange\",\n  4532: \"nummultirange\",\n  4533: \"tsmultirange\",\n  4534: \"tstzmultirange\",\n  4535: \"datemultirange\",\n  4536: \"int8multirange\",\n  4537: \"anymultirange\",\n  4538: \"anycompatiblemultirange\",\n  4600: \"pg_brin_bloom_summary\",\n  4601: \"pg_brin_minmax_multi_summary\",\n  5017: \"pg_mcv_list\",\n  5038: \"pg_snapshot\",\n  5069: \"xid8\",\n  5077: \"anycompatible\",\n  5078: \"anycompatiblearray\",\n  5079: \"anycompatiblenonarray\",\n  5080: \"anycompatiblerange\"\n};\nvar UnsupportedNativeDataType = _UnsupportedNativeDataType;\nfunction fieldToColumnType(fieldTypeId) {\n  switch (fieldTypeId) {\n    case ScalarColumnType.INT2:\n    case ScalarColumnType.INT4:\n      return ColumnTypeEnum.Int32;\n    case ScalarColumnType.INT8:\n      return ColumnTypeEnum.Int64;\n    case ScalarColumnType.FLOAT4:\n      return ColumnTypeEnum.Float;\n    case ScalarColumnType.FLOAT8:\n      return ColumnTypeEnum.Double;\n    case ScalarColumnType.BOOL:\n      return ColumnTypeEnum.Boolean;\n    case ScalarColumnType.DATE:\n      return ColumnTypeEnum.Date;\n    case ScalarColumnType.TIME:\n    case ScalarColumnType.TIMETZ:\n      return ColumnTypeEnum.Time;\n    case ScalarColumnType.TIMESTAMP:\n    case ScalarColumnType.TIMESTAMPTZ:\n      return ColumnTypeEnum.DateTime;\n    case ScalarColumnType.NUMERIC:\n    case ScalarColumnType.MONEY:\n      return ColumnTypeEnum.Numeric;\n    case ScalarColumnType.JSON:\n    case ScalarColumnType.JSONB:\n      return ColumnTypeEnum.Json;\n    case ScalarColumnType.UUID:\n      return ColumnTypeEnum.Uuid;\n    case ScalarColumnType.OID:\n      return ColumnTypeEnum.Int64;\n    case ScalarColumnType.BPCHAR:\n    case ScalarColumnType.TEXT:\n    case ScalarColumnType.VARCHAR:\n    case ScalarColumnType.BIT:\n    case ScalarColumnType.VARBIT:\n    case ScalarColumnType.INET:\n    case ScalarColumnType.CIDR:\n    case ScalarColumnType.XML:\n      return ColumnTypeEnum.Text;\n    case ScalarColumnType.BYTEA:\n      return ColumnTypeEnum.Bytes;\n    case ArrayColumnType.INT2_ARRAY:\n    case ArrayColumnType.INT4_ARRAY:\n      return ColumnTypeEnum.Int32Array;\n    case ArrayColumnType.FLOAT4_ARRAY:\n      return ColumnTypeEnum.FloatArray;\n    case ArrayColumnType.FLOAT8_ARRAY:\n      return ColumnTypeEnum.DoubleArray;\n    case ArrayColumnType.NUMERIC_ARRAY:\n    case ArrayColumnType.MONEY_ARRAY:\n      return ColumnTypeEnum.NumericArray;\n    case ArrayColumnType.BOOL_ARRAY:\n      return ColumnTypeEnum.BooleanArray;\n    case ArrayColumnType.CHAR_ARRAY:\n      return ColumnTypeEnum.CharacterArray;\n    case ArrayColumnType.BPCHAR_ARRAY:\n    case ArrayColumnType.TEXT_ARRAY:\n    case ArrayColumnType.VARCHAR_ARRAY:\n    case ArrayColumnType.VARBIT_ARRAY:\n    case ArrayColumnType.BIT_ARRAY:\n    case ArrayColumnType.INET_ARRAY:\n    case ArrayColumnType.CIDR_ARRAY:\n    case ArrayColumnType.XML_ARRAY:\n      return ColumnTypeEnum.TextArray;\n    case ArrayColumnType.DATE_ARRAY:\n      return ColumnTypeEnum.DateArray;\n    case ArrayColumnType.TIME_ARRAY:\n      return ColumnTypeEnum.TimeArray;\n    case ArrayColumnType.TIMESTAMP_ARRAY:\n      return ColumnTypeEnum.DateTimeArray;\n    case ArrayColumnType.JSON_ARRAY:\n    case ArrayColumnType.JSONB_ARRAY:\n      return ColumnTypeEnum.JsonArray;\n    case ArrayColumnType.BYTEA_ARRAY:\n      return ColumnTypeEnum.BytesArray;\n    case ArrayColumnType.UUID_ARRAY:\n      return ColumnTypeEnum.UuidArray;\n    case ArrayColumnType.INT8_ARRAY:\n    case ArrayColumnType.OID_ARRAY:\n      return ColumnTypeEnum.Int64Array;\n    default:\n      if (fieldTypeId >= 1e4) {\n        return ColumnTypeEnum.Text;\n      }\n      throw new UnsupportedNativeDataType(fieldTypeId);\n  }\n}\nfunction normalize_array(element_normalizer) {\n  return (str) => parseArray(str, element_normalizer);\n}\nfunction normalize_numeric(numeric) {\n  return numeric;\n}\nfunction normalize_date(date) {\n  return date;\n}\nfunction normalize_timestamp(time) {\n  return time;\n}\nfunction normalize_timestampz(time) {\n  return time.split(\"+\")[0];\n}\nfunction normalize_time(time) {\n  return time;\n}\nfunction normalize_timez(time) {\n  return time.split(\"+\")[0];\n}\nfunction normalize_money(money) {\n  return money.slice(1);\n}\nfunction normalize_xml(xml) {\n  return xml;\n}\nfunction toJson(json) {\n  return json;\n}\nfunction encodeBuffer(buffer) {\n  return Array.from(new Uint8Array(buffer));\n}\nvar parsePgBytes = getTypeParser(ScalarColumnType.BYTEA);\nvar parseBytesArray = getTypeParser(ArrayColumnType.BYTEA_ARRAY);\nfunction normalizeByteaArray(serializedBytesArray) {\n  const buffers = parseBytesArray(serializedBytesArray);\n  return buffers.map((buf) => buf ? encodeBuffer(buf) : null);\n}\nfunction convertBytes(serializedBytes) {\n  const buffer = parsePgBytes(serializedBytes);\n  return encodeBuffer(buffer);\n}\nfunction normalizeBit(bit) {\n  return bit;\n}\nvar customParsers = {\n  [ScalarColumnType.NUMERIC]: normalize_numeric,\n  [ArrayColumnType.NUMERIC_ARRAY]: normalize_array(normalize_numeric),\n  [ScalarColumnType.TIME]: normalize_time,\n  [ArrayColumnType.TIME_ARRAY]: normalize_array(normalize_time),\n  [ScalarColumnType.TIMETZ]: normalize_timez,\n  [ScalarColumnType.DATE]: normalize_date,\n  [ArrayColumnType.DATE_ARRAY]: normalize_array(normalize_date),\n  [ScalarColumnType.TIMESTAMP]: normalize_timestamp,\n  [ArrayColumnType.TIMESTAMP_ARRAY]: normalize_array(normalize_timestamp),\n  [ScalarColumnType.TIMESTAMPTZ]: normalize_timestampz,\n  [ScalarColumnType.MONEY]: normalize_money,\n  [ArrayColumnType.MONEY_ARRAY]: normalize_array(normalize_money),\n  [ScalarColumnType.JSON]: toJson,\n  [ScalarColumnType.JSONB]: toJson,\n  [ScalarColumnType.BYTEA]: convertBytes,\n  [ArrayColumnType.BYTEA_ARRAY]: normalizeByteaArray,\n  [ArrayColumnType.BIT_ARRAY]: normalize_array(normalizeBit),\n  [ArrayColumnType.VARBIT_ARRAY]: normalize_array(normalizeBit),\n  [ArrayColumnType.XML_ARRAY]: normalize_array(normalize_xml)\n};\nfunction fixArrayBufferValues(values) {\n  for (let i = 0; i < values.length; i++) {\n    const list = values[i];\n    if (!Array.isArray(list)) {\n      continue;\n    }\n    for (let j = 0; j < list.length; j++) {\n      const listItem = list[j];\n      if (ArrayBuffer.isView(listItem)) {\n        list[j] = Buffer.from(listItem.buffer, listItem.byteOffset, listItem.byteLength);\n      }\n    }\n  }\n  return values;\n}\n\n// src/neon.ts\nvar debug = Debug(\"prisma:driver-adapter:neon\");\nvar NeonQueryable = class {\n  constructor() {\n    this.provider = \"postgres\";\n    this.adapterName = name;\n  }\n  /**\n   * Execute a query given as SQL, interpolating the given parameters.\n   */\n  async queryRaw(query) {\n    const tag = \"[js::query_raw]\";\n    debug(`${tag} %O`, query);\n    const res = await this.performIO(query);\n    if (!res.ok) {\n      return err(res.error);\n    }\n    const { fields, rows } = res.value;\n    const columnNames = fields.map((field) => field.name);\n    let columnTypes = [];\n    try {\n      columnTypes = fields.map((field) => fieldToColumnType(field.dataTypeID));\n    } catch (e) {\n      if (e instanceof UnsupportedNativeDataType) {\n        return err({\n          kind: \"UnsupportedNativeDataType\",\n          type: e.type\n        });\n      }\n      throw e;\n    }\n    return ok({\n      columnNames,\n      columnTypes,\n      rows\n    });\n  }\n  /**\n   * Execute a query given as SQL, interpolating the given parameters and\n   * returning the number of affected rows.\n   * Note: Queryable expects a u64, but napi.rs only supports u32.\n   */\n  async executeRaw(query) {\n    const tag = \"[js::execute_raw]\";\n    debug(`${tag} %O`, query);\n    return (await this.performIO(query)).map((r) => r.rowCount ?? 0);\n  }\n};\nvar NeonWsQueryable = class extends NeonQueryable {\n  constructor(client) {\n    super();\n    this.client = client;\n  }\n  async performIO(query) {\n    const { sql, args: values } = query;\n    try {\n      const result = await this.client.query(\n        {\n          text: sql,\n          values: fixArrayBufferValues(values),\n          rowMode: \"array\",\n          types: {\n            // This is the error expected:\n            // No overload matches this call.\n            // The last overload gave the following error.\n            //   Type '(oid: number, format?: any) => (json: string) => unknown' is not assignable to type '{ <T>(oid: number): TypeParser<string, string | T>; <T>(oid: number, format: \"text\"): TypeParser<string, string | T>; <T>(oid: number, format: \"binary\"): TypeParser<...>; }'.\n            //     Type '(json: string) => unknown' is not assignable to type 'TypeParser<Buffer, any>'.\n            //       Types of parameters 'json' and 'value' are incompatible.\n            //         Type 'Buffer' is not assignable to type 'string'.ts(2769)\n            //\n            // Because pg-types types expect us to handle both binary and text protocol versions,\n            // where as far we can see, pg will ever pass only text version.\n            //\n            // @ts-expect-error\n            getTypeParser: (oid, format) => {\n              if (format === \"text\" && customParsers[oid]) {\n                return customParsers[oid];\n              }\n              return neon.types.getTypeParser(oid, format);\n            }\n          }\n        },\n        fixArrayBufferValues(values)\n      );\n      return ok(result);\n    } catch (e) {\n      debug(\"Error in performIO: %O\", e);\n      if (e && typeof e.code === \"string\" && typeof e.severity === \"string\" && typeof e.message === \"string\") {\n        return err({\n          kind: \"postgres\",\n          code: e.code,\n          severity: e.severity,\n          message: e.message,\n          detail: e.detail,\n          column: e.column,\n          hint: e.hint\n        });\n      }\n      throw e;\n    }\n  }\n};\nvar NeonTransaction = class extends NeonWsQueryable {\n  constructor(client, options) {\n    super(client);\n    this.options = options;\n  }\n  async commit() {\n    debug(`[js::commit]`);\n    this.client.release();\n    return Promise.resolve(ok(void 0));\n  }\n  async rollback() {\n    debug(`[js::rollback]`);\n    this.client.release();\n    return Promise.resolve(ok(void 0));\n  }\n};\nvar NeonTransactionContext = class extends NeonWsQueryable {\n  constructor(conn) {\n    super(conn);\n    this.conn = conn;\n  }\n  async startTransaction() {\n    const options = {\n      usePhantomQuery: false\n    };\n    const tag = \"[js::startTransaction]\";\n    debug(\"%s options: %O\", tag, options);\n    return ok(new NeonTransaction(this.conn, options));\n  }\n};\nvar PrismaNeon = class extends NeonWsQueryable {\n  constructor(pool, options) {\n    if (!(pool instanceof neon.Pool)) {\n      throw new TypeError(`PrismaNeon must be initialized with an instance of Pool:\nimport { Pool } from '@neondatabase/serverless'\nconst pool = new Pool({ connectionString: url })\nconst adapter = new PrismaNeon(pool)\n`);\n    }\n    super(pool);\n    this.options = options;\n    this.isRunning = true;\n  }\n  getConnectionInfo() {\n    return ok({\n      schemaName: this.options?.schema\n    });\n  }\n  async transactionContext() {\n    const conn = await this.client.connect();\n    return ok(new NeonTransactionContext(conn));\n  }\n  async close() {\n    if (this.isRunning) {\n      await this.client.end();\n      this.isRunning = false;\n    }\n    return ok(void 0);\n  }\n};\nvar PrismaNeonHTTP = class extends NeonQueryable {\n  constructor(client) {\n    super();\n    this.client = client;\n  }\n  async performIO(query) {\n    const { sql, args: values } = query;\n    return ok(\n      await this.client(sql, values, {\n        arrayMode: true,\n        fullResults: true,\n        // pass type parsers to neon() HTTP client, same as in WS client above\n        //\n        // requires @neondatabase/serverless >= 0.9.5\n        // - types option added in https://github.com/neondatabase/serverless/pull/92\n        types: {\n          getTypeParser: (oid, format) => {\n            if (format === \"text\" && customParsers[oid]) {\n              return customParsers[oid];\n            }\n            return neon.types.getTypeParser(oid, format);\n          }\n        }\n        // type `as` cast required until neon types are corrected:\n        // https://github.com/neondatabase/serverless/pull/110#issuecomment-2458992991\n      })\n    );\n  }\n  transactionContext() {\n    return Promise.reject(new Error(\"Transactions are not supported in HTTP mode\"));\n  }\n};\nexport {\n  PrismaNeon,\n  PrismaNeonHTTP\n};\n"], "names": [], "mappings": "AAAA,cAAc;;;;;AACd;AACA;AAAA;AAQA;;;AANA,eAAe;AACf,IAAI,OAAO;;;;AAMX,IAAI,EAAE,UAAU,gBAAgB,EAAE,aAAa,EAAE,GAAG,iOAAA,CAAA,QAAK;AACzD,IAAI,kBAAkB;IACpB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,aAAa;IACb,eAAe;IACf,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,eAAe;IACf,WAAW;AACb;AACA,IAAI,6BAA6B,MAAM,mCAAmC;IACxE,YAAY,IAAI,CAAE;QAChB,KAAK;QACL,IAAI,CAAC,IAAI,GAAG,2BAA2B,SAAS,CAAC,KAAK,IAAI;QAC1D,IAAI,CAAC,OAAO,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,IAAI,EAAE;IACvD;AACF;AACA,kCAAkC;AAClC,2BAA2B,SAAS,GAAG;IACrC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR;AACA,IAAI,4BAA4B;AAChC,SAAS,kBAAkB,WAAW;IACpC,OAAQ;QACN,KAAK,iBAAiB,IAAI;QAC1B,KAAK,iBAAiB,IAAI;YACxB,OAAO,6QAAA,CAAA,iBAAc,CAAC,KAAK;QAC7B,KAAK,iBAAiB,IAAI;YACxB,OAAO,6QAAA,CAAA,iBAAc,CAAC,KAAK;QAC7B,KAAK,iBAAiB,MAAM;YAC1B,OAAO,6QAAA,CAAA,iBAAc,CAAC,KAAK;QAC7B,KAAK,iBAAiB,MAAM;YAC1B,OAAO,6QAAA,CAAA,iBAAc,CAAC,MAAM;QAC9B,KAAK,iBAAiB,IAAI;YACxB,OAAO,6QAAA,CAAA,iBAAc,CAAC,OAAO;QAC/B,KAAK,iBAAiB,IAAI;YACxB,OAAO,6QAAA,CAAA,iBAAc,CAAC,IAAI;QAC5B,KAAK,iBAAiB,IAAI;QAC1B,KAAK,iBAAiB,MAAM;YAC1B,OAAO,6QAAA,CAAA,iBAAc,CAAC,IAAI;QAC5B,KAAK,iBAAiB,SAAS;QAC/B,KAAK,iBAAiB,WAAW;YAC/B,OAAO,6QAAA,CAAA,iBAAc,CAAC,QAAQ;QAChC,KAAK,iBAAiB,OAAO;QAC7B,KAAK,iBAAiB,KAAK;YACzB,OAAO,6QAAA,CAAA,iBAAc,CAAC,OAAO;QAC/B,KAAK,iBAAiB,IAAI;QAC1B,KAAK,iBAAiB,KAAK;YACzB,OAAO,6QAAA,CAAA,iBAAc,CAAC,IAAI;QAC5B,KAAK,iBAAiB,IAAI;YACxB,OAAO,6QAAA,CAAA,iBAAc,CAAC,IAAI;QAC5B,KAAK,iBAAiB,GAAG;YACvB,OAAO,6QAAA,CAAA,iBAAc,CAAC,KAAK;QAC7B,KAAK,iBAAiB,MAAM;QAC5B,KAAK,iBAAiB,IAAI;QAC1B,KAAK,iBAAiB,OAAO;QAC7B,KAAK,iBAAiB,GAAG;QACzB,KAAK,iBAAiB,MAAM;QAC5B,KAAK,iBAAiB,IAAI;QAC1B,KAAK,iBAAiB,IAAI;QAC1B,KAAK,iBAAiB,GAAG;YACvB,OAAO,6QAAA,CAAA,iBAAc,CAAC,IAAI;QAC5B,KAAK,iBAAiB,KAAK;YACzB,OAAO,6QAAA,CAAA,iBAAc,CAAC,KAAK;QAC7B,KAAK,gBAAgB,UAAU;QAC/B,KAAK,gBAAgB,UAAU;YAC7B,OAAO,6QAAA,CAAA,iBAAc,CAAC,UAAU;QAClC,KAAK,gBAAgB,YAAY;YAC/B,OAAO,6QAAA,CAAA,iBAAc,CAAC,UAAU;QAClC,KAAK,gBAAgB,YAAY;YAC/B,OAAO,6QAAA,CAAA,iBAAc,CAAC,WAAW;QACnC,KAAK,gBAAgB,aAAa;QAClC,KAAK,gBAAgB,WAAW;YAC9B,OAAO,6QAAA,CAAA,iBAAc,CAAC,YAAY;QACpC,KAAK,gBAAgB,UAAU;YAC7B,OAAO,6QAAA,CAAA,iBAAc,CAAC,YAAY;QACpC,KAAK,gBAAgB,UAAU;YAC7B,OAAO,6QAAA,CAAA,iBAAc,CAAC,cAAc;QACtC,KAAK,gBAAgB,YAAY;QACjC,KAAK,gBAAgB,UAAU;QAC/B,KAAK,gBAAgB,aAAa;QAClC,KAAK,gBAAgB,YAAY;QACjC,KAAK,gBAAgB,SAAS;QAC9B,KAAK,gBAAgB,UAAU;QAC/B,KAAK,gBAAgB,UAAU;QAC/B,KAAK,gBAAgB,SAAS;YAC5B,OAAO,6QAAA,CAAA,iBAAc,CAAC,SAAS;QACjC,KAAK,gBAAgB,UAAU;YAC7B,OAAO,6QAAA,CAAA,iBAAc,CAAC,SAAS;QACjC,KAAK,gBAAgB,UAAU;YAC7B,OAAO,6QAAA,CAAA,iBAAc,CAAC,SAAS;QACjC,KAAK,gBAAgB,eAAe;YAClC,OAAO,6QAAA,CAAA,iBAAc,CAAC,aAAa;QACrC,KAAK,gBAAgB,UAAU;QAC/B,KAAK,gBAAgB,WAAW;YAC9B,OAAO,6QAAA,CAAA,iBAAc,CAAC,SAAS;QACjC,KAAK,gBAAgB,WAAW;YAC9B,OAAO,6QAAA,CAAA,iBAAc,CAAC,UAAU;QAClC,KAAK,gBAAgB,UAAU;YAC7B,OAAO,6QAAA,CAAA,iBAAc,CAAC,SAAS;QACjC,KAAK,gBAAgB,UAAU;QAC/B,KAAK,gBAAgB,SAAS;YAC5B,OAAO,6QAAA,CAAA,iBAAc,CAAC,UAAU;QAClC;YACE,IAAI,eAAe,KAAK;gBACtB,OAAO,6QAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B;YACA,MAAM,IAAI,0BAA0B;IACxC;AACF;AACA,SAAS,gBAAgB,kBAAkB;IACzC,OAAO,CAAC,MAAQ,CAAA,GAAA,0MAAA,CAAA,QAAU,AAAD,EAAE,KAAK;AAClC;AACA,SAAS,kBAAkB,OAAO;IAChC,OAAO;AACT;AACA,SAAS,eAAe,IAAI;IAC1B,OAAO;AACT;AACA,SAAS,oBAAoB,IAAI;IAC/B,OAAO;AACT;AACA,SAAS,qBAAqB,IAAI;IAChC,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;AAC3B;AACA,SAAS,eAAe,IAAI;IAC1B,OAAO;AACT;AACA,SAAS,gBAAgB,IAAI;IAC3B,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;AAC3B;AACA,SAAS,gBAAgB,KAAK;IAC5B,OAAO,MAAM,KAAK,CAAC;AACrB;AACA,SAAS,cAAc,GAAG;IACxB,OAAO;AACT;AACA,SAAS,OAAO,IAAI;IAClB,OAAO;AACT;AACA,SAAS,aAAa,MAAM;IAC1B,OAAO,MAAM,IAAI,CAAC,IAAI,WAAW;AACnC;AACA,IAAI,eAAe,cAAc,iBAAiB,KAAK;AACvD,IAAI,kBAAkB,cAAc,gBAAgB,WAAW;AAC/D,SAAS,oBAAoB,oBAAoB;IAC/C,MAAM,UAAU,gBAAgB;IAChC,OAAO,QAAQ,GAAG,CAAC,CAAC,MAAQ,MAAM,aAAa,OAAO;AACxD;AACA,SAAS,aAAa,eAAe;IACnC,MAAM,SAAS,aAAa;IAC5B,OAAO,aAAa;AACtB;AACA,SAAS,aAAa,GAAG;IACvB,OAAO;AACT;AACA,IAAI,gBAAgB;IAClB,CAAC,iBAAiB,OAAO,CAAC,EAAE;IAC5B,CAAC,gBAAgB,aAAa,CAAC,EAAE,gBAAgB;IACjD,CAAC,iBAAiB,IAAI,CAAC,EAAE;IACzB,CAAC,gBAAgB,UAAU,CAAC,EAAE,gBAAgB;IAC9C,CAAC,iBAAiB,MAAM,CAAC,EAAE;IAC3B,CAAC,iBAAiB,IAAI,CAAC,EAAE;IACzB,CAAC,gBAAgB,UAAU,CAAC,EAAE,gBAAgB;IAC9C,CAAC,iBAAiB,SAAS,CAAC,EAAE;IAC9B,CAAC,gBAAgB,eAAe,CAAC,EAAE,gBAAgB;IACnD,CAAC,iBAAiB,WAAW,CAAC,EAAE;IAChC,CAAC,iBAAiB,KAAK,CAAC,EAAE;IAC1B,CAAC,gBAAgB,WAAW,CAAC,EAAE,gBAAgB;IAC/C,CAAC,iBAAiB,IAAI,CAAC,EAAE;IACzB,CAAC,iBAAiB,KAAK,CAAC,EAAE;IAC1B,CAAC,iBAAiB,KAAK,CAAC,EAAE;IAC1B,CAAC,gBAAgB,WAAW,CAAC,EAAE;IAC/B,CAAC,gBAAgB,SAAS,CAAC,EAAE,gBAAgB;IAC7C,CAAC,gBAAgB,YAAY,CAAC,EAAE,gBAAgB;IAChD,CAAC,gBAAgB,SAAS,CAAC,EAAE,gBAAgB;AAC/C;AACA,SAAS,qBAAqB,MAAM;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;YACxB;QACF;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,WAAW,IAAI,CAAC,EAAE;YACxB,IAAI,YAAY,MAAM,CAAC,WAAW;gBAChC,IAAI,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,SAAS,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS,UAAU;YACjF;QACF;IACF;IACA,OAAO;AACT;AAEA,cAAc;AACd,IAAI,QAAQ,CAAA,GAAA,mNAAA,CAAA,QAAK,AAAD,EAAE;AAClB,IAAI,gBAAgB;IAClB,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,WAAW,GAAG;IACrB;IACA;;GAEC,GACD,MAAM,SAAS,KAAK,EAAE;QACpB,MAAM,MAAM;QACZ,MAAM,GAAG,IAAI,GAAG,CAAC,EAAE;QACnB,MAAM,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE,EAAE;YACX,OAAO,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE,IAAI,KAAK;QACtB;QACA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK;QAClC,MAAM,cAAc,OAAO,GAAG,CAAC,CAAC,QAAU,MAAM,IAAI;QACpD,IAAI,cAAc,EAAE;QACpB,IAAI;YACF,cAAc,OAAO,GAAG,CAAC,CAAC,QAAU,kBAAkB,MAAM,UAAU;QACxE,EAAE,OAAO,GAAG;YACV,IAAI,aAAa,2BAA2B;gBAC1C,OAAO,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE;oBACT,MAAM;oBACN,MAAM,EAAE,IAAI;gBACd;YACF;YACA,MAAM;QACR;QACA,OAAO,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE;YACR;YACA;YACA;QACF;IACF;IACA;;;;GAIC,GACD,MAAM,WAAW,KAAK,EAAE;QACtB,MAAM,MAAM;QACZ,MAAM,GAAG,IAAI,GAAG,CAAC,EAAE;QACnB,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAM,EAAE,QAAQ,IAAI;IAChE;AACF;AACA,IAAI,kBAAkB,cAAc;IAClC,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,MAAM,UAAU,KAAK,EAAE;QACrB,MAAM,EAAE,GAAG,EAAE,MAAM,MAAM,EAAE,GAAG;QAC9B,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CACpC;gBACE,MAAM;gBACN,QAAQ,qBAAqB;gBAC7B,SAAS;gBACT,OAAO;oBACL,8BAA8B;oBAC9B,iCAAiC;oBACjC,8CAA8C;oBAC9C,8QAA8Q;oBAC9Q,4FAA4F;oBAC5F,iEAAiE;oBACjE,oEAAoE;oBACpE,EAAE;oBACF,qFAAqF;oBACrF,gEAAgE;oBAChE,EAAE;oBACF,mBAAmB;oBACnB,eAAe,CAAC,KAAK;wBACnB,IAAI,WAAW,UAAU,aAAa,CAAC,IAAI,EAAE;4BAC3C,OAAO,aAAa,CAAC,IAAI;wBAC3B;wBACA,OAAO,iOAAA,CAAA,QAAU,CAAC,aAAa,CAAC,KAAK;oBACvC;gBACF;YACF,GACA,qBAAqB;YAEvB,OAAO,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE;QACZ,EAAE,OAAO,GAAG;YACV,MAAM,0BAA0B;YAChC,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,YAAY,OAAO,EAAE,QAAQ,KAAK,YAAY,OAAO,EAAE,OAAO,KAAK,UAAU;gBACtG,OAAO,CAAA,GAAA,6QAAA,CAAA,MAAG,AAAD,EAAE;oBACT,MAAM;oBACN,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,QAAQ;oBACpB,SAAS,EAAE,OAAO;oBAClB,QAAQ,EAAE,MAAM;oBAChB,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,IAAI;gBACd;YACF;YACA,MAAM;QACR;IACF;AACF;AACA,IAAI,kBAAkB,cAAc;IAClC,YAAY,MAAM,EAAE,OAAO,CAAE;QAC3B,KAAK,CAAC;QACN,IAAI,CAAC,OAAO,GAAG;IACjB;IACA,MAAM,SAAS;QACb,MAAM,CAAC,YAAY,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,OAAO;QACnB,OAAO,QAAQ,OAAO,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,KAAK;IACjC;IACA,MAAM,WAAW;QACf,MAAM,CAAC,cAAc,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,OAAO;QACnB,OAAO,QAAQ,OAAO,CAAC,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,KAAK;IACjC;AACF;AACA,IAAI,yBAAyB,cAAc;IACzC,YAAY,IAAI,CAAE;QAChB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;IACA,MAAM,mBAAmB;QACvB,MAAM,UAAU;YACd,iBAAiB;QACnB;QACA,MAAM,MAAM;QACZ,MAAM,kBAAkB,KAAK;QAC7B,OAAO,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,IAAI,gBAAgB,IAAI,CAAC,IAAI,EAAE;IAC3C;AACF;AACA,IAAI,aAAa,cAAc;IAC7B,YAAY,IAAI,EAAE,OAAO,CAAE;QACzB,IAAI,CAAC,CAAC,gBAAgB,iOAAA,CAAA,OAAS,GAAG;YAChC,MAAM,IAAI,UAAU,CAAC;;;;AAI3B,CAAC;QACG;QACA,KAAK,CAAC;QACN,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,oBAAoB;QAClB,OAAO,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE;YACR,YAAY,IAAI,CAAC,OAAO,EAAE;QAC5B;IACF;IACA,MAAM,qBAAqB;QACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO;QACtC,OAAO,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,IAAI,uBAAuB;IACvC;IACA,MAAM,QAAQ;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG;YACrB,IAAI,CAAC,SAAS,GAAG;QACnB;QACA,OAAO,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EAAE,KAAK;IACjB;AACF;AACA,IAAI,iBAAiB,cAAc;IACjC,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,MAAM,UAAU,KAAK,EAAE;QACrB,MAAM,EAAE,GAAG,EAAE,MAAM,MAAM,EAAE,GAAG;QAC9B,OAAO,CAAA,GAAA,6QAAA,CAAA,KAAE,AAAD,EACN,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;YAC7B,WAAW;YACX,aAAa;YACb,sEAAsE;YACtE,EAAE;YACF,6CAA6C;YAC7C,6EAA6E;YAC7E,OAAO;gBACL,eAAe,CAAC,KAAK;oBACnB,IAAI,WAAW,UAAU,aAAa,CAAC,IAAI,EAAE;wBAC3C,OAAO,aAAa,CAAC,IAAI;oBAC3B;oBACA,OAAO,iOAAA,CAAA,QAAU,CAAC,aAAa,CAAC,KAAK;gBACvC;YACF;QAGF;IAEJ;IACA,qBAAqB;QACnB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}