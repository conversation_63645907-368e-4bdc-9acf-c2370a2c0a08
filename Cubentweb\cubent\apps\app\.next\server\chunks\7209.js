exports.id=7209,exports.ids=[7209],exports.modules={2382:(e,t,r)=>{"use strict";r.d(t,{_:()=>_});var n=r(28364),o=r(71166),s=r(25),i=r(90647),a=r(23167),l=r(51460),c=r(16248),d=r(18686),E=r(12238);let _=(0,o.w)({extends:[(0,o.w)({server:{CLERK_SECRET_KEY:s.z.string().startsWith("sk_"),CLERK_WEBHOOK_SECRET:s.z.string().startsWith("whsec_").optional()},client:{NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:s.z.string().startsWith("pk_"),NEXT_PUBLIC_CLERK_DOMAIN:s.z.string().optional(),NEXT_PUBLIC_CLERK_SIGN_IN_URL:s.z.string().startsWith("/"),NEXT_PUBLIC_CLERK_SIGN_UP_URL:s.z.string().startsWith("/"),NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL:s.z.string().startsWith("/"),NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL:s.z.string().startsWith("/")},runtimeEnv:{CLERK_SECRET_KEY:process.env.CLERK_SECRET_KEY,CLERK_WEBHOOK_SECRET:process.env.CLERK_WEBHOOK_SECRET,NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:"pk_live_Y2xlcmsuY3ViZW50LmRldiQ",NEXT_PUBLIC_CLERK_DOMAIN:"cubent.dev",NEXT_PUBLIC_CLERK_SIGN_IN_URL:"/sign-in",NEXT_PUBLIC_CLERK_SIGN_UP_URL:"/sign-up",NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL:"/auth-success",NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL:"/terms"}}),(0,n.H)(),(0,i.H)(),(0,o.w)({extends:[(0,c.II)()],server:{ANALYZE:s.z.string().optional(),NEXT_RUNTIME:s.z.enum(["nodejs","edge"]).optional()},client:{NEXT_PUBLIC_APP_URL:s.z.string().url(),NEXT_PUBLIC_WEB_URL:s.z.string().url(),NEXT_PUBLIC_API_URL:s.z.string().url().optional(),NEXT_PUBLIC_DOCS_URL:s.z.string().url().optional()},runtimeEnv:{ANALYZE:process.env.ANALYZE,NEXT_RUNTIME:"nodejs",NEXT_PUBLIC_APP_URL:"http://localhost:3000",NEXT_PUBLIC_WEB_URL:"http://localhost:3001",NEXT_PUBLIC_API_URL:process.env.NEXT_PUBLIC_API_URL,NEXT_PUBLIC_DOCS_URL:"https://cubentdev.mintlify.app"}}),(0,a.H)(),(0,o.w)({server:{RESEND_FROM:s.z.string().email(),RESEND_TOKEN:s.z.string().startsWith("re_")},runtimeEnv:{RESEND_FROM:process.env.RESEND_FROM,RESEND_TOKEN:process.env.RESEND_TOKEN}}),(0,l.H)(),(0,o.w)({server:{KNOCK_SECRET_API_KEY:s.z.string().optional()},client:{NEXT_PUBLIC_KNOCK_API_KEY:s.z.string().optional(),NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:s.z.string().optional()},runtimeEnv:{NEXT_PUBLIC_KNOCK_API_KEY:"",NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:"",KNOCK_SECRET_API_KEY:process.env.KNOCK_SECRET_API_KEY}}),(0,o.w)({server:{BETTERSTACK_API_KEY:s.z.string().optional(),BETTERSTACK_URL:s.z.string().optional(),SENTRY_ORG:s.z.string().optional(),SENTRY_PROJECT:s.z.string().optional()},client:{NEXT_PUBLIC_SENTRY_DSN:s.z.string().url().optional()},runtimeEnv:{BETTERSTACK_API_KEY:process.env.BETTERSTACK_API_KEY,BETTERSTACK_URL:process.env.BETTERSTACK_URL,SENTRY_ORG:process.env.SENTRY_ORG,SENTRY_PROJECT:process.env.SENTRY_PROJECT,NEXT_PUBLIC_SENTRY_DSN:process.env.NEXT_PUBLIC_SENTRY_DSN}}),(0,d.H)(),(0,E.H)()],server:{CRON_SECRET:s.z.string().optional()},client:{},runtimeEnv:{CRON_SECRET:process.env.CRON_SECRET}})},8963:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=8963,e.exports=t},12238:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var n=r(71166),o=r(25);let s=()=>(0,n.w)({server:{SVIX_TOKEN:o.z.union([o.z.string().startsWith("sk_"),o.z.string().startsWith("testsk_")]).optional()},runtimeEnv:{SVIX_TOKEN:process.env.SVIX_TOKEN}})},15330:(e,t,r)=>{"use strict";r.d(t,{PostHogProvider:()=>o});var n=r(6340);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\analytics\\posthog\\client.tsx","PostHogProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAnalytics() from the server but useAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\analytics\\posthog\\client.tsx","useAnalytics")},18686:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var n=r(71166),o=r(25);let s=()=>(0,n.w)({server:{ARCJET_KEY:o.z.string().startsWith("ajkey_").optional()},runtimeEnv:{ARCJET_KEY:process.env.ARCJET_KEY}})},18915:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var n=r(99730),o=r(79830),s=r(45550),i=r(48585);let a=({privacyUrl:e,termsUrl:t,helpUrl:r,...a})=>{let{resolvedTheme:l}=(0,i.D)(),c="dark"===l?s.dark:void 0;return(0,n.jsx)(o.lJ,{...a,appearance:{layout:{privacyPageUrl:e,termsPageUrl:t,helpPageUrl:r},baseTheme:c,elements:{dividerLine:"bg-border",socialButtonsIconButton:"bg-card",navbarButton:"text-foreground",organizationSwitcherTrigger__open:"bg-background",organizationPreviewMainIdentifier:"text-foreground",organizationSwitcherTriggerIcon:"text-muted-foreground",organizationPreview__organizationSwitcherTrigger:"gap-2",organizationPreviewAvatarContainer:"shrink-0"},variables:{fontFamily:"var(--font-sans)",fontFamilyButtons:"var(--font-sans)",fontWeight:{bold:"var(--font-weight-bold)",normal:"var(--font-weight-normal)",medium:"var(--font-weight-medium)"}}}})}},20269:(e,t,r)=>{Promise.resolve().then(r.bind(r,81929))},22477:(e,t,r)=>{Promise.resolve().then(r.bind(r,70814))},28364:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var n=r(71166),o=r(25);let s=()=>(0,n.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:o.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:o.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:o.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}})},29622:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>U});var n=r(94752),o=r(2382);r(52497);var s=r(29329),i=r(28364),a=r(15330),l=r(44920);let{NEXT_PUBLIC_GA_MEASUREMENT_ID:c}=(0,i.H)(),d=({children:e})=>(0,n.jsxs)(a.PostHogProvider,{children:[e,(0,n.jsx)(l.Analytics,{}),c&&(0,n.jsx)(s.GoogleAnalytics,{gaId:c})]});var E=r(44577),_=r(54191),p=r(90411),v=r(15991);let u=({children:e,...t})=>(0,n.jsx)(v.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,...t,children:e}),m=({children:e,privacyUrl:t,termsUrl:r,helpUrl:o,...s})=>(0,n.jsx)(u,{...s,children:(0,n.jsx)(E.AuthProvider,{privacyUrl:t,termsUrl:r,helpUrl:o,children:(0,n.jsxs)(d,{children:[(0,n.jsx)(p.TooltipProvider,{children:e}),(0,n.jsx)(_.Toaster,{})]})})});var h=r(58559),C=r(94055),T=r.n(C),g=r(29663),b=r.n(g);let P=(0,h.cn)(b().variable,T().variable,"touch-manipulation font-sans antialiased");var R=r(99956),f=r(51460);let N=()=>(0,f.H)().FLAGS_SECRET?(0,n.jsx)(R.N,{}):null;var L=r(74265);let U=({children:e})=>(0,n.jsx)("html",{lang:"en",className:P,suppressHydrationWarning:!0,children:(0,n.jsx)("body",{children:(0,n.jsxs)(d,{children:[(0,n.jsxs)(m,{privacyUrl:new URL("/legal/privacy",o._.NEXT_PUBLIC_WEB_URL).toString(),termsUrl:new URL("/legal/terms",o._.NEXT_PUBLIC_WEB_URL).toString(),helpUrl:o._.NEXT_PUBLIC_DOCS_URL,children:[e,(0,n.jsx)(L.CrossDomainAuthSync,{})]}),(0,n.jsx)(N,{})]})})})},30846:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,99597,23)),Promise.resolve().then(r.t.bind(r,71453,23)),Promise.resolve().then(r.t.bind(r,20213,23)),Promise.resolve().then(r.t.bind(r,13748,23)),Promise.resolve().then(r.t.bind(r,52812,23)),Promise.resolve().then(r.t.bind(r,73488,23)),Promise.resolve().then(r.t.bind(r,20134,23)),Promise.resolve().then(r.t.bind(r,15336,23))},31555:(e,t,r)=>{Promise.resolve().then(r.bind(r,87495)),Promise.resolve().then(r.bind(r,86332)),Promise.resolve().then(r.bind(r,19859)),Promise.resolve().then(r.bind(r,19865)),Promise.resolve().then(r.bind(r,354)),Promise.resolve().then(r.bind(r,27022)),Promise.resolve().then(r.bind(r,38980)),Promise.resolve().then(r.bind(r,15138)),Promise.resolve().then(r.bind(r,48585)),Promise.resolve().then(r.t.bind(r,96908,23)),Promise.resolve().then(r.bind(r,22683)),Promise.resolve().then(r.bind(r,89620)),Promise.resolve().then(r.bind(r,18915)),Promise.resolve().then(r.bind(r,84685)),Promise.resolve().then(r.bind(r,41033))},35574:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,10487,23)),Promise.resolve().then(r.t.bind(r,89819,23)),Promise.resolve().then(r.t.bind(r,73391,23)),Promise.resolve().then(r.t.bind(r,31310,23)),Promise.resolve().then(r.t.bind(r,28138,23)),Promise.resolve().then(r.t.bind(r,82926,23)),Promise.resolve().then(r.t.bind(r,58084,23)),Promise.resolve().then(r.t.bind(r,1934,23))},41033:(e,t,r)=>{"use strict";r.d(t,{TooltipProvider:()=>i,ZI:()=>c,k$:()=>l,m_:()=>a});var n=r(99730);r(57752);var o=r(47887),s=r(83590);function i({delayDuration:e=0,...t}){return(0,n.jsx)(o.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function a({...e}){return(0,n.jsx)(i,{children:(0,n.jsx)(o.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,n.jsx)(o.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:r,...i}){return(0,n.jsx)(o.ZL,{children:(0,n.jsxs)(o.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...i,children:[r,(0,n.jsx)(o.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},44490:(e,t,r)=>{"use strict";r.d(t,{y:()=>o});var n=r(96242);let o=(0,n.createServerReference)("7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},44577:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\auth\\provider.tsx","AuthProvider")},51460:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var n=r(71166),o=r(25);let s=()=>(0,n.w)({server:{FLAGS_SECRET:o.z.string().optional()},runtimeEnv:{FLAGS_SECRET:process.env.FLAGS_SECRET}})},52497:()=>{},54191:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sonner.tsx","Toaster")},55531:(e,t,r)=>{Promise.resolve().then(r.bind(r,74265)),Promise.resolve().then(r.t.bind(r,21034,23)),Promise.resolve().then(r.t.bind(r,41853,23)),Promise.resolve().then(r.t.bind(r,48031,23)),Promise.resolve().then(r.t.bind(r,80476,23)),Promise.resolve().then(r.bind(r,44920)),Promise.resolve().then(r.bind(r,11106)),Promise.resolve().then(r.bind(r,8340)),Promise.resolve().then(r.bind(r,15991)),Promise.resolve().then(r.t.bind(r,21970,23)),Promise.resolve().then(r.bind(r,93665)),Promise.resolve().then(r.bind(r,15330)),Promise.resolve().then(r.bind(r,44577)),Promise.resolve().then(r.bind(r,54191)),Promise.resolve().then(r.bind(r,90411))},58559:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s}),r(74433);var n=r(35371);r(93665);var o=r(48410);let s=(...e)=>(0,o.QP)((0,n.$)(e))},70814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\apps\\\\app\\\\app\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx","default")},74265:(e,t,r)=>{"use strict";r.d(t,{CrossDomainAuthSync:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call CrossDomainAuthSync() from the server but CrossDomainAuthSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\components\\CrossDomainAuthSync.tsx","CrossDomainAuthSync")},74433:(e,t,r)=>{"use strict";r.d(t,{u:()=>s});var n=r(4520),o=r(76741);let s=e=>{let t="An error occurred";t=e instanceof Error||e&&"object"==typeof e&&"message"in e?e.message:String(e);try{(0,n.captureException)(e),o.R.error(`Parsing error: ${t}`)}catch(e){console.error("Error parsing error:",e)}return t}},74619:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=74619,e.exports=t},74938:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var n=r(99730);r(57752);var o=r(58576),s=r(72795),i=r(83590);let a=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:s=!1,...l}){let c=s?o.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,i.cn)(a({variant:t,size:r,className:e})),...l})}},76741:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});let n=r(42870).log},81929:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(99730),o=r(74938),s=r(83590),i=r(24319),a=r.n(i),l=r(24087),c=r.n(l);let d=(0,s.cn)(c().variable,a().variable,"touch-manipulation font-sans antialiased");var E=r(74562),_=r(57752);let p=({error:e,reset:t})=>((0,_.useEffect)(()=>{(0,E.captureException)(e)},[e]),(0,n.jsx)("html",{lang:"en",className:d,children:(0,n.jsxs)("body",{children:[(0,n.jsx)("h1",{children:"Oops, something went wrong"}),(0,n.jsx)(o.$,{onClick:()=>t(),children:"Try again"})]})}))},83590:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s}),r(60188);var n=r(29085);r(22683);var o=r(63632);let s=(...e)=>(0,o.QP)((0,n.$)(e))},84685:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>i});var n=r(99730),o=r(48585),s=r(22683);let i=({...e})=>{let{theme:t="system"}=(0,o.D)();return(0,n.jsx)(s.l,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},87495:(e,t,r)=>{"use strict";r.d(t,{CrossDomainAuthSync:()=>o});var n=r(2233);function o(){let{isLoaded:e,isSignedIn:t,user:r}=(0,n.Jd)();return null}r(57752)},89620:(e,t,r)=>{"use strict";r.d(t,{PostHogProvider:()=>d,s:()=>s.sf});var n=r(99730),o=r(1429),s=r(87805),i=r(57752),a=r(79796),l=r(42294);let c=()=>(0,a.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:l.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:l.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:l.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}}),d=e=>((0,i.useEffect)(()=>{try{let e=c();e.NEXT_PUBLIC_POSTHOG_KEY&&e.NEXT_PUBLIC_POSTHOG_HOST?o.Ay.init(e.NEXT_PUBLIC_POSTHOG_KEY,{api_host:"/ingest",ui_host:e.NEXT_PUBLIC_POSTHOG_HOST,person_profiles:"identified_only",capture_pageview:!1,capture_pageleave:!0}):console.warn("PostHog environment variables not configured. Analytics disabled.")}catch(e){console.warn("PostHog initialization failed:",e)}},[]),(0,n.jsx)(s.so,{client:o.Ay,...e}))},90411:(e,t,r)=>{"use strict";r.d(t,{TooltipProvider:()=>o});var n=r(6340);(0,n.registerClientReference)(function(){throw Error("Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","Tooltip"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipTrigger"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipContent");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipProvider")},90647:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var n=r(71166),o=r(25);let s=()=>(0,n.w)({server:{LIVEBLOCKS_SECRET:o.z.string().startsWith("sk_").optional()},runtimeEnv:{LIVEBLOCKS_SECRET:process.env.LIVEBLOCKS_SECRET}})}};