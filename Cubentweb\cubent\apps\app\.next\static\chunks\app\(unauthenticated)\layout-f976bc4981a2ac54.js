(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7311],{31918:(e,t,n)=>{"use strict";n.d(t,{cn:()=>s}),n(63410);var r=n(49973);n(13957);var a=n(22928);let s=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.QP)((0,r.$)(t))}},38726:(e,t,n)=>{"use strict";n.d(t,{ModeToggle:()=>f});var r=n(6024),a=n(14844),s=n(72881),o=n(70234);n(50628);var i=n(75447),d=n(31918);function l(e){let{...t}=e;return(0,r.jsx)(i.bL,{"data-slot":"dropdown-menu",...t})}function u(e){let{...t}=e;return(0,r.jsx)(i.l9,{"data-slot":"dropdown-menu-trigger",...t})}function c(e){let{className:t,sideOffset:n=4,...a}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsx)(i.UC,{"data-slot":"dropdown-menu-content",sideOffset:n,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...a})})}function m(e){let{className:t,inset:n,variant:a="default",...s}=e;return(0,r.jsx)(i.q7,{"data-slot":"dropdown-menu-item","data-inset":n,"data-variant":a,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}let v=[{label:"Light",value:"light"},{label:"Dark",value:"dark"},{label:"System",value:"system"}],f=()=>{let{setTheme:e}=(0,s.D)();return(0,r.jsxs)(l,{children:[(0,r.jsx)(u,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"shrink-0 text-foreground",children:[(0,r.jsx)(a.gLX,{className:"dark:-rotate-90 h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:scale-0"}),(0,r.jsx)(a.rRK,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsx)(c,{children:v.map(t=>{let{label:n,value:a}=t;return(0,r.jsx)(m,{onClick:()=>e(a),children:n},a)})})]})}},64714:(e,t,n)=>{"use strict";n.d(t,{C:()=>o});var r=n(50628),a=n(98064),s=n(84268),o=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[a,o]=r.useState(),d=r.useRef(null),l=r.useRef(e),u=r.useRef("none"),[c,m]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(d.current);u.current="mounted"===c?e:"none"},[c]),(0,s.N)(()=>{let t=d.current,n=l.current;if(n!==e){let r=u.current,a=i(t);e?m("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?m("UNMOUNT"):n&&r!==a?m("ANIMATION_OUT"):m("UNMOUNT"),l.current=e}},[e,m]),(0,s.N)(()=>{if(a){var e;let t,n=null!=(e=a.ownerDocument.defaultView)?e:window,r=e=>{let r=i(d.current).includes(e.animationName);if(e.target===a&&r&&(m("ANIMATION_END"),!l.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},s=e=>{e.target===a&&(u.current=i(d.current))};return a.addEventListener("animationstart",s),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{n.clearTimeout(t),a.removeEventListener("animationstart",s),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}m("ANIMATION_END")},[a,m]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{d.current=e?getComputedStyle(e):null,o(e)},[])}}(t),d="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),l=(0,a.s)(o.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,a=r&&"isReactWarning"in r&&r.isReactWarning;return a?e.ref:(a=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof n||o.isPresent?r.cloneElement(d,{ref:l}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},70234:(e,t,n)=>{"use strict";n.d(t,{$:()=>d});var r=n(6024);n(50628);var a=n(89840),s=n(81197),o=n(31918);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:n,size:s,asChild:d=!1,...l}=e,u=d?a.DX:"button";return(0,r.jsx)(u,{"data-slot":"button",className:(0,o.cn)(i({variant:n,size:s,className:t})),...l})}},72881:(e,t,n)=>{"use strict";n.d(t,{D:()=>u,ThemeProvider:()=>c});var r=n(50628),a=(e,t,n,r,a,s,o,i)=>{let d=document.documentElement,l=["light","dark"];function u(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&s?a.map(e=>s[e]||e):a;n?(d.classList.remove(...r),d.classList.add(s&&s[t]?s[t]:t)):d.setAttribute(e,t)}),n=t,i&&l.includes(n)&&(d.style.colorScheme=n)}if(r)u(r);else try{let e=localStorage.getItem(t)||n,r=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;u(r)}catch(e){}},s=["light","dark"],o="(prefers-color-scheme: dark)",i="undefined"==typeof window,d=r.createContext(void 0),l={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=r.useContext(d))?e:l},c=e=>r.useContext(d)?r.createElement(r.Fragment,null,e.children):r.createElement(v,{...e}),m=["light","dark"],v=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:a=!0,enableColorScheme:i=!0,storageKey:l="theme",themes:u=m,defaultTheme:c=a?"system":"light",attribute:v="data-theme",value:b,children:y,nonce:x,scriptProps:w}=e,[N,k]=r.useState(()=>h(l,c)),[T,E]=r.useState(()=>"system"===N?p():N),C=b?Object.values(b):u,O=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&a&&(t=p());let r=b?b[t]:t,o=n?g(x):null,d=document.documentElement,l=e=>{"class"===e?(d.classList.remove(...C),r&&d.classList.add(r)):e.startsWith("data-")&&(r?d.setAttribute(e,r):d.removeAttribute(e))};if(Array.isArray(v)?v.forEach(l):l(v),i){let e=s.includes(c)?c:null,n=s.includes(t)?t:e;d.style.colorScheme=n}null==o||o()},[x]),S=r.useCallback(e=>{let t="function"==typeof e?e(N):e;k(t);try{localStorage.setItem(l,t)}catch(e){}},[N]),M=r.useCallback(e=>{E(p(e)),"system"===N&&a&&!t&&O("system")},[N,t]);r.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),r.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?k(e.newValue):S(c))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),r.useEffect(()=>{O(null!=t?t:N)},[t,N]);let _=r.useMemo(()=>({theme:N,setTheme:S,forcedTheme:t,resolvedTheme:"system"===N?T:N,themes:a?[...u,"system"]:u,systemTheme:a?T:void 0}),[N,S,t,T,a,u]);return r.createElement(d.Provider,{value:_},r.createElement(f,{forcedTheme:t,storageKey:l,attribute:v,enableSystem:a,enableColorScheme:i,defaultTheme:c,value:b,themes:u,nonce:x,scriptProps:w}),y)},f=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:s,enableSystem:o,enableColorScheme:i,defaultTheme:d,value:l,themes:u,nonce:c,scriptProps:m}=e,v=JSON.stringify([s,n,d,t,u,l,o,i]).slice(1,-1);return r.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?c:"",dangerouslySetInnerHTML:{__html:"(".concat(a.toString(),")(").concat(v,")")}})}),h=(e,t)=>{let n;if(!i){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},g=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},p=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},98755:(e,t,n)=>{Promise.resolve().then(n.bind(n,38726))}},e=>{var t=t=>e(e.s=t);e.O(0,[6584,449,9222,3572,5514,7764,5447,2913,4499,7358],()=>t(98755)),_N_E=e.O()}]);