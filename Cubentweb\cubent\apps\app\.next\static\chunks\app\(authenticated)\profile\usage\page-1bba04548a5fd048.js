(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2689],{29011:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(6024);s(50628);var r=s(89840),n=s(81197),i=s(31918);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:n=!1,...c}=e,o=n?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(l({variant:s}),t),...c})}},31918:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n}),s(63410);var a=s(49973);s(13957);var r=s(22928);let n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},66592:(e,t,s)=>{Promise.resolve().then(s.bind(s,94179))},69680:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i});var a=s(6024);s(50628);var r=s(31918);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},70234:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(6024);s(50628);var r=s(89840),n=s(81197),i=s(31918);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:n,asChild:c=!1,...o}=e,d=c?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...o})}},94179:(e,t,s)=>{"use strict";s.d(t,{UsageAnalytics:()=>A});var a=s(6024),r=s(50628),n=s(70234),i=s(69680),l=s(29011),c=s(55844),o=s(31918);function d(e){let{className:t,value:s,...r}=e;return(0,a.jsx)(c.bL,{"data-slot":"progress",className:(0,o.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...r,children:(0,a.jsx)(c.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}var x=s(41095);function u(e){let{className:t,...s}=e;return(0,a.jsx)(x.bL,{"data-slot":"tabs",className:(0,o.cn)("flex flex-col gap-2",t),...s})}function m(e){let{className:t,...s}=e;return(0,a.jsx)(x.B8,{"data-slot":"tabs-list",className:(0,o.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(x.l9,{"data-slot":"tabs-trigger",className:(0,o.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function g(e){let{className:t,...s}=e;return(0,a.jsx)(x.UC,{"data-slot":"tabs-content",className:(0,o.cn)("flex-1 outline-none",t),...s})}var f=s(10968),p=s(60326),v=s(6092),b=s(74144),j=s(84665),y=s(51109),N=s(44e3),w=s(25866),k=s(35685),U=s.n(k);function C(e){let{data:t}=e,s=(0,r.useMemo)(()=>{let e=new Date,s=new Date(e);s.setDate(e.getDate()-29);let a=[];for(let e=0;e<30;e++){let r=new Date(s);r.setDate(s.getDate()+e);let n=t.find(e=>new Date(e.date).toDateString()===r.toDateString());a.push({date:r.toLocaleDateString("en-US",{month:"short",day:"numeric"}),cubentUnits:(null==n?void 0:n.cubentUnitsUsed)||0,requests:(null==n?void 0:n.requestsMade)||0})}return a},[t]),n=Math.max(...s.map(e=>e.cubentUnits),1),i=Math.max(...s.map(e=>e.requests),1),l=e=>{if(e.length<2)return"";let t="M ".concat(e[0].x," ").concat(e[0].y);for(let r=1;r<e.length;r++){let n=e[r-1],i=e[r],l=e[r+1];if(1===r){let e=n.x+(i.x-n.x)*.3,s=n.y,a=i.x-(i.x-n.x)*.3,r=i.y;t+=" C ".concat(e," ").concat(s,", ").concat(a," ").concat(r,", ").concat(i.x," ").concat(i.y)}else if(r===e.length-1){let e=n.x+(i.x-n.x)*.3,s=n.y,a=i.x-(i.x-n.x)*.3,r=i.y;t+=" C ".concat(e," ").concat(s,", ").concat(a," ").concat(r,", ").concat(i.x," ").concat(i.y)}else{var s,a;let c=n.x+(i.x-n.x)*.3,o=n.y+.1*(l.y-(null==(s=e[r-2])?void 0:s.y)||0),d=i.x-(i.x-n.x)*.3,x=i.y-.1*((null==(a=e[r+1])?void 0:a.y)-n.y||0);t+=" C ".concat(c," ").concat(o,", ").concat(d," ").concat(x,", ").concat(i.x," ").concat(i.y)}}return t};return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"relative h-64 w-full",children:[(0,a.jsxs)("div",{className:"absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-400 dark:text-gray-500 py-4 pr-4",children:[(0,a.jsx)("span",{children:n.toFixed(0)}),(0,a.jsx)("span",{children:(.75*n).toFixed(0)}),(0,a.jsx)("span",{children:(.5*n).toFixed(0)}),(0,a.jsx)("span",{children:(.25*n).toFixed(0)}),(0,a.jsx)("span",{children:"0"})]}),(0,a.jsx)("div",{className:"absolute inset-0 left-12 flex flex-col justify-between py-4",children:[0,1,2,3,4].map(e=>(0,a.jsx)("div",{className:"w-full border-t border-gray-100 dark:border-gray-800"},e))}),(0,a.jsxs)("div",{className:"relative h-full ml-12 py-4",children:[(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 ".concat(10*s.length," 200"),children:[(0,a.jsxs)("defs",{children:[(0,a.jsxs)("linearGradient",{id:"blueGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#3b82f6",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#3b82f6",stopOpacity:"0.05"})]}),(0,a.jsxs)("linearGradient",{id:"greenGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#10b981",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#10b981",stopOpacity:"0.05"})]})]}),(()=>{let e=s.map((e,t)=>({x:10*t+5,y:200-(n>0?e.cubentUnits/n*180:0)})),t=l(e),r=t+" L ".concat(e[e.length-1].x," 200 L ").concat(e[0].x," 200 Z");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("path",{d:r,fill:"url(#blueGradient)"}),(0,a.jsx)("path",{d:t,stroke:"#3b82f6",strokeWidth:"2",fill:"none",className:"drop-shadow-sm"}),e.map((e,t)=>(0,a.jsx)("circle",{cx:e.x,cy:e.y,r:"3",fill:"#3b82f6",className:"hover:r-4 transition-all cursor-pointer"},t))]})})(),(()=>{let e=s.map((e,t)=>({x:10*t+5,y:200-(i>0?e.requests/i*60:0)})),t=l(e);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("path",{d:t,stroke:"#10b981",strokeWidth:"2",fill:"none",strokeDasharray:"5,5",className:"opacity-70"}),e.map((e,t)=>(0,a.jsx)("circle",{cx:e.x,cy:e.y,r:"2",fill:"#10b981",className:"hover:r-3 transition-all cursor-pointer"},t))]})})()]}),(0,a.jsx)("div",{className:"absolute inset-0 flex",children:s.map((e,t)=>(0,a.jsxs)("div",{className:"flex-1 relative group cursor-pointer",children:[(0,a.jsx)("div",{className:"absolute inset-0 hover:bg-blue-50 dark:hover:bg-blue-950/20 transition-colors rounded"}),(0,a.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-20 shadow-lg border border-gray-700",children:[(0,a.jsx)("div",{className:"font-medium text-center",children:e.date}),(0,a.jsxs)("div",{className:"text-blue-300",children:[e.cubentUnits.toFixed(2)," units"]}),(0,a.jsxs)("div",{className:"text-green-300",children:[e.requests," messages"]}),(0,a.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900 dark:border-t-gray-800"})]})]},t))})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-12 right-0 flex justify-between text-xs text-gray-400 dark:text-gray-500 mt-2",children:s.map((e,t)=>t%6==0&&(0,a.jsx)("div",{className:"transform -rotate-45 origin-left",children:e.date},t))})]}),(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-100 dark:border-gray-800",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:s.reduce((e,t)=>e+t.cubentUnits,0).toFixed(1)}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total Units"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:s.reduce((e,t)=>e+t.requests,0)}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total Messages"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:s.reduce((e,t)=>e+t.cubentUnits,0)>0&&s.reduce((e,t)=>e+t.requests,0)>0?(s.reduce((e,t)=>e+t.cubentUnits,0)/s.reduce((e,t)=>e+t.requests,0)).toFixed(2):"0.00"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Avg Units/Message"})]})]})})]})}function A(e){let{initialData:t}=e,[s,c]=(0,r.useState)(t),[o,x]=(0,r.useState)(!1),[k,A]=(0,r.useState)(new Date),D=async()=>{x(!0);try{let e=await fetch("/api/extension/usage/stats",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){let t=await e.json();t.success&&(c(e=>({...e,totalCubentUnits:t.totalCubentUnits,totalMessages:t.totalMessages,userLimit:t.userLimit,subscriptionTier:t.subscriptionTier})),A(new Date))}}catch(e){console.error("Failed to refresh usage data:",e)}finally{x(!1)}};(0,r.useEffect)(()=>{let e=setInterval(D,3e4);return()=>clearInterval(e)},[]);let Z=s.totalCubentUnits/s.userLimit*100,F=Z>80,M=Z>100,T=(e=>{switch(e){case"pro":return{name:"Pro",icon:f.A,color:"text-yellow-600"};case"premium":return{name:"Premium",icon:p.A,color:"text-purple-600"};default:return{name:"Free Trial",icon:v.A,color:"text-blue-600"}}})(s.subscriptionTier),B=T.icon;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",children:[(0,a.jsx)("div",{className:"flex items-center gap-2 px-4",children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(U(),{href:"/profile",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})})}),(0,a.jsxs)("div",{className:"ml-auto flex items-center gap-2 px-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last updated"}),(0,a.jsx)("p",{className:"text-sm font-medium",children:k.toLocaleTimeString()})]}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:D,disabled:o,children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2 ".concat(o?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(n.$,{size:"sm",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})]}),(0,a.jsxs)("main",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:[(0,a.jsx)("div",{className:"flex items-center justify-between space-y-2",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Usage Analytics"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Here's what happening with your usage today"})]})}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Cubent Units"}),(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.totalCubentUnits.toFixed(2)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{className:"font-medium ".concat(M?"text-red-500":F?"text-yellow-500":"text-green-500"),children:[Z.toFixed(0),"%"]}),(0,a.jsxs)("span",{children:["of ",s.userLimit," limit"]})]}),(0,a.jsx)(d,{value:Math.min(Z,100),className:"mt-2 h-1"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Messages"}),(0,a.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.totalMessages.toLocaleString()}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["+",Math.round(s.totalMessages/30*7)," this week"]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Efficiency"}),(0,a.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.totalMessages>0?(s.totalCubentUnits/s.totalMessages).toFixed(2):"0.00"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"units per message"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Subscription"}),(0,a.jsx)(B,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.userLimit}),(0,a.jsx)("div",{className:"flex items-center space-x-2 text-xs text-muted-foreground",children:(0,a.jsxs)(l.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(B,{className:"h-3 w-3 mr-1 ".concat(T.color)}),T.name]})})]})]})]}),(0,a.jsxs)(u,{defaultValue:"overview",className:"space-y-4",children:[(0,a.jsxs)(m,{children:[(0,a.jsx)(h,{value:"overview",children:"Overview"}),(0,a.jsx)(h,{value:"analytics",children:"Analytics"}),(0,a.jsx)(h,{value:"reports",children:"Reports"})]}),(0,a.jsx)(g,{value:"overview",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Usage Overview"}),(0,a.jsx)(i.BT,{children:"Daily consumption for the last 30 days"})]}),(0,a.jsx)(i.Wu,{className:"pl-2",children:(0,a.jsx)(C,{data:s.chartData})})]})}),(0,a.jsx)(g,{value:"analytics",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Detailed Analytics"}),(0,a.jsx)(i.BT,{children:"Advanced usage metrics and trends"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"Advanced analytics coming soon..."})})]})}),(0,a.jsx)(g,{value:"reports",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Usage Reports"}),(0,a.jsx)(i.BT,{children:"Export and download usage reports"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"Report generation coming soon..."})})]})})]}),(F||M)&&"free_trial"===s.subscriptionTier&&(0,a.jsx)(i.Zp,{className:"border-yellow-200 bg-yellow-50 dark:border-yellow-800/50 dark:bg-yellow-900/10",children:(0,a.jsxs)(i.Wu,{className:"flex items-center justify-between p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"flex h-12 w-12 items-center justify-center rounded-xl bg-yellow-100 dark:bg-yellow-900/30",children:(0,a.jsx)(f.A,{className:"h-6 w-6 text-yellow-600 dark:text-yellow-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:M?"Usage Limit Exceeded":"Approaching Usage Limit"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Upgrade to Pro for unlimited Cubent Units and advanced features."})]})]}),(0,a.jsxs)(n.$,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Upgrade Now"]})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,7651,7764,4094,2913,4499,7358],()=>t(66592)),_N_E=e.O()}]);