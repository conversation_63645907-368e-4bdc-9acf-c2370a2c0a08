exports.id=1359,exports.ids=[1359],exports.modules={1359:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});var n=r(12901),i=r(37838);async function s(){r(1447);let{userId:e}=await (0,i.j)();return e?(await (0,n.$)()).users.getUser(e):null}},1447:()=>{},12901:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(8741),i=r(23056),s=r(76315),a=r(97495);let l=new(r(16698)).AsyncLocalStorage;var o=r(60606);let d=async()=>{var e,t;let r;try{let e=await (0,i.TG)(),t=(0,a._b)(e,n.AA.Headers.ClerkRequestData);r=(0,o.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let d=null!=(t=null==(e=l.getStore())?void 0:e.get("requestData"))?t:r;return(null==d?void 0:d.secretKey)||(null==d?void 0:d.publishableKey)?(0,s.n)(d):(0,s.n)({})}},26790:(e,t,r)=>{"use strict";r.d(t,{z:()=>j});var n,i,s,a,l,o,d,u,c,h,g,p,f,y,S,m,b,w,v,k=r(45940);r(92867);var A=r(37081);r(27322),r(6264);var x=r(49530),K=r(57136),E=r(94051),T=class{constructor(){(0,E.VK)(this,s),(0,E.VK)(this,n,"clerk_telemetry_throttler"),(0,E.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,E.S7)(this,s,o))return!1;let t=Date.now(),r=(0,E.jq)(this,s,a).call(this,e),d=(0,E.S7)(this,s,l)?.[r];if(!d){let e={...(0,E.S7)(this,s,l),[r]:t};localStorage.setItem((0,E.S7)(this,n),JSON.stringify(e))}if(d&&t-d>(0,E.S7)(this,i)){let e=(0,E.S7)(this,s,l);delete e[r],localStorage.setItem((0,E.S7)(this,n),JSON.stringify(e))}return!!d}};n=new WeakMap,i=new WeakMap,s=new WeakSet,a=function(e){let{sk:t,pk:r,payload:n,...i}=e,s={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>s[e]))},l=function(){let e=localStorage.getItem((0,E.S7)(this,n));return e?JSON.parse(e):{}},o=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,E.S7)(this,n)),!1}};var U={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},N=class{constructor(e){(0,E.VK)(this,p),(0,E.VK)(this,d),(0,E.VK)(this,u),(0,E.VK)(this,c,{}),(0,E.VK)(this,h,[]),(0,E.VK)(this,g),(0,E.OV)(this,d,{maxBufferSize:e.maxBufferSize??U.maxBufferSize,samplingRate:e.samplingRate??U.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:U.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,E.S7)(this,c).clerkVersion=e.clerkVersion??"":(0,E.S7)(this,c).clerkVersion="",(0,E.S7)(this,c).sdk=e.sdk,(0,E.S7)(this,c).sdkVersion=e.sdkVersion,(0,E.S7)(this,c).publishableKey=e.publishableKey??"";let t=(0,K.q5)(e.publishableKey);t&&((0,E.S7)(this,c).instanceType=t.instanceType),e.secretKey&&((0,E.S7)(this,c).secretKey=e.secretKey.substring(0,16)),(0,E.OV)(this,u,new T)}get isEnabled(){return!("development"!==(0,E.S7)(this,c).instanceType||(0,E.S7)(this,d).disabled||"undefined"!=typeof process&&(0,x.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,E.S7)(this,d).debug||"undefined"!=typeof process&&(0,x.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,E.jq)(this,p,v).call(this,e.event,e.payload);(0,E.jq)(this,p,b).call(this,t.event,t),(0,E.jq)(this,p,f).call(this,t,e.eventSamplingRate)&&((0,E.S7)(this,h).push(t),(0,E.jq)(this,p,S).call(this))}};d=new WeakMap,u=new WeakMap,c=new WeakMap,h=new WeakMap,g=new WeakMap,p=new WeakSet,f=function(e,t){return this.isEnabled&&!this.isDebug&&(0,E.jq)(this,p,y).call(this,e,t)},y=function(e,t){let r=Math.random();return!!(r<=(0,E.S7)(this,d).samplingRate&&(void 0===t||r<=t))&&!(0,E.S7)(this,u).isEventThrottled(e)},S=function(){if("undefined"==typeof window)return void(0,E.jq)(this,p,m).call(this);if((0,E.S7)(this,h).length>=(0,E.S7)(this,d).maxBufferSize){(0,E.S7)(this,g)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,E.S7)(this,g)),(0,E.jq)(this,p,m).call(this);return}(0,E.S7)(this,g)||("requestIdleCallback"in window?(0,E.OV)(this,g,requestIdleCallback(()=>{(0,E.jq)(this,p,m).call(this)})):(0,E.OV)(this,g,setTimeout(()=>{(0,E.jq)(this,p,m).call(this)},0)))},m=function(){fetch(new URL("/v1/event",(0,E.S7)(this,d).endpoint),{method:"POST",body:JSON.stringify({events:(0,E.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,E.OV)(this,h,[])}).catch(()=>void 0)},b=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},w=function(){let e={name:(0,E.S7)(this,c).sdk,version:(0,E.S7)(this,c).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){let r=(0,E.jq)(this,p,w).call(this);return{event:e,cv:(0,E.S7)(this,c).clerkVersion??"",it:(0,E.S7)(this,c).instanceType??"",sdk:r.name,sdkv:r.version,...(0,E.S7)(this,c).publishableKey?{pk:(0,E.S7)(this,c).publishableKey}:{},...(0,E.S7)(this,c).secretKey?{sk:(0,E.S7)(this,c).secretKey}:{},payload:t}};function j(e){let t={...e},r=(0,k.y3)(t),n=(0,k.Bs)({options:t,apiClient:r}),i=new N({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...n,telemetry:i}}(0,A.C)(k.nr)},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>D});var n=r(8741),i=r(62923),s=r(54726),a=r(87553),l=r(3680);let o=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},d=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?o(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,o(t)])),null,2)).join(", "),u=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){var i,s;for(let n of(console.log((i=e,`[clerk debug start: ${i}]`)),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return n.decode(i).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((s=e,`[clerk debug end: ${s}] (@clerk/nextjs=6.20.0,next=${l.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},c=(e,t)=>(...r)=>{let n=("string"==typeof e?u(e,d):e)(),i=t(n);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};var h=r(74365),g=r(37081),p=r(27322),f=r(6264);function y(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return p.r0.stringify(r,{pad:!1})}async function S(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let n=new TextEncoder,i=(0,p.hJ)(r.algorithm);if(!i)return{errors:[new f.xy(`Unsupported algorithm ${r.algorithm}`)]};let s=await (0,p.Fh)(t,i,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let l=y(a),o=y(e),d=`${l}.${o}`;try{let e=await p.fA.crypto.subtle.sign(i,s,n.encode(d));return{data:`${d}.${p.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new f.xy(e?.message)]}}}(0,g.C)(p.J0);var m=(0,g.R)(p.iU);(0,g.C)(S),(0,g.C)(p.nk);var b=r(97495),w=r(60606);function v(e,{treatPendingAsSignedOut:t=!0,...r}={}){var i,a,l;let o,d=(0,b.NE)(e,"AuthStatus"),u=(0,b.NE)(e,"AuthToken"),c=(0,b.NE)(e,"AuthMessage"),h=(0,b.NE)(e,"AuthReason"),g=(0,b.NE)(e,"AuthSignature");null==(i=r.logger)||i.debug("headers",{authStatus:d,authMessage:c,authReason:h});let p=(0,b._b)(e,n.AA.Headers.ClerkRequestData),f=(0,w.Kk)(p),y={secretKey:(null==r?void 0:r.secretKey)||f.secretKey||s.rB,publishableKey:f.publishableKey||s.At,apiUrl:s.H$,apiVersion:s.mG,authStatus:d,authMessage:c,authReason:h,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",y),d&&d===n.TD.SignedIn){(0,w._l)(u,y.secretKey,g);let e=m(u);null==(l=r.logger)||l.debug("jwt",e.raw),o=(0,n.Z5)(y,e.raw.text,e.payload)}else o=(0,n.wI)(y);return t&&"pending"===o.sessionStatus&&(o=(0,n.wI)(y,o.sessionStatus)),o}var k=r(68478);let A=({debugLoggerName:e,noAuthStatusMessage:t})=>c(e,e=>async(i,s)=>{if((0,a.zz)((0,b._b)(i,n.AA.Headers.EnableDebug))&&e.enable(),!(0,b.Zd)(i)){h.M&&(0,w.$K)(i,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,w.$K)(i,t)}return v(i,{...s,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>c(e,e=>(r,i)=>((0,a.zz)((0,b._b)(r,n.AA.Headers.EnableDebug))&&e.enable(),(0,w.$K)(r,t),v(r,{...i,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,k.AG)()});let x={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},K=e=>{var t,r;return!!e.headers.get(x.Headers.NextUrl)&&((null==(t=e.headers.get(n.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(n.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(x.Headers.NextAction))},E=e=>{var t;return"document"===e.headers.get(n.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(n.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(n.AA.Headers.Accept))?void 0:t.includes("text/html"))||T(e)||N(e)},T=e=>!!e.headers.get(x.Headers.NextUrl)&&!K(e)||U(),U=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},N=e=>!!e.headers.get(x.Headers.NextjsData);var j=r(23056);let D=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,j.TG)(),a=async()=>{if(h.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},l=await A({debugLoggerName:"auth()",noAuthStatusMessage:(0,k.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),o=(0,b.NE)(t,"ClerkUrl"),d=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,n.tl)(t),d=a.clerkUrl.searchParams.get(n.AA.QueryParameters.DevBrowser)||a.cookies.get(n.AA.Cookies.DevBrowser),u=(0,b._b)(t,n.AA.Headers.ClerkRequestData),c=(0,w.Kk)(u);return[(0,n.vH)({redirectAdapter:i.redirect,devBrowserToken:d,baseUrl:a.clerkUrl.toString(),publishableKey:c.publishableKey||s.At,signInUrl:c.signInUrl||s.qW,signUpUrl:c.signUpUrl||s.sE,sessionStatus:l.sessionStatus}),null===r?"":r||(null==o?void 0:o.toString())]};return Object.assign(l,{redirectToSignIn:(e={})=>{let[t,r]=d(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=d(e);return t.redirectToSignUp({returnBackUrl:r})}})};D.protect=async(...e)=>{r(1447);let t=await (0,j.TG)(),n=await D();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:n,notFound:i,request:s}=e;return async(...e)=>{var a,l,o,d,u,c;let h=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(l=e[0])?void 0:l.unauthorizedUrl)?void 0:e[0],g=(null==(o=e[0])?void 0:o.unauthenticatedUrl)||(null==(d=e[1])?void 0:d.unauthenticatedUrl),p=(null==(u=e[0])?void 0:u.unauthorizedUrl)||(null==(c=e[1])?void 0:c.unauthorizedUrl),f=()=>p?n(p):i();return"pending"!==r.sessionStatus&&r.userId?h?"function"==typeof h?h(r.has)?r:f():r.has(h)?r:f():r:g?n(g):E(s)?t():i()}})({request:t,authObject:n,redirectToSignIn:n.redirectToSignIn,notFound:i.notFound,redirect:i.redirect})(...e)}},76315:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(26790),i=r(54726);let s={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},a=e=>(0,n.z)({...s,...e})}};