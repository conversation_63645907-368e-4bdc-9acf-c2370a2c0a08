{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/throttler.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/collector.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/component-mounted.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/method-called.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/framework-metadata.ts"], "sourcesContent": ["import type { TelemetryEvent } from '@clerk/types';\n\ntype TtlInMilliseconds = number;\n\nconst DEFAULT_CACHE_TTL_MS = 86400000; // 24 hours\n\n/**\n * Manages throttling for telemetry events using the browser's localStorage to\n * mitigate event flooding in frequently executed code paths.\n */\nexport class TelemetryEventThrottler {\n  #storageKey = 'clerk_telemetry_throttler';\n  #cacheTtl = DEFAULT_CACHE_TTL_MS;\n\n  isEventThrottled(payload: TelemetryEvent): boolean {\n    if (!this.#isValidBrowser) {\n      return false;\n    }\n\n    const now = Date.now();\n    const key = this.#generateKey(payload);\n    const entry = this.#cache?.[key];\n\n    if (!entry) {\n      const updatedCache = {\n        ...this.#cache,\n        [key]: now,\n      };\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    const shouldInvalidate = entry && now - entry > this.#cacheTtl;\n    if (shouldInvalidate) {\n      const updatedCache = this.#cache;\n      delete updatedCache[key];\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    return !!entry;\n  }\n\n  /**\n   * Generates a consistent unique key for telemetry events by sorting payload properties.\n   * This ensures that payloads with identical content in different orders produce the same key.\n   */\n  #generateKey(event: TelemetryEvent): string {\n    const { sk: _sk, pk: _pk, payload, ...rest } = event;\n\n    const sanitizedEvent: Omit<TelemetryEvent, 'sk' | 'pk' | 'payload'> & TelemetryEvent['payload'] = {\n      ...payload,\n      ...rest,\n    };\n\n    return JSON.stringify(\n      Object.keys({\n        ...payload,\n        ...rest,\n      })\n        .sort()\n        .map(key => sanitizedEvent[key]),\n    );\n  }\n\n  get #cache(): Record<string, TtlInMilliseconds> | undefined {\n    const cacheString = localStorage.getItem(this.#storageKey);\n\n    if (!cacheString) {\n      return {};\n    }\n\n    return JSON.parse(cacheString);\n  }\n\n  /**\n   * Checks if the browser's localStorage is supported and writable.\n   *\n   * If any of these operations fail, it indicates that localStorage is either\n   * not supported or not writable (e.g., in cases where the storage is full or\n   * the browser is in a privacy mode that restricts localStorage usage).\n   */\n  get #isValidBrowser(): boolean {\n    if (typeof window === 'undefined') {\n      return false;\n    }\n\n    const storage = window.localStorage;\n    if (!storage) {\n      return false;\n    }\n\n    try {\n      const testKey = 'test';\n      storage.setItem(testKey, testKey);\n      storage.removeItem(testKey);\n\n      return true;\n    } catch (err: unknown) {\n      const isQuotaExceededError =\n        err instanceof DOMException &&\n        // Check error names for different browsers\n        (err.name === 'QuotaExceededError' || err.name === 'NS_ERROR_DOM_QUOTA_REACHED');\n\n      if (isQuotaExceededError && storage.length > 0) {\n        storage.removeItem(this.#storageKey);\n      }\n\n      return false;\n    }\n  }\n}\n", "/**\n * The `TelemetryCollector` class handles collection of telemetry events from Clerk <PERSON>. Telemetry is opt-out and can be disabled by setting a CLERK_TELEMETRY_DISABLED environment variable.\n * The `Clerk<PERSON>rovider` also accepts a `telemetry` prop that will be passed to the collector during initialization:\n *\n * ```jsx\n * <ClerkProvider telemetry={false}>\n *    ...\n * </ClerkProvider>\n * ```\n *\n * For more information, please see the telemetry documentation page: https://clerk.com/docs/telemetry\n */\nimport type {\n  InstanceType,\n  TelemetryCollector as TelemetryCollectorInterface,\n  TelemetryEvent,\n  TelemetryEventRaw,\n} from '@clerk/types';\n\nimport { parsePublishableKey } from '../keys';\nimport { isTruthy } from '../underscore';\nimport { TelemetryEventThrottler } from './throttler';\nimport type { TelemetryCollectorOptions } from './types';\n\ntype TelemetryCollectorConfig = Pick<\n  TelemetryCollectorOptions,\n  'samplingRate' | 'disabled' | 'debug' | 'maxBufferSize'\n> & {\n  endpoint: string;\n};\n\ntype TelemetryMetadata = Required<\n  Pick<TelemetryCollectorOptions, 'clerkVersion' | 'sdk' | 'sdkVersion' | 'publishableKey' | 'secretKey'>\n> & {\n  /**\n   * The instance type, derived from the provided publishableKey.\n   */\n  instanceType: InstanceType;\n};\n\nconst DEFAULT_CONFIG: Partial<TelemetryCollectorConfig> = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: 'https://clerk-telemetry.com',\n};\n\nexport class TelemetryCollector implements TelemetryCollectorInterface {\n  #config: Required<TelemetryCollectorConfig>;\n  #eventThrottler: TelemetryEventThrottler;\n  #metadata: TelemetryMetadata = {} as TelemetryMetadata;\n  #buffer: TelemetryEvent[] = [];\n  #pendingFlush: any;\n\n  constructor(options: TelemetryCollectorOptions) {\n    this.#config = {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint,\n    } as Required<TelemetryCollectorConfig>;\n\n    if (!options.clerkVersion && typeof window === 'undefined') {\n      // N/A in a server environment\n      this.#metadata.clerkVersion = '';\n    } else {\n      this.#metadata.clerkVersion = options.clerkVersion ?? '';\n    }\n\n    // We will try to grab the SDK data lazily when an event is triggered, so it should always be defined once the event is sent.\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdk = options.sdk!;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdkVersion = options.sdkVersion!;\n\n    this.#metadata.publishableKey = options.publishableKey ?? '';\n\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      this.#metadata.instanceType = parsedKey.instanceType;\n    }\n\n    if (options.secretKey) {\n      // Only send the first 16 characters of the secret key to to avoid sending the full key. We can still query against the partial key.\n      this.#metadata.secretKey = options.secretKey.substring(0, 16);\n    }\n\n    this.#eventThrottler = new TelemetryEventThrottler();\n  }\n\n  get isEnabled(): boolean {\n    if (this.#metadata.instanceType !== 'development') {\n      return false;\n    }\n\n    // In browser or client environments, we most likely pass the disabled option to the collector, but in environments\n    // where environment variables are available we also check for `CLERK_TELEMETRY_DISABLED`.\n    if (this.#config.disabled || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DISABLED))) {\n      return false;\n    }\n\n    // navigator.webdriver is a property generally set by headless browsers that are running in an automated testing environment.\n    // Data from these environments is not meaningful for us and has the potential to produce a large volume of events, so we disable\n    // collection in this case. (ref: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/webdriver)\n    if (typeof window !== 'undefined' && !!window?.navigator?.webdriver) {\n      return false;\n    }\n\n    return true;\n  }\n\n  get isDebug(): boolean {\n    return this.#config.debug || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DEBUG));\n  }\n\n  record(event: TelemetryEventRaw): void {\n    const preparedPayload = this.#preparePayload(event.event, event.payload);\n\n    this.#logEvent(preparedPayload.event, preparedPayload);\n\n    if (!this.#shouldRecord(preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n\n    this.#buffer.push(preparedPayload);\n\n    this.#scheduleFlush();\n  }\n\n  #shouldRecord(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    return this.isEnabled && !this.isDebug && this.#shouldBeSampled(preparedPayload, eventSamplingRate);\n  }\n\n  #shouldBeSampled(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    const randomSeed = Math.random();\n\n    const toBeSampled =\n      randomSeed <= this.#config.samplingRate &&\n      (typeof eventSamplingRate === 'undefined' || randomSeed <= eventSamplingRate);\n\n    if (!toBeSampled) {\n      return false;\n    }\n\n    return !this.#eventThrottler.isEventThrottled(preparedPayload);\n  }\n\n  #scheduleFlush(): void {\n    // On the server, we want to flush immediately as we have less guarantees about the lifecycle of the process\n    if (typeof window === 'undefined') {\n      this.#flush();\n      return;\n    }\n\n    const isBufferFull = this.#buffer.length >= this.#config.maxBufferSize;\n    if (isBufferFull) {\n      // If the buffer is full, flush immediately to make sure we minimize the chance of event loss.\n      // Cancel any pending flushes as we're going to flush immediately\n      if (this.#pendingFlush) {\n        const cancel = typeof cancelIdleCallback !== 'undefined' ? cancelIdleCallback : clearTimeout;\n        cancel(this.#pendingFlush);\n      }\n      this.#flush();\n      return;\n    }\n\n    // If we have a pending flush, do nothing\n    if (this.#pendingFlush) {\n      return;\n    }\n\n    if ('requestIdleCallback' in window) {\n      this.#pendingFlush = requestIdleCallback(() => {\n        this.#flush();\n      });\n    } else {\n      // This is not an ideal solution, but it at least waits until the next tick\n      this.#pendingFlush = setTimeout(() => {\n        this.#flush();\n      }, 0);\n    }\n  }\n\n  #flush(): void {\n    fetch(new URL('/v1/event', this.#config.endpoint), {\n      method: 'POST',\n      // TODO: We send an array here with that idea that we can eventually send multiple events.\n      body: JSON.stringify({\n        events: this.#buffer,\n      }),\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n      .catch(() => void 0)\n      .then(() => {\n        this.#buffer = [];\n      })\n      .catch(() => void 0);\n  }\n\n  /**\n   * If running in debug mode, log the event and its payload to the console.\n   */\n  #logEvent(event: TelemetryEvent['event'], payload: Record<string, any>) {\n    if (!this.isDebug) {\n      return;\n    }\n\n    if (typeof console.groupCollapsed !== 'undefined') {\n      console.groupCollapsed('[clerk/telemetry]', event);\n      console.log(payload);\n      console.groupEnd();\n    } else {\n      console.log('[clerk/telemetry]', event, payload);\n    }\n  }\n\n  /**\n   * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n   *\n   * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n   */\n  #getSDKMetadata() {\n    let sdkMetadata = {\n      name: this.#metadata.sdk,\n      version: this.#metadata.sdkVersion,\n    };\n\n    // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n    if (typeof window !== 'undefined' && window.Clerk) {\n      // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n      sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n    }\n\n    return sdkMetadata;\n  }\n\n  /**\n   * Append relevant metadata from the Clerk singleton to the event payload.\n   */\n  #preparePayload(event: TelemetryEvent['event'], payload: TelemetryEvent['payload']): TelemetryEvent {\n    const sdkMetadata = this.#getSDKMetadata();\n\n    return {\n      event,\n      cv: this.#metadata.clerkVersion ?? '',\n      it: this.#metadata.instanceType ?? '',\n      sdk: sdkMetadata.name,\n      sdkv: sdkMetadata.version,\n      ...(this.#metadata.publishableKey ? { pk: this.#metadata.publishableKey } : {}),\n      ...(this.#metadata.secretKey ? { sk: this.#metadata.secretKey } : {}),\n      payload,\n    };\n  }\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_COMPONENT_MOUNTED = 'COMPONENT_MOUNTED';\nconst EVENT_COMPONENT_OPENED = 'COMPONENT_OPENED';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype ComponentMountedBase = {\n  component: string;\n};\n\ntype EventPrebuiltComponent = ComponentMountedBase & {\n  appearanceProp: boolean;\n  elements: boolean;\n  variables: boolean;\n  baseTheme: boolean;\n};\n\ntype EventComponentMounted = ComponentMountedBase & TelemetryEventRaw['payload'];\n\nfunction createPrebuiltComponentEvent(event: typeof EVENT_COMPONENT_MOUNTED | typeof EVENT_COMPONENT_OPENED) {\n  return function (\n    component: string,\n    props?: Record<string, any>,\n    additionalPayload?: TelemetryEventRaw['payload'],\n  ): TelemetryEventRaw<EventPrebuiltComponent> {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Bo<PERSON>an(props?.appearance),\n        baseTheme: <PERSON><PERSON><PERSON>(props?.appearance?.baseTheme),\n        elements: <PERSON><PERSON><PERSON>(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload,\n      },\n    };\n  };\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is mounted.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentMounted('SignUp', props));\n */\nexport function eventPrebuiltComponentMounted(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is opened as a modal.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentOpened('GoogleOneTap', props));\n */\nexport function eventPrebuiltComponentOpened(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a component is mounted. Use `eventPrebuiltComponentMounted` for prebuilt components.\n *\n * **Caution:** Filter the `props` you pass to this function to avoid sending too much data.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Ideally you only pass a handful of props here.\n *\n * @example\n * telemetry.record(eventComponentMounted('SignUp', props));\n */\nexport function eventComponentMounted(\n  component: string,\n  props: TelemetryEventRaw['payload'] = {},\n): TelemetryEventRaw<EventComponentMounted> {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_METHOD_CALLED = 'METHOD_CALLED';\n\ntype EventMethodCalled = {\n  method: string;\n} & Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventMethodCalled(\n  method: string,\n  payload?: Record<string, unknown>,\n): TelemetryEventRaw<EventMethodCalled> {\n  return {\n    event: EVENT_METHOD_CALLED,\n    payload: {\n      method,\n      ...payload,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_FRAMEWORK_METADATA = 'FRAMEWORK_METADATA';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype EventFrameworkMetadata = Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventFrameworkMetadata(payload: EventFrameworkMetadata): TelemetryEventRaw<EventFrameworkMetadata> {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload,\n  };\n}\n"], "names": ["EVENT_SAMPLING_RATE"], "mappings": ";;;;;;;;;;;;;;;AAIA,IAAM,uBAAuB;AAJ7B,IAAA,aAAA,WAAA,oCAAA,gBAAA,WAAA;AAUO,IAAM,0BAAN,MAA8B;IAA9B,aAAA;QAAA,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACL,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA,aAAc;QACd,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA,WAAY;IAAA;IAEZ,iBAAiB,OAAA,EAAkC;QACjD,IAAI,CAAC,+RAAA,EAAA,IAAA,EAAK,oCAAA,qBAAiB;YACzB,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAA,CAAI;QACrB,MAAM,MAAM,kSAAA,EAAA,IAAA,EAAK,oCAAA,gBAAL,IAAA,CAAA,IAAA,EAAkB;QAC9B,MAAM,wRAAQ,eAAA,EAAA,IAAA,EAAK,oCAAA,YAAA,CAAS,GAAG,CAAA;QAE/B,IAAI,CAAC,OAAO;YACV,MAAM,eAAe;gBACnB,mRAAG,eAAA,EAAA,IAAA,EAAK,oCAAA,UAAA;gBACR,CAAC,GAAG,CAAA,EAAG;YACT;YAEA,aAAa,OAAA,iRAAQ,eAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,MAAM,mBAAmB,SAAS,MAAM,wRAAQ,eAAA,EAAA,IAAA,EAAK;QACrD,IAAI,kBAAkB;YACpB,MAAM,+RAAe,eAAA,EAAA,IAAA,EAAK,oCAAA;YAC1B,OAAO,YAAA,CAAa,GAAG,CAAA;YAEvB,aAAa,OAAA,CAAQ,+RAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,OAAO,CAAC,CAAC;IACX;AAsEF;AApGE,cAAA,IAAA;AACA,YAAA,IAAA;AAFK,qCAAA,IAAA;AAAA;;;CAAA,GAqCL,iBAAY,SAAC,KAAA,EAA+B;IAC1C,MAAM,EAAE,IAAI,GAAA,EAAK,IAAI,GAAA,EAAK,OAAA,EAAS,GAAG,KAAK,CAAA,GAAI;IAE/C,MAAM,iBAA4F;QAChG,GAAG,OAAA;QACH,GAAG,IAAA;IACL;IAEA,OAAO,KAAK,SAAA,CACV,OAAO,IAAA,CAAK;QACV,GAAG,OAAA;QACH,GAAG,IAAA;IACL,CAAC,EACE,IAAA,CAAK,EACL,GAAA,CAAI,CAAA,MAAO,cAAA,CAAe,GAAG,CAAC;AAErC;AAEI,YAAM,WAAkD;IAC1D,MAAM,cAAc,aAAa,OAAA,iRAAQ,eAAA,EAAA,IAAA,EAAK,YAAW;IAEzD,IAAI,CAAC,aAAa;QAChB,OAAO,CAAC;IACV;IAEA,OAAO,KAAK,KAAA,CAAM,WAAW;AAC/B;AASI,qBAAe,WAAY;IAC7B,IAAI,OAAO,WAAW,kBAAa;QACjC,OAAO;IACT;;IAEA,MAAM,UAAU,OAAO;AAuBzB;;ACtEF,IAAM,iBAAoD;IACxD,cAAc;IACd,eAAe;IAAA,mDAAA;IAAA,wDAAA;IAAA,+BAAA;IAIf,UAAU;AACZ;AA/CA,IAAA,SAAA,iBAAA,WAAA,SAAA,eAAA,+BAAA,iBAAA,oBAAA,kBAAA,UAAA,aAAA,mBAAA;AAiDO,IAAM,qBAAN,MAAgE;IAOrE,YAAY,OAAA,CAAoC;QAP3C,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACL,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA,WAA+B,CAAC;QAChC,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA,SAA4B,CAAC,CAAA;QAC7B,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QAGE,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAU;YACb,eAAe,QAAQ,aAAA,IAAiB,eAAe,aAAA;YACvD,cAAc,QAAQ,YAAA,IAAgB,eAAe,YAAA;YACrD,UAAU,QAAQ,QAAA,IAAY;YAC9B,OAAO,QAAQ,KAAA,IAAS;YACxB,UAAU,eAAe,QAAA;QAC3B;QAEA,IAAI,CAAC,QAAQ,YAAA,IAAgB,OAAO,SAAW,aAAa;YAE1D,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe;QAChC,OAAO;YACL,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,QAAQ,YAAA,IAAgB;QACxD;QAIA,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,GAAA,GAAM,QAAQ,GAAA;QAE7B,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,UAAA,GAAa,QAAQ,UAAA;QAEpC,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB,QAAQ,cAAA,IAAkB;QAE1D,MAAM,4RAAY,sBAAA,EAAoB,QAAQ,cAAc;QAC5D,IAAI,WAAW;YACb,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,UAAU,YAAA;QAC1C;QAEA,IAAI,QAAQ,SAAA,EAAW;YAErB,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY,QAAQ,SAAA,CAAU,SAAA,CAAU,GAAG,EAAE;QAC9D;QAEA,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,iBAAkB,IAAI,wBAAwB;IACrD;IAEA,IAAI,YAAqB;QACvB,oRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,KAAiB,eAAe;YACjD,OAAO;QACT;QAIA,oRAAI,eAAA,EAAA,IAAA,EAAK,SAAQ,QAAA,IAAa,OAAO,YAAY,8RAAe,YAAA,EAAS,QAAQ,GAAA,CAAI,wBAAwB,GAAI;YAC/G,OAAO;QACT;QAKA,IAAI,OAAO,SAAW,eAAe,CAAC,CAAC,QAAQ,WAAW,WAAW;;QAErE;QAEA,OAAO;IACT;IAEA,IAAI,UAAmB;QACrB,uRAAO,eAAA,EAAA,IAAA,EAAK,SAAQ,KAAA,IAAU,OAAO,YAAY,+RAAe,WAAA,EAAS,QAAQ,GAAA,CAAI,qBAAqB;IAC5G;IAEA,OAAO,KAAA,EAAgC;QACrC,MAAM,kBAAkB,kSAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA,EAAqB,MAAM,KAAA,EAAO,MAAM,OAAA;QAEhE,CAAA,GAAA,2QAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,aAAL,IAAA,CAAA,IAAA,EAAe,gBAAgB,KAAA,EAAO;QAEtC,IAAI,iRAAC,kBAAA,EAAA,IAAA,EAAK,+BAAA,iBAAL,IAAA,CAAA,IAAA,EAAmB,iBAAiB,MAAM,iBAAA,GAAoB;YACjE;QACF;QAEA,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAQ,IAAA,CAAK,eAAe;QAEjC,CAAA,GAAA,2QAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,kBAAL,IAAA,CAAA,IAAA;IACF;AAgIF;AAhNE,UAAA,IAAA;AACA,kBAAA,IAAA;AACA,YAAA,IAAA;AACA,UAAA,IAAA;AACA,gBAAA,IAAA;AALK,gCAAA,IAAA;AAmFL,kBAAa,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IACzE,OAAO,IAAA,CAAK,SAAA,IAAa,CAAC,IAAA,CAAK,OAAA,oRAAW,kBAAA,EAAA,IAAA,EAAK,+BAAA,oBAAL,IAAA,CAAA,IAAA,EAAsB,iBAAiB;AACnF;AAEA,qBAAgB,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IAC5E,MAAM,aAAa,KAAK,MAAA,CAAO;IAE/B,MAAM,cACJ,8RAAc,eAAA,EAAA,IAAA,EAAK,SAAQ,YAAA,IAAA,CAC1B,OAAO,sBAAsB,eAAe,cAAc,iBAAA;IAE7D,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,OAAO,CAAC,+RAAA,EAAA,IAAA,EAAK,iBAAgB,gBAAA,CAAiB,eAAe;AAC/D;AAEA,mBAAc,WAAS;IAErB,IAAI,OAAO,WAAW,kBAAa;QACjC,CAAA,GAAA,2QAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,UAAL,IAAA,CAAA,IAAA;QACA;IACF;;IAEA,MAAM,eAAe,mBAAK,SAAQ,UAAU,mBAAK,SAAQ;AA2B3D;AAEA,WAAM,WAAS;IACb,MAAM,IAAI,IAAI,iBAAa,2RAAA,EAAA,IAAA,EAAK,SAAQ,QAAQ,GAAG;QACjD,QAAQ;QAAA,0FAAA;QAER,MAAM,KAAK,SAAA,CAAU;YACnB,QAAQ,+RAAA,EAAA,IAAA,EAAK;QACf,CAAC;QACD,SAAS;YACP,gBAAgB;QAClB;IACF,CAAC,EACE,KAAA,CAAM,IAAM,KAAA,CAAM,EAClB,IAAA,CAAK,MAAM;QACV,CAAA,GAAA,2QAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAU,CAAC,CAAA;IAClB,CAAC,EACA,KAAA,CAAM,IAAM,KAAA,CAAM;AACvB;AAAA;;CAAA,GAKA,cAAS,SAAC,KAAA,EAAgC,OAAA,EAA8B;IACtE,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;QACjB;IACF;IAEA,IAAI,OAAO,QAAQ,cAAA,KAAmB,aAAa;QACjD,QAAQ,cAAA,CAAe,qBAAqB,KAAK;QACjD,QAAQ,GAAA,CAAI,OAAO;QACnB,QAAQ,QAAA,CAAS;IACnB,OAAO;QACL,QAAQ,GAAA,CAAI,qBAAqB,OAAO,OAAO;IACjD;AACF;AAAA;;;;CAAA,GAOA,oBAAe,WAAG;IAChB,IAAI,cAAc;QAChB,MAAM,+RAAA,EAAA,IAAA,EAAK,WAAU,GAAA;QACrB,yRAAS,eAAA,EAAA,IAAA,EAAK,WAAU,UAAA;IAC1B;IAGA,IAAI,OAAO,WAAW,eAAe,EAAc,KAAP;;IAG5C;IAEA,OAAO;AACT;AAAA;;CAAA,GAKA,oBAAe,SAAC,KAAA,EAAgC,OAAA,EAAoD;IAClG,MAAM,8RAAc,kBAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA;IAEpB,OAAO;QACL;QACA,oRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,mRAAI,gBAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,KAAK,YAAY,IAAA;QACjB,MAAM,YAAY,OAAA;QAClB,GAAI,+RAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB;YAAE,oRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,cAAA;QAAe,IAAI,CAAC,CAAA;QAC7E,mRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY;YAAE,oRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA;QAAU,IAAI,CAAC,CAAA;QACnE;IACF;AACF;;AC/PF,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAe5B,SAAS,6BAA6B,KAAA,EAAuE;IAC3G,OAAO,SACL,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;QAC3C,OAAO;YACL;YACA,mBAAmB;YACnB,SAAS;gBACP;gBACA,gBAAgB,QAAQ,OAAO,UAAU;gBACzC,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,UAAU,QAAQ,OAAO,YAAY,QAAQ;gBAC7C,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,GAAG,iBAAA;YACL;QACF;IACF;AACF;AAYO,SAAS,8BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,uBAAuB,EAAE,WAAW,OAAO,iBAAiB;AAClG;AAYO,SAAS,6BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,sBAAsB,EAAE,WAAW,OAAO,iBAAiB;AACjG;AAaO,SAAS,sBACd,SAAA,EACA,QAAsC,CAAC,CAAA,EACG;IAC1C,OAAO;QACL,OAAO;QACP,mBAAmB;QACnB,SAAS;YACP;YACA,GAAG,KAAA;QACL;IACF;AACF;;ACjGA,IAAM,sBAAsB;AASrB,SAAS,kBACd,MAAA,EACA,OAAA,EACsC;IACtC,OAAO;QACL,OAAO;QACP,SAAS;YACP;YACA,GAAG,OAAA;QACL;IACF;AACF;;ACpBA,IAAM,2BAA2B;AACjC,IAAMA,uBAAsB;AAOrB,SAAS,uBAAuB,OAAA,EAA4E;IACjH,OAAO;QACL,OAAO;QACP,mBAAmBA;QACnB;IACF;AACF", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bbackend%401.33.0_react_faf293b1b908462ede200692940d58e0/node_modules/%40clerk/backend/src/index.ts"], "sourcesContent": ["import type { TelemetryCollectorOptions } from '@clerk/shared/telemetry';\nimport { TelemetryCollector } from '@clerk/shared/telemetry';\nimport type { SDKMetadata } from '@clerk/types';\n\nimport type { ApiClient, CreateBackendApiOptions } from './api';\nimport { createBackendApiClient } from './api';\nimport { withLegacyReturn } from './jwt/legacyReturn';\nimport type { CreateAuthenticateRequestOptions } from './tokens/factory';\nimport { createAuthenticateRequest } from './tokens/factory';\nimport { verifyToken as _verifyToken } from './tokens/verify';\n\nexport const verifyToken = withLegacyReturn(_verifyToken);\n\nexport type ClerkOptions = CreateBackendApiOptions &\n  Partial<\n    Pick<\n      CreateAuthenticateRequestOptions['options'],\n      'audience' | 'jwtKey' | 'proxyUrl' | 'secretKey' | 'publishableKey' | 'domain' | 'isSatellite'\n    >\n  > & { sdkMetadata?: SDKMetadata; telemetry?: Pick<TelemetryCollectorOptions, 'disabled' | 'debug'> };\n\n// The current exported type resolves the following issue in packages importing createClerkClient\n// TS4023: Exported variable 'clerkClient' has or is using name 'AuthErrorReason' from external module \"/packages/backend/dist/index\" but cannot be named.\nexport type ClerkClient = {\n  telemetry: TelemetryCollector;\n} & ApiClient &\n  ReturnType<typeof createAuthenticateRequest>;\n\nexport function createClerkClient(options: ClerkOptions): ClerkClient {\n  const opts = { ...options };\n  const apiClient = createBackendApiClient(opts);\n  const requestState = createAuthenticateRequest({ options: opts, apiClient });\n  const telemetry = new TelemetryCollector({\n    ...options.telemetry,\n    publishableKey: opts.publishableKey,\n    secretKey: opts.secretKey,\n    samplingRate: 0.1,\n    ...(opts.sdkMetadata ? { sdk: opts.sdkMetadata.name, sdkVersion: opts.sdkMetadata.version } : {}),\n  });\n\n  return {\n    ...apiClient,\n    ...requestState,\n    telemetry,\n  };\n}\n\n/**\n * General Types\n */\nexport type { OrganizationMembershipRole } from './api/resources';\nexport type { VerifyTokenOptions } from './tokens/verify';\n/**\n * JSON types\n */\nexport type {\n  ActorTokenJSON,\n  AccountlessApplicationJSON,\n  ClerkResourceJSON,\n  TokenJSON,\n  AllowlistIdentifierJSON,\n  BlocklistIdentifierJSON,\n  ClientJSON,\n  CnameTargetJSON,\n  DomainJSON,\n  EmailJSON,\n  EmailAddressJSON,\n  ExternalAccountJSON,\n  IdentificationLinkJSON,\n  InstanceJSON,\n  InstanceRestrictionsJSON,\n  InstanceSettingsJSON,\n  InvitationJSON,\n  JwtTemplateJSON,\n  OauthAccessTokenJSON,\n  OAuthApplicationJSON,\n  OrganizationJSON,\n  OrganizationDomainJSON,\n  OrganizationDomainVerificationJSON,\n  OrganizationInvitationJSON,\n  OrganizationSettingsJSON,\n  PublicOrganizationDataJSON,\n  OrganizationMembershipJSON,\n  OrganizationMembershipPublicUserDataJSON,\n  PhoneNumberJSON,\n  ProxyCheckJSON,\n  RedirectUrlJSON,\n  SessionJSON,\n  SignInJSON,\n  SignInTokenJSON,\n  SignUpJSON,\n  SignUpVerificationJSON,\n  SignUpVerificationsJSON,\n  SMSMessageJSON,\n  UserJSON,\n  VerificationJSON,\n  WaitlistEntryJSON,\n  Web3WalletJSON,\n  DeletedObjectJSON,\n  PaginatedResponseJSON,\n  TestingTokenJSON,\n  WebhooksSvixJSON,\n} from './api/resources/JSON';\n\n/**\n * Resources\n */\nexport type {\n  ActorToken,\n  AccountlessApplication,\n  AllowlistIdentifier,\n  BlocklistIdentifier,\n  Client,\n  CnameTarget,\n  Domain,\n  EmailAddress,\n  ExternalAccount,\n  Instance,\n  InstanceRestrictions,\n  InstanceSettings,\n  Invitation,\n  JwtTemplate,\n  OauthAccessToken,\n  OAuthApplication,\n  Organization,\n  OrganizationDomain,\n  OrganizationDomainVerification,\n  OrganizationInvitation,\n  OrganizationMembership,\n  OrganizationMembershipPublicUserData,\n  OrganizationSettings,\n  PhoneNumber,\n  Session,\n  SignInToken,\n  SignUpAttempt,\n  SMSMessage,\n  Token,\n  User,\n  TestingToken,\n} from './api/resources';\n\n/**\n * Webhooks event types\n */\nexport type {\n  EmailWebhookEvent,\n  OrganizationWebhookEvent,\n  OrganizationDomainWebhookEvent,\n  OrganizationInvitationWebhookEvent,\n  OrganizationMembershipWebhookEvent,\n  RoleWebhookEvent,\n  PermissionWebhookEvent,\n  SessionWebhookEvent,\n  SMSWebhookEvent,\n  UserWebhookEvent,\n  WaitlistEntryWebhookEvent,\n  WebhookEvent,\n  WebhookEventType,\n} from './api/resources/Webhooks';\n\n/**\n * Auth objects\n */\nexport type { AuthObject } from './tokens/authObjects';\n"], "names": ["verifyToken"], "mappings": ";;;;;;;;;;;AACA,SAAS,0BAA0B;;;;;;;AAU5B,IAAMA,6RAAc,mBAAA,4QAAiB,cAAY;AAiBjD,SAAS,kBAAkB,OAAA,EAAoC;IACpE,MAAM,OAAO;QAAE,GAAG,OAAA;IAAQ;IAC1B,MAAM,0RAAY,yBAAA,EAAuB,IAAI;IAC7C,MAAM,6RAAe,4BAAA,EAA0B;QAAE,SAAS;QAAM;IAAU,CAAC;IAC3E,MAAM,YAAY,+QAAI,sBAAA,CAAmB;QACvC,GAAG,QAAQ,SAAA;QACX,gBAAgB,KAAK,cAAA;QACrB,WAAW,KAAK,SAAA;QAChB,cAAc;QACd,GAAI,KAAK,WAAA,GAAc;YAAE,KAAK,KAAK,WAAA,CAAY,IAAA;YAAM,YAAY,KAAK,WAAA,CAAY,OAAA;QAAQ,IAAI,CAAC,CAAA;IACjG,CAAC;IAED,OAAO;QACL,GAAG,SAAA;QACH,GAAG,YAAA;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/index.ts"], "sourcesContent": ["/**\n * Generic exports\n */\nexport { createRouteMatcher } from './routeMatcher';\n\nexport { verifyToken, createClerkClient } from '@clerk/backend';\nexport { clerkClient } from './clerkClient';\n\n/**\n * Webhook-specific exports\n */\nexport type {\n  DeletedObjectJSON,\n  EmailJSON,\n  OrganizationJSON,\n  OrganizationDomainJSON,\n  OrganizationDomainVerificationJSON,\n  OrganizationInvitationJSON,\n  OrganizationMembershipJSON,\n  SessionJSON,\n  SMSMessageJSON,\n  UserJSON,\n  WaitlistEntryJSON,\n  WebhookEvent,\n  WebhookEventType,\n  UserWebhookEvent,\n  EmailWebhookEvent,\n  OrganizationWebhookEvent,\n  OrganizationDomainWebhookEvent,\n  OrganizationMembershipWebhookEvent,\n  OrganizationInvitationWebhookEvent,\n  PermissionWebhookEvent,\n  RoleWebhookEvent,\n  SessionWebhookEvent,\n  SMSWebhookEvent,\n  WaitlistEntryWebhookEvent,\n} from '@clerk/backend';\n\n/**\n * NextJS-specific exports\n */\nexport { getAuth } from './createGetAuth';\nexport { buildClerkProps } from './buildClerkProps';\nexport { auth } from '../app-router/server/auth';\nexport { currentUser } from '../app-router/server/currentUser';\nexport { clerkMiddleware } from './clerkMiddleware';\nexport type { ClerkMiddlewareAuth, ClerkMiddlewareAuthObject, ClerkMiddlewareOptions } from './clerkMiddleware';\n\n/**\n * Re-export resource types from @clerk/backend\n */\nexport type {\n  OrganizationMembershipRole,\n  // Resources\n  AllowlistIdentifier,\n  Client,\n  OrganizationMembership,\n  EmailAddress,\n  ExternalAccount,\n  Invitation,\n  OauthAccessToken,\n  Organization,\n  OrganizationInvitation,\n  OrganizationMembershipPublicUserData,\n  PhoneNumber,\n  Session,\n  SignInToken,\n  SMSMessage,\n  Token,\n  User,\n} from '@clerk/backend';\n\n/**\n * Utilities for reverification\n */\nexport { reverificationErrorResponse, reverificationError } from '@clerk/backend/internal';\n"], "names": [], "mappings": ";AAKA,SAAS,aAAa,yBAAyB;AAsE/C,SAAS,6BAA6B,2BAA2B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/pathMatcher.ts"], "sourcesContent": ["import type { Autocomplete } from '@clerk/types';\n\nimport { pathToRegexp } from './pathToRegexp';\n\nexport type WithPathPatternWildcard<T = string> = `${T & string}(.*)`;\nexport type PathPattern = Autocomplete<WithPathPatternWildcard>;\nexport type PathMatcherParam = Array<RegExp | PathPattern> | RegExp | PathPattern;\n\nconst precomputePathRegex = (patterns: Array<string | RegExp>) => {\n  return patterns.map(pattern => (pattern instanceof RegExp ? pattern : pathToRegexp(pattern)));\n};\n\n/**\n * Creates a function that matches paths against a set of patterns.\n *\n * @param patterns - A string, RegExp, or array of patterns to match against\n * @returns A function that takes a pathname and returns true if it matches any of the patterns\n */\nexport const createPathMatcher = (patterns: PathMatcherParam) => {\n  const routePatterns = [patterns || ''].flat().filter(Boolean);\n  const matchers = precomputePathRegex(routePatterns);\n  return (pathname: string) => matchers.some(matcher => matcher.test(pathname));\n};\n"], "names": [], "mappings": ";;;;;;AAQA,IAAM,sBAAsB,CAAC,aAAqC;IAChE,OAAO,SAAS,GAAA,CAAI,CAAA,UAAY,mBAAmB,SAAS,0RAAU,eAAA,EAAa,OAAO,CAAE;AAC9F;AAQO,IAAM,oBAAoB,CAAC,aAA+B;IAC/D,MAAM,gBAAgB;QAAC,YAAY,EAAE;KAAA,CAAE,IAAA,CAAK,EAAE,MAAA,CAAO,OAAO;IAC5D,MAAM,WAAW,oBAAoB,aAAa;IAClD,OAAO,CAAC,WAAqB,SAAS,IAAA,CAAK,CAAA,UAAW,QAAQ,IAAA,CAAK,QAAQ,CAAC;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/routeMatcher.ts"], "sourcesContent": ["import { createPathMatcher, type WithPathPatternWildcard } from '@clerk/shared/pathMatcher';\nimport type { Autocomplete } from '@clerk/types';\nimport type Link from 'next/link';\nimport type { NextRequest } from 'next/server';\n\ntype NextTypedRoute<T = Parameters<typeof Link>['0']['href']> = T extends string ? T : never;\ntype RouteMatcherWithNextTypedRoutes = Autocomplete<WithPathPatternWildcard<NextTypedRoute> | NextTypedRoute>;\n\nexport type RouteMatcherParam =\n  | Array<RegExp | RouteMatcherWithNextTypedRoutes>\n  | RegExp\n  | RouteMatcherWithNextTypedRoutes\n  | ((req: NextRequest) => boolean);\n\n/**\n * Returns a function that accepts a `Request` object and returns whether the request matches the list of\n * predefined routes that can be passed in as the first argument.\n *\n * You can use glob patterns to match multiple routes or a function to match against the request object.\n * Path patterns and regular expressions are supported, for example: `['/foo', '/bar(.*)'] or `[/^\\/foo\\/.*$/]`\n * For more information, see: https://clerk.com/docs\n */\nexport const createRouteMatcher = (routes: RouteMatcherParam) => {\n  if (typeof routes === 'function') {\n    return (req: NextRequest) => routes(req);\n  }\n\n  const matcher = createPathMatcher(routes);\n  return (req: NextRequest) => matcher(req.nextUrl.pathname);\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,yBAAuD;;;;AAsBzD,MAAM,qBAAqB,CAAC,WAA8B;IAC/D,IAAI,OAAO,WAAW,YAAY;QAChC,OAAO,CAAC,MAAqB,OAAO,GAAG;IACzC;IAEA,MAAM,0RAAU,oBAAA,EAAkB,MAAM;IACxC,OAAO,CAAC,MAAqB,QAAQ,IAAI,OAAA,CAAQ,QAAQ;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/createClerkClient.ts"], "sourcesContent": ["import { createClerkClient } from '@clerk/backend';\n\nimport {\n  API_URL,\n  API_VERSION,\n  DOMAIN,\n  IS_SATELLITE,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED,\n} from './constants';\n\nconst clerkClientDefaultOptions = {\n  secretKey: SECRET_KEY,\n  publishableKey: PUBLISHABLE_KEY,\n  apiUrl: API_URL,\n  apiVersion: API_VERSION,\n  userAgent: `${PACKAGE_NAME}@${PACKAGE_VERSION}`,\n  proxyUrl: PROXY_URL,\n  domain: DOMAIN,\n  isSatellite: IS_SATELLITE,\n  sdkMetadata: SDK_METADATA,\n  telemetry: {\n    disabled: TELEMETRY_DISABLED,\n    debug: TELEMETRY_DEBUG,\n  },\n};\n\nexport const createClerkClientWithOptions: typeof createClerkClient = options =>\n  createClerkClient({ ...clerkClientDefaultOptions, ...options });\n"], "names": [], "mappings": ";;;AAAA,SAAS,yBAAyB;AAElC;;;;AAaA,MAAM,4BAA4B;IAChC,+RAAW,aAAA;IACX,oSAAgB,kBAAA;IAChB,4RAAQ,UAAA;IACR,+RAAY,eAAA;IACZ,WAAW,GAAG,eAAY,CAAA,CAAA,EAAI,QAAe,EAAA;IAC7C,8RAAU,YAAA;IACV,4RAAQ,SAAA;IACR,aAAa,mSAAA;IACb,iSAAa,eAAA;IACb,WAAW;QACT,8RAAU,qBAAA;QACV,2RAAO,kBAAA;IACT;AACF;AAEO,MAAM,+BAAyD,CAAA,4QACpE,oBAAA,EAAkB;QAAE,GAAG,yBAAA;QAA2B,GAAG,OAAA;IAAQ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/middleware-storage.ts"], "sourcesContent": ["import { AsyncLocalStorage } from 'node:async_hooks';\n\nimport type { AuthenticateRequestOptions } from '@clerk/backend/internal';\n\nexport const clerkMiddlewareRequestDataStore = new Map<'requestData', AuthenticateRequestOptions>();\nexport const clerkMiddlewareRequestDataStorage = new AsyncLocalStorage<typeof clerkMiddlewareRequestDataStore>();\n"], "names": [], "mappings": ";;;;AAAA,SAAS,yBAAyB;;;AAI3B,MAAM,kCAAkC,aAAA,GAAA,IAAI,IAA+C;AAC3F,MAAM,oCAAoC,oIAAI,oBAAA,CAA0D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/clerkClient.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\n\nimport { buildRequestLike, isPrerenderingBailout } from '../app-router/server/utils';\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { getHeader } from './headers-utils';\nimport { clerkMiddlewareRequestDataStorage } from './middleware-storage';\nimport { decryptClerkRequestData } from './utils';\n\n/**\n * Constructs a BAPI client that accesses request data within the runtime.\n * Necessary if middleware dynamic keys are used.\n */\nconst clerkClient = async () => {\n  let requestData;\n\n  try {\n    const request = await buildRequestLike();\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    requestData = decryptClerkRequestData(encryptedRequestData);\n  } catch (err) {\n    if (err && isPrerenderingBailout(err)) {\n      // eslint-disable-next-line @typescript-eslint/only-throw-error\n      throw err;\n    }\n  }\n\n  // Fallbacks between options from middleware runtime and `NextRequest` from application server\n  const options = clerkMiddlewareRequestDataStorage.getStore()?.get('requestData') ?? requestData;\n  if (options?.secretKey || options?.publishableKey) {\n    return createClerkClientWithOptions(options);\n  }\n\n  return createClerkClientWithOptions({});\n};\n\nexport { clerkClient };\n"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB;;AAE1B,SAAS,kBAAkB,6BAA6B;AACxD,SAAS,oCAAoC;AAC7C,SAAS,iBAAiB;AAC1B,SAAS,yCAAyC;AAClD,SAAS,+BAA+B;;;;;;;;AAMxC,MAAM,cAAc,YAAY;IAZhC,IAAA,IAAA;IAaE,IAAI;IAEJ,IAAI;QACF,MAAM,UAAU,MAAM,wTAAA,CAAiB;QACvC,MAAM,sTAAuB,YAAA,EAAU,mRAAS,YAAA,CAAU,OAAA,CAAQ,gBAAgB;QAClF,kSAAc,0BAAA,EAAwB,oBAAoB;IAC5D,EAAA,OAAS,KAAK;QACZ,IAAI,OAAO,6TAAA,EAAsB,GAAG,GAAG;YAErC,MAAM;QACR;IACF;IAGA,MAAM,UAAA,CAAU,KAAA,CAAA,KAAA,+RAAA,CAAA,oCAAA,CAAkC,QAAA,CAAS,CAAA,KAA3C,OAAA,KAAA,IAAA,GAA8C,GAAA,CAAI,cAAA,KAAlD,OAAA,KAAoE;IACpF,IAAA,CAAI,WAAA,OAAA,KAAA,IAAA,QAAS,SAAA,KAAA,CAAa,WAAA,OAAA,KAAA,IAAA,QAAS,cAAA,GAAgB;QACjD,uSAAO,+BAAA,EAA6B,OAAO;IAC7C;IAEA,uSAAO,+BAAA,EAA6B,CAAC,CAAC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/buildClerkProps.ts"], "sourcesContent": ["import type { AuthObject, Organization, Session, User } from '@clerk/backend';\nimport { makeAuthObjectSerializable, stripPrivateDataFromObject } from '@clerk/backend/internal';\n\nimport { getAuthDataFromRequest } from './data/getAuthDataFromRequest';\nimport type { RequestLike } from './types';\n\ntype BuildClerkPropsInitState = { user?: User | null; session?: Session | null; organization?: Organization | null };\n\ntype BuildClerkProps = (req: RequestLike, authState?: BuildClerkPropsInitState) => Record<string, unknown>;\n\n/**\n * Clerk uses `buildClerkProps` to inform the client-side helpers of the authentication state of the user. This function is used SSR in the `getServerSideProps` function of your Next.js application.\n *\n * @example\n * ### Basic usage\n *\n * ```tsx {{ filename: 'pages/myServerSidePage.tsx' }}\n * import { getAuth, buildClerkProps } from '@clerk/nextjs/server'\n * import { GetServerSideProps } from 'next'\n *\n * export const getServerSideProps: GetServerSideProps = async (ctx) => {\n *  const { userId } = getAuth(ctx.req)\n *\n *  if (!userId) {\n *    // handle user is not signed in.\n *  }\n *\n *  // Load any data your application needs for the page using the userId\n *  return { props: { ...buildClerkProps(ctx.req) } }\n * }\n * ```\n *\n * @example\n * ### Usage with `clerkClient`\n *\n * The `clerkClient` allows you to access the Clerk API. You can use this to retrieve or update data.\n *\n * ```tsx {{ filename: 'pages/api/example.ts' }}\n * import { getAuth, buildClerkProps, clerkClient } from '@clerk/nextjs/server'\n * import { GetServerSideProps } from 'next'\n *\n * export const getServerSideProps: GetServerSideProps = async (ctx) => {\n *  const { userId } = getAuth(ctx.req)\n *\n *  const user = userId ? await clerkClient().users.getUser(userId) : undefined\n *\n *  return { props: { ...buildClerkProps(ctx.req, { user }) } }\n * }\n * ```\n */\nexport const buildClerkProps: BuildClerkProps = (req, initialState = {}) => {\n  const sanitizedAuthObject = getDynamicAuthData(req, initialState);\n\n  // Serializing the state on dev env is a temp workaround for the following issue:\n  // https://github.com/vercel/next.js/discussions/11209|Next.js\n  const __clerk_ssr_state =\n    process.env.NODE_ENV !== 'production' ? JSON.parse(JSON.stringify(sanitizedAuthObject)) : sanitizedAuthObject;\n  return { __clerk_ssr_state };\n};\n\nexport function getDynamicAuthData(req: RequestLike, initialState = {}) {\n  const authObject = getAuthDataFromRequest(req);\n\n  return makeAuthObjectSerializable(stripPrivateDataFromObject({ ...authObject, ...initialState })) as AuthObject;\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,4BAA4B,kCAAkC;;;AAEvE,SAAS,8BAA8B;;;;AA+ChC,MAAM,kBAAmC,CAAC,KAAK,eAAe,CAAC,CAAA,KAAM;IAC1E,MAAM,sBAAsB,mBAAmB,KAAK,YAAY;IAIhE,MAAM,oBACJ,QAAQ,IAAI,aAAa,cAAe,KAAK,KAAA,CAAM,KAAK,SAAA,CAAU,mBAAmB,CAAC,IAAI;IAC5F,OAAO;QAAE;IAAkB;AAC7B;AAEO,SAAS,mBAAmB,GAAA,EAAkB,eAAe,CAAC,CAAA,EAAG;IACtE,MAAM,0TAAa,yBAAA,EAAuB,GAAG;IAE7C,qRAAO,6BAAA,uRAA2B,6BAAA,EAA2B;QAAE,GAAG,UAAA;QAAY,GAAG,YAAA;IAAa,CAAC,CAAC;AAClG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/app-router/server/currentUser.ts"], "sourcesContent": ["import type { User } from '@clerk/backend';\n\nimport { clerkClient } from '../../server/clerkClient';\nimport { auth } from './auth';\n\n/**\n * The `currentUser` helper returns the [Backend User](https://clerk.com/docs/references/backend/types/backend-user) object of the currently active user. It can be used in Server Components, Route Handlers, and Server Actions.\n *\n * Under the hood, this helper:\n * - calls `fetch()`, so it is automatically deduped per request.\n * - uses the [`GET /v1/users/{user_id}`](https://clerk.com/docs/reference/backend-api/tag/Users#operation/GetUser) endpoint.\n * - counts towards the [Backend API request rate limit](https://clerk.com/docs/backend-requests/resources/rate-limits).\n *\n * @example\n * ```tsx {{ filename: 'app/page.tsx' }}\n * import { currentUser } from '@clerk/nextjs/server'\n *\n * export default async function Page() {\n *  const user = await currentUser()\n *\n *  if (!user) return <div>Not signed in</div>\n *\n *  return <div>Hello {user?.firstName}</div>\n * }\n * ```\n */\nexport async function currentUser(): Promise<User | null> {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const { userId } = await auth();\n  if (!userId) {\n    return null;\n  }\n\n  return (await clerkClient()).users.getUser(userId);\n}\n"], "names": [], "mappings": ";;;AAEA,SAAS,mBAAmB;AAC5B,SAAS,YAAY;;;;AAuBrB,eAAsB,cAAoC;;IAIxD,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI,0SAAM,OAAA,CAAK;IAC9B,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,OAAA,CAAQ,gSAAM,cAAA,CAAY,EAAA,EAAG,KAAA,CAAM,OAAA,CAAQ,MAAM;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/response.ts"], "sourcesContent": ["import { constants as nextConstants } from '../constants';\n\nexport const isRedirect = (res: Response) => {\n  return res.headers.get(nextConstants.Headers.NextRedirect);\n};\n\nexport const setHeader = <T extends Response>(res: T, name: string, val: string): T => {\n  res.headers.set(name, val);\n  return res;\n};\n"], "names": [], "mappings": ";;;;AAAA,SAAS,aAAa,qBAAqB;;;AAEpC,MAAM,aAAa,CAAC,QAAkB;IAC3C,OAAO,IAAI,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAc,OAAA,CAAQ,YAAY;AAC3D;AAEO,MAAM,YAAY,CAAqB,KAAQ,MAAc,QAAmB;IACrF,IAAI,OAAA,CAAQ,GAAA,CAAI,MAAM,GAAG;IACzB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/devBrowser.ts"], "sourcesContent": ["export const DEV_BROWSER_JWT_KEY = '__clerk_db_jwt';\nexport const DEV_BROWSER_JWT_HEADER = 'Clerk-Db-Jwt';\n\n// Sets the dev_browser JWT in the hash or the search\nexport function setDevBrowserJWTInURL(url: URL, jwt: string): URL {\n  const resultURL = new URL(url);\n\n  // extract & strip existing jwt from search\n  const jwtFromSearch = resultURL.searchParams.get(DEV_BROWSER_JWT_KEY);\n  resultURL.searchParams.delete(DEV_BROWSER_JWT_KEY);\n\n  // Existing jwt takes precedence\n  const jwtToSet = jwtFromSearch || jwt;\n\n  if (jwtToSet) {\n    resultURL.searchParams.set(DEV_BROWSER_JWT_KEY, jwtToSet);\n  }\n\n  return resultURL;\n}\n\n/**\n * Gets the __clerk_db_jwt JWT from either the hash or the search\n * Side effect:\n * Removes __clerk_db_jwt JWT from the URL (hash and searchParams) and updates the browser history\n */\nexport function extractDevBrowserJWTFromURL(url: URL): string {\n  const jwt = readDevBrowserJwtFromSearchParams(url);\n  const cleanUrl = removeDevBrowserJwt(url);\n  if (cleanUrl.href !== url.href && typeof globalThis.history !== 'undefined') {\n    globalThis.history.replaceState(null, '', removeDevBrowserJwt(url));\n  }\n  return jwt;\n}\n\nconst readDevBrowserJwtFromSearchParams = (url: URL) => {\n  return url.searchParams.get(DEV_BROWSER_JWT_KEY) || '';\n};\n\nconst removeDevBrowserJwt = (url: URL) => {\n  return removeDevBrowserJwtFromURLSearchParams(removeLegacyDevBrowserJwt(url));\n};\n\nconst removeDevBrowserJwtFromURLSearchParams = (_url: URL) => {\n  const url = new URL(_url);\n  url.searchParams.delete(DEV_BROWSER_JWT_KEY);\n  return url;\n};\n\n/**\n * Removes the __clerk_db_jwt JWT from the URL hash, as well as\n * the legacy __dev_session JWT from the URL searchParams\n * We no longer need to use this value, however, we should remove it from the URL\n * Existing v4 apps will write the JWT to the hash and the search params in order to ensure\n * backwards compatibility with older v4 apps.\n * The only use case where this is needed now is when a user upgrades to clerk@5 locally\n * without changing the component's version on their dashboard.\n * In this scenario, the AP@4 -> localhost@5 redirect will still have the JWT in the hash,\n * in which case we need to remove it.\n */\nconst removeLegacyDevBrowserJwt = (_url: URL) => {\n  const DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\\[(.*)\\]/;\n  const DEV_BROWSER_JWT_LEGACY_KEY = '__dev_session';\n  const url = new URL(_url);\n  url.searchParams.delete(DEV_BROWSER_JWT_LEGACY_KEY);\n  url.hash = decodeURI(url.hash).replace(DEV_BROWSER_JWT_MARKER_REGEXP, '');\n  if (url.href.endsWith('#')) {\n    url.hash = '';\n  }\n  return url;\n};\n"], "names": [], "mappings": ";;;;;;;AAAO,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAG/B,SAAS,sBAAsB,GAAA,EAAU,GAAA,EAAkB;IAChE,MAAM,YAAY,IAAI,IAAI,GAAG;IAG7B,MAAM,gBAAgB,UAAU,YAAA,CAAa,GAAA,CAAI,mBAAmB;IACpE,UAAU,YAAA,CAAa,MAAA,CAAO,mBAAmB;IAGjD,MAAM,WAAW,iBAAiB;IAElC,IAAI,UAAU;QACZ,UAAU,YAAA,CAAa,GAAA,CAAI,qBAAqB,QAAQ;IAC1D;IAEA,OAAO;AACT;AAOO,SAAS,4BAA4B,GAAA,EAAkB;IAC5D,MAAM,MAAM,kCAAkC,GAAG;IACjD,MAAM,WAAW,oBAAoB,GAAG;IACxC,IAAI,SAAS,IAAA,KAAS,IAAI,IAAA,IAAQ,OAAO,WAAW,OAAA,KAAY,aAAa;QAC3E,WAAW,OAAA,CAAQ,YAAA,CAAa,MAAM,IAAI,oBAAoB,GAAG,CAAC;IACpE;IACA,OAAO;AACT;AAEA,IAAM,oCAAoC,CAAC,QAAa;IACtD,OAAO,IAAI,YAAA,CAAa,GAAA,CAAI,mBAAmB,KAAK;AACtD;AAEA,IAAM,sBAAsB,CAAC,QAAa;IACxC,OAAO,uCAAuC,0BAA0B,GAAG,CAAC;AAC9E;AAEA,IAAM,yCAAyC,CAAC,SAAc;IAC5D,MAAM,MAAM,IAAI,IAAI,IAAI;IACxB,IAAI,YAAA,CAAa,MAAA,CAAO,mBAAmB;IAC3C,OAAO;AACT;AAaA,IAAM,4BAA4B,CAAC,SAAc;IAC/C,MAAM,gCAAgC;IACtC,MAAM,6BAA6B;IACnC,MAAM,MAAM,IAAI,IAAI,IAAI;IACxB,IAAI,YAAA,CAAa,MAAA,CAAO,0BAA0B;IAClD,IAAI,IAAA,GAAO,UAAU,IAAI,IAAI,EAAE,OAAA,CAAQ,+BAA+B,EAAE;IACxE,IAAI,IAAI,IAAA,CAAK,QAAA,CAAS,GAAG,GAAG;QAC1B,IAAI,IAAA,GAAO;IACb;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/serverRedirectWithAuth.ts"], "sourcesContent": ["// Middleware runs on the server side, before clerk-js is loaded, that's why we need Cookies.\nimport type { ClerkRequest } from '@clerk/backend/internal';\nimport { constants } from '@clerk/backend/internal';\nimport { DEV_BROWSER_JWT_KEY, setDevBrowserJWTInURL } from '@clerk/shared/devBrowser';\nimport { isDevelopmentFromSecretKey } from '@clerk/shared/keys';\nimport { NextResponse } from 'next/server';\n\n/**\n * Grabs the dev browser JWT from cookies and appends it to the redirect URL when redirecting to cross-origin.\n */\nexport const serverRedirectWithAuth = (clerkRequest: ClerkRequest, res: Response, opts: { secretKey: string }) => {\n  const location = res.headers.get('location');\n  const shouldAppendDevBrowser = res.headers.get(constants.Headers.ClerkRedirectTo) === 'true';\n\n  if (\n    shouldAppendDevBrowser &&\n    !!location &&\n    isDevelopmentFromSecretKey(opts.secretKey) &&\n    clerkRequest.clerkUrl.isCrossOrigin(location)\n  ) {\n    const dbJwt = clerkRequest.cookies.get(DEV_BROWSER_JWT_KEY) || '';\n    // Next.js 12.1+ allows redirects only to absolute URLs\n    const url = new URL(location);\n    const urlWithDevBrowser = setDevBrowserJWTInURL(url, dbJwt);\n    return NextResponse.redirect(urlWithDevBrowser.href, res);\n  }\n  return res;\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB;;;AAC1B,SAAS,qBAAqB,6BAA6B;;AAC3D,SAAS,kCAAkC;AAC3C,SAAS,oBAAoB;;;;;;AAKtB,MAAM,yBAAyB,CAAC,cAA4B,KAAe,SAAgC;IAChH,MAAM,WAAW,IAAI,OAAA,CAAQ,GAAA,CAAI,UAAU;IAC3C,MAAM,yBAAyB,IAAI,OAAA,CAAQ,GAAA,CAAI,sRAAA,CAAU,OAAA,CAAQ,eAAe,MAAM;IAEtF,IACE,0BACA,CAAC,CAAC,4RACF,6BAAA,EAA2B,KAAK,SAAS,KACzC,aAAa,QAAA,CAAS,aAAA,CAAc,QAAQ,GAC5C;QACA,MAAM,QAAQ,aAAa,OAAA,CAAQ,GAAA,6QAAI,sBAAmB,KAAK;QAE/D,MAAM,MAAM,IAAI,IAAI,QAAQ;QAC5B,MAAM,oSAAoB,wBAAA,EAAsB,KAAK,KAAK;QAC1D,uPAAO,eAAA,CAAa,QAAA,CAAS,kBAAkB,IAAA,EAAM,GAAG;IAC1D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/content-security-policy.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\n\n/**\n * Valid CSP directives according to the CSP Level 3 specification\n */\nexport type ContentSecurityPolicyDirective =\n  // Default resource directives\n  | 'connect-src'\n  | 'default-src'\n  | 'font-src'\n  | 'img-src'\n  | 'media-src'\n  | 'object-src'\n  | 'script-src'\n  | 'style-src'\n  // Framing and navigation directives\n  | 'base-uri'\n  | 'child-src'\n  | 'form-action'\n  | 'frame-ancestors'\n  | 'frame-src'\n  | 'manifest-src'\n  | 'navigate-to'\n  | 'prefetch-src'\n  | 'worker-src'\n  // Sandbox and plugin directives\n  | 'plugin-types'\n  | 'require-sri-for'\n  | 'sandbox'\n  // Trusted types and upgrade directives\n  | 'block-all-mixed-content'\n  | 'require-trusted-types-for'\n  | 'trusted-types'\n  | 'upgrade-insecure-requests'\n  // Reporting directives\n  | 'report-to'\n  | 'report-uri'\n  // CSP Level 3 additional directives\n  | 'script-src-attr'\n  | 'script-src-elem'\n  | 'style-src-attr'\n  | 'style-src-elem';\n\n/**\n * Partial record of directives and their values\n */\ntype ContentSecurityPolicyValues = Partial<Record<ContentSecurityPolicyDirective, string[]>>;\n\n/**\n * Directives and their values\n */\ntype ContentSecurityPolicyDirectiveSet = Record<ContentSecurityPolicyDirective, Set<string>>;\n\nexport interface ContentSecurityPolicyHeaders {\n  /**\n   * Array of formatted headers to be added to the response.\n   *\n   * Includes both standard and report-only headers when applicable.\n   * Includes nonce when strict mode is enabled.\n   */\n  headers: [string, string][];\n}\n\nexport interface ContentSecurityPolicyOptions {\n  /**\n   * When set to true, enhances security by applying the `strict-dynamic` attribute to the `script-src` CSP directive\n   */\n  strict?: boolean;\n  /**\n   * Custom CSP directives to merge with Clerk's default directives\n   */\n  directives?: Partial<Record<ContentSecurityPolicyDirective, string[]>>;\n  /**\n   * When set to true, the Content-Security-Policy-Report-Only header will be used instead of\n   * Content-Security-Policy. This allows monitoring policy violations without blocking content.\n   */\n  reportOnly?: boolean;\n  /**\n   * Specifies a reporting endpoint for CSP violations. This value will be used in the\n   * 'report-to' directive of the Content-Security-Policy header.\n   */\n  reportTo?: string;\n}\n\nclass ContentSecurityPolicyDirectiveManager {\n  /** Set of special keywords that require quoting in CSP directives */\n  private static readonly KEYWORDS = new Set([\n    'none',\n    'self',\n    'strict-dynamic',\n    'unsafe-eval',\n    'unsafe-hashes',\n    'unsafe-inline',\n  ]);\n\n  /** Default CSP directives and their values */\n  static readonly DEFAULT_DIRECTIVES: ContentSecurityPolicyValues = {\n    'connect-src': [\n      'self',\n      'https://clerk-telemetry.com',\n      'https://*.clerk-telemetry.com',\n      'https://api.stripe.com',\n      'https://maps.googleapis.com',\n    ],\n    'default-src': ['self'],\n    'form-action': ['self'],\n    'frame-src': [\n      'self',\n      'https://challenges.cloudflare.com',\n      'https://*.js.stripe.com',\n      'https://js.stripe.com',\n      'https://hooks.stripe.com',\n    ],\n    'img-src': ['self', 'https://img.clerk.com'],\n    'script-src': [\n      'self',\n      ...(process.env.NODE_ENV !== 'production' ? ['unsafe-eval'] : []),\n      'unsafe-inline',\n      'https:',\n      'http:',\n      'https://*.js.stripe.com',\n      'https://js.stripe.com',\n      'https://maps.googleapis.com',\n    ],\n    'style-src': ['self', 'unsafe-inline'],\n    'worker-src': ['self', 'blob:'],\n  };\n\n  /**\n   * Creates a new ContentSecurityPolicyDirectiveSet with default values\n   * @returns A new ContentSecurityPolicyDirectiveSet with default values\n   */\n  static createDefaultDirectives(): ContentSecurityPolicyDirectiveSet {\n    return Object.entries(this.DEFAULT_DIRECTIVES).reduce((acc, [key, values]) => {\n      acc[key as ContentSecurityPolicyDirective] = new Set(values);\n      return acc;\n    }, {} as ContentSecurityPolicyDirectiveSet);\n  }\n\n  /**\n   * Checks if a value is a special keyword that requires quoting\n   * @param value - The value to check\n   * @returns True if the value is a special keyword\n   */\n  static isKeyword(value: string): boolean {\n    return this.KEYWORDS.has(value.replace(/^'|'$/g, ''));\n  }\n\n  /**\n   * Formats a value according to CSP rules, adding quotes for special keywords\n   * @param value - The value to format\n   * @returns The formatted value\n   */\n  static formatValue(value: string): string {\n    const unquoted = value.replace(/^'|'$/g, '');\n    return this.isKeyword(unquoted) ? `'${unquoted}'` : value;\n  }\n\n  /**\n   * Handles directive values, ensuring proper formatting and special case handling\n   * @param values - Array of values to process\n   * @returns Set of formatted values\n   */\n  static handleDirectiveValues(values: string[]): Set<string> {\n    const result = new Set<string>();\n\n    if (values.includes(\"'none'\") || values.includes('none')) {\n      result.add(\"'none'\");\n      return result;\n    }\n\n    values.forEach(v => result.add(this.formatValue(v)));\n    return result;\n  }\n}\n\n/**\n * Handles merging of existing directives with new values\n * @param mergedCSP - The current merged CSP state\n * @param key - The directive key to handle\n * @param values - New values to merge\n */\nfunction handleExistingDirective(\n  mergedCSP: ContentSecurityPolicyDirectiveSet,\n  key: ContentSecurityPolicyDirective,\n  values: string[],\n) {\n  // None overrides all other values\n  if (values.includes(\"'none'\") || values.includes('none')) {\n    mergedCSP[key] = new Set([\"'none'\"]);\n    return;\n  }\n\n  // For existing directives, merge the values rather than replacing\n  const deduplicatedSet = new Set<string>();\n\n  mergedCSP[key].forEach(value => {\n    deduplicatedSet.add(ContentSecurityPolicyDirectiveManager.formatValue(value));\n  });\n\n  values.forEach(value => {\n    deduplicatedSet.add(ContentSecurityPolicyDirectiveManager.formatValue(value));\n  });\n\n  mergedCSP[key] = deduplicatedSet;\n}\n\n/**\n * Handles custom directives that are not part of the default set\n * @param customDirectives - Map of custom directives\n * @param key - The directive key\n * @param values - Values for the directive\n */\nfunction handleCustomDirective(customDirectives: Map<string, Set<string>>, key: string, values: string[]) {\n  // None overrides all other values\n  if (values.includes(\"'none'\") || values.includes('none')) {\n    customDirectives.set(key, new Set([\"'none'\"]));\n    return;\n  }\n\n  const formattedValues = new Set<string>();\n  values.forEach(value => {\n    const formattedValue = ContentSecurityPolicyDirectiveManager.formatValue(value);\n    formattedValues.add(formattedValue);\n  });\n\n  customDirectives.set(key, formattedValues);\n}\n\n/**\n * Applies formatting to the CSP header\n * @param mergedCSP - The merged CSP state to format\n * @returns Formatted CSP header string\n */\nfunction formatCSPHeader(mergedCSP: Record<string, Set<string>>): string {\n  return Object.entries(mergedCSP)\n    .sort(([a], [b]) => a.localeCompare(b))\n    .map(([key, values]) => {\n      const valueObjs = Array.from(values).map(v => ({\n        raw: v,\n        formatted: ContentSecurityPolicyDirectiveManager.formatValue(v),\n      }));\n\n      return `${key} ${valueObjs.map(item => item.formatted).join(' ')}`;\n    })\n    .join('; ');\n}\n\n/**\n * Generates a secure random nonce for CSP headers\n * @returns A base64-encoded random nonce\n */\nexport function generateNonce(): string {\n  const randomBytes = new Uint8Array(16);\n  crypto.getRandomValues(randomBytes);\n  const binaryString = Array.from(randomBytes, byte => String.fromCharCode(byte)).join('');\n  return btoa(binaryString);\n}\n\n/**\n * Builds a complete set of CSP directives by combining defaults with custom directives,\n * applying special configurations like strict mode and nonce, and formatting them into a valid CSP string.\n */\nfunction buildContentSecurityPolicyDirectives(\n  strict: boolean,\n  host: string,\n  customDirectives?: Partial<Record<ContentSecurityPolicyDirective, string[]>>,\n  nonce?: string,\n): string {\n  const directives = Object.entries(ContentSecurityPolicyDirectiveManager.DEFAULT_DIRECTIVES).reduce(\n    (acc, [key, values]) => {\n      acc[key as ContentSecurityPolicyDirective] = new Set(values);\n      return acc;\n    },\n    {} as ContentSecurityPolicyDirectiveSet,\n  );\n\n  directives['connect-src'].add(host);\n\n  if (strict) {\n    directives['script-src'].delete('http:');\n    directives['script-src'].delete('https:');\n    directives['script-src'].add(\"'strict-dynamic'\");\n    if (nonce) {\n      directives['script-src'].add(`'nonce-${nonce}'`);\n    }\n  }\n\n  if (customDirectives) {\n    const customDirectivesMap = new Map<string, Set<string>>();\n    Object.entries(customDirectives).forEach(([key, values]) => {\n      const valuesArray = Array.isArray(values) ? values : [values];\n      if (ContentSecurityPolicyDirectiveManager.DEFAULT_DIRECTIVES[key as ContentSecurityPolicyDirective]) {\n        handleExistingDirective(directives, key as ContentSecurityPolicyDirective, valuesArray);\n      } else {\n        handleCustomDirective(customDirectivesMap, key, valuesArray);\n      }\n    });\n\n    // Merge all custom directives into the final directives object\n    customDirectivesMap.forEach((values, key) => {\n      directives[key as ContentSecurityPolicyDirective] = values;\n    });\n  }\n\n  return formatCSPHeader(directives);\n}\n\n/**\n * Creates Content Security Policy (CSP) headers with the specified configuration\n * @returns Object containing the formatted CSP headers\n */\nexport function createContentSecurityPolicyHeaders(\n  host: string,\n  options: ContentSecurityPolicyOptions,\n): ContentSecurityPolicyHeaders {\n  const headers: [string, string][] = [];\n\n  const nonce = options.strict ? generateNonce() : undefined;\n\n  let cspHeader = buildContentSecurityPolicyDirectives(options.strict ?? false, host, options.directives, nonce);\n\n  if (options.reportTo) {\n    cspHeader += '; report-to csp-endpoint';\n    headers.push([constants.Headers.ReportingEndpoints, `csp-endpoint=\"${options.reportTo}\"`]);\n  }\n\n  if (options.reportOnly) {\n    headers.push([constants.Headers.ContentSecurityPolicyReportOnly, cspHeader]);\n  } else {\n    headers.push([constants.Headers.ContentSecurityPolicy, cspHeader]);\n  }\n\n  if (nonce) {\n    headers.push([constants.Headers.Nonce, nonce]);\n  }\n\n  return {\n    headers,\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,iBAAiB;;;AAoF1B,MAAM,sCAAsC;IAAA;;;GAAA,GAgD1C,OAAO,0BAA6D;QAClE,OAAO,OAAO,OAAA,CAAQ,IAAA,CAAK,kBAAkB,EAAE,MAAA,CAAO,CAAC,KAAK,CAAC,KAAK,MAAM,CAAA,KAAM;YAC5E,GAAA,CAAI,GAAqC,CAAA,GAAI,IAAI,IAAI,MAAM;YAC3D,OAAO;QACT,GAAG,CAAC,CAAsC;IAC5C;IAAA;;;;GAAA,GAOA,OAAO,UAAU,KAAA,EAAwB;QACvC,OAAO,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,MAAM,OAAA,CAAQ,UAAU,EAAE,CAAC;IACtD;IAAA;;;;GAAA,GAOA,OAAO,YAAY,KAAA,EAAuB;QACxC,MAAM,WAAW,MAAM,OAAA,CAAQ,UAAU,EAAE;QAC3C,OAAO,IAAA,CAAK,SAAA,CAAU,QAAQ,IAAI,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAA,CAAA,GAAM;IACtD;IAAA;;;;GAAA,GAOA,OAAO,sBAAsB,MAAA,EAA+B;QAC1D,MAAM,SAAS,aAAA,GAAA,IAAI,IAAY;QAE/B,IAAI,OAAO,QAAA,CAAS,QAAQ,KAAK,OAAO,QAAA,CAAS,MAAM,GAAG;YACxD,OAAO,GAAA,CAAI,QAAQ;YACnB,OAAO;QACT;QAEA,OAAO,OAAA,CAAQ,CAAA,IAAK,OAAO,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC,CAAC;QACnD,OAAO;IACT;AACF;AAAA,mEAAA,GA1FM,sCAEoB,QAAA,GAAW,aAAA,GAAA,IAAI,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;CACD;AAAA,4CAAA,GATG,sCAYY,kBAAA,GAAkD;IAChE,eAAe;QACb;QACA;QACA;QACA;QACA;KACF;IACA,eAAe;QAAC,MAAM;KAAA;IACtB,eAAe;QAAC,MAAM;KAAA;IACtB,aAAa;QACX;QACA;QACA;QACA;QACA;KACF;IACA,WAAW;QAAC;QAAQ,uBAAuB;KAAA;IAC3C,cAAc;QACZ;WACI,QAAQ,IAAI,aAAa,cAAe;YAAC,aAAa;SAAA,GAAI,CAAC;QAC/D;QACA;QACA;QACA;QACA;QACA;KACF;IACA,aAAa;QAAC;QAAQ,eAAe;KAAA;IACrC,cAAc;QAAC;QAAQ,OAAO;KAAA;AAChC;AAwDF,SAAS,wBACP,SAAA,EACA,GAAA,EACA,MAAA,EACA;IAEA,IAAI,OAAO,QAAA,CAAS,QAAQ,KAAK,OAAO,QAAA,CAAS,MAAM,GAAG;QACxD,SAAA,CAAU,GAAG,CAAA,GAAI,aAAA,GAAA,IAAI,IAAI;YAAC,QAAQ;SAAC;QACnC;IACF;IAGA,MAAM,kBAAkB,aAAA,GAAA,IAAI,IAAY;IAExC,SAAA,CAAU,GAAG,CAAA,CAAE,OAAA,CAAQ,CAAA,UAAS;QAC9B,gBAAgB,GAAA,CAAI,sCAAsC,WAAA,CAAY,KAAK,CAAC;IAC9E,CAAC;IAED,OAAO,OAAA,CAAQ,CAAA,UAAS;QACtB,gBAAgB,GAAA,CAAI,sCAAsC,WAAA,CAAY,KAAK,CAAC;IAC9E,CAAC;IAED,SAAA,CAAU,GAAG,CAAA,GAAI;AACnB;AAQA,SAAS,sBAAsB,gBAAA,EAA4C,GAAA,EAAa,MAAA,EAAkB;IAExG,IAAI,OAAO,QAAA,CAAS,QAAQ,KAAK,OAAO,QAAA,CAAS,MAAM,GAAG;QACxD,iBAAiB,GAAA,CAAI,KAAK,aAAA,GAAA,IAAI,IAAI;YAAC,QAAQ;SAAC,CAAC;QAC7C;IACF;IAEA,MAAM,kBAAkB,aAAA,GAAA,IAAI,IAAY;IACxC,OAAO,OAAA,CAAQ,CAAA,UAAS;QACtB,MAAM,iBAAiB,sCAAsC,WAAA,CAAY,KAAK;QAC9E,gBAAgB,GAAA,CAAI,cAAc;IACpC,CAAC;IAED,iBAAiB,GAAA,CAAI,KAAK,eAAe;AAC3C;AAOA,SAAS,gBAAgB,SAAA,EAAgD;IACvE,OAAO,OAAO,OAAA,CAAQ,SAAS,EAC5B,IAAA,CAAK,CAAC,CAAC,CAAC,CAAA,EAAG,CAAC,CAAC,CAAA,GAAM,EAAE,aAAA,CAAc,CAAC,CAAC,EACrC,GAAA,CAAI,CAAC,CAAC,KAAK,MAAM,CAAA,KAAM;QACtB,MAAM,YAAY,MAAM,IAAA,CAAK,MAAM,EAAE,GAAA,CAAI,CAAA,IAAA,CAAM;gBAC7C,KAAK;gBACL,WAAW,sCAAsC,WAAA,CAAY,CAAC;YAChE,CAAA,CAAE;QAEF,OAAO,GAAG,GAAG,CAAA,CAAA,EAAI,UAAU,GAAA,CAAI,CAAA,OAAQ,KAAK,SAAS,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA;IAClE,CAAC,EACA,IAAA,CAAK,IAAI;AACd;AAMO,SAAS,gBAAwB;IACtC,MAAM,cAAc,IAAI,WAAW,EAAE;IACrC,OAAO,eAAA,CAAgB,WAAW;IAClC,MAAM,eAAe,MAAM,IAAA,CAAK,aAAa,CAAA,OAAQ,OAAO,YAAA,CAAa,IAAI,CAAC,EAAE,IAAA,CAAK,EAAE;IACvF,OAAO,KAAK,YAAY;AAC1B;AAMA,SAAS,qCACP,MAAA,EACA,IAAA,EACA,gBAAA,EACA,KAAA,EACQ;IACR,MAAM,aAAa,OAAO,OAAA,CAAQ,sCAAsC,kBAAkB,EAAE,MAAA,CAC1F,CAAC,KAAK,CAAC,KAAK,MAAM,CAAA,KAAM;QACtB,GAAA,CAAI,GAAqC,CAAA,GAAI,IAAI,IAAI,MAAM;QAC3D,OAAO;IACT,GACA,CAAC;IAGH,UAAA,CAAW,aAAa,CAAA,CAAE,GAAA,CAAI,IAAI;IAElC,IAAI,QAAQ;QACV,UAAA,CAAW,YAAY,CAAA,CAAE,MAAA,CAAO,OAAO;QACvC,UAAA,CAAW,YAAY,CAAA,CAAE,MAAA,CAAO,QAAQ;QACxC,UAAA,CAAW,YAAY,CAAA,CAAE,GAAA,CAAI,kBAAkB;QAC/C,IAAI,OAAO;YACT,UAAA,CAAW,YAAY,CAAA,CAAE,GAAA,CAAI,CAAA,OAAA,EAAU,KAAK,CAAA,CAAA,CAAG;QACjD;IACF;IAEA,IAAI,kBAAkB;QACpB,MAAM,sBAAsB,aAAA,GAAA,IAAI,IAAyB;QACzD,OAAO,OAAA,CAAQ,gBAAgB,EAAE,OAAA,CAAQ,CAAC,CAAC,KAAK,MAAM,CAAA,KAAM;YAC1D,MAAM,cAAc,MAAM,OAAA,CAAQ,MAAM,IAAI,SAAS;gBAAC,MAAM;aAAA;YAC5D,IAAI,sCAAsC,kBAAA,CAAmB,GAAqC,CAAA,EAAG;gBACnG,wBAAwB,YAAY,KAAuC,WAAW;YACxF,OAAO;gBACL,sBAAsB,qBAAqB,KAAK,WAAW;YAC7D;QACF,CAAC;QAGD,oBAAoB,OAAA,CAAQ,CAAC,QAAQ,QAAQ;YAC3C,UAAA,CAAW,GAAqC,CAAA,GAAI;QACtD,CAAC;IACH;IAEA,OAAO,gBAAgB,UAAU;AACnC;AAMO,SAAS,mCACd,IAAA,EACA,OAAA,EAC8B;IA3ThC,IAAA;IA4TE,MAAM,UAA8B,CAAC,CAAA;IAErC,MAAM,QAAQ,QAAQ,MAAA,GAAS,cAAc,IAAI,KAAA;IAEjD,IAAI,YAAY,qCAAA,CAAqC,KAAA,QAAQ,MAAA,KAAR,OAAA,KAAkB,OAAO,MAAM,QAAQ,UAAA,EAAY,KAAK;IAE7G,IAAI,QAAQ,QAAA,EAAU;QACpB,aAAa;QACb,QAAQ,IAAA,CAAK;sRAAC,YAAA,CAAU,OAAA,CAAQ,kBAAA;YAAoB,CAAA,cAAA,EAAiB,QAAQ,QAAQ,CAAA,CAAA,CAAG;SAAC;IAC3F;IAEA,IAAI,QAAQ,UAAA,EAAY;QACtB,QAAQ,IAAA,CAAK;sRAAC,YAAA,CAAU,OAAA,CAAQ,+BAAA;YAAiC,SAAS;SAAC;IAC7E,OAAO;QACL,QAAQ,IAAA,CAAK;sRAAC,YAAA,CAAU,OAAA,CAAQ,qBAAA;YAAuB,SAAS;SAAC;IACnE;IAEA,IAAI,OAAO;QACT,QAAQ,IAAA,CAAK;sRAAC,YAAA,CAAU,OAAA,CAAQ,KAAA;YAAO,KAAK;SAAC;IAC/C;IAEA,OAAO;QACL;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/keyless.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\n\nimport { canUseKeyless } from '../utils/feature-flags';\n\nconst keylessCookiePrefix = `__clerk_keys_`;\n\nasync function hashString(str: string) {\n  const encoder = new TextEncoder();\n  const data = encoder.encode(str);\n  const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n  const hashArray = Array.from(new Uint8Array(hashBuffer));\n  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n  return hashHex.slice(0, 16); // Take only the first 16 characters\n}\n\nasync function getKeylessCookieName(): Promise<string> {\n  // eslint-disable-next-line turbo/no-undeclared-env-vars\n  const PATH = process.env.PWD;\n\n  // Handle gracefully missing PWD\n  if (!PATH) {\n    return `${keylessCookiePrefix}${0}`;\n  }\n\n  const lastThreeDirs = PATH.split('/').filter(Boolean).slice(-3).reverse().join('/');\n\n  // Hash the resulting string\n  const hash = await hashString(lastThreeDirs);\n\n  return `${keylessCookiePrefix}${hash}`;\n}\n\nasync function getKeylessCookieValue(\n  getter: (cookieName: string) => string | undefined,\n): Promise<AccountlessApplication | undefined> {\n  if (!canUseKeyless) {\n    return undefined;\n  }\n\n  const keylessCookieName = await getKeylessCookieName();\n  let keyless;\n\n  try {\n    if (keylessCookieName) {\n      keyless = JSON.parse(getter(keylessCookieName) || '{}');\n    }\n  } catch {\n    keyless = undefined;\n  }\n\n  return keyless;\n}\n\nexport { getKeylessCookieValue, getKeylessCookieName };\n"], "names": [], "mappings": ";;;;AAEA,SAAS,qBAAqB;;;AAE9B,MAAM,sBAAsB,CAAA,aAAA,CAAA;AAE5B,eAAe,WAAW,GAAA,EAAa;IACrC,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,OAAO,QAAQ,MAAA,CAAO,GAAG;IAC/B,MAAM,aAAa,MAAM,OAAO,MAAA,CAAO,MAAA,CAAO,WAAW,IAAI;IAC7D,MAAM,YAAY,MAAM,IAAA,CAAK,IAAI,WAAW,UAAU,CAAC;IACvD,MAAM,UAAU,UAAU,GAAA,CAAI,CAAA,IAAK,EAAE,QAAA,CAAS,EAAE,EAAE,QAAA,CAAS,GAAG,GAAG,CAAC,EAAE,IAAA,CAAK,EAAE;IAC3E,OAAO,QAAQ,KAAA,CAAM,GAAG,EAAE;AAC5B;AAEA,eAAe,uBAAwC;IAErD,MAAM,OAAO,QAAQ,GAAA,CAAI,GAAA;IAGzB,IAAI,CAAC,MAAM;QACT,OAAO,GAAG,mBAAmB,GAAG,CAAC,EAAA;IACnC;IAEA,MAAM,gBAAgB,KAAK,KAAA,CAAM,GAAG,EAAE,MAAA,CAAO,OAAO,EAAE,KAAA,CAAM,CAAA,CAAE,EAAE,OAAA,CAAQ,EAAE,IAAA,CAAK,GAAG;IAGlF,MAAM,OAAO,MAAM,WAAW,aAAa;IAE3C,OAAO,GAAG,mBAAmB,GAAG,IAAI,EAAA;AACtC;AAEA,eAAe,sBACb,MAAA,EAC6C;IAC7C,IAAI,2RAAC,gBAAA,EAAe;QAClB,OAAO,KAAA;IACT;IAEA,MAAM,oBAAoB,MAAM,qBAAqB;IACrD,IAAI;IAEJ,IAAI;QACF,IAAI,oCAAmB;YACrB,UAAU,KAAK,KAAA,CAAM,OAAO,iBAAiB,KAAK,IAAI;QACxD;IACF,EAAA,OAAQ;QACN,UAAU,KAAA;IACZ;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/nextErrors.ts"], "sourcesContent": ["/**\n * Clerk's identifiers that are used alongside the ones from Next.js\n */\nconst CONTROL_FLOW_ERROR = {\n  REDIRECT_TO_URL: 'CLERK_PROTECT_REDIRECT_TO_URL',\n  REDIRECT_TO_SIGN_IN: 'CLERK_PROTECT_REDIRECT_TO_SIGN_IN',\n  REDIRECT_TO_SIGN_UP: 'CLERK_PROTECT_REDIRECT_TO_SIGN_UP',\n};\n\n/**\n * In-house implementation of `notFound()`\n * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/not-found.ts\n */\nconst LEGACY_NOT_FOUND_ERROR_CODE = 'NEXT_NOT_FOUND';\n\ntype LegacyNotFoundError = Error & {\n  digest: typeof LEGACY_NOT_FOUND_ERROR_CODE;\n};\n\n/**\n * Checks for the error thrown from `notFound()` for versions <= next@15.0.4\n */\nfunction isLegacyNextjsNotFoundError(error: unknown): error is LegacyNotFoundError {\n  if (typeof error !== 'object' || error === null || !('digest' in error)) {\n    return false;\n  }\n\n  return error.digest === LEGACY_NOT_FOUND_ERROR_CODE;\n}\n\nconst HTTPAccessErrorStatusCodes = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n};\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatusCodes));\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK';\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`;\n};\n\nexport function isHTTPAccessFallbackError(error: unknown): error is HTTPAccessFallbackError {\n  if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n    return false;\n  }\n  const [prefix, httpStatus] = error.digest.split(';');\n\n  return prefix === HTTP_ERROR_FALLBACK_ERROR_CODE && ALLOWED_CODES.has(Number(httpStatus));\n}\n\nexport function whichHTTPAccessFallbackError(error: unknown): number | undefined {\n  if (!isHTTPAccessFallbackError(error)) {\n    return undefined;\n  }\n\n  const [, httpStatus] = error.digest.split(';');\n  return Number(httpStatus);\n}\n\nfunction isNextjsNotFoundError(error: unknown): error is LegacyNotFoundError | HTTPAccessFallbackError {\n  return (\n    isLegacyNextjsNotFoundError(error) ||\n    // Checks for the error thrown from `notFound()` for canary versions of next@15\n    whichHTTPAccessFallbackError(error) === HTTPAccessErrorStatusCodes.NOT_FOUND\n  );\n}\n\n/**\n * In-house implementation of `redirect()` extended with a `clerk_digest` property\n * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/redirect.ts\n */\n\nconst REDIRECT_ERROR_CODE = 'NEXT_REDIRECT';\n\ntype RedirectError<T = unknown> = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${'replace'};${string};${307};`;\n  clerk_digest: typeof CONTROL_FLOW_ERROR.REDIRECT_TO_URL | typeof CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN;\n} & T;\n\nfunction nextjsRedirectError(\n  url: string,\n  extra: Record<string, unknown>,\n  type: 'replace' = 'replace',\n  statusCode: 307 = 307,\n): never {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError;\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`;\n  error.clerk_digest = CONTROL_FLOW_ERROR.REDIRECT_TO_URL;\n  Object.assign(error, extra);\n  throw error;\n}\n\nfunction buildReturnBackUrl(url: string, returnBackUrl?: string | URL | null): string | URL {\n  return returnBackUrl === null ? '' : returnBackUrl || url;\n}\n\nfunction redirectToSignInError(url: string, returnBackUrl?: string | URL | null): never {\n  nextjsRedirectError(url, {\n    clerk_digest: CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN,\n    returnBackUrl: buildReturnBackUrl(url, returnBackUrl),\n  });\n}\n\nfunction redirectToSignUpError(url: string, returnBackUrl?: string | URL | null): never {\n  nextjsRedirectError(url, {\n    clerk_digest: CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_UP,\n    returnBackUrl: buildReturnBackUrl(url, returnBackUrl),\n  });\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nfunction isNextjsRedirectError(error: unknown): error is RedirectError<{ redirectUrl: string | URL }> {\n  if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n    return false;\n  }\n\n  const digest = error.digest.split(';');\n  const [errorCode, type] = digest;\n  const destination = digest.slice(2, -2).join(';');\n  const status = digest.at(-2);\n\n  const statusCode = Number(status);\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode === 307\n  );\n}\n\nfunction isRedirectToSignInError(error: unknown): error is RedirectError<{ returnBackUrl: string | URL }> {\n  if (isNextjsRedirectError(error) && 'clerk_digest' in error) {\n    return error.clerk_digest === CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN;\n  }\n\n  return false;\n}\n\nfunction isRedirectToSignUpError(error: unknown): error is RedirectError<{ returnBackUrl: string | URL }> {\n  if (isNextjsRedirectError(error) && 'clerk_digest' in error) {\n    return error.clerk_digest === CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_UP;\n  }\n\n  return false;\n}\n\nexport {\n  isNextjsNotFoundError,\n  isLegacyNextjsNotFoundError,\n  redirectToSignInError,\n  redirectToSignUpError,\n  nextjsRedirectError,\n  isNextjsRedirectError,\n  isRedirectToSignInError,\n  isRedirectToSignUpError,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA,MAAM,qBAAqB;IACzB,iBAAiB;IACjB,qBAAqB;IACrB,qBAAqB;AACvB;AAMA,MAAM,8BAA8B;AASpC,SAAS,4BAA4B,KAAA,EAA8C;IACjF,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAA,CAAE,YAAY,KAAA,GAAQ;QACvE,OAAO;IACT;IAEA,OAAO,MAAM,MAAA,KAAW;AAC1B;AAEA,MAAM,6BAA6B;IACjC,WAAW;IACX,WAAW;IACX,cAAc;AAChB;AAEA,MAAM,gBAAgB,IAAI,IAAI,OAAO,MAAA,CAAO,0BAA0B,CAAC;AAEhE,MAAM,iCAAiC;AAMvC,SAAS,0BAA0B,KAAA,EAAkD;IAC1F,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAA,CAAE,YAAY,KAAA,KAAU,OAAO,MAAM,MAAA,KAAW,UAAU;QAC3G,OAAO;IACT;IACA,MAAM,CAAC,QAAQ,UAAU,CAAA,GAAI,MAAM,MAAA,CAAO,KAAA,CAAM,GAAG;IAEnD,OAAO,WAAW,kCAAkC,cAAc,GAAA,CAAI,OAAO,UAAU,CAAC;AAC1F;AAEO,SAAS,6BAA6B,KAAA,EAAoC;IAC/E,IAAI,CAAC,0BAA0B,KAAK,GAAG;QACrC,OAAO,KAAA;IACT;IAEA,MAAM,CAAC,EAAE,UAAU,CAAA,GAAI,MAAM,MAAA,CAAO,KAAA,CAAM,GAAG;IAC7C,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,sBAAsB,KAAA,EAAwE;IACrG,OACE,4BAA4B,KAAK,KAAA,+EAAA;IAEjC,6BAA6B,KAAK,MAAM,2BAA2B,SAAA;AAEvE;AAOA,MAAM,sBAAsB;AAO5B,SAAS,oBACP,GAAA,EACA,KAAA,EACA,OAAkB,SAAA,EAClB,aAAkB,GAAA,EACX;IACP,MAAM,QAAQ,IAAI,MAAM,mBAAmB;IAC3C,MAAM,MAAA,GAAS,GAAG,mBAAmB,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,EAAI,GAAG,CAAA,CAAA,EAAI,UAAU,CAAA,CAAA,CAAA;IAClE,MAAM,YAAA,GAAe,mBAAmB,eAAA;IACxC,OAAO,MAAA,CAAO,OAAO,KAAK;IAC1B,MAAM;AACR;AAEA,SAAS,mBAAmB,GAAA,EAAa,aAAA,EAAmD;IAC1F,OAAO,kBAAkB,OAAO,KAAK,iBAAiB;AACxD;AAEA,SAAS,sBAAsB,GAAA,EAAa,aAAA,EAA4C;IACtF,oBAAoB,KAAK;QACvB,cAAc,mBAAmB,mBAAA;QACjC,eAAe,mBAAmB,KAAK,aAAa;IACtD,CAAC;AACH;AAEA,SAAS,sBAAsB,GAAA,EAAa,aAAA,EAA4C;IACtF,oBAAoB,KAAK;QACvB,cAAc,mBAAmB,mBAAA;QACjC,eAAe,mBAAmB,KAAK,aAAa;IACtD,CAAC;AACH;AASA,SAAS,sBAAsB,KAAA,EAAuE;IACpG,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAA,CAAE,YAAY,KAAA,KAAU,OAAO,MAAM,MAAA,KAAW,UAAU;QAC3G,OAAO;IACT;IAEA,MAAM,SAAS,MAAM,MAAA,CAAO,KAAA,CAAM,GAAG;IACrC,MAAM,CAAC,WAAW,IAAI,CAAA,GAAI;IAC1B,MAAM,cAAc,OAAO,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,GAAG;IAChD,MAAM,SAAS,OAAO,EAAA,CAAG,CAAA,CAAE;IAE3B,MAAM,aAAa,OAAO,MAAM;IAEhC,OACE,cAAc,uBAAA,CACb,SAAS,aAAa,SAAS,MAAA,KAChC,OAAO,gBAAgB,YACvB,CAAC,MAAM,UAAU,KACjB,eAAe;AAEnB;AAEA,SAAS,wBAAwB,KAAA,EAAyE;IACxG,IAAI,sBAAsB,KAAK,KAAK,kBAAkB,OAAO;QAC3D,OAAO,MAAM,YAAA,KAAiB,mBAAmB,mBAAA;IACnD;IAEA,OAAO;AACT;AAEA,SAAS,wBAAwB,KAAA,EAAyE;IACxG,IAAI,sBAAsB,KAAK,KAAK,kBAAkB,OAAO;QAC3D,OAAO,MAAM,YAAA,KAAiB,mBAAmB,mBAAA;IACnD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/clerkMiddleware.ts"], "sourcesContent": ["import type { AuthObject, ClerkClient } from '@clerk/backend';\nimport type { AuthenticateRequestOptions, ClerkRequest, RedirectFun, RequestState } from '@clerk/backend/internal';\nimport { AuthStatus, constants, createClerkRequest, createRedirect } from '@clerk/backend/internal';\nimport { parsePublishableKey } from '@clerk/shared/keys';\nimport type { PendingSessionOptions } from '@clerk/types';\nimport { notFound as nextjsNotFound } from 'next/navigation';\nimport type { NextMiddleware, NextRequest } from 'next/server';\nimport { NextResponse } from 'next/server';\n\nimport { isRedirect, serverRedirectWithAuth, setHeader } from '../utils';\nimport { withLogger } from '../utils/debugLogger';\nimport { canUseKeyless } from '../utils/feature-flags';\nimport { clerkClient } from './clerkClient';\nimport { PUBLISHABLE_KEY, SECRET_KEY, SIGN_IN_URL, SIGN_UP_URL } from './constants';\nimport { type ContentSecurityPolicyOptions, createContentSecurityPolicyHeaders } from './content-security-policy';\nimport { errorThrower } from './errorThrower';\nimport { getKeylessCookieValue } from './keyless';\nimport { clerkMiddlewareRequestDataStorage, clerkMiddlewareRequestDataStore } from './middleware-storage';\nimport {\n  isNextjsNotFoundError,\n  isNextjsRedirectError,\n  isRedirectToSignInError,\n  isRedirectToSignUpError,\n  nextjsRedirectError,\n  redirectToSignInError,\n  redirectToSignUpError,\n} from './nextErrors';\nimport type { AuthProtect } from './protect';\nimport { createProtect } from './protect';\nimport type { NextMiddlewareEvtParam, NextMiddlewareRequestParam, NextMiddlewareReturn } from './types';\nimport {\n  assertKey,\n  decorateRequest,\n  handleMultiDomainAndProxy,\n  redirectAdapter,\n  setRequestHeadersOnNextResponse,\n} from './utils';\n\nexport type ClerkMiddlewareAuthObject = AuthObject & {\n  redirectToSignIn: RedirectFun<Response>;\n  redirectToSignUp: RedirectFun<Response>;\n};\n\nexport interface ClerkMiddlewareAuth {\n  (opts?: PendingSessionOptions): Promise<ClerkMiddlewareAuthObject>;\n\n  protect: AuthProtect;\n}\n\ntype ClerkMiddlewareHandler = (\n  auth: ClerkMiddlewareAuth,\n  request: NextMiddlewareRequestParam,\n  event: NextMiddlewareEvtParam,\n) => NextMiddlewareReturn;\n\n/**\n * The `clerkMiddleware()` function accepts an optional object. The following options are available.\n * @interface\n */\nexport interface ClerkMiddlewareOptions extends AuthenticateRequestOptions {\n  /**\n   * If true, additional debug information will be logged to the console.\n   */\n  debug?: boolean;\n\n  /**\n   * When set, automatically injects a Content-Security-Policy header(s) compatible with Clerk.\n   */\n  contentSecurityPolicy?: ContentSecurityPolicyOptions;\n}\n\ntype ClerkMiddlewareOptionsCallback = (req: NextRequest) => ClerkMiddlewareOptions | Promise<ClerkMiddlewareOptions>;\n\n/**\n * Middleware for Next.js that handles authentication and authorization with Clerk.\n * For more details, please refer to the docs: https://clerk.com/docs/references/nextjs/clerk-middleware\n */\ninterface ClerkMiddleware {\n  /**\n   * @example\n   * export default clerkMiddleware((auth, request, event) => { ... }, options);\n   */\n  (handler: ClerkMiddlewareHandler, options?: ClerkMiddlewareOptions): NextMiddleware;\n\n  /**\n   * @example\n   * export default clerkMiddleware((auth, request, event) => { ... }, (req) => options);\n   */\n  (handler: ClerkMiddlewareHandler, options?: ClerkMiddlewareOptionsCallback): NextMiddleware;\n\n  /**\n   * @example\n   * export default clerkMiddleware(options);\n   */\n  (options?: ClerkMiddlewareOptions): NextMiddleware;\n\n  /**\n   * @example\n   * export default clerkMiddleware;\n   */\n  (request: NextMiddlewareRequestParam, event: NextMiddlewareEvtParam): NextMiddlewareReturn;\n}\n\n/**\n * The `clerkMiddleware()` helper integrates Clerk authentication into your Next.js application through Middleware. `clerkMiddleware()` is compatible with both the App and Pages routers.\n */\nexport const clerkMiddleware = ((...args: unknown[]): NextMiddleware | NextMiddlewareReturn => {\n  const [request, event] = parseRequestAndEvent(args);\n  const [handler, params] = parseHandlerAndOptions(args);\n\n  const middleware = clerkMiddlewareRequestDataStorage.run(clerkMiddlewareRequestDataStore, () => {\n    const baseNextMiddleware: NextMiddleware = withLogger('clerkMiddleware', logger => async (request, event) => {\n      // Handles the case where `options` is a callback function to dynamically access `NextRequest`\n      const resolvedParams = typeof params === 'function' ? await params(request) : params;\n\n      const keyless = await getKeylessCookieValue(name => request.cookies.get(name)?.value);\n\n      const publishableKey = assertKey(\n        resolvedParams.publishableKey || PUBLISHABLE_KEY || keyless?.publishableKey,\n        () => errorThrower.throwMissingPublishableKeyError(),\n      );\n\n      const secretKey = assertKey(resolvedParams.secretKey || SECRET_KEY || keyless?.secretKey, () =>\n        errorThrower.throwMissingSecretKeyError(),\n      );\n      const signInUrl = resolvedParams.signInUrl || SIGN_IN_URL;\n      const signUpUrl = resolvedParams.signUpUrl || SIGN_UP_URL;\n\n      const options = {\n        publishableKey,\n        secretKey,\n        signInUrl,\n        signUpUrl,\n        ...resolvedParams,\n      };\n\n      // Propagates the request data to be accessed on the server application runtime from helpers such as `clerkClient`\n      clerkMiddlewareRequestDataStore.set('requestData', options);\n      const resolvedClerkClient = await clerkClient();\n\n      if (options.debug) {\n        logger.enable();\n      }\n      const clerkRequest = createClerkRequest(request);\n      logger.debug('options', options);\n      logger.debug('url', () => clerkRequest.toJSON());\n\n      const authHeader = request.headers.get(constants.Headers.Authorization);\n      if (authHeader && authHeader.startsWith('Basic ')) {\n        logger.debug('Basic Auth detected');\n      }\n\n      const cspHeader = request.headers.get(constants.Headers.ContentSecurityPolicy);\n      if (cspHeader) {\n        logger.debug('Content-Security-Policy detected', () => ({\n          value: cspHeader,\n        }));\n      }\n\n      const requestState = await resolvedClerkClient.authenticateRequest(\n        clerkRequest,\n        createAuthenticateRequestOptions(clerkRequest, options),\n      );\n\n      logger.debug('requestState', () => ({\n        status: requestState.status,\n        // @ts-expect-error : FIXME\n        headers: JSON.stringify(Object.fromEntries(requestState.headers)),\n        reason: requestState.reason,\n      }));\n\n      const locationHeader = requestState.headers.get(constants.Headers.Location);\n      if (locationHeader) {\n        return new Response(null, { status: 307, headers: requestState.headers });\n      } else if (requestState.status === AuthStatus.Handshake) {\n        throw new Error('Clerk: handshake status without redirect');\n      }\n\n      const authObject = requestState.toAuth();\n      logger.debug('auth', () => ({ auth: authObject, debug: authObject.debug() }));\n\n      const redirectToSignIn = createMiddlewareRedirectToSignIn(clerkRequest);\n      const redirectToSignUp = createMiddlewareRedirectToSignUp(clerkRequest);\n      const protect = await createMiddlewareProtect(clerkRequest, authObject, redirectToSignIn);\n\n      const authHandler = (opts?: PendingSessionOptions) => {\n        const authObjWithMethods: ClerkMiddlewareAuthObject = Object.assign(requestState.toAuth(opts), {\n          redirectToSignIn,\n          redirectToSignUp,\n        });\n\n        return Promise.resolve(authObjWithMethods);\n      };\n      authHandler.protect = protect;\n\n      let handlerResult: Response = NextResponse.next();\n      try {\n        const userHandlerResult = await clerkMiddlewareRequestDataStorage.run(\n          clerkMiddlewareRequestDataStore,\n          async () => handler?.(authHandler, request, event),\n        );\n        handlerResult = userHandlerResult || handlerResult;\n      } catch (e: any) {\n        handlerResult = handleControlFlowErrors(e, clerkRequest, request, requestState);\n      }\n      if (options.contentSecurityPolicy) {\n        const { headers } = createContentSecurityPolicyHeaders(\n          (parsePublishableKey(publishableKey)?.frontendApi ?? '').replace('$', ''),\n          options.contentSecurityPolicy,\n        );\n\n        headers.forEach(([key, value]) => {\n          setHeader(handlerResult, key, value);\n        });\n\n        logger.debug('Clerk generated CSP', () => ({\n          headers,\n        }));\n      }\n\n      // TODO @nikos: we need to make this more generic\n      // and move the logic in clerk/backend\n      if (requestState.headers) {\n        requestState.headers.forEach((value, key) => {\n          if (key === constants.Headers.ContentSecurityPolicy) {\n            logger.debug('Content-Security-Policy detected', () => ({\n              value,\n            }));\n          }\n          handlerResult.headers.append(key, value);\n        });\n      }\n\n      if (isRedirect(handlerResult)) {\n        logger.debug('handlerResult is redirect');\n        return serverRedirectWithAuth(clerkRequest, handlerResult, options);\n      }\n\n      if (options.debug) {\n        setRequestHeadersOnNextResponse(handlerResult, clerkRequest, { [constants.Headers.EnableDebug]: 'true' });\n      }\n\n      const keylessKeysForRequestData =\n        // Only pass keyless credentials when there are no explicit keys\n        secretKey === keyless?.secretKey\n          ? {\n              publishableKey: keyless?.publishableKey,\n              secretKey: keyless?.secretKey,\n            }\n          : {};\n\n      decorateRequest(clerkRequest, handlerResult, requestState, resolvedParams, keylessKeysForRequestData);\n\n      return handlerResult;\n    });\n\n    const keylessMiddleware: NextMiddleware = async (request, event) => {\n      /**\n       * This mechanism replaces a full-page reload. Ensures that middleware will re-run and authenticate the request properly without the secret key or publishable key to be missing.\n       */\n      if (isKeylessSyncRequest(request)) {\n        return returnBackFromKeylessSync(request);\n      }\n\n      const resolvedParams = typeof params === 'function' ? await params(request) : params;\n      const keyless = await getKeylessCookieValue(name => request.cookies.get(name)?.value);\n      const isMissingPublishableKey = !(resolvedParams.publishableKey || PUBLISHABLE_KEY || keyless?.publishableKey);\n      /**\n       * In keyless mode, if the publishable key is missing, let the request through, to render `<ClerkProvider/>` that will resume the flow gracefully.\n       */\n      if (isMissingPublishableKey) {\n        const res = NextResponse.next();\n        setRequestHeadersOnNextResponse(res, request, {\n          [constants.Headers.AuthStatus]: 'signed-out',\n        });\n        return res;\n      }\n\n      return baseNextMiddleware(request, event);\n    };\n\n    const nextMiddleware: NextMiddleware = async (request, event) => {\n      if (canUseKeyless) {\n        return keylessMiddleware(request, event);\n      }\n\n      return baseNextMiddleware(request, event);\n    };\n\n    // If we have a request and event, we're being called as a middleware directly\n    // eg, export default clerkMiddleware;\n    if (request && event) {\n      return nextMiddleware(request, event);\n    }\n\n    // Otherwise, return a middleware that can be called with a request and event\n    // eg, export default clerkMiddleware(auth => { ... });\n    return nextMiddleware;\n  });\n\n  return middleware;\n}) as ClerkMiddleware;\n\nconst parseRequestAndEvent = (args: unknown[]) => {\n  return [args[0] instanceof Request ? args[0] : undefined, args[0] instanceof Request ? args[1] : undefined] as [\n    NextMiddlewareRequestParam | undefined,\n    NextMiddlewareEvtParam | undefined,\n  ];\n};\n\nconst parseHandlerAndOptions = (args: unknown[]) => {\n  return [\n    typeof args[0] === 'function' ? args[0] : undefined,\n    (args.length === 2 ? args[1] : typeof args[0] === 'function' ? {} : args[0]) || {},\n  ] as [ClerkMiddlewareHandler | undefined, ClerkMiddlewareOptions | ClerkMiddlewareOptionsCallback];\n};\n\nconst isKeylessSyncRequest = (request: NextMiddlewareRequestParam) =>\n  request.nextUrl.pathname === '/clerk-sync-keyless';\n\nconst returnBackFromKeylessSync = (request: NextMiddlewareRequestParam) => {\n  const returnUrl = request.nextUrl.searchParams.get('returnUrl');\n  const url = new URL(request.url);\n  url.pathname = '';\n\n  return NextResponse.redirect(returnUrl || url.toString());\n};\n\ntype AuthenticateRequest = Pick<ClerkClient, 'authenticateRequest'>['authenticateRequest'];\n\nexport const createAuthenticateRequestOptions = (\n  clerkRequest: ClerkRequest,\n  options: ClerkMiddlewareOptions,\n): Parameters<AuthenticateRequest>[1] => {\n  return {\n    ...options,\n    ...handleMultiDomainAndProxy(clerkRequest, options),\n  };\n};\n\nconst createMiddlewareRedirectToSignIn = (\n  clerkRequest: ClerkRequest,\n): ClerkMiddlewareAuthObject['redirectToSignIn'] => {\n  return (opts = {}) => {\n    const url = clerkRequest.clerkUrl.toString();\n    redirectToSignInError(url, opts.returnBackUrl);\n  };\n};\n\nconst createMiddlewareRedirectToSignUp = (\n  clerkRequest: ClerkRequest,\n): ClerkMiddlewareAuthObject['redirectToSignUp'] => {\n  return (opts = {}) => {\n    const url = clerkRequest.clerkUrl.toString();\n    redirectToSignUpError(url, opts.returnBackUrl);\n  };\n};\n\nconst createMiddlewareProtect = (\n  clerkRequest: ClerkRequest,\n  authObject: AuthObject,\n  redirectToSignIn: RedirectFun<Response>,\n) => {\n  return (async (params: any, options: any) => {\n    const notFound = () => nextjsNotFound();\n\n    const redirect = (url: string) =>\n      nextjsRedirectError(url, {\n        redirectUrl: url,\n      });\n\n    return createProtect({ request: clerkRequest, redirect, notFound, authObject, redirectToSignIn })(params, options);\n  }) as unknown as Promise<AuthProtect>;\n};\n\n// Handle errors thrown by protect() and redirectToSignIn() calls,\n// as we want to align the APIs between middleware, pages and route handlers\n// Normally, middleware requires to explicitly return a response, but we want to\n// avoid discrepancies between the APIs as it's easy to miss the `return` statement\n// especially when copy-pasting code from one place to another.\n// This function handles the known errors thrown by the APIs described above,\n// and returns the appropriate response.\nconst handleControlFlowErrors = (\n  e: any,\n  clerkRequest: ClerkRequest,\n  nextRequest: NextRequest,\n  requestState: RequestState,\n): Response => {\n  if (isNextjsNotFoundError(e)) {\n    // Rewrite to a bogus URL to force not found error\n    return setHeader(\n      // This is an internal rewrite purely to trigger a not found error. We do not want Next.js to think that the\n      // destination URL is a valid page, so we use `nextRequest.url` as the base for the fake URL, which Next.js\n      // understands is an internal URL and won't run middleware against the request.\n      NextResponse.rewrite(new URL(`/clerk_${Date.now()}`, nextRequest.url)),\n      constants.Headers.AuthReason,\n      'protect-rewrite',\n    );\n  }\n\n  const isRedirectToSignIn = isRedirectToSignInError(e);\n  const isRedirectToSignUp = isRedirectToSignUpError(e);\n\n  if (isRedirectToSignIn || isRedirectToSignUp) {\n    const redirect = createRedirect({\n      redirectAdapter,\n      baseUrl: clerkRequest.clerkUrl,\n      signInUrl: requestState.signInUrl,\n      signUpUrl: requestState.signUpUrl,\n      publishableKey: requestState.publishableKey,\n      sessionStatus: requestState.toAuth()?.sessionStatus,\n    });\n\n    const { returnBackUrl } = e;\n    return redirect[isRedirectToSignIn ? 'redirectToSignIn' : 'redirectToSignUp']({ returnBackUrl });\n  }\n\n  if (isNextjsRedirectError(e)) {\n    return redirectAdapter(e.redirectUrl);\n  }\n\n  throw e;\n};\n"], "names": ["request", "event", "_a"], "mappings": ";;;;;AAEA,SAAS,YAAY,WAAW,oBAAoB,sBAAsB;;AAC1E,SAAS,2BAA2B;;AAEpC,SAAS,YAAY,sBAAsB;;AAE3C,SAAS,oBAAoB;;AAE7B,SAAS,YAAY,wBAAwB,iBAAiB;AAC9D,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,mBAAmB;AAC5B,SAAS,iBAAiB,YAAY,aAAa,mBAAmB;AACtE,SAA4C,0CAA0C;AACtF,SAAS,oBAAoB;AAC7B,SAAS,6BAA6B;AACtC,SAAS,mCAAmC,uCAAuC;AACnF;AAUA,SAAS,qBAAqB;AAE9B;;;;;;;;;;;;;;;;;;AA4EO,MAAM,kBAAmB,CAAA,GAAI,SAA2D;IAC7F,MAAM,CAAC,SAAS,KAAK,CAAA,GAAI,qBAAqB,IAAI;IAClD,MAAM,CAAC,SAAS,MAAM,CAAA,GAAI,uBAAuB,IAAI;IAErD,MAAM,6SAAa,oCAAA,CAAkC,GAAA,iSAAI,kCAAA,EAAiC,MAAM;QAC9F,MAAM,8SAAqC,aAAA,EAAW,mBAAmB,CAAA,SAAU,OAAOA,UAASC,WAAU;gBA/GjH,IAAA,IAAA;gBAiHM,MAAM,iBAAiB,OAAO,WAAW,aAAa,MAAM,OAAOD,QAAO,IAAI;gBAE9E,MAAM,UAAU,4RAAM,wBAAA,EAAsB,CAAA,SAAK;oBAnHvD,IAAAE;oBAmH0D,OAAA,CAAAA,MAAAF,SAAQ,OAAA,CAAQ,GAAA,CAAI,IAAI,CAAA,KAAxB,OAAA,KAAA,IAAAE,IAA2B,KAAA;gBAAA,CAAK;gBAEpF,MAAM,qSAAiB,YAAA,EACrB,eAAe,cAAA,wRAAkB,kBAAA,IAAA,CAAmB,WAAA,OAAA,KAAA,IAAA,QAAS,cAAA,GAC7D,2RAAM,eAAA,CAAa,+BAAA,CAAgC;gBAGrD,MAAM,gSAAY,YAAA,EAAU,eAAe,SAAA,IAAa,iSAAA,IAAA,CAAc,WAAA,OAAA,KAAA,IAAA,QAAS,SAAA,GAAW,2RACxF,eAAA,CAAa,0BAAA,CAA2B;gBAE1C,MAAM,YAAY,eAAe,SAAA,wRAAa,cAAA;gBAC9C,MAAM,YAAY,eAAe,SAAA,IAAa,kSAAA;gBAE9C,MAAM,UAAU;oBACd;oBACA;oBACA;oBACA;oBACA,GAAG,cAAA;gBACL;gBAGA,+RAAA,CAAA,kCAAA,CAAgC,GAAA,CAAI,eAAe,OAAO;gBAC1D,MAAM,sBAAsB,gSAAM,cAAA,CAAY;gBAE9C,IAAI,QAAQ,KAAA,EAAO;oBACjB,OAAO,MAAA,CAAO;gBAChB;gBACA,MAAM,mBAAe,+RAAA,EAAmBF,QAAO;gBAC/C,OAAO,KAAA,CAAM,WAAW,OAAO;gBAC/B,OAAO,KAAA,CAAM,OAAO,IAAM,aAAa,MAAA,CAAO,CAAC;gBAE/C,MAAM,aAAaA,SAAQ,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,aAAa;gBACtE,IAAI,cAAc,WAAW,UAAA,CAAW,QAAQ,GAAG;oBACjD,OAAO,KAAA,CAAM,qBAAqB;gBACpC;gBAEA,MAAM,YAAYA,SAAQ,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,qBAAqB;gBAC7E,IAAI,WAAW;oBACb,OAAO,KAAA,CAAM,oCAAoC,IAAA,CAAO;4BACtD,OAAO;wBACT,CAAA,CAAE;gBACJ;gBAEA,MAAM,eAAe,MAAM,oBAAoB,mBAAA,CAC7C,cACA,iCAAiC,cAAc,OAAO;gBAGxD,OAAO,KAAA,CAAM,gBAAgB,IAAA,CAAO;wBAClC,QAAQ,aAAa,MAAA;wBAAA,2BAAA;wBAErB,SAAS,KAAK,SAAA,CAAU,OAAO,WAAA,CAAY,aAAa,OAAO,CAAC;wBAChE,QAAQ,aAAa,MAAA;oBACvB,CAAA,CAAE;gBAEF,MAAM,iBAAiB,aAAa,OAAA,CAAQ,GAAA,2QAAI,YAAA,CAAU,OAAA,CAAQ,QAAQ;gBAC1E,IAAI,gBAAgB;oBAClB,OAAO,IAAI,SAAS,MAAM;wBAAE,QAAQ;wBAAK,SAAS,aAAa,OAAA;oBAAQ,CAAC;gBAC1E,OAAA,IAAW,aAAa,MAAA,KAAW,uRAAA,CAAW,SAAA,EAAW;oBACvD,MAAM,IAAI,MAAM,0CAA0C;gBAC5D;gBAEA,MAAM,aAAa,aAAa,MAAA,CAAO;gBACvC,OAAO,KAAA,CAAM,QAAQ,IAAA,CAAO;wBAAE,MAAM;wBAAY,OAAO,WAAW,KAAA,CAAM;oBAAE,CAAA,CAAE;gBAE5E,MAAM,mBAAmB,iCAAiC,YAAY;gBACtE,MAAM,mBAAmB,iCAAiC,YAAY;gBACtE,MAAM,UAAU,MAAM,wBAAwB,cAAc,YAAY,gBAAgB;gBAExF,MAAM,cAAc,CAAC,SAAiC;oBACpD,MAAM,qBAAgD,OAAO,MAAA,CAAO,aAAa,MAAA,CAAO,IAAI,GAAG;wBAC7F;wBACA;oBACF,CAAC;oBAED,OAAO,QAAQ,OAAA,CAAQ,kBAAkB;gBAC3C;gBACA,YAAY,OAAA,GAAU;gBAEtB,IAAI,gQAA0B,eAAA,CAAa,IAAA,CAAK;gBAChD,IAAI;oBACF,MAAM,oBAAoB,sSAAM,oCAAA,CAAkC,GAAA,iSAChE,kCAAA,EACA,UAAY,WAAA,OAAA,KAAA,IAAA,QAAU,aAAaA,UAASC;oBAE9C,gBAAgB,qBAAqB;gBACvC,EAAA,OAAS,GAAQ;oBACf,gBAAgB,wBAAwB,GAAG,cAAcD,UAAS,YAAY;gBAChF;gBACA,IAAI,QAAQ,qBAAA,EAAuB;oBACjC,MAAM,EAAE,OAAA,CAAQ,CAAA,OAAI,6UAAA,EAAA,CAAA,CACjB,KAAA,CAAA,KAAA,CAAA,GAAA,2QAAA,CAAA,sBAAA,EAAoB,cAAc,CAAA,KAAlC,OAAA,KAAA,IAAA,GAAqC,WAAA,KAArC,OAAA,KAAoD,EAAA,EAAI,OAAA,CAAQ,KAAK,EAAE,GACxE,QAAQ,qBAAA;oBAGV,QAAQ,OAAA,CAAQ,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;wBAChC,CAAA,GAAA,iRAAA,CAAA,YAAA,EAAU,eAAe,KAAK,KAAK;oBACrC,CAAC;oBAED,OAAO,KAAA,CAAM,uBAAuB,IAAA,CAAO;4BACzC;wBACF,CAAA,CAAE;gBACJ;gBAIA,IAAI,aAAa,OAAA,EAAS;oBACxB,aAAa,OAAA,CAAQ,OAAA,CAAQ,CAAC,OAAO,QAAQ;wBAC3C,IAAI,kRAAQ,YAAA,CAAU,OAAA,CAAQ,qBAAA,EAAuB;4BACnD,OAAO,KAAA,CAAM,oCAAoC,IAAA,CAAO;oCACtD;gCACF,CAAA,CAAE;wBACJ;wBACA,cAAc,OAAA,CAAQ,MAAA,CAAO,KAAK,KAAK;oBACzC,CAAC;gBACH;gBAEA,IAAI,mSAAA,EAAW,aAAa,GAAG;oBAC7B,OAAO,KAAA,CAAM,2BAA2B;oBACxC,OAAO,6TAAA,EAAuB,cAAc,eAAe,OAAO;gBACpE;gBAEA,IAAI,QAAQ,KAAA,EAAO;oBACjB,CAAA,GAAA,+QAAA,CAAA,kCAAA,EAAgC,eAAe,cAAc;wBAAE,2QAAC,YAAA,CAAU,OAAA,CAAQ,WAAW,CAAA,EAAG;oBAAO,CAAC;gBAC1G;gBAEA,MAAM,4BAAA,gEAAA;gBAEJ,cAAA,CAAc,WAAA,OAAA,KAAA,IAAA,QAAS,SAAA,IACnB;oBACE,gBAAgB,WAAA,OAAA,KAAA,IAAA,QAAS,cAAA;oBACzB,WAAW,WAAA,OAAA,KAAA,IAAA,QAAS,SAAA;gBACtB,IACA,CAAC;gBAEP,CAAA,GAAA,+QAAA,CAAA,kBAAA,EAAgB,cAAc,eAAe,cAAc,gBAAgB,yBAAyB;gBAEpG,OAAO;YACT,CAAC;QAED,MAAM,oBAAoC,OAAOA,UAASC,WAAU;YAIlE,IAAI,qBAAqBD,QAAO,GAAG;gBACjC,OAAO,0BAA0BA,QAAO;YAC1C;YAEA,MAAM,iBAAiB,OAAO,WAAW,aAAa,MAAM,OAAOA,QAAO,IAAI;YAC9E,MAAM,UAAU,4RAAM,wBAAA,EAAsB,CAAA,SAAK;gBAzQvD,IAAA;gBAyQ0D,OAAA,CAAA,KAAAA,SAAQ,OAAA,CAAQ,GAAA,CAAI,IAAI,CAAA,KAAxB,OAAA,KAAA,IAAA,GAA2B,KAAA;YAAA,CAAK;YACpF,MAAM,0BAA0B,CAAA,CAAE,eAAe,cAAA,wRAAkB,kBAAA,IAAA,CAAmB,WAAA,OAAA,KAAA,IAAA,QAAS,cAAA,CAAA;YAI/F,IAAI,yBAAyB;gBAC3B,MAAM,sPAAM,eAAA,CAAa,IAAA,CAAK;gBAC9B,CAAA,GAAA,+QAAA,CAAA,kCAAA,EAAgC,KAAKA,UAAS;oBAC5C,0QAAC,aAAA,CAAU,OAAA,CAAQ,UAAU,CAAA,EAAG;gBAClC,CAAC;gBACD,OAAO;YACT;YAEA,OAAO,mBAAmBA,UAASC,MAAK;QAC1C;QAEA,MAAM,iBAAiC,OAAOD,UAASC,WAAU;YAC/D,8RAAI,gBAAA,EAAe;gBACjB,OAAO,kBAAkBD,UAASC,MAAK;YACzC;YAEA,OAAO,mBAAmBD,UAASC,MAAK;QAC1C;QAIA,IAAI,WAAW,OAAO;YACpB,OAAO,eAAe,SAAS,KAAK;QACtC;QAIA,OAAO;IACT,CAAC;IAED,OAAO;AACT;AAEA,MAAM,uBAAuB,CAAC,SAAoB;IAChD,OAAO;QAAC,IAAA,CAAK,CAAC,CAAA,YAAa,UAAU,IAAA,CAAK,CAAC,CAAA,GAAI,KAAA;QAAW,IAAA,CAAK,CAAC,CAAA,YAAa,UAAU,IAAA,CAAK,CAAC,CAAA,GAAI,KAAA,CAAS;KAAA;AAI5G;AAEA,MAAM,yBAAyB,CAAC,SAAoB;IAClD,OAAO;QACL,OAAO,IAAA,CAAK,CAAC,CAAA,KAAM,aAAa,IAAA,CAAK,CAAC,CAAA,GAAI,KAAA;QAAA,CACzC,KAAK,MAAA,KAAW,IAAI,IAAA,CAAK,CAAC,CAAA,GAAI,OAAO,IAAA,CAAK,CAAC,CAAA,KAAM,aAAa,CAAC,IAAI,IAAA,CAAK,CAAC,CAAA,KAAM,CAAC;KACnF;AACF;AAEA,MAAM,uBAAuB,CAAC,UAC5B,QAAQ,OAAA,CAAQ,QAAA,KAAa;AAE/B,MAAM,4BAA4B,CAAC,YAAwC;IACzE,MAAM,YAAY,QAAQ,OAAA,CAAQ,YAAA,CAAa,GAAA,CAAI,WAAW;IAC9D,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;IAC/B,IAAI,QAAA,GAAW;IAEf,OAAO,+PAAA,CAAa,QAAA,CAAS,aAAa,IAAI,QAAA,CAAS,CAAC;AAC1D;AAIO,MAAM,mCAAmC,CAC9C,cACA,YACuC;IACvC,OAAO;QACL,GAAG,OAAA;QACH,uRAAG,4BAAA,EAA0B,cAAc,OAAO,CAAA;IACpD;AACF;AAEA,MAAM,mCAAmC,CACvC,iBACkD;IAClD,OAAO,CAAC,OAAO,CAAC,CAAA,KAAM;QACpB,MAAM,MAAM,aAAa,QAAA,CAAS,QAAA,CAAS;QAC3C,CAAA,GAAA,oRAAA,CAAA,wBAAA,EAAsB,KAAK,KAAK,aAAa;IAC/C;AACF;AAEA,MAAM,mCAAmC,CACvC,iBACkD;IAClD,OAAO,CAAC,OAAO,CAAC,CAAA,KAAM;QACpB,MAAM,MAAM,aAAa,QAAA,CAAS,QAAA,CAAS;QAC3C,CAAA,GAAA,oRAAA,CAAA,wBAAA,EAAsB,KAAK,KAAK,aAAa;IAC/C;AACF;AAEA,MAAM,0BAA0B,CAC9B,cACA,YACA,qBACG;IACH,OAAQ,OAAO,QAAa,YAAiB;QAC3C,MAAM,WAAW,+SAAM,WAAA,CAAe;QAEtC,MAAM,WAAW,CAAC,UAChB,2SAAA,EAAoB,KAAK;gBACvB,aAAa;YACf,CAAC;QAEH,6RAAO,gBAAA,EAAc;YAAE,SAAS;YAAc;YAAU;YAAU;YAAY;QAAiB,CAAC,EAAE,QAAQ,OAAO;IACnH;AACF;AASA,MAAM,0BAA0B,CAC9B,GACA,cACA,aACA,iBACa;IAnYf,IAAA;IAoYE,6RAAI,wBAAA,EAAsB,CAAC,GAAG;QAE5B,6RAAO,YAAA,EAAA,4GAAA;QAAA,2GAAA;QAAA,+EAAA;uPAIL,gBAAA,CAAa,OAAA,CAAQ,IAAI,IAAI,CAAA,OAAA,EAAU,KAAK,GAAA,CAAI,CAAC,EAAA,EAAI,YAAY,GAAG,CAAC,6QACrE,YAAA,CAAU,OAAA,CAAQ,UAAA,EAClB;IAEJ;IAEA,MAAM,qBAAqB,mTAAA,EAAwB,CAAC;IACpD,MAAM,8SAAqB,0BAAA,EAAwB,CAAC;IAEpD,IAAI,sBAAsB,oBAAoB;QAC5C,MAAM,gSAAW,iBAAA,EAAe;6SAC9B,kBAAA;YACA,SAAS,aAAa,QAAA;YACtB,WAAW,aAAa,SAAA;YACxB,WAAW,aAAa,SAAA;YACxB,gBAAgB,aAAa,cAAA;YAC7B,eAAA,CAAe,KAAA,aAAa,MAAA,CAAO,CAAA,KAApB,OAAA,KAAA,IAAA,GAAuB,aAAA;QACxC,CAAC;QAED,MAAM,EAAE,aAAA,CAAc,CAAA,GAAI;QAC1B,OAAO,QAAA,CAAS,qBAAqB,qBAAqB,kBAAkB,CAAA,CAAE;YAAE;QAAc,CAAC;IACjG;IAEA,6RAAI,wBAAA,EAAsB,CAAC,GAAG;QAC5B,2RAAO,kBAAA,EAAgB,EAAE,WAAW;IACtC;IAEA,MAAM;AACR", "ignoreList": [0], "debugId": null}}]}