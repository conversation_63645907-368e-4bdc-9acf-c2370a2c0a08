(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[33],{6356:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(i=n;0!=i--;)if(!e(t[i],r[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(o=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(i=n;0!=i--;)if(!Object.prototype.hasOwnProperty.call(r,o[i]))return!1;for(i=n;0!=i--;){var n,i,o,a=o[i];if(!e(t[a],r[a]))return!1}return!0}return t!=t&&r!=r}},9502:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},10944:(e,t,r)=>{"use strict";var n=r(20102),i=r(94007),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,s.prototype),t}function s(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return u(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e){var n=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!s.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|p(n,i),l=a(o),c=l.write(n,i);return c!==o&&(l=l.slice(0,c)),l}if(ArrayBuffer.isView(e)){var u=e;if(T(u,Uint8Array)){var m=new Uint8Array(u);return d(m.buffer,m.byteOffset,m.byteLength)}return h(u)}if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(T(e,ArrayBuffer)||e&&T(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(T(e,SharedArrayBuffer)||e&&T(e.buffer,SharedArrayBuffer)))return d(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var g=e.valueOf&&e.valueOf();if(null!=g&&g!==e)return s.from(g,t,r);var v=function(e){if(s.isBuffer(e)){var t=0|f(e.length),r=a(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?a(0):h(e):"Buffer"===e.type&&Array.isArray(e.data)?h(e.data):void 0}(e);if(v)return v;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return s.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function u(e){return c(e),a(e<0?0:0|f(e))}function h(e){for(var t=e.length<0?0:0|f(e.length),r=a(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function d(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),s.prototype),n}function f(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(s.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||T(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return _(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return R(e).length;default:if(i)return n?-1:_(e).length;t=(""+t).toLowerCase(),i=!0}}function m(e,t,r){var i,o,a,s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=O[e[o]];return i}(this,t,r);case"utf8":case"utf-8":return b(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,o=t,a=r,0===o&&a===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function v(e,t,r,n,i){var o;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:y(e,t,r,n,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return y(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function y(e,t,r,n,i){var o,a=1,s=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;a=2,s/=2,l/=2,r/=2}function c(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var u=-1;for(o=r;o<s;o++)if(c(e,o)===c(t,-1===u?0:o-u)){if(-1===u&&(u=o),o-u+1===l)return u*a}else -1!==u&&(o-=o-u),u=-1}else for(r+l>s&&(r=s-l),o=r;o>=0;o--){for(var h=!0,d=0;d<l;d++)if(c(e,o+d)!==c(t,d)){h=!1;break}if(h)return o}return -1}function b(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,s,l,c=e[i],u=null,h=c>239?4:c>223?3:c>191?2:1;if(i+h<=r)switch(h){case 1:c<128&&(u=c);break;case 2:(192&(o=e[i+1]))==128&&(l=(31&c)<<6|63&o)>127&&(u=l);break;case 3:o=e[i+1],a=e[i+2],(192&o)==128&&(192&a)==128&&(l=(15&c)<<12|(63&o)<<6|63&a)>2047&&(l<55296||l>57343)&&(u=l);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],(192&o)==128&&(192&a)==128&&(192&s)==128&&(l=(15&c)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&l<1114112&&(u=l)}null===u?(u=65533,h=1):u>65535&&(u-=65536,n.push(u>>>10&1023|55296),u=56320|1023&u),n.push(u),i+=h}var d=n,f=d.length;if(f<=4096)return String.fromCharCode.apply(String,d);for(var p="",m=0;m<f;)p+=String.fromCharCode.apply(String,d.slice(m,m+=4096));return p}function w(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function k(e,t,r,n,i,o){if(!s.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function C(e,t,r,n,i,o){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function E(e,t,r,n,o){return t*=1,r>>>=0,o||C(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function x(e,t,r,n,o){return t*=1,r>>>=0,o||C(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}t.hp=s,t.IS=50,s.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(e,t,r){return(c(e),e<=0)?a(e):void 0!==t?"string"==typeof r?a(e).fill(t,r):a(e).fill(t):a(e)},s.allocUnsafe=function(e){return u(e)},s.allocUnsafeSlow=function(e){return u(e)},s.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==s.prototype},s.compare=function(e,t){if(T(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),T(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(e)||!s.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=s.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(T(o,Uint8Array))i+o.length>n.length?s.from(o).copy(n,i):Uint8Array.prototype.set.call(n,o,i);else if(s.isBuffer(o))o.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=o.length}return n},s.byteLength=p,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},s.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?b(this,0,e):m.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(e){if(!s.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.IS;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},o&&(s.prototype[o]=s.prototype.inspect),s.prototype.compare=function(e,t,r,n,i){if(T(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var o=i-n,a=r-t,l=Math.min(o,a),c=this.slice(n,i),u=e.slice(t,r),h=0;h<l;++h)if(c[h]!==u[h]){o=c[h],a=u[h];break}return o<a?-1:+(a<o)},s.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},s.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,a,s,l,c,u,h,d=this.length-t;if((void 0===r||r>d)&&(r=d),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var f=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;n>o/2&&(n=o/2);for(var a=0;a<n;++a){var s,l=parseInt(t.substr(2*a,2),16);if((s=l)!=s)break;e[r+a]=l}return a}(this,e,t,r);case"utf8":case"utf-8":return i=t,o=r,S(_(e,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return a=t,s=r,S(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(e),this,a,s);case"base64":return l=t,c=r,S(R(e),this,l,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return u=t,h=r,S(function(e,t){for(var r,n,i=[],o=0;o<e.length&&!((t-=2)<0);++o)n=(r=e.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(e,this.length-u),this,u,h);default:if(f)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),f=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},s.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUintLE=s.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},s.prototype.readUintBE=s.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},s.prototype.readUint8=s.prototype.readUInt8=function(e,t){return e>>>=0,t||w(e,1,this.length),this[e]},s.prototype.readUint16LE=s.prototype.readUInt16LE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUint16BE=s.prototype.readUInt16BE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUint32LE=s.prototype.readUInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},s.prototype.readUint32BE=s.prototype.readUInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},s.prototype.readInt8=function(e,t){return(e>>>=0,t||w(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},s.prototype.readInt16LE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt16BE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!1,52,8)},s.prototype.writeUintLE=s.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;k(this,e,t,r,i,0)}var o=1,a=0;for(this[t]=255&e;++a<r&&(o*=256);)this[t+a]=e/o&255;return t+r},s.prototype.writeUintBE=s.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;k(this,e,t,r,i,0)}var o=r-1,a=1;for(this[t+o]=255&e;--o>=0&&(a*=256);)this[t+o]=e/a&255;return t+r},s.prototype.writeUint8=s.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,1,255,0),this[t]=255&e,t+1},s.prototype.writeUint16LE=s.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeUint16BE=s.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeUint32LE=s.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},s.prototype.writeUint32BE=s.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);k(this,e,t,r,i-1,-i)}var o=0,a=1,s=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a|0)-s&255;return t+r},s.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);k(this,e,t,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a|0)-s&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},s.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeFloatLE=function(e,t,r){return E(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return E(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return x(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return x(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,n){if(!s.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),i},s.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,o=e.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(e=o)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var a=s.isBuffer(e)?e:s.from(e,n),l=a.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=a[i%l]}return this};var A=/[^+/0-9A-Za-z-_]/g;function _(e,t){t=t||1/0;for(var r,n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319||a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function R(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(A,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function S(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function T(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var O=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},11740:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(95851).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},14467:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(95851).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},17988:e=>{"use strict";var t={}.hasOwnProperty,r=function(e,r){if(!e)return r;var n={};for(var i in r)n[i]=t.call(e,i)?e[i]:r[i];return n},n=/[ -,\.\/:-@\[-\^`\{-~]/,i=/[ -,\.\/:-@\[\]\^`\{-~]/,o=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,a=function e(t,a){"single"!=(a=r(a,e.options)).quotes&&"double"!=a.quotes&&(a.quotes="single");for(var s="double"==a.quotes?'"':"'",l=a.isIdentifier,c=t.charAt(0),u="",h=0,d=t.length;h<d;){var f=t.charAt(h++),p=f.charCodeAt(),m=void 0;if(p<32||p>126){if(p>=55296&&p<=56319&&h<d){var g=t.charCodeAt(h++);(64512&g)==56320?p=((1023&p)<<10)+(1023&g)+65536:h--}m="\\"+p.toString(16).toUpperCase()+" "}else m=a.escapeEverything?n.test(f)?"\\"+f:"\\"+p.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B]/.test(f)?"\\"+p.toString(16).toUpperCase()+" ":"\\"==f||!l&&('"'==f&&s==f||"'"==f&&s==f)||l&&i.test(f)?"\\"+f:f;u+=m}return(l&&(/^-[-\d]/.test(u)?u="\\-"+u.slice(1):/\d/.test(c)&&(u="\\3"+c+" "+u.slice(1))),u=u.replace(o,function(e,t,r){return t&&t.length%2?e:(t||"")+r}),!l&&a.wrap)?s+u+s:u};a.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},a.version="3.0.0",e.exports=a},20102:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,o=l(e),a=o[0],s=o[1],c=new i((a+s)*3/4-s),u=0,h=s>0?a-4:a;for(r=0;r<h;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[u++]=t>>16&255,c[u++]=t>>8&255,c[u++]=255&t;return 2===s&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[u++]=255&t),1===s&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[u++]=t>>8&255,c[u++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,o=[],a=0,s=n-i;a<s;a+=16383)o.push(function(e,t,n){for(var i,o=[],a=t;a<n;a+=3)i=(e[a]<<16&0xff0000)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(e,a,a+16383>s?s:a+16383));return 1===i?o.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&o.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,s=o.length;a<s;++a)r[a]=o[a],n[o.charCodeAt(a)]=a;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},25159:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},25451:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},27941:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(95851).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},28975:(e,t,r)=>{"use strict";var n=r(50628),i=r(71885),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=i.useSyncExternalStore,s=n.useRef,l=n.useEffect,c=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var h=s(null);if(null===h.current){var d={hasValue:!1,value:null};h.current=d}else d=h.current;var f=a(e,(h=c(function(){function e(e){if(!l){if(l=!0,a=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return s=t}return s=e}if(t=s,o(a,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(a=e,t):(a=e,s=r)}var a,s,l=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],h[1]);return l(function(){d.hasValue=!0,d.value=f},[f]),u(f),f}},33089:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(95851).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},34027:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(95851).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},38305:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},39255:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(95851).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},42078:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},44e3:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},45799:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("life-buoy",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.93 4.93 4.24 4.24",key:"1ymg45"}],["path",{d:"m14.83 9.17 4.24-4.24",key:"1cb5xl"}],["path",{d:"m14.83 14.83 4.24 4.24",key:"q42g0n"}],["path",{d:"m9.17 14.83-4.24 4.24",key:"bqpfvv"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]])},47111:(e,t,r)=>{"use strict";let n;r.d(t,{WL:()=>im,eb:()=>nF,Lq:()=>oF,PS:()=>oz});var i,o,a,s,l,c,u,h,d,f={};r.r(f),r.d(f,{hasBrowserEnv:()=>eK,hasStandardBrowserEnv:()=>eJ,hasStandardBrowserWebWorkerEnv:()=>eX,navigator:()=>eG,origin:()=>eY});var p=r(50628);let m={en:{translations:{archiveNotification:"Archive this notification",archiveRead:"Archive Read",markAllAsRead:"Mark all as read",notifications:"Notifications",emptyFeedTitle:"No notifications yet",emptyFeedBody:"We'll let you know when we've got something new for you.",all:"All",unread:"Unread",read:"Read",unseen:"Unseen",msTeamsChannelSetError:"Error setting channel.",msTeamsConnect:"Connect to Microsoft Teams",msTeamsConnected:"Connected",msTeamsConnecting:"Connecting to Microsoft Teams…",msTeamsConnectionErrorExists:"Try reconnecting to Microsoft Teams to find and select channels from your teams.",msTeamsConnectionErrorOccurred:"There was an error connecting to Microsoft Teams. Try reconnecting to find and select channels from your teams.",msTeamsConnectContainerDescription:"Connect to get notifications in Microsoft Teams",msTeamsDisconnect:"Disconnect",msTeamsDisconnecting:"Disconnecting from Microsoft Teams…",msTeamsError:"Error",msTeamsReconnect:"Reconnect",msTeamsTenantIdNotSet:"Microsoft Teams tenant ID not set.",slackConnectChannel:"Connect channel",slackChannelId:"Slack channel ID",slackConnecting:"Connecting to Slack...",slackDisconnecting:"Disconnecting...",slackConnect:"Connect to Slack",slackConnected:"Connected",slackConnectContainerDescription:"Connect to get notifications in your Slack workspace.",slackSearchbarDisconnected:"Slack is not connected.",slackSearchbarNoChannelsConnected:"Search channels",slackSearchbarNoChannelsFound:"No slack channels.",slackSearchbarChannelsError:"Error fetching channels.",slackSearchChannels:"Search channels",slackConnectionErrorExists:"Try reconnecting to Slack to find and select channels from your workspace.",slackConnectionErrorOccurred:"There was an error connecting to Slack. Try reconnecting to find and select channels from your workspace.",slackChannelAlreadyConnected:"Error: already connected",slackError:"Error",slackDisconnect:"Disconnect",slackChannelSetError:"Error setting channel.",slackAccessTokenNotSet:"Access token not set.",slackReconnect:"Reconnect"},locale:"en"}},g=p.createContext(m.en),v=({i18n:e=m.en,...t})=>p.createElement(g.Provider,{...t,value:e});var y=r(79606);let b=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,t)=>((t&=63)<36?e+=t.toString(36):t<62?e+=(t-26).toString(36).toUpperCase():t>62?e+="-":e+="_",e),""),w=/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;var k=(e=>(e.loading="loading",e.fetchMore="fetchMore",e.ready="ready",e.error="error",e))(k||{});function C(e){return["loading","fetchMore"].includes(e)}class E{constructor(e,t){this.listeners=new Set,this._batching=!1,this._flushing=0,this.subscribe=e=>{var t,r;this.listeners.add(e);let n=null==(r=null==(t=this.options)?void 0:t.onSubscribe)?void 0:r.call(t,e,this);return()=>{this.listeners.delete(e),null==n||n()}},this.setState=e=>{var t,r,n;let i=this.state;this.state=(null==(t=this.options)?void 0:t.updateFn)?this.options.updateFn(i)(e):e(i),null==(n=null==(r=this.options)?void 0:r.onUpdate)||n.call(r),this._flush()},this._flush=()=>{if(this._batching)return;let e=++this._flushing;this.listeners.forEach(t=>{this._flushing===e&&t()})},this.batch=e=>{if(this._batching)return e();this._batching=!0,e(),this._batching=!1,this._flush()},this.state=e,this.options=t}}var x=Object.defineProperty,A=(e,t,r)=>t in e?x(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,_=(e,t,r)=>A(e,"symbol"!=typeof t?t+"":t,r);let R={NewMessage:"new-message"},S=[R.NewMessage];class T{constructor(e){_(this,"channels"),_(this,"params"),_(this,"inbox"),this.socket=e,this.channels={},this.params={},this.inbox=new E({})}join(e){let t=e.socketChannelTopic,r=e.referenceId,n=e.defaultOptions;this.socket.isConnected()||this.socket.connect(),this.params[t]||(this.params[t]={});let i=this.params[t][r],o=!i||JSON.stringify(i)!==JSON.stringify(n);if(o&&(this.params[t]={...this.params[t],[r]:n}),!this.channels[t]||o){let e=this.socket.channel(t,this.params[t]);for(let t of S)e.on(t,e=>this.setInbox(e));this.channels[t]=e}let a=this.channels[t];return["closed","errored"].includes(a.state)&&a.join(),this.inbox.subscribe(()=>{let t=this.inbox.state[r];t&&e.handleSocketEvent(t)})}leave(e){var t;null==(t=e.unsubscribeFromSocketEvents)||t.call(e);let r=e.socketChannelTopic,n=e.referenceId,i={...this.params},o=i[r]||{};o[n]&&delete o[n];let a={...this.channels},s=a[r];if(s&&0===Object.keys(o).length){for(let e of S)s.off(e);s.leave(),delete a[r]}this.params=i,this.channels=a}setInbox(e){let{attn:t,...r}=e;this.inbox.setState(()=>t.reduce((e,t)=>({...e,[t]:r}),{}))}}let O=e=>{let t,r=new Set,n=(e,n)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=n?n:"object"!=typeof i||null===i)?i:Object.assign({},t,i),r.forEach(r=>r(t,e))}},i=()=>t,o={setState:n,getState:i,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),r.clear()}},a=t=e(n,i,o);return o},L=e=>e?O(e):O;function j(e){let{inserted_at_date_range:t,...r}=e;if(!t)return r;let n={},i=t.inclusive??!1;return t.start&&(n[i?"inserted_at.gte":"inserted_at.gt"]=t.start),t.end&&(n[i?"inserted_at.lte":"inserted_at.lt"]=t.end),{...r,...n}}let I={shouldSetPage:!0,shouldAppend:!1},N={items:[],metadata:{total_count:0,unread_count:0,unseen_count:0},pageInfo:{before:null,after:null,page_size:50}};var P=Object.defineProperty,M=(e,t,r)=>t in e?P(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,B=(e,t,r)=>M(e,"symbol"!=typeof t?t+"":t,r);let D={archived:"exclude"};class F{constructor(e,t,r,n){B(this,"defaultOptions"),B(this,"referenceId"),B(this,"unsubscribeFromSocketEvents"),B(this,"socketManager"),B(this,"userFeedId"),B(this,"broadcaster"),B(this,"broadcastChannel"),B(this,"disconnectTimer",null),B(this,"hasSubscribedToRealTimeUpdates",!1),B(this,"visibilityChangeHandler",()=>{}),B(this,"visibilityChangeListenerConnected",!1),B(this,"store"),this.knock=e,this.feedId=t,t&&w.test(t)||this.knock.log("[Feed] Invalid or missing feedId provided to the Feed constructor. The feed should be a UUID of an in-app feed channel (`in_app_feed`) found in the Knock dashboard. Please provide a valid feedId to the Feed constructor.",!0),this.feedId=t,this.userFeedId=this.buildUserFeedId(),this.referenceId="client_"+b(),this.socketManager=n,this.store=L()(e=>({...N,networkStatus:k.ready,loading:!1,setNetworkStatus:t=>e(()=>({networkStatus:t,loading:t===k.loading})),setResult:({entries:t,meta:r,page_info:n},i=I)=>e(e=>({items:i.shouldAppend?(function(e){let t={};return e.reduce((e,r)=>t[r.id]?e:(t[r.id]=!0,[...e,r]),[])})(e.items.concat(t)).sort((e,t)=>new Date(t.inserted_at).getTime()-new Date(e.inserted_at).getTime()):t,metadata:r,pageInfo:i.shouldSetPage?n:e.pageInfo,loading:!1,networkStatus:k.ready})),setMetadata:t=>e(()=>({metadata:t})),resetStore:(t=N.metadata)=>e(()=>({...N,metadata:t})),setItemAttrs:(t,r)=>{let n=t.reduce((e,t)=>({...e,[t]:r}),{});return e(e=>({items:e.items.map(e=>n[e.id]?{...e,...n[e.id]}:e)}))}})),this.broadcaster=new y({wildcard:!0,delimiter:"."}),this.defaultOptions={...D,...j(r)},this.knock.log(`[Feed] Initialized a feed on channel ${t}`),this.initializeRealtimeConnection(),this.setupBroadcastChannel()}reinitialize(e){this.socketManager=e,this.userFeedId=this.buildUserFeedId(),this.initializeRealtimeConnection(),this.setupBroadcastChannel()}teardown(){var e;this.knock.log("[Feed] Tearing down feed instance"),null==(e=this.socketManager)||e.leave(this),this.tearDownVisibilityListeners(),this.disconnectTimer&&(clearTimeout(this.disconnectTimer),this.disconnectTimer=null),this.broadcastChannel&&this.broadcastChannel.close()}dispose(){this.knock.log("[Feed] Disposing of feed instance"),this.teardown(),this.broadcaster.removeAllListeners(),this.knock.feeds.removeInstance(this)}listenForUpdates(){var e;if(this.knock.log("[Feed] Connecting to real-time service"),this.hasSubscribedToRealTimeUpdates=!0,!this.knock.isAuthenticated())return void this.knock.log("[Feed] User is not authenticated, skipping listening for updates");this.unsubscribeFromSocketEvents=null==(e=this.socketManager)?void 0:e.join(this)}on(e,t){this.broadcaster.on(e,t)}off(e,t){this.broadcaster.off(e,t)}getState(){return this.store.getState()}async markAsSeen(e){let t=new Date().toISOString();return this.optimisticallyPerformStatusUpdate(e,"seen",{seen_at:t},"unseen_count"),this.makeStatusUpdate(e,"seen")}async markAllAsSeen(){let{metadata:e,items:t,...r}=this.store.getState();if("unseen"===this.defaultOptions.status)r.resetStore({...e,total_count:0,unseen_count:0});else{r.setMetadata({...e,unseen_count:0});let n={seen_at:new Date().toISOString()},i=t.map(e=>e.id);r.setItemAttrs(i,n)}let n=await this.makeBulkStatusUpdate("seen");return this.emitEvent("all_seen",t),n}async markAsUnseen(e){return this.optimisticallyPerformStatusUpdate(e,"unseen",{seen_at:null},"unseen_count"),this.makeStatusUpdate(e,"unseen")}async markAsRead(e){let t=new Date().toISOString();return this.optimisticallyPerformStatusUpdate(e,"read",{read_at:t},"unread_count"),this.makeStatusUpdate(e,"read")}async markAllAsRead(){let{metadata:e,items:t,...r}=this.store.getState();if("unread"===this.defaultOptions.status)r.resetStore({...e,total_count:0,unread_count:0});else{r.setMetadata({...e,unread_count:0});let n={read_at:new Date().toISOString()},i=t.map(e=>e.id);r.setItemAttrs(i,n)}let n=await this.makeBulkStatusUpdate("read");return this.emitEvent("all_read",t),n}async markAsUnread(e){return this.optimisticallyPerformStatusUpdate(e,"unread",{read_at:null},"unread_count"),this.makeStatusUpdate(e,"unread")}async markAsInteracted(e,t){let r=new Date().toISOString();return this.optimisticallyPerformStatusUpdate(e,"interacted",{read_at:r,interacted_at:r},"unread_count"),this.makeStatusUpdate(e,"interacted",t)}async markAsArchived(e){let t=this.store.getState(),r="exclude"===this.defaultOptions.archived,n=Array.isArray(e)?e:[e],i=n.map(e=>e.id);if(r){let e=n.filter(e=>!e.seen_at).length,r=n.filter(e=>!e.read_at).length,o={...t.metadata,total_count:Math.max(0,t.metadata.total_count-n.length),unseen_count:Math.max(0,t.metadata.unseen_count-e),unread_count:Math.max(0,t.metadata.unread_count-r)},a=t.items.filter(e=>!i.includes(e.id));t.setResult({entries:a,meta:o,page_info:t.pageInfo})}else t.setItemAttrs(i,{archived_at:new Date().toISOString()});return this.makeStatusUpdate(e,"archived")}async markAllAsArchived(){let{items:e,...t}=this.store.getState();if("exclude"===this.defaultOptions.archived)t.resetStore();else{let r=e.map(e=>e.id);t.setItemAttrs(r,{archived_at:new Date().toISOString()})}let r=await this.makeBulkStatusUpdate("archive");return this.emitEvent("all_archived",e),r}async markAllReadAsArchived(){let{items:e,...t}=this.store.getState(),r=e.filter(e=>null===e.read_at).map(e=>e.id);if(t.setItemAttrs(r,{archived_at:new Date().toISOString()}),"exclude"===this.defaultOptions.archived){let n=e.filter(e=>!r.includes(e.id)),i={...t.metadata,total_count:n.length,unread_count:0};t.setResult({entries:n,meta:i,page_info:t.pageInfo})}return await this.makeBulkStatusUpdate("archive")}async markAsUnarchived(e){return this.optimisticallyPerformStatusUpdate(e,"unarchived",{archived_at:null}),this.makeStatusUpdate(e,"unarchived")}async fetch(e={}){var t;let{networkStatus:r,...n}=this.store.getState();if(!this.knock.isAuthenticated())return void this.knock.log("[Feed] User is not authenticated, skipping fetch");if(C(r))return void this.knock.log("[Feed] Request is in flight, skipping fetch");n.setNetworkStatus(e.__loadingType??k.loading);let i="object"==typeof(t={...this.defaultOptions,...e}).trigger_data?JSON.stringify(t.trigger_data):"string"==typeof(null==t?void 0:t.trigger_data)?t.trigger_data:void 0,o={...this.defaultOptions,...j(e),trigger_data:i,__loadingType:void 0,__fetchSource:void 0,__experimentalCrossBrowserUpdates:void 0,auto_manage_socket_connection:void 0,auto_manage_socket_connection_delay:void 0},a=await this.knock.client().makeRequest({method:"GET",url:`/v1/users/${this.knock.userId}/feeds/${this.feedId}`,params:o});if("error"===a.statusCode||!a.body)return n.setNetworkStatus(k.error),{status:a.statusCode,data:a.error||a.body};let s={entries:a.body.entries,meta:a.body.meta,page_info:a.body.page_info};e.before?n.setResult(s,{shouldSetPage:!1,shouldAppend:!0}):e.after?n.setResult(s,{shouldSetPage:!0,shouldAppend:!0}):n.setResult(s),this.broadcast("messages.new",s);let l="socket"===e.__fetchSource?"items.received.realtime":"items.received.page",c={items:s.entries,metadata:s.meta,event:l};return this.broadcast(c.event,c),{data:s,status:a.statusCode}}async fetchNextPage(e={}){let{pageInfo:t}=this.store.getState();t.after&&this.fetch({...e,after:t.after,__loadingType:k.fetchMore})}get socketChannelTopic(){return`feeds:${this.userFeedId}`}broadcast(e,t){this.broadcaster.emit(e,t)}async onNewMessageReceived({data:e}){var t;this.knock.log("[Feed] Received new real-time message");let{items:r,...n}=this.store.getState(),i=r[0],o=null==(t=e[this.referenceId])?void 0:t.metadata;o&&n.setMetadata(o),this.fetch({before:null==i?void 0:i.__cursor,__fetchSource:"socket"})}buildUserFeedId(){return`${this.feedId}:${this.knock.userId}`}optimisticallyPerformStatusUpdate(e,t,r,n){let i=this.store.getState(),o=Array.isArray(e)?e:[e],a=o.map(e=>e.id);if(n){let{metadata:e}=i,r=o.filter(e=>{switch(t){case"seen":return null===e.seen_at;case"unseen":return null!==e.seen_at;case"read":case"interacted":return null===e.read_at;case"unread":return null!==e.read_at;default:return!0}}),a=t.startsWith("un")?r.length:-r.length;i.setMetadata({...e,[n]:Math.max(0,e[n]+a)})}i.setItemAttrs(a,r)}async makeStatusUpdate(e,t,r){let n=Array.isArray(e)?e:[e],i=n.map(e=>e.id),o=await this.knock.messages.batchUpdateStatuses(i,t,{metadata:r});return this.emitEvent(t,n),o}async makeBulkStatusUpdate(e){let t={user_ids:[this.knock.userId],engagement_status:"all"!==this.defaultOptions.status?this.defaultOptions.status:void 0,archived:this.defaultOptions.archived,has_tenant:this.defaultOptions.has_tenant,tenants:this.defaultOptions.tenant?[this.defaultOptions.tenant]:void 0};return await this.knock.messages.bulkUpdateAllStatusesInChannel({channelId:this.feedId,status:e,options:t})}setupBroadcastChannel(){this.broadcastChannel="u">typeof self&&"BroadcastChannel"in self?new BroadcastChannel(`knock:feed:${this.userFeedId}`):null,this.broadcastChannel&&!0===this.defaultOptions.__experimentalCrossBrowserUpdates&&(this.broadcastChannel.onmessage=e=>{switch(e.data.type){case"items:archived":case"items:unarchived":case"items:seen":case"items:unseen":case"items:read":case"items:unread":case"items:all_read":case"items:all_seen":case"items:all_archived":return this.fetch();default:return null}})}broadcastOverChannel(e,t){if(this.broadcastChannel)try{let r=JSON.parse(JSON.stringify(t));this.broadcastChannel.postMessage({type:e,payload:r})}catch(t){console.warn(`Could not broadcast ${e}, got error: ${t}`)}}initializeRealtimeConnection(){var e;this.socketManager&&(this.defaultOptions.auto_manage_socket_connection&&this.setUpVisibilityListeners(),this.hasSubscribedToRealTimeUpdates&&this.knock.isAuthenticated()&&(this.unsubscribeFromSocketEvents=null==(e=this.socketManager)?void 0:e.join(this)))}async handleSocketEvent(e){if(e.event!==R.NewMessage)return void e.event;this.onNewMessageReceived(e)}setUpVisibilityListeners(){typeof document>"u"||this.visibilityChangeListenerConnected||(this.visibilityChangeHandler=this.handleVisibilityChange.bind(this),this.visibilityChangeListenerConnected=!0,document.addEventListener("visibilitychange",this.visibilityChangeHandler))}tearDownVisibilityListeners(){typeof document>"u"||(document.removeEventListener("visibilitychange",this.visibilityChangeHandler),this.visibilityChangeListenerConnected=!1)}emitEvent(e,t){this.broadcaster.emit(`items.${e}`,{items:t}),this.broadcaster.emit(`items:${e}`,{items:t}),this.broadcastOverChannel(`items:${e}`,{items:t})}handleVisibilityChange(){var e,t;let r=this.defaultOptions.auto_manage_socket_connection_delay??2e3,n=this.knock.client();"hidden"===document.visibilityState?this.disconnectTimer=setTimeout(()=>{var e;null==(e=n.socket)||e.disconnect(),this.disconnectTimer=null},r):"visible"===document.visibilityState&&(this.disconnectTimer&&(clearTimeout(this.disconnectTimer),this.disconnectTimer=null),null!=(e=n.socket)&&e.isConnected()||null==(t=n.socket)||t.connect())}}var U=Object.defineProperty,z=(e,t,r)=>t in e?U(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,$=(e,t,r)=>z(e,"symbol"!=typeof t?t+"":t,r);class V{constructor(e){$(this,"instance"),$(this,"feedInstances",[]),$(this,"socketManager"),this.instance=e}initialize(e,t={}){this.initSocketManager();let r=new F(this.instance,e,t,this.socketManager);return this.feedInstances.push(r),r}removeInstance(e){this.feedInstances=this.feedInstances.filter(t=>t!==e)}teardownInstances(){for(let e of this.feedInstances)e.teardown()}reinitializeInstances(){var e;for(let t of this.feedInstances)null==(e=this.socketManager)||e.leave(t);for(let e of(this.socketManager=void 0,this.initSocketManager(),this.feedInstances))e.reinitialize(this.socketManager)}initSocketManager(){let e=this.instance.client().socket;e&&!this.socketManager&&(this.socketManager=new T(e))}}class q extends Error{}function W(e,t){return function(){return e.apply(t,arguments)}}q.prototype.name="InvalidTokenError";var H=r(37811);let{toString:K}=Object.prototype,{getPrototypeOf:G}=Object,J=(e=>t=>{let r=K.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),X=e=>(e=e.toLowerCase(),t=>J(t)===e),Y=e=>t=>typeof t===e,{isArray:Z}=Array,Q=Y("undefined"),ee=X("ArrayBuffer"),et=Y("string"),er=Y("function"),en=Y("number"),ei=e=>null!==e&&"object"==typeof e,eo=e=>{if("object"!==J(e))return!1;let t=G(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},ea=X("Date"),es=X("File"),el=X("Blob"),ec=X("FileList"),eu=X("URLSearchParams"),[eh,ed,ef,ep]=["ReadableStream","Request","Response","Headers"].map(X);function em(e,t,{allOwnKeys:r=!1}={}){let n,i;if(null!=e)if("object"!=typeof e&&(e=[e]),Z(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{let i,o=r?Object.getOwnPropertyNames(e):Object.keys(e),a=o.length;for(n=0;n<a;n++)i=o[n],t.call(null,e[i],i,e)}}function eg(e,t){let r;t=t.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(t===(r=n[i]).toLowerCase())return r;return null}let ev="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,ey=e=>!Q(e)&&e!==ev,eb=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&G(Uint8Array)),ew=X("HTMLFormElement"),ek=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),eC=X("RegExp"),eE=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),n={};em(r,(r,i)=>{let o;!1!==(o=t(r,i,e))&&(n[i]=o||r)}),Object.defineProperties(e,n)},ex=X("AsyncFunction"),eA=(i="function"==typeof setImmediate,o=er(ev.postMessage),i?setImmediate:o?((e,t)=>(ev.addEventListener("message",({source:r,data:n})=>{r===ev&&n===e&&t.length&&t.shift()()},!1),r=>{t.push(r),ev.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e)),e_="undefined"!=typeof queueMicrotask?queueMicrotask.bind(ev):void 0!==H&&H.nextTick||eA,eR={isArray:Z,isArrayBuffer:ee,isBuffer:function(e){return null!==e&&!Q(e)&&null!==e.constructor&&!Q(e.constructor)&&er(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||er(e.append)&&("formdata"===(t=J(e))||"object"===t&&er(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&ee(e.buffer)},isString:et,isNumber:en,isBoolean:e=>!0===e||!1===e,isObject:ei,isPlainObject:eo,isReadableStream:eh,isRequest:ed,isResponse:ef,isHeaders:ep,isUndefined:Q,isDate:ea,isFile:es,isBlob:el,isRegExp:eC,isFunction:er,isStream:e=>ei(e)&&er(e.pipe),isURLSearchParams:eu,isTypedArray:eb,isFileList:ec,forEach:em,merge:function e(){let{caseless:t}=ey(this)&&this||{},r={},n=(n,i)=>{let o=t&&eg(r,i)||i;eo(r[o])&&eo(n)?r[o]=e(r[o],n):eo(n)?r[o]=e({},n):Z(n)?r[o]=n.slice():r[o]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&em(arguments[e],n);return r},extend:(e,t,r,{allOwnKeys:n}={})=>(em(t,(t,n)=>{r&&er(t)?e[n]=W(t,r):e[n]=t},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,r,n)=>{let i,o,a,s={};if(t=t||{},null==e)return t;do{for(o=(i=Object.getOwnPropertyNames(e)).length;o-- >0;)a=i[o],(!n||n(a,e,t))&&!s[a]&&(t[a]=e[a],s[a]=!0);e=!1!==r&&G(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:J,kindOfTest:X,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;let n=e.indexOf(t,r);return -1!==n&&n===r},toArray:e=>{if(!e)return null;if(Z(e))return e;let t=e.length;if(!en(t))return null;let r=Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{let r,n=(e&&e[Symbol.iterator]).call(e);for(;(r=n.next())&&!r.done;){let n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let r,n=[];for(;null!==(r=e.exec(t));)n.push(r);return n},isHTMLForm:ew,hasOwnProperty:ek,hasOwnProp:ek,reduceDescriptors:eE,freezeMethods:e=>{eE(e,(t,r)=>{if(er(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(er(e[r])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(e,t)=>{let r={};return(Z(e)?e:String(e).split(t)).forEach(e=>{r[e]=!0}),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,r){return t.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e*=1)?e:t,findKey:eg,global:ev,isContextDefined:ey,isSpecCompliantForm:function(e){return!!(e&&er(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let t=Array(10),r=(e,n)=>{if(ei(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;let i=Z(e)?[]:{};return em(e,(e,t)=>{let o=r(e,n+1);Q(o)||(i[t]=o)}),t[n]=void 0,i}}return e};return r(e,0)},isAsyncFn:ex,isThenable:e=>e&&(ei(e)||er(e))&&er(e.then)&&er(e.catch),setImmediate:eA,asap:e_};function eS(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}eR.inherits(eS,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:eR.toJSONObject(this.config),code:this.code,status:this.status}}});let eT=eS.prototype,eO={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{eO[e]={value:e}}),Object.defineProperties(eS,eO),Object.defineProperty(eT,"isAxiosError",{value:!0}),eS.from=(e,t,r,n,i,o)=>{let a=Object.create(eT);return eR.toFlatObject(e,a,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),eS.call(a,e.message,t,r,n,i),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};var eL=r(10944).hp;function ej(e){return eR.isPlainObject(e)||eR.isArray(e)}function eI(e){return eR.endsWith(e,"[]")?e.slice(0,-2):e}function eN(e,t,r){return e?e.concat(t).map(function(e,t){return e=eI(e),!r&&t?"["+e+"]":e}).join(r?".":""):t}let eP=eR.toFlatObject(eR,{},null,function(e){return/^is[A-Z]/.test(e)}),eM=function(e,t,r){if(!eR.isObject(e))throw TypeError("target must be an object");t=t||new FormData;let n=(r=eR.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!eR.isUndefined(t[e])})).metaTokens,i=r.visitor||c,o=r.dots,a=r.indexes,s=(r.Blob||"undefined"!=typeof Blob&&Blob)&&eR.isSpecCompliantForm(t);if(!eR.isFunction(i))throw TypeError("visitor must be a function");function l(e){if(null===e)return"";if(eR.isDate(e))return e.toISOString();if(!s&&eR.isBlob(e))throw new eS("Blob is not supported. Use a Buffer instead.");return eR.isArrayBuffer(e)||eR.isTypedArray(e)?s&&"function"==typeof Blob?new Blob([e]):eL.from(e):e}function c(e,r,i){let s=e;if(e&&!i&&"object"==typeof e)if(eR.endsWith(r,"{}"))r=n?r:r.slice(0,-2),e=JSON.stringify(e);else{var c;if(eR.isArray(e)&&(c=e,eR.isArray(c)&&!c.some(ej))||(eR.isFileList(e)||eR.endsWith(r,"[]"))&&(s=eR.toArray(e)))return r=eI(r),s.forEach(function(e,n){eR.isUndefined(e)||null===e||t.append(!0===a?eN([r],n,o):null===a?r:r+"[]",l(e))}),!1}return!!ej(e)||(t.append(eN(i,r,o),l(e)),!1)}let u=[],h=Object.assign(eP,{defaultVisitor:c,convertValue:l,isVisitable:ej});if(!eR.isObject(e))throw TypeError("data must be an object");return!function e(r,n){if(!eR.isUndefined(r)){if(-1!==u.indexOf(r))throw Error("Circular reference detected in "+n.join("."));u.push(r),eR.forEach(r,function(r,o){!0===(!(eR.isUndefined(r)||null===r)&&i.call(t,r,eR.isString(o)?o.trim():o,n,h))&&e(r,n?n.concat(o):[o])}),u.pop()}}(e),t};function eB(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function eD(e,t){this._pairs=[],e&&eM(e,this,t)}let eF=eD.prototype;function eU(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ez(e,t,r){let n;if(!t)return e;let i=r&&r.encode||eU;eR.isFunction(r)&&(r={serialize:r});let o=r&&r.serialize;if(n=o?o(t,r):eR.isURLSearchParams(t)?t.toString():new eD(t,r).toString(i)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}eF.append=function(e,t){this._pairs.push([e,t])},eF.toString=function(e){let t=e?function(t){return e.call(this,t,eB)}:eB;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class e${constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){eR.forEach(this.handlers,function(t){null!==t&&e(t)})}}let eV={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},eq="undefined"!=typeof URLSearchParams?URLSearchParams:eD,eW="undefined"!=typeof FormData?FormData:null,eH="undefined"!=typeof Blob?Blob:null,eK="undefined"!=typeof window&&"undefined"!=typeof document,eG="object"==typeof navigator&&navigator||void 0,eJ=eK&&(!eG||0>["ReactNative","NativeScript","NS"].indexOf(eG.product)),eX="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,eY=eK&&window.location.href||"http://localhost",eZ={...f,isBrowser:!0,classes:{URLSearchParams:eq,FormData:eW,Blob:eH},protocols:["http","https","file","blob","url","data"]},eQ=function(e){if(eR.isFormData(e)&&eR.isFunction(e.entries)){let t={};return eR.forEachEntry(e,(e,r)=>{!function e(t,r,n,i){let o=t[i++];if("__proto__"===o)return!0;let a=Number.isFinite(+o),s=i>=t.length;return(o=!o&&eR.isArray(n)?n.length:o,s)?eR.hasOwnProp(n,o)?n[o]=[n[o],r]:n[o]=r:(n[o]&&eR.isObject(n[o])||(n[o]=[]),e(t,r,n[o],i)&&eR.isArray(n[o])&&(n[o]=function(e){let t,r,n={},i=Object.keys(e),o=i.length;for(t=0;t<o;t++)n[r=i[t]]=e[r];return n}(n[o]))),!a}(eR.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),r,t,0)}),t}return null},e0={transitional:eV,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let r,n=t.getContentType()||"",i=n.indexOf("application/json")>-1,o=eR.isObject(e);if(o&&eR.isHTMLForm(e)&&(e=new FormData(e)),eR.isFormData(e))return i?JSON.stringify(eQ(e)):e;if(eR.isArrayBuffer(e)||eR.isBuffer(e)||eR.isStream(e)||eR.isFile(e)||eR.isBlob(e)||eR.isReadableStream(e))return e;if(eR.isArrayBufferView(e))return e.buffer;if(eR.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1){var a,s;return(a=e,s=this.formSerializer,eM(a,new eZ.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,n){return eZ.isNode&&eR.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},s))).toString()}if((r=eR.isFileList(e))||n.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return eM(r?{"files[]":e}:e,t&&new t,this.formSerializer)}}if(o||i){t.setContentType("application/json",!1);var l=e;if(eR.isString(l))try{return(0,JSON.parse)(l),eR.trim(l)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(l)}return e}],transformResponse:[function(e){let t=this.transitional||e0.transitional,r=t&&t.forcedJSONParsing,n="json"===this.responseType;if(eR.isResponse(e)||eR.isReadableStream(e))return e;if(e&&eR.isString(e)&&(r&&!this.responseType||n)){let r=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!r&&n){if("SyntaxError"===e.name)throw eS.from(e,eS.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eZ.classes.FormData,Blob:eZ.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};eR.forEach(["delete","get","head","post","put","patch"],e=>{e0.headers[e]={}});let e1=eR.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),e2=e=>{let t,r,n,i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),t=e.substring(0,n).trim().toLowerCase(),r=e.substring(n+1).trim(),!t||i[t]&&e1[t]||("set-cookie"===t?i[t]?i[t].push(r):i[t]=[r]:i[t]=i[t]?i[t]+", "+r:r)}),i},e5=Symbol("internals");function e4(e){return e&&String(e).trim().toLowerCase()}function e3(e){return!1===e||null==e?e:eR.isArray(e)?e.map(e3):String(e)}let e9=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function e6(e,t,r,n,i){if(eR.isFunction(n))return n.call(this,t,r);if(i&&(t=r),eR.isString(t)){if(eR.isString(n))return -1!==t.indexOf(n);if(eR.isRegExp(n))return n.test(t)}}class e8{constructor(e){e&&this.set(e)}set(e,t,r){let n=this;function i(e,t,r){let i=e4(t);if(!i)throw Error("header name must be a non-empty string");let o=eR.findKey(n,i);o&&void 0!==n[o]&&!0!==r&&(void 0!==r||!1===n[o])||(n[o||t]=e3(e))}let o=(e,t)=>eR.forEach(e,(e,r)=>i(e,r,t));if(eR.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(eR.isString(e)&&(e=e.trim())&&!e9(e))o(e2(e),t);else if(eR.isHeaders(e))for(let[t,n]of e.entries())i(n,t,r);else null!=e&&i(t,e,r);return this}get(e,t){if(e=e4(e)){let r=eR.findKey(this,e);if(r){let e=this[r];if(!t)return e;if(!0===t){let t,r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=n.exec(e);)r[t[1]]=t[2];return r}if(eR.isFunction(t))return t.call(this,e,r);if(eR.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=e4(e)){let r=eR.findKey(this,e);return!!(r&&void 0!==this[r]&&(!t||e6(this,this[r],r,t)))}return!1}delete(e,t){let r=this,n=!1;function i(e){if(e=e4(e)){let i=eR.findKey(r,e);i&&(!t||e6(r,r[i],i,t))&&(delete r[i],n=!0)}}return eR.isArray(e)?e.forEach(i):i(e),n}clear(e){let t=Object.keys(this),r=t.length,n=!1;for(;r--;){let i=t[r];(!e||e6(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let t=this,r={};return eR.forEach(this,(n,i)=>{let o=eR.findKey(r,i);if(o){t[o]=e3(n),delete t[i];return}let a=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r):String(i).trim();a!==i&&delete t[i],t[a]=e3(n),r[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return eR.forEach(this,(r,n)=>{null!=r&&!1!==r&&(t[n]=e&&eR.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let r=new this(e);return t.forEach(e=>r.set(e)),r}static accessor(e){let t=(this[e5]=this[e5]={accessors:{}}).accessors,r=this.prototype;function n(e){let n=e4(e);if(!t[n]){let i=eR.toCamelCase(" "+e);["get","set","has"].forEach(t=>{Object.defineProperty(r,t+i,{value:function(r,n,i){return this[t].call(this,e,r,n,i)},configurable:!0})}),t[n]=!0}}return eR.isArray(e)?e.forEach(n):n(e),this}}function e7(e,t){let r=this||e0,n=t||r,i=e8.from(n.headers),o=n.data;return eR.forEach(e,function(e){o=e.call(r,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function te(e){return!!(e&&e.__CANCEL__)}function tt(e,t,r){eS.call(this,null==e?"canceled":e,eS.ERR_CANCELED,t,r),this.name="CanceledError"}function tr(e,t,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new eS("Request failed with status code "+r.status,[eS.ERR_BAD_REQUEST,eS.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}e8.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),eR.reduceDescriptors(e8.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}}),eR.freezeMethods(e8),eR.inherits(tt,eS,{__CANCEL__:!0});let tn=function(e,t){let r,n=Array(e=e||10),i=Array(e),o=0,a=0;return t=void 0!==t?t:1e3,function(s){let l=Date.now(),c=i[a];r||(r=l),n[o]=s,i[o]=l;let u=a,h=0;for(;u!==o;)h+=n[u++],u%=e;if((o=(o+1)%e)===a&&(a=(a+1)%e),l-r<t)return;let d=c&&l-c;return d?Math.round(1e3*h/d):void 0}},ti=function(e,t){let r,n,i=0,o=1e3/t,a=(t,o=Date.now())=>{i=o,r=null,n&&(clearTimeout(n),n=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),s=t-i;s>=o?a(e,t):(r=e,n||(n=setTimeout(()=>{n=null,a(r)},o-s)))},()=>r&&a(r)]},to=(e,t,r=3)=>{let n=0,i=tn(50,250);return ti(r=>{let o=r.loaded,a=r.lengthComputable?r.total:void 0,s=o-n,l=i(s);n=o,e({loaded:o,total:a,progress:a?o/a:void 0,bytes:s,rate:l||void 0,estimated:l&&a&&o<=a?(a-o)/l:void 0,event:r,lengthComputable:null!=a,[t?"download":"upload"]:!0})},r)},ta=(e,t)=>{let r=null!=e;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},ts=e=>(...t)=>eR.asap(()=>e(...t)),tl=eZ.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,eZ.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(eZ.origin),eZ.navigator&&/(msie|trident)/i.test(eZ.navigator.userAgent)):()=>!0,tc=eZ.hasStandardBrowserEnv?{write(e,t,r,n,i,o){let a=[e+"="+encodeURIComponent(t)];eR.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),eR.isString(n)&&a.push("path="+n),eR.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function tu(e,t,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(n||!1==r)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}let th=e=>e instanceof e8?{...e}:e;function td(e,t){t=t||{};let r={};function n(e,t,r,n){return eR.isPlainObject(e)&&eR.isPlainObject(t)?eR.merge.call({caseless:n},e,t):eR.isPlainObject(t)?eR.merge({},t):eR.isArray(t)?t.slice():t}function i(e,t,r,i){return eR.isUndefined(t)?eR.isUndefined(e)?void 0:n(void 0,e,r,i):n(e,t,r,i)}function o(e,t){if(!eR.isUndefined(t))return n(void 0,t)}function a(e,t){return eR.isUndefined(t)?eR.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function s(r,i,o){return o in t?n(r,i):o in e?n(void 0,r):void 0}let l={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:s,headers:(e,t,r)=>i(th(e),th(t),r,!0)};return eR.forEach(Object.keys(Object.assign({},e,t)),function(n){let o=l[n]||i,a=o(e[n],t[n],n);eR.isUndefined(a)&&o!==s||(r[n]=a)}),r}let tf=e=>{let t,r=td({},e),{data:n,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:a,headers:s,auth:l}=r;if(r.headers=s=e8.from(s),r.url=ez(tu(r.baseURL,r.url,r.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),eR.isFormData(n)){if(eZ.hasStandardBrowserEnv||eZ.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(t=s.getContentType())){let[e,...r]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...r].join("; "))}}if(eZ.hasStandardBrowserEnv&&(i&&eR.isFunction(i)&&(i=i(r)),i||!1!==i&&tl(r.url))){let e=o&&a&&tc.read(a);e&&s.set(o,e)}return r},tp="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,r){let n,i,o,a,s,l=tf(e),c=l.data,u=e8.from(l.headers).normalize(),{responseType:h,onUploadProgress:d,onDownloadProgress:f}=l;function p(){a&&a(),s&&s(),l.cancelToken&&l.cancelToken.unsubscribe(n),l.signal&&l.signal.removeEventListener("abort",n)}let m=new XMLHttpRequest;function g(){if(!m)return;let n=e8.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());tr(function(e){t(e),p()},function(e){r(e),p()},{data:h&&"text"!==h&&"json"!==h?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:n,config:e,request:m}),m=null}m.open(l.method.toUpperCase(),l.url,!0),m.timeout=l.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(r(new eS("Request aborted",eS.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new eS("Network Error",eS.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=l.timeout?"timeout of "+l.timeout+"ms exceeded":"timeout exceeded",n=l.transitional||eV;l.timeoutErrorMessage&&(t=l.timeoutErrorMessage),r(new eS(t,n.clarifyTimeoutError?eS.ETIMEDOUT:eS.ECONNABORTED,e,m)),m=null},void 0===c&&u.setContentType(null),"setRequestHeader"in m&&eR.forEach(u.toJSON(),function(e,t){m.setRequestHeader(t,e)}),eR.isUndefined(l.withCredentials)||(m.withCredentials=!!l.withCredentials),h&&"json"!==h&&(m.responseType=l.responseType),f&&([o,s]=to(f,!0),m.addEventListener("progress",o)),d&&m.upload&&([i,a]=to(d),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",a)),(l.cancelToken||l.signal)&&(n=t=>{m&&(r(!t||t.type?new tt(null,e,m):t),m.abort(),m=null)},l.cancelToken&&l.cancelToken.subscribe(n),l.signal&&(l.signal.aborted?n():l.signal.addEventListener("abort",n)));let v=function(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(l.url);if(v&&-1===eZ.protocols.indexOf(v))return void r(new eS("Unsupported protocol "+v+":",eS.ERR_BAD_REQUEST,e));m.send(c||null)})},tm=(e,t)=>{let{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,n=new AbortController,i=function(e){if(!r){r=!0,a();let t=e instanceof Error?e:this.reason;n.abort(t instanceof eS?t:new tt(t instanceof Error?t.message:t))}},o=t&&setTimeout(()=>{o=null,i(new eS(`timeout ${t} of ms exceeded`,eS.ETIMEDOUT))},t),a=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:s}=n;return s.unsubscribe=()=>eR.asap(a),s}},tg=function*(e,t){let r,n=e.byteLength;if(!t||n<t)return void(yield e);let i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},tv=async function*(e,t){for await(let r of ty(e))yield*tg(r,t)},ty=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)break;yield r}}finally{await t.cancel()}},tb=(e,t,r,n)=>{let i,o=tv(e,t),a=0,s=e=>{!i&&(i=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:t,value:n}=await o.next();if(t){s(),e.close();return}let i=n.byteLength;if(r){let e=a+=i;r(e)}e.enqueue(new Uint8Array(n))}catch(e){throw s(e),e}},cancel:e=>(s(e),o.return())},{highWaterMark:2})},tw="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tk=tw&&"function"==typeof ReadableStream,tC=tw&&("function"==typeof TextEncoder?(n=new TextEncoder,e=>n.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),tE=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},tx=tk&&tE(()=>{let e=!1,t=new Request(eZ.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),tA=tk&&tE(()=>eR.isReadableStream(new Response("").body)),t_={stream:tA&&(e=>e.body)};tw&&(a=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{t_[e]||(t_[e]=eR.isFunction(a[e])?t=>t[e]():(t,r)=>{throw new eS(`Response type '${e}' is not supported`,eS.ERR_NOT_SUPPORT,r)})}));let tR=async e=>{if(null==e)return 0;if(eR.isBlob(e))return e.size;if(eR.isSpecCompliantForm(e)){let t=new Request(eZ.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return eR.isArrayBufferView(e)||eR.isArrayBuffer(e)?e.byteLength:(eR.isURLSearchParams(e)&&(e+=""),eR.isString(e))?(await tC(e)).byteLength:void 0},tS=async(e,t)=>{let r=eR.toFiniteNumber(e.getContentLength());return null==r?tR(t):r},tT={http:null,xhr:tp,fetch:tw&&(async e=>{let t,r,{url:n,method:i,data:o,signal:a,cancelToken:s,timeout:l,onDownloadProgress:c,onUploadProgress:u,responseType:h,headers:d,withCredentials:f="same-origin",fetchOptions:p}=tf(e);h=h?(h+"").toLowerCase():"text";let m=tm([a,s&&s.toAbortSignal()],l),g=m&&m.unsubscribe&&(()=>{m.unsubscribe()});try{if(u&&tx&&"get"!==i&&"head"!==i&&0!==(r=await tS(d,o))){let e,t=new Request(n,{method:"POST",body:o,duplex:"half"});if(eR.isFormData(o)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){let[e,n]=ta(r,to(ts(u)));o=tb(t.body,65536,e,n)}}eR.isString(f)||(f=f?"include":"omit");let a="credentials"in Request.prototype;t=new Request(n,{...p,signal:m,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:a?f:void 0});let s=await fetch(t),l=tA&&("stream"===h||"response"===h);if(tA&&(c||l&&g)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});let t=eR.toFiniteNumber(s.headers.get("content-length")),[r,n]=c&&ta(t,to(ts(c),!0))||[];s=new Response(tb(s.body,65536,r,()=>{n&&n(),g&&g()}),e)}h=h||"text";let v=await t_[eR.findKey(t_,h)||"text"](s,e);return!l&&g&&g(),await new Promise((r,n)=>{tr(r,n,{data:v,headers:e8.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:t})})}catch(r){if(g&&g(),r&&"TypeError"===r.name&&/fetch/i.test(r.message))throw Object.assign(new eS("Network Error",eS.ERR_NETWORK,e,t),{cause:r.cause||r});throw eS.from(r,r&&r.code,e,t)}})};eR.forEach(tT,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let tO=e=>`- ${e}`,tL=e=>eR.isFunction(e)||null===e||!1===e,tj={getAdapter:e=>{let t,r,{length:n}=e=eR.isArray(e)?e:[e],i={};for(let o=0;o<n;o++){let n;if(r=t=e[o],!tL(t)&&void 0===(r=tT[(n=String(t)).toLowerCase()]))throw new eS(`Unknown adapter '${n}'`);if(r)break;i[n||"#"+o]=r}if(!r){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new eS("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(tO).join("\n"):" "+tO(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r}};function tI(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new tt(null,e)}function tN(e){return tI(e),e.headers=e8.from(e.headers),e.data=e7.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),tj.getAdapter(e.adapter||e0.adapter)(e).then(function(t){return tI(e),t.data=e7.call(e,e.transformResponse,t),t.headers=e8.from(t.headers),t},function(t){return!te(t)&&(tI(e),t&&t.response&&(t.response.data=e7.call(e,e.transformResponse,t.response),t.response.headers=e8.from(t.response.headers))),Promise.reject(t)})}let tP="1.8.4",tM={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{tM[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});let tB={};tM.transitional=function(e,t,r){function n(e,t){return"[Axios v"+tP+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,i,o)=>{if(!1===e)throw new eS(n(i," has been removed"+(t?" in "+t:"")),eS.ERR_DEPRECATED);return t&&!tB[i]&&(tB[i]=!0,console.warn(n(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,i,o)}},tM.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};let tD={assertOptions:function(e,t,r){if("object"!=typeof e)throw new eS("options must be an object",eS.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let o=n[i],a=t[o];if(a){let t=e[o],r=void 0===t||a(t,o,e);if(!0!==r)throw new eS("option "+o+" must be "+r,eS.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new eS("Unknown option "+o,eS.ERR_BAD_OPTION)}},validators:tM},tF=tD.validators;class tU{constructor(e){this.defaults=e,this.interceptors={request:new e$,response:new e$}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let r=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?r&&!String(e.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+r):e.stack=r}catch(e){}}throw e}}_request(e,t){let r,n;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:i,paramsSerializer:o,headers:a}=t=td(this.defaults,t);void 0!==i&&tD.assertOptions(i,{silentJSONParsing:tF.transitional(tF.boolean),forcedJSONParsing:tF.transitional(tF.boolean),clarifyTimeoutError:tF.transitional(tF.boolean)},!1),null!=o&&(eR.isFunction(o)?t.paramsSerializer={serialize:o}:tD.assertOptions(o,{encode:tF.function,serialize:tF.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tD.assertOptions(t,{baseUrl:tF.spelling("baseURL"),withXsrfToken:tF.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=a&&eR.merge(a.common,a[t.method]);a&&eR.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=e8.concat(s,a);let l=[],c=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(c=c&&e.synchronous,l.unshift(e.fulfilled,e.rejected))});let u=[];this.interceptors.response.forEach(function(e){u.push(e.fulfilled,e.rejected)});let h=0;if(!c){let e=[tN.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,u),n=e.length,r=Promise.resolve(t);h<n;)r=r.then(e[h++],e[h++]);return r}n=l.length;let d=t;for(h=0;h<n;){let e=l[h++],t=l[h++];try{d=e(d)}catch(e){t.call(this,e);break}}try{r=tN.call(this,d)}catch(e){return Promise.reject(e)}for(h=0,n=u.length;h<n;)r=r.then(u[h++],u[h++]);return r}getUri(e){return ez(tu((e=td(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}eR.forEach(["delete","get","head","options"],function(e){tU.prototype[e]=function(t,r){return this.request(td(r||{},{method:e,url:t,data:(r||{}).data}))}}),eR.forEach(["post","put","patch"],function(e){function t(t){return function(r,n,i){return this.request(td(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}tU.prototype[e]=t(),tU.prototype[e+"Form"]=t(!0)});class tz{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let r=this;this.promise.then(e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null}),this.promise.then=e=>{let t,n=new Promise(e=>{r.subscribe(e),t=e}).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e(function(e,n,i){r.reason||(r.reason=new tt(e,n,i),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason)return void e(this.reason);this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new tz(function(t){e=t}),cancel:e}}}let t$={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(t$).forEach(([e,t])=>{t$[t]=e});let tV=function e(t){let r=new tU(t),n=W(tU.prototype.request,r);return eR.extend(n,tU.prototype,r,{allOwnKeys:!0}),eR.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return e(td(t,r))},n}(e0);tV.Axios=tU,tV.CanceledError=tt,tV.CancelToken=tz,tV.isCancel=te,tV.VERSION=tP,tV.toFormData=eM,tV.AxiosError=eS,tV.Cancel=tV.CanceledError,tV.all=function(e){return Promise.all(e)},tV.spread=function(e){return function(t){return e.apply(null,t)}},tV.isAxiosError=function(e){return eR.isObject(e)&&!0===e.isAxiosError},tV.mergeConfig=td,tV.AxiosHeaders=e8,tV.formToJSON=e=>eQ(eR.isHTMLForm(e)?new FormData(e):e),tV.getAdapter=tj.getAdapter,tV.HttpStatusCode=t$,tV.default=tV;var tq=r(67029);let tW="axios-retry";function tH(e){return!(e.response||!e.code||["ERR_CANCELED","ECONNABORTED"].includes(e.code))&&tq(e)}let tK=["get","head","options"],tG=tK.concat(["put","delete"]);function tJ(e){return"ECONNABORTED"!==e.code&&(!e.response||429===e.response.status||e.response.status>=500&&e.response.status<=599)}function tX(e){return!!e.config?.method&&tJ(e)&&-1!==tG.indexOf(e.config.method)}function tY(e){return tH(e)||tX(e)}function tZ(e){let t=e?.response?.headers["retry-after"];if(!t)return 0;let r=1e3*(Number(t)||0);return 0===r&&(r=(new Date(t).valueOf()||0)-Date.now()),Math.max(0,r)}let tQ={retries:3,retryCondition:tY,retryDelay:function(e=0,t){return Math.max(0,tZ(t))},shouldResetTimeout:!1,onRetry:()=>{},onMaxRetryTimesExceeded:()=>{},validateResponse:null};function t0(e,t,r=!1){var n;let i=(n=t||{},{...tQ,...n,...e[tW]});return i.retryCount=i.retryCount||0,(!i.lastRequestTime||r)&&(i.lastRequestTime=Date.now()),e[tW]=i,i}async function t1(e,t){let{retries:r,retryCondition:n}=e,i=(e.retryCount||0)<r&&n(t);if("object"==typeof i)try{let e=await i;return!1!==e}catch(e){return!1}return i}async function t2(e,t,r,n){t.retryCount+=1;let{retryDelay:i,shouldResetTimeout:o,onRetry:a}=t,s=i(t.retryCount,r);if(e.defaults.agent===n.agent&&delete n.agent,e.defaults.httpAgent===n.httpAgent&&delete n.httpAgent,e.defaults.httpsAgent===n.httpsAgent&&delete n.httpsAgent,!o&&n.timeout&&t.lastRequestTime){let e=Date.now()-t.lastRequestTime,i=n.timeout-e-s;if(i<=0)return Promise.reject(r);n.timeout=i}return(n.transformRequest=[e=>e],await a(t.retryCount,r,n),n.signal?.aborted)?Promise.resolve(e(n)):new Promise(t=>{let r=()=>{clearTimeout(i),t(e(n))},i=setTimeout(()=>{t(e(n)),n.signal?.removeEventListener&&n.signal.removeEventListener("abort",r)},s);n.signal?.addEventListener&&n.signal.addEventListener("abort",r,{once:!0})})}async function t5(e,t){e.retryCount>=e.retries&&await e.onMaxRetryTimesExceeded(t,e.retryCount)}let t4=(e,t)=>({requestInterceptorId:e.interceptors.request.use(e=>(t0(e,t,!0),e[tW]?.validateResponse&&(e.validateStatus=()=>!1),e)),responseInterceptorId:e.interceptors.response.use(null,async r=>{let{config:n}=r;if(!n)return Promise.reject(r);let i=t0(n,t);return r.response&&i.validateResponse?.(r.response)?r.response:await t1(i,r)?t2(e,i,r,n):(await t5(i,r),Promise.reject(r))})});t4.isNetworkError=tH,t4.isSafeRequestError=function(e){return!!e.config?.method&&tJ(e)&&-1!==tK.indexOf(e.config.method)},t4.isIdempotentRequestError=tX,t4.isNetworkOrIdempotentRequestError=tY,t4.exponentialDelay=function(e=0,t,r=100){let n=Math.max(2**e*r,tZ(t)),i=.2*n*Math.random();return n+i},t4.linearDelay=function(e=100){return(t=0,r)=>Math.max(t*e,tZ(r))},t4.isRetryableError=tJ;var t3=e=>"function"==typeof e?e:function(){return e},t9="undefined"!=typeof window?window:null,t6=("undefined"!=typeof self?self:null)||t9||t6,t8={connecting:0,open:1,closing:2,closed:3},t7={closed:"closed",errored:"errored",joined:"joined",joining:"joining",leaving:"leaving"},re={close:"phx_close",error:"phx_error",join:"phx_join",reply:"phx_reply",leave:"phx_leave"},rt={longpoll:"longpoll",websocket:"websocket"},rr={complete:4},rn=class{constructor(e,t,r,n){this.channel=e,this.event=t,this.payload=r||function(){return{}},this.receivedResp=null,this.timeout=n,this.timeoutTimer=null,this.recHooks=[],this.sent=!1}resend(e){this.timeout=e,this.reset(),this.send()}send(){this.hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload(),ref:this.ref,join_ref:this.channel.joinRef()}))}receive(e,t){return this.hasReceived(e)&&t(this.receivedResp.response),this.recHooks.push({status:e,callback:t}),this}reset(){this.cancelRefEvent(),this.ref=null,this.refEvent=null,this.receivedResp=null,this.sent=!1}matchReceive({status:e,response:t,_ref:r}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}cancelRefEvent(){this.refEvent&&this.channel.off(this.refEvent)}cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=null}startTimeout(){this.timeoutTimer&&this.cancelTimeout(),this.ref=this.channel.socket.makeRef(),this.refEvent=this.channel.replyEventName(this.ref),this.channel.on(this.refEvent,e=>{this.cancelRefEvent(),this.cancelTimeout(),this.receivedResp=e,this.matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}trigger(e,t){this.channel.trigger(this.refEvent,{status:e,response:t})}},ri=class{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=null,this.tries=0}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}},ro=class{constructor(e,t,r){this.state=t7.closed,this.topic=e,this.params=t3(t||{}),this.socket=r,this.bindings=[],this.bindingRef=0,this.timeout=this.socket.timeout,this.joinedOnce=!1,this.joinPush=new rn(this,re.join,this.params,this.timeout),this.pushBuffer=[],this.stateChangeRefs=[],this.rejoinTimer=new ri(()=>{this.socket.isConnected()&&this.rejoin()},this.socket.rejoinAfterMs),this.stateChangeRefs.push(this.socket.onError(()=>this.rejoinTimer.reset())),this.stateChangeRefs.push(this.socket.onOpen(()=>{this.rejoinTimer.reset(),this.isErrored()&&this.rejoin()})),this.joinPush.receive("ok",()=>{this.state=t7.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this.joinPush.receive("error",()=>{this.state=t7.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.onClose(()=>{this.rejoinTimer.reset(),this.socket.hasLogger()&&this.socket.log("channel",`close ${this.topic} ${this.joinRef()}`),this.state=t7.closed,this.socket.remove(this)}),this.onError(e=>{this.socket.hasLogger()&&this.socket.log("channel",`error ${this.topic}`,e),this.isJoining()&&this.joinPush.reset(),this.state=t7.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.joinPush.receive("timeout",()=>{this.socket.hasLogger()&&this.socket.log("channel",`timeout ${this.topic} (${this.joinRef()})`,this.joinPush.timeout),new rn(this,re.leave,t3({}),this.timeout).send(),this.state=t7.errored,this.joinPush.reset(),this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.on(re.reply,(e,t)=>{this.trigger(this.replyEventName(t),e)})}join(e=this.timeout){if(!this.joinedOnce)return this.timeout=e,this.joinedOnce=!0,this.rejoin(),this.joinPush;throw Error("tried to join multiple times. 'join' can only be called a single time per channel instance")}onClose(e){this.on(re.close,e)}onError(e){return this.on(re.error,t=>e(t))}on(e,t){let r=this.bindingRef++;return this.bindings.push({event:e,ref:r,callback:t}),r}off(e,t){this.bindings=this.bindings.filter(r=>r.event!==e||void 0!==t&&t!==r.ref)}canPush(){return this.socket.isConnected()&&this.isJoined()}push(e,t,r=this.timeout){if(t=t||{},!this.joinedOnce)throw Error(`tried to push '${e}' to '${this.topic}' before joining. Use channel.join() before pushing events`);let n=new rn(this,e,function(){return t},r);return this.canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}leave(e=this.timeout){this.rejoinTimer.reset(),this.joinPush.cancelTimeout(),this.state=t7.leaving;let t=()=>{this.socket.hasLogger()&&this.socket.log("channel",`leave ${this.topic}`),this.trigger(re.close,"leave")},r=new rn(this,re.leave,t3({}),e);return r.receive("ok",()=>t()).receive("timeout",()=>t()),r.send(),this.canPush()||r.trigger("ok",{}),r}onMessage(e,t,r){return t}isMember(e,t,r,n){return this.topic===e&&(!n||n===this.joinRef()||(this.socket.hasLogger()&&this.socket.log("channel","dropping outdated message",{topic:e,event:t,payload:r,joinRef:n}),!1))}joinRef(){return this.joinPush.ref}rejoin(e=this.timeout){this.isLeaving()||(this.socket.leaveOpenTopic(this.topic),this.state=t7.joining,this.joinPush.resend(e))}trigger(e,t,r,n){let i=this.onMessage(e,t,r,n);if(t&&!i)throw Error("channel onMessage callbacks must return the payload, modified or unmodified");let o=this.bindings.filter(t=>t.event===e);for(let e=0;e<o.length;e++)o[e].callback(i,r,n||this.joinRef())}replyEventName(e){return`chan_reply_${e}`}isClosed(){return this.state===t7.closed}isErrored(){return this.state===t7.errored}isJoined(){return this.state===t7.joined}isJoining(){return this.state===t7.joining}isLeaving(){return this.state===t7.leaving}},ra=class{static request(e,t,r,n,i,o,a){if(t6.XDomainRequest){let r=new t6.XDomainRequest;return this.xdomainRequest(r,e,t,n,i,o,a)}{let s=new t6.XMLHttpRequest;return this.xhrRequest(s,e,t,r,n,i,o,a)}}static xdomainRequest(e,t,r,n,i,o,a){return e.timeout=i,e.open(t,r),e.onload=()=>{let t=this.parseJSON(e.responseText);a&&a(t)},o&&(e.ontimeout=o),e.onprogress=()=>{},e.send(n),e}static xhrRequest(e,t,r,n,i,o,a,s){return e.open(t,r,!0),e.timeout=o,e.setRequestHeader("Content-Type",n),e.onerror=()=>s&&s(null),e.onreadystatechange=()=>{e.readyState===rr.complete&&s&&s(this.parseJSON(e.responseText))},a&&(e.ontimeout=a),e.send(i),e}static parseJSON(e){if(!e||""===e)return null;try{return JSON.parse(e)}catch(t){return console&&console.log("failed to parse JSON response",e),null}}static serialize(e,t){let r=[];for(var n in e){if(!Object.prototype.hasOwnProperty.call(e,n))continue;let i=t?`${t}[${n}]`:n,o=e[n];"object"==typeof o?r.push(this.serialize(o,i)):r.push(encodeURIComponent(i)+"="+encodeURIComponent(o))}return r.join("&")}static appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?";return`${e}${r}${this.serialize(t)}`}},rs=e=>{let t="",r=new Uint8Array(e),n=r.byteLength;for(let e=0;e<n;e++)t+=String.fromCharCode(r[e]);return btoa(t)},rl=class{constructor(e){this.endPoint=null,this.token=null,this.skipHeartbeat=!0,this.reqs=new Set,this.awaitingBatchAck=!1,this.currentBatch=null,this.currentBatchTimer=null,this.batchBuffer=[],this.onopen=function(){},this.onerror=function(){},this.onmessage=function(){},this.onclose=function(){},this.pollEndpoint=this.normalizeEndpoint(e),this.readyState=t8.connecting,setTimeout(()=>this.poll(),0)}normalizeEndpoint(e){return e.replace("ws://","http://").replace("wss://","https://").replace(RegExp("(.*)/"+rt.websocket),"$1/"+rt.longpoll)}endpointURL(){return ra.appendParams(this.pollEndpoint,{token:this.token})}closeAndRetry(e,t,r){this.close(e,t,r),this.readyState=t8.connecting}ontimeout(){this.onerror("timeout"),this.closeAndRetry(1005,"timeout",!1)}isActive(){return this.readyState===t8.open||this.readyState===t8.connecting}poll(){this.ajax("GET","application/json",null,()=>this.ontimeout(),e=>{if(e){var{status:t,token:r,messages:n}=e;this.token=r}else t=0;switch(t){case 200:n.forEach(e=>{setTimeout(()=>this.onmessage({data:e}),0)}),this.poll();break;case 204:this.poll();break;case 410:this.readyState=t8.open,this.onopen({}),this.poll();break;case 403:this.onerror(403),this.close(1008,"forbidden",!1);break;case 0:case 500:this.onerror(500),this.closeAndRetry(1011,"internal server error",500);break;default:throw Error(`unhandled poll status ${t}`)}})}send(e){"string"!=typeof e&&(e=rs(e)),this.currentBatch?this.currentBatch.push(e):this.awaitingBatchAck?this.batchBuffer.push(e):(this.currentBatch=[e],this.currentBatchTimer=setTimeout(()=>{this.batchSend(this.currentBatch),this.currentBatch=null},0))}batchSend(e){this.awaitingBatchAck=!0,this.ajax("POST","application/x-ndjson",e.join("\n"),()=>this.onerror("timeout"),e=>{this.awaitingBatchAck=!1,e&&200===e.status?this.batchBuffer.length>0&&(this.batchSend(this.batchBuffer),this.batchBuffer=[]):(this.onerror(e&&e.status),this.closeAndRetry(1011,"internal server error",!1))})}close(e,t,r){for(let e of this.reqs)e.abort();this.readyState=t8.closed;let n=Object.assign({code:1e3,reason:void 0,wasClean:!0},{code:e,reason:t,wasClean:r});this.batchBuffer=[],clearTimeout(this.currentBatchTimer),this.currentBatchTimer=null,"undefined"!=typeof CloseEvent?this.onclose(new CloseEvent("close",n)):this.onclose(n)}ajax(e,t,r,n,i){let o;o=ra.request(e,this.endpointURL(),t,r,this.timeout,()=>{this.reqs.delete(o),n()},e=>{this.reqs.delete(o),this.isActive()&&i(e)}),this.reqs.add(o)}},rc={HEADER_LENGTH:1,META_LENGTH:4,KINDS:{push:0,reply:1,broadcast:2},encode(e,t){return e.payload.constructor===ArrayBuffer?t(this.binaryEncode(e)):t(JSON.stringify([e.join_ref,e.ref,e.topic,e.event,e.payload]))},decode(e,t){if(e.constructor===ArrayBuffer)return t(this.binaryDecode(e));{let[r,n,i,o,a]=JSON.parse(e);return t({join_ref:r,ref:n,topic:i,event:o,payload:a})}},binaryEncode(e){let{join_ref:t,ref:r,event:n,topic:i,payload:o}=e,a=this.META_LENGTH+t.length+r.length+i.length+n.length,s=new ArrayBuffer(this.HEADER_LENGTH+a),l=new DataView(s),c=0;l.setUint8(c++,this.KINDS.push),l.setUint8(c++,t.length),l.setUint8(c++,r.length),l.setUint8(c++,i.length),l.setUint8(c++,n.length),Array.from(t,e=>l.setUint8(c++,e.charCodeAt(0))),Array.from(r,e=>l.setUint8(c++,e.charCodeAt(0))),Array.from(i,e=>l.setUint8(c++,e.charCodeAt(0))),Array.from(n,e=>l.setUint8(c++,e.charCodeAt(0)));var u=new Uint8Array(s.byteLength+o.byteLength);return u.set(new Uint8Array(s),0),u.set(new Uint8Array(o),s.byteLength),u.buffer},binaryDecode(e){let t=new DataView(e),r=t.getUint8(0),n=new TextDecoder;switch(r){case this.KINDS.push:return this.decodePush(e,t,n);case this.KINDS.reply:return this.decodeReply(e,t,n);case this.KINDS.broadcast:return this.decodeBroadcast(e,t,n)}},decodePush(e,t,r){let n=t.getUint8(1),i=t.getUint8(2),o=t.getUint8(3),a=this.HEADER_LENGTH+this.META_LENGTH-1,s=r.decode(e.slice(a,a+n));a+=n;let l=r.decode(e.slice(a,a+i));a+=i;let c=r.decode(e.slice(a,a+o));return a+=o,{join_ref:s,ref:null,topic:l,event:c,payload:e.slice(a,e.byteLength)}},decodeReply(e,t,r){let n=t.getUint8(1),i=t.getUint8(2),o=t.getUint8(3),a=t.getUint8(4),s=this.HEADER_LENGTH+this.META_LENGTH,l=r.decode(e.slice(s,s+n));s+=n;let c=r.decode(e.slice(s,s+i));s+=i;let u=r.decode(e.slice(s,s+o));s+=o;let h=r.decode(e.slice(s,s+a));s+=a;let d=e.slice(s,e.byteLength);return{join_ref:l,ref:c,topic:u,event:re.reply,payload:{status:h,response:d}}},decodeBroadcast(e,t,r){let n=t.getUint8(1),i=t.getUint8(2),o=this.HEADER_LENGTH+2,a=r.decode(e.slice(o,o+n));o+=n;let s=r.decode(e.slice(o,o+i));return o+=i,{join_ref:null,ref:null,topic:a,event:s,payload:e.slice(o,e.byteLength)}}},ru=class{constructor(e,t={}){this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.channels=[],this.sendBuffer=[],this.ref=0,this.timeout=t.timeout||1e4,this.transport=t.transport||t6.WebSocket||rl,this.primaryPassedHealthCheck=!1,this.longPollFallbackMs=t.longPollFallbackMs,this.fallbackTimer=null,this.sessionStore=t.sessionStorage||t6&&t6.sessionStorage,this.establishedConnections=0,this.defaultEncoder=rc.encode.bind(rc),this.defaultDecoder=rc.decode.bind(rc),this.closeWasClean=!1,this.binaryType=t.binaryType||"arraybuffer",this.connectClock=1,this.transport!==rl?(this.encode=t.encode||this.defaultEncoder,this.decode=t.decode||this.defaultDecoder):(this.encode=this.defaultEncoder,this.decode=this.defaultDecoder);let r=null;t9&&t9.addEventListener&&(t9.addEventListener("pagehide",e=>{this.conn&&(this.disconnect(),r=this.connectClock)}),t9.addEventListener("pageshow",e=>{r===this.connectClock&&(r=null,this.connect())})),this.heartbeatIntervalMs=t.heartbeatIntervalMs||3e4,this.rejoinAfterMs=e=>t.rejoinAfterMs?t.rejoinAfterMs(e):[1e3,2e3,5e3][e-1]||1e4,this.reconnectAfterMs=e=>t.reconnectAfterMs?t.reconnectAfterMs(e):[10,50,100,150,200,250,500,1e3,2e3][e-1]||5e3,this.logger=t.logger||null,!this.logger&&t.debug&&(this.logger=(e,t,r)=>{console.log(`${e}: ${t}`,r)}),this.longpollerTimeout=t.longpollerTimeout||2e4,this.params=t3(t.params||{}),this.endPoint=`${e}/${rt.websocket}`,this.vsn=t.vsn||"2.0.0",this.heartbeatTimeoutTimer=null,this.heartbeatTimer=null,this.pendingHeartbeatRef=null,this.reconnectTimer=new ri(()=>{this.teardown(()=>this.connect())},this.reconnectAfterMs)}getLongPollTransport(){return rl}replaceTransport(e){this.connectClock++,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.conn&&(this.conn.close(),this.conn=null),this.transport=e}protocol(){return location.protocol.match(/^https/)?"wss":"ws"}endPointURL(){let e=ra.appendParams(ra.appendParams(this.endPoint,this.params()),{vsn:this.vsn});return"/"!==e.charAt(0)?e:"/"===e.charAt(1)?`${this.protocol()}:${e}`:`${this.protocol()}://${location.host}${e}`}disconnect(e,t,r){this.connectClock++,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.teardown(e,t,r)}connect(e){e&&(console&&console.log("passing params to connect is deprecated. Instead pass :params to the Socket constructor"),this.params=t3(e)),this.conn||(this.longPollFallbackMs&&this.transport!==rl?this.connectWithFallback(rl,this.longPollFallbackMs):this.transportConnect())}log(e,t,r){this.logger&&this.logger(e,t,r)}hasLogger(){return null!==this.logger}onOpen(e){let t=this.makeRef();return this.stateChangeCallbacks.open.push([t,e]),t}onClose(e){let t=this.makeRef();return this.stateChangeCallbacks.close.push([t,e]),t}onError(e){let t=this.makeRef();return this.stateChangeCallbacks.error.push([t,e]),t}onMessage(e){let t=this.makeRef();return this.stateChangeCallbacks.message.push([t,e]),t}ping(e){if(!this.isConnected())return!1;let t=this.makeRef(),r=Date.now();this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:t});let n=this.onMessage(i=>{i.ref===t&&(this.off([n]),e(Date.now()-r))});return!0}transportConnect(){this.connectClock++,this.closeWasClean=!1,this.conn=new this.transport(this.endPointURL()),this.conn.binaryType=this.binaryType,this.conn.timeout=this.longpollerTimeout,this.conn.onopen=()=>this.onConnOpen(),this.conn.onerror=e=>this.onConnError(e),this.conn.onmessage=e=>this.onConnMessage(e),this.conn.onclose=e=>this.onConnClose(e)}getSession(e){return this.sessionStore&&this.sessionStore.getItem(e)}storeSession(e,t){this.sessionStore&&this.sessionStore.setItem(e,t)}connectWithFallback(e,t=2500){let r,n;clearTimeout(this.fallbackTimer);let i=!1,o=!0,a=t=>{this.log("transport",`falling back to ${e.name}...`,t),this.off([r,n]),o=!1,this.replaceTransport(e),this.transportConnect()};if(this.getSession(`phx:fallback:${e.name}`))return a("memorized");this.fallbackTimer=setTimeout(a,t),n=this.onError(e=>{this.log("transport","error",e),o&&!i&&(clearTimeout(this.fallbackTimer),a(e))}),this.onOpen(()=>{if(i=!0,!o)return this.primaryPassedHealthCheck||this.storeSession(`phx:fallback:${e.name}`,"true"),this.log("transport",`established ${e.name} fallback`);clearTimeout(this.fallbackTimer),this.fallbackTimer=setTimeout(a,t),this.ping(e=>{this.log("transport","connected to primary after",e),this.primaryPassedHealthCheck=!0,clearTimeout(this.fallbackTimer)})}),this.transportConnect()}clearHeartbeats(){clearTimeout(this.heartbeatTimer),clearTimeout(this.heartbeatTimeoutTimer)}onConnOpen(){this.hasLogger()&&this.log("transport",`${this.transport.name} connected to ${this.endPointURL()}`),this.closeWasClean=!1,this.establishedConnections++,this.flushSendBuffer(),this.reconnectTimer.reset(),this.resetHeartbeat(),this.stateChangeCallbacks.open.forEach(([,e])=>e())}heartbeatTimeout(){this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null,this.hasLogger()&&this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.triggerChanError(),this.closeWasClean=!1,this.teardown(()=>this.reconnectTimer.scheduleTimeout(),1e3,"heartbeat timeout"))}resetHeartbeat(){this.conn&&this.conn.skipHeartbeat||(this.pendingHeartbeatRef=null,this.clearHeartbeats(),this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs))}teardown(e,t,r){if(!this.conn)return e&&e();this.waitForBufferDone(()=>{this.conn&&(t?this.conn.close(t,r||""):this.conn.close()),this.waitForSocketClosed(()=>{this.conn&&(this.conn.onopen=function(){},this.conn.onerror=function(){},this.conn.onmessage=function(){},this.conn.onclose=function(){},this.conn=null),e&&e()})})}waitForBufferDone(e,t=1){if(5===t||!this.conn||!this.conn.bufferedAmount)return void e();setTimeout(()=>{this.waitForBufferDone(e,t+1)},150*t)}waitForSocketClosed(e,t=1){if(5===t||!this.conn||this.conn.readyState===t8.closed)return void e();setTimeout(()=>{this.waitForSocketClosed(e,t+1)},150*t)}onConnClose(e){let t=e&&e.code;this.hasLogger()&&this.log("transport","close",e),this.triggerChanError(),this.clearHeartbeats(),this.closeWasClean||1e3===t||this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(([,t])=>t(e))}onConnError(e){this.hasLogger()&&this.log("transport",e);let t=this.transport,r=this.establishedConnections;this.stateChangeCallbacks.error.forEach(([,n])=>{n(e,t,r)}),(t===this.transport||r>0)&&this.triggerChanError()}triggerChanError(){this.channels.forEach(e=>{e.isErrored()||e.isLeaving()||e.isClosed()||e.trigger(re.error)})}connectionState(){switch(this.conn&&this.conn.readyState){case t8.connecting:return"connecting";case t8.open:return"open";case t8.closing:return"closing";default:return"closed"}}isConnected(){return"open"===this.connectionState()}remove(e){this.off(e.stateChangeRefs),this.channels=this.channels.filter(t=>t!==e)}off(e){for(let t in this.stateChangeCallbacks)this.stateChangeCallbacks[t]=this.stateChangeCallbacks[t].filter(([t])=>-1===e.indexOf(t))}channel(e,t={}){let r=new ro(e,t,this);return this.channels.push(r),r}push(e){if(this.hasLogger()){let{topic:t,event:r,payload:n,ref:i,join_ref:o}=e;this.log("push",`${t} ${r} (${o}, ${i})`,n)}this.isConnected()?this.encode(e,e=>this.conn.send(e)):this.sendBuffer.push(()=>this.encode(e,e=>this.conn.send(e)))}makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}sendHeartbeat(){(!this.pendingHeartbeatRef||this.isConnected())&&(this.pendingHeartbeatRef=this.makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatTimeoutTimer=setTimeout(()=>this.heartbeatTimeout(),this.heartbeatIntervalMs))}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:n,ref:i,join_ref:o}=e;i&&i===this.pendingHeartbeatRef&&(this.clearHeartbeats(),this.pendingHeartbeatRef=null,this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)),this.hasLogger()&&this.log("receive",`${n.status||""} ${t} ${r} ${i&&"("+i+")"||""}`,n);for(let e=0;e<this.channels.length;e++){let a=this.channels[e];a.isMember(t,r,n,o)&&a.trigger(r,n,i,o)}for(let t=0;t<this.stateChangeCallbacks.message.length;t++){let[,r]=this.stateChangeCallbacks.message[t];r(e)}})}leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t.isJoined()||t.isJoining()));t&&(this.hasLogger()&&this.log("transport",`leaving duplicate topic "${e}"`),t.leave())}},rh=Object.defineProperty,rd=(e,t,r)=>t in e?rh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rf=(e,t,r)=>rd(e,"symbol"!=typeof t?t+"":t,r);class rp{constructor(e){rf(this,"host"),rf(this,"apiKey"),rf(this,"userToken"),rf(this,"axiosClient"),rf(this,"socket"),this.host=e.host,this.apiKey=e.apiKey,this.userToken=e.userToken||null,this.axiosClient=tV.create({baseURL:this.host,headers:{Accept:"application/json","Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`,"X-Knock-User-Token":this.userToken}}),"u">typeof window&&(this.socket=new ru(`${this.host.replace("http","ws")}/ws/v1`,{params:{user_token:this.userToken,api_key:this.apiKey}})),t4(this.axiosClient,{retries:3,retryCondition:this.canRetryRequest,retryDelay:t4.exponentialDelay})}async makeRequest(e){try{let t=await this.axiosClient(e);return{statusCode:t.status<300?"ok":"error",body:t.data,error:void 0,status:t.status}}catch(e){return console.error(e),{statusCode:"error",status:500,body:void 0,error:e}}}canRetryRequest(e){return!!t4.isNetworkError(e)||!!e.response&&(e.response.status>=500&&e.response.status<=599||429===e.response.status)}}var rm=Object.defineProperty,rg=(e,t,r)=>t in e?rm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rv=(e,t,r)=>rg(e,"symbol"!=typeof t?t+"":t,r);class ry{constructor(e){rv(this,"knock"),this.knock=e}async get(e){let t=await this.knock.client().makeRequest({method:"GET",url:`/v1/messages/${e}`});return this.handleResponse(t)}async updateStatus(e,t,r){let n="interacted"===t&&r?{metadata:r.metadata}:void 0,i=await this.knock.client().makeRequest({method:"PUT",url:`/v1/messages/${e}/${t}`,data:n});return this.handleResponse(i)}async removeStatus(e,t){let r=await this.knock.client().makeRequest({method:"DELETE",url:`/v1/messages/${e}/${t}`});return this.handleResponse(r)}async batchUpdateStatuses(e,t,r){let n="interacted"===t&&r?{metadata:r.metadata}:{},i=await this.knock.client().makeRequest({method:"POST",url:`/v1/messages/batch/${t}`,data:{message_ids:e,...n}});return this.handleResponse(i)}async bulkUpdateAllStatusesInChannel({channelId:e,status:t,options:r}){let n=await this.knock.client().makeRequest({method:"POST",url:`/v1/channels/${e}/messages/bulk/${t}`,data:r});return this.handleResponse(n)}handleResponse(e){var t,r;if("error"===e.statusCode){if((null==(r=null==(t=e.error)?void 0:t.response)?void 0:r.status)<500)return e.error||e.body;throw Error(e.error||e.body)}return e.body}}let rb="$tenants";var rw=Object.defineProperty,rk=(e,t,r)=>t in e?rw(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rC=(e,t,r)=>rk(e,"symbol"!=typeof t?t+"":t,r);class rE{constructor(e){rC(this,"instance"),this.instance=e}async authCheck({tenant:e,knockChannelId:t}){let r=await this.instance.client().makeRequest({method:"GET",url:`/v1/providers/ms-teams/${t}/auth_check`,params:{ms_teams_tenant_object:{object_id:e,collection:rb},channel_id:t}});return this.handleResponse(r)}async getTeams(e){let{knockChannelId:t,tenant:r}=e,n=e.queryOptions||{},i=await this.instance.client().makeRequest({method:"GET",url:`/v1/providers/ms-teams/${t}/teams`,params:{ms_teams_tenant_object:{object_id:r,collection:rb},query_options:{$filter:n.$filter,$select:n.$select,$top:n.$top,$skiptoken:n.$skiptoken}}});return this.handleResponse(i)}async getChannels(e){let{knockChannelId:t,teamId:r,tenant:n}=e,i=e.queryOptions||{},o=await this.instance.client().makeRequest({method:"GET",url:`/v1/providers/ms-teams/${t}/channels`,params:{ms_teams_tenant_object:{object_id:n,collection:rb},team_id:r,query_options:{$filter:i.$filter,$select:i.$select}}});return this.handleResponse(o)}async revokeAccessToken({tenant:e,knockChannelId:t}){let r=await this.instance.client().makeRequest({method:"PUT",url:`/v1/providers/ms-teams/${t}/revoke_access`,params:{ms_teams_tenant_object:{object_id:e,collection:rb},channel_id:t}});return this.handleResponse(r)}handleResponse(e){var t,r;if("error"===e.statusCode){if((null==(r=null==(t=e.error)?void 0:t.response)?void 0:r.status)<500)return e.error||e.body;throw Error(e.error||e.body)}return e.body}}var rx=Object.defineProperty,rA=(e,t,r)=>t in e?rx(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,r_=(e,t,r)=>rA(e,"symbol"!=typeof t?t+"":t,r);class rR{constructor(e){r_(this,"instance"),this.instance=e}async getChannelData({collection:e,objectId:t,channelId:r}){let n=await this.instance.client().makeRequest({method:"GET",url:`/v1/objects/${e}/${t}/channel_data/${r}`});return this.handleResponse(n)}async setChannelData({objectId:e,collection:t,channelId:r,data:n}){let i=await this.instance.client().makeRequest({method:"PUT",url:`v1/objects/${t}/${e}/channel_data/${r}`,data:{data:n}});return this.handleResponse(i)}handleResponse(e){if("error"===e.statusCode)throw Error(e.error||e.body);return e.body}}var rS=Object.defineProperty,rT=(e,t,r)=>t in e?rS(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rO=(e,t,r)=>rT(e,"symbol"!=typeof t?t+"":t,r);let rL="default";function rj(e){return"object"==typeof e?e:{subscribed:e}}class rI{constructor(e){rO(this,"instance"),this.instance=e}async getAll(){this.instance.failIfNotAuthenticated();let e=await this.instance.client().makeRequest({method:"GET",url:`/v1/users/${this.instance.userId}/preferences`});return this.handleResponse(e)}async get(e={}){this.instance.failIfNotAuthenticated();let t=e.preferenceSet||rL,r=await this.instance.client().makeRequest({method:"GET",url:`/v1/users/${this.instance.userId}/preferences/${t}`});return this.handleResponse(r)}async set(e,t={}){this.instance.failIfNotAuthenticated();let r=t.preferenceSet||rL,n=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${r}`,data:e});return this.handleResponse(n)}async setChannelTypes(e,t={}){this.instance.failIfNotAuthenticated();let r=t.preferenceSet||rL,n=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${r}/channel_types`,data:e});return this.handleResponse(n)}async setChannelType(e,t,r={}){this.instance.failIfNotAuthenticated();let n=r.preferenceSet||rL,i=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${n}/channel_types/${e}`,data:{subscribed:t}});return this.handleResponse(i)}async setWorkflows(e,t={}){this.instance.failIfNotAuthenticated();let r=t.preferenceSet||rL,n=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${r}/workflows`,data:e});return this.handleResponse(n)}async setWorkflow(e,t,r={}){this.instance.failIfNotAuthenticated();let n=r.preferenceSet||rL,i=rj(t),o=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${n}/workflows/${e}`,data:i});return this.handleResponse(o)}async setCategories(e,t={}){this.instance.failIfNotAuthenticated();let r=t.preferenceSet||rL,n=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${r}/categories`,data:e});return this.handleResponse(n)}async setCategory(e,t,r={}){this.instance.failIfNotAuthenticated();let n=r.preferenceSet||rL,i=rj(t),o=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${n}/categories/${e}`,data:i});return this.handleResponse(o)}handleResponse(e){if("error"===e.statusCode)throw Error(e.error||e.body);return e.body}}var rN=Object.defineProperty,rP=(e,t,r)=>t in e?rN(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rM=(e,t,r)=>rP(e,"symbol"!=typeof t?t+"":t,r);class rB{constructor(e){rM(this,"instance"),this.instance=e}async authCheck({tenant:e,knockChannelId:t}){let r=await this.instance.client().makeRequest({method:"GET",url:`/v1/providers/slack/${t}/auth_check`,params:{access_token_object:{object_id:e,collection:rb},channel_id:t}});return this.handleResponse(r)}async getChannels(e){let{knockChannelId:t,tenant:r}=e,n=e.queryOptions||{},i=await this.instance.client().makeRequest({method:"GET",url:`/v1/providers/slack/${t}/channels`,params:{access_token_object:{object_id:r,collection:rb},channel_id:t,query_options:{cursor:n.cursor,limit:n.limit,exclude_archived:n.excludeArchived,team_id:n.teamId,types:n.types}}});return this.handleResponse(i)}async revokeAccessToken({tenant:e,knockChannelId:t}){let r=await this.instance.client().makeRequest({method:"PUT",url:`/v1/providers/slack/${t}/revoke_access`,params:{access_token_object:{object_id:e,collection:rb},channel_id:t}});return this.handleResponse(r)}handleResponse(e){var t,r;if("error"===e.statusCode){if((null==(r=null==(t=e.error)?void 0:t.response)?void 0:r.status)<500)return e.error||e.body;throw Error(e.error||e.body)}return e.body}}var rD=Object.defineProperty,rF=(e,t)=>rD(e,"name",{value:t,configurable:!0}),rU=class{type=3;name="";prefix="";value="";suffix="";modifier=3;constructor(e,t,r,n,i,o){this.type=e,this.name=t,this.prefix=r,this.value=n,this.suffix=i,this.modifier=o}hasCustomName(){return""!==this.name&&"number"!=typeof this.name}};rF(rU,"Part");var rz=/[$_\p{ID_Start}]/u,r$=/[$_\u200C\u200D\p{ID_Continue}]/u;function rV(e,t){return(t?/^[\x00-\xFF]*$/:/^[\x00-\x7F]*$/).test(e)}function rq(e,t=!1){let r=[],n=0;for(;n<e.length;){let i=e[n],o=rF(function(i){if(!t)throw TypeError(i);r.push({type:"INVALID_CHAR",index:n,value:e[n++]})},"ErrorOrInvalid");if("*"===i){r.push({type:"ASTERISK",index:n,value:e[n++]});continue}if("+"===i||"?"===i){r.push({type:"OTHER_MODIFIER",index:n,value:e[n++]});continue}if("\\"===i){r.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===i){r.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===i){r.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===i){let t="",i=n+1;for(;i<e.length;){let r=e.substr(i,1);if(i===n+1&&rz.test(r)||i!==n+1&&r$.test(r)){t+=e[i++];continue}break}if(!t){o(`Missing parameter name at ${n}`);continue}r.push({type:"NAME",index:n,value:t}),n=i;continue}if("("===i){let t=1,i="",a=n+1,s=!1;if("?"===e[a]){o(`Pattern cannot start with "?" at ${a}`);continue}for(;a<e.length;){if(!rV(e[a],!1)){o(`Invalid character '${e[a]}' at ${a}.`),s=!0;break}if("\\"===e[a]){i+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--t){a++;break}}else if("("===e[a]&&(t++,"?"!==e[a+1])){o(`Capturing groups are not allowed at ${a}`),s=!0;break}i+=e[a++]}if(s)continue;if(t){o(`Unbalanced pattern at ${n}`);continue}if(!i){o(`Missing pattern at ${n}`);continue}r.push({type:"REGEX",index:n,value:i}),n=a;continue}r.push({type:"CHAR",index:n,value:e[n++]})}return r.push({type:"END",index:n,value:""}),r}function rW(e,t={}){let r=rq(e);t.delimiter??="/#?",t.prefixes??="./";let n=`[^${rH(t.delimiter)}]+?`,i=[],o=0,a=0,s=new Set,l=rF(e=>{if(a<r.length&&r[a].type===e)return r[a++].value},"tryConsume"),c=rF(()=>l("OTHER_MODIFIER")??l("ASTERISK"),"tryConsumeModifier"),u=rF(e=>{let t=l(e);if(void 0!==t)return t;let{type:n,index:i}=r[a];throw TypeError(`Unexpected ${n} at ${i}, expected ${e}`)},"mustConsume"),h=rF(()=>{let e="",t;for(;t=l("CHAR")??l("ESCAPED_CHAR");)e+=t;return e},"consumeText"),d=rF(e=>e,"DefaultEncodePart"),f=t.encodePart||d,p="",m=rF(e=>{p+=e},"appendToPendingFixedValue"),g=rF(()=>{p.length&&(i.push(new rU(3,"","",f(p),"",3)),p="")},"maybeAddPartFromPendingFixedValue"),v=rF((e,t,r,a,l)=>{let c,u,h=3;switch(l){case"?":h=1;break;case"*":h=0;break;case"+":h=2}if(!t&&!r&&3===h)return void m(e);if(g(),!t&&!r){if(!e)return;i.push(new rU(3,"","",f(e),"",h));return}let d=2;if((c=r?"*"===r?".*":r:n)===n?(d=1,c=""):".*"===c&&(d=0,c=""),t?u=t:r&&(u=o++),s.has(u))throw TypeError(`Duplicate name '${u}'.`);s.add(u),i.push(new rU(d,u,f(e),c,f(a),h))},"addPart");for(;a<r.length;){let e=l("CHAR"),r=l("NAME"),n=l("REGEX");if(r||n||(n=l("ASTERISK")),r||n){let i=e??"";-1===t.prefixes.indexOf(i)&&(m(i),i=""),g(),v(i,r,n,"",c());continue}let i=e??l("ESCAPED_CHAR");if(i){m(i);continue}if(l("OPEN")){let e=h(),t=l("NAME"),r=l("REGEX");t||r||(r=l("ASTERISK"));let n=h();u("CLOSE"),v(e,t,r,n,c());continue}g(),u("END")}return i}function rH(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}function rK(e){return e&&e.ignoreCase?"ui":"u"}function rG(e,t,r){return rX(rW(e,r),t,r)}function rJ(e){switch(e){case 0:return"*";case 1:return"?";case 2:return"+";case 3:return""}}function rX(e,t,r={}){r.delimiter??="/#?",r.prefixes??="./",r.sensitive??=!1,r.strict??=!1,r.end??=!0,r.start??=!0,r.endsWith="";let n=r.start?"^":"";for(let i of e){if(3===i.type){3===i.modifier?n+=rH(i.value):n+=`(?:${rH(i.value)})${rJ(i.modifier)}`;continue}t&&t.push(i.name);let e=`[^${rH(r.delimiter)}]+?`,o=i.value;if(1===i.type?o=e:0===i.type&&(o=".*"),!i.prefix.length&&!i.suffix.length){3===i.modifier||1===i.modifier?n+=`(${o})${rJ(i.modifier)}`:n+=`((?:${o})${rJ(i.modifier)})`;continue}if(3===i.modifier||1===i.modifier){n+=`(?:${rH(i.prefix)}(${o})${rH(i.suffix)})`,n+=rJ(i.modifier);continue}n+=`(?:${rH(i.prefix)}`,n+=`((?:${o})(?:`,n+=rH(i.suffix),n+=rH(i.prefix),n+=`(?:${o}))*)${rH(i.suffix)})`,0===i.modifier&&(n+="?")}let i=`[${rH(r.endsWith)}]|$`,o=`[${rH(r.delimiter)}]`;if(r.end)return r.strict||(n+=`${o}?`),r.endsWith.length?n+=`(?=${i})`:n+="$",new RegExp(n,rK(r));r.strict||(n+=`(?:${o}(?=${i}))?`);let a=!1;if(e.length){let t=e[e.length-1];3===t.type&&3===t.modifier&&(a=r.delimiter.indexOf(t)>-1)}return a||(n+=`(?=${o}|${i})`),new RegExp(n,rK(r))}rF(rV,"isASCII"),rF(rq,"lexer"),rF(rW,"parse"),rF(rH,"escapeString"),rF(rK,"flags"),rF(rG,"stringToRegexp"),rF(rJ,"modifierToString"),rF(rX,"partsToRegexp");var rY={delimiter:"",prefixes:"",sensitive:!0,strict:!0},rZ={delimiter:".",prefixes:"",sensitive:!0,strict:!0},rQ={delimiter:"/",prefixes:"/",sensitive:!0,strict:!0};function r0(e,t){return!!e.length&&("/"===e[0]||!!t&&!(e.length<2)&&("\\"==e[0]||"{"==e[0])&&"/"==e[1])}function r1(e,t){return e.startsWith(t)?e.substring(t.length,e.length):e}function r2(e,t){return e.endsWith(t)?e.substr(0,e.length-t.length):e}function r5(e){return!!e&&!(e.length<2)&&("["===e[0]||("\\"===e[0]||"{"===e[0])&&"["===e[1])}rF(r0,"isAbsolutePathname"),rF(r1,"maybeStripPrefix"),rF(r2,"maybeStripSuffix"),rF(r5,"treatAsIPv6Hostname");var r4=["ftp","file","http","https","ws","wss"];function r3(e){if(!e)return!0;for(let t of r4)if(e.test(t))return!0;return!1}function r9(e,t){if(e=r1(e,"#"),t||""===e)return e;let r=new URL("https://example.com");return r.hash=e,r.hash?r.hash.substring(1,r.hash.length):""}function r6(e,t){if(e=r1(e,"?"),t||""===e)return e;let r=new URL("https://example.com");return r.search=e,r.search?r.search.substring(1,r.search.length):""}function r8(e,t){return t||""===e?e:r5(e)?nc(e):nl(e)}function r7(e,t){if(t||""===e)return e;let r=new URL("https://example.com");return r.password=e,r.password}function ne(e,t){if(t||""===e)return e;let r=new URL("https://example.com");return r.username=e,r.username}function nt(e,t,r){if(r||""===e)return e;if(t&&!r4.includes(t))return new URL(`${t}:${e}`).pathname;let n="/"==e[0];return e=new URL(n?e:"/-"+e,"https://example.com").pathname,n||(e=e.substring(2,e.length)),e}function nr(e,t,r){return ni(t)===e&&(e=""),r||""===e?e:nu(e)}function nn(e,t){return e=r2(e,":"),t||""===e?e:no(e)}function ni(e){switch(e){case"ws":case"http":return"80";case"wws":case"https":return"443";case"ftp":return"21";default:return""}}function no(e){if(""===e)return e;if(/^[-+.A-Za-z0-9]*$/.test(e))return e.toLowerCase();throw TypeError(`Invalid protocol '${e}'.`)}function na(e){if(""===e)return e;let t=new URL("https://example.com");return t.username=e,t.username}function ns(e){if(""===e)return e;let t=new URL("https://example.com");return t.password=e,t.password}function nl(e){if(""===e)return e;if(/[\t\n\r #%/:<>?@[\]^\\|]/g.test(e))throw TypeError(`Invalid hostname '${e}'`);let t=new URL("https://example.com");return t.hostname=e,t.hostname}function nc(e){if(""===e)return e;if(/[^0-9a-fA-F[\]:]/g.test(e))throw TypeError(`Invalid IPv6 hostname '${e}'`);return e.toLowerCase()}function nu(e){if(""===e||/^[0-9]*$/.test(e)&&65535>=parseInt(e))return e;throw TypeError(`Invalid port '${e}'.`)}function nh(e){if(""===e)return e;let t=new URL("https://example.com");return t.pathname="/"!==e[0]?"/-"+e:e,"/"!==e[0]?t.pathname.substring(2,t.pathname.length):t.pathname}function nd(e){return""===e?e:new URL(`data:${e}`).pathname}function nf(e){if(""===e)return e;let t=new URL("https://example.com");return t.search=e,t.search.substring(1,t.search.length)}function np(e){if(""===e)return e;let t=new URL("https://example.com");return t.hash=e,t.hash.substring(1,t.hash.length)}rF(r3,"isSpecialScheme"),rF(r9,"canonicalizeHash"),rF(r6,"canonicalizeSearch"),rF(r8,"canonicalizeHostname"),rF(r7,"canonicalizePassword"),rF(ne,"canonicalizeUsername"),rF(nt,"canonicalizePathname"),rF(nr,"canonicalizePort"),rF(nn,"canonicalizeProtocol"),rF(ni,"defaultPortForProtocol"),rF(no,"protocolEncodeCallback"),rF(na,"usernameEncodeCallback"),rF(ns,"passwordEncodeCallback"),rF(nl,"hostnameEncodeCallback"),rF(nc,"ipv6HostnameEncodeCallback"),rF(nu,"portEncodeCallback"),rF(nh,"standardURLPathnameEncodeCallback"),rF(nd,"pathURLPathnameEncodeCallback"),rF(nf,"searchEncodeCallback"),rF(np,"hashEncodeCallback");var nm=class{#e;#t=[];#r={};#n=0;#i=1;#o=0;#a=0;#s=0;#l=0;#c=!1;constructor(e){this.#e=e}get result(){return this.#r}parse(){for(this.#t=rq(this.#e,!0);this.#n<this.#t.length;this.#n+=this.#i){if(this.#i=1,"END"===this.#t[this.#n].type){if(0===this.#a){this.#u(),this.#h()?this.#d(9,1):this.#f()?this.#d(8,1):this.#d(7,0);continue}if(2===this.#a){this.#p(5);continue}this.#d(10,0);break}if(this.#s>0)if(!this.#m())continue;else this.#s-=1;if(this.#g()){this.#s+=1;continue}switch(this.#a){case 0:this.#v()&&this.#p(1);break;case 1:if(this.#v()){this.#y();let e=7,t=1;this.#b()?(e=2,t=3):this.#c&&(e=2),this.#d(e,t)}break;case 2:this.#w()?this.#p(3):(this.#k()||this.#f()||this.#h())&&this.#p(5);break;case 3:this.#C()?this.#d(4,1):this.#w()&&this.#d(5,1);break;case 4:this.#w()&&this.#d(5,1);break;case 5:this.#E()?this.#l+=1:this.#x()&&(this.#l-=1),this.#A()&&!this.#l?this.#d(6,1):this.#k()?this.#d(7,0):this.#f()?this.#d(8,1):this.#h()&&this.#d(9,1);break;case 6:this.#k()?this.#d(7,0):this.#f()?this.#d(8,1):this.#h()&&this.#d(9,1);break;case 7:this.#f()?this.#d(8,1):this.#h()&&this.#d(9,1);break;case 8:this.#h()&&this.#d(9,1)}}void 0!==this.#r.hostname&&void 0===this.#r.port&&(this.#r.port="")}#d(e,t){switch(this.#a){case 0:case 2:break;case 1:this.#r.protocol=this.#_();break;case 3:this.#r.username=this.#_();break;case 4:this.#r.password=this.#_();break;case 5:this.#r.hostname=this.#_();break;case 6:this.#r.port=this.#_();break;case 7:this.#r.pathname=this.#_();break;case 8:this.#r.search=this.#_();break;case 9:this.#r.hash=this.#_()}0!==this.#a&&10!==e&&([1,2,3,4].includes(this.#a)&&[6,7,8,9].includes(e)&&(this.#r.hostname??=""),[1,2,3,4,5,6].includes(this.#a)&&[8,9].includes(e)&&(this.#r.pathname??=this.#c?"/":""),[1,2,3,4,5,6,7].includes(this.#a)&&9===e&&(this.#r.search??="")),this.#R(e,t)}#R(e,t){this.#a=e,this.#o=this.#n+t,this.#n+=t,this.#i=0}#u(){this.#n=this.#o,this.#i=0}#p(e){this.#u(),this.#a=e}#S(e){return e<0&&(e=this.#t.length-e),e<this.#t.length?this.#t[e]:this.#t[this.#t.length-1]}#T(e,t){let r=this.#S(e);return r.value===t&&("CHAR"===r.type||"ESCAPED_CHAR"===r.type||"INVALID_CHAR"===r.type)}#v(){return this.#T(this.#n,":")}#b(){return this.#T(this.#n+1,"/")&&this.#T(this.#n+2,"/")}#w(){return this.#T(this.#n,"@")}#C(){return this.#T(this.#n,":")}#A(){return this.#T(this.#n,":")}#k(){return this.#T(this.#n,"/")}#f(){if(this.#T(this.#n,"?"))return!0;if("?"!==this.#t[this.#n].value)return!1;let e=this.#S(this.#n-1);return"NAME"!==e.type&&"REGEX"!==e.type&&"CLOSE"!==e.type&&"ASTERISK"!==e.type}#h(){return this.#T(this.#n,"#")}#g(){return"OPEN"==this.#t[this.#n].type}#m(){return"CLOSE"==this.#t[this.#n].type}#E(){return this.#T(this.#n,"[")}#x(){return this.#T(this.#n,"]")}#_(){let e=this.#t[this.#n],t=this.#S(this.#o).index;return this.#e.substring(t,e.index)}#y(){let e={};Object.assign(e,rY),e.encodePart=no;let t=rG(this.#_(),void 0,e);this.#c=r3(t)}};rF(nm,"Parser");var ng=["protocol","username","password","hostname","port","pathname","search","hash"];function nv(e,t){if("string"!=typeof e)throw TypeError("parameter 1 is not of type 'string'.");let r=new URL(e,t);return{protocol:r.protocol.substring(0,r.protocol.length-1),username:r.username,password:r.password,hostname:r.hostname,port:r.port,pathname:r.pathname,search:""!==r.search?r.search.substring(1,r.search.length):void 0,hash:""!==r.hash?r.hash.substring(1,r.hash.length):void 0}}function ny(e,t){return t?nw(e):e}function nb(e,t,r){let n;if("string"==typeof t.baseURL)try{n=new URL(t.baseURL),void 0===t.protocol&&(e.protocol=ny(n.protocol.substring(0,n.protocol.length-1),r)),r||void 0!==t.protocol||void 0!==t.hostname||void 0!==t.port||void 0!==t.username||(e.username=ny(n.username,r)),r||void 0!==t.protocol||void 0!==t.hostname||void 0!==t.port||void 0!==t.username||void 0!==t.password||(e.password=ny(n.password,r)),void 0===t.protocol&&void 0===t.hostname&&(e.hostname=ny(n.hostname,r)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&(e.port=ny(n.port,r)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.pathname&&(e.pathname=ny(n.pathname,r)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.pathname&&void 0===t.search&&(e.search=ny(n.search.substring(1,n.search.length),r)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.pathname&&void 0===t.search&&void 0===t.hash&&(e.hash=ny(n.hash.substring(1,n.hash.length),r))}catch{throw TypeError(`invalid baseURL '${t.baseURL}'.`)}if("string"==typeof t.protocol&&(e.protocol=nn(t.protocol,r)),"string"==typeof t.username&&(e.username=ne(t.username,r)),"string"==typeof t.password&&(e.password=r7(t.password,r)),"string"==typeof t.hostname&&(e.hostname=r8(t.hostname,r)),"string"==typeof t.port&&(e.port=nr(t.port,e.protocol,r)),"string"==typeof t.pathname){if(e.pathname=t.pathname,n&&!r0(e.pathname,r)){let t=n.pathname.lastIndexOf("/");t>=0&&(e.pathname=ny(n.pathname.substring(0,t+1),r)+e.pathname)}e.pathname=nt(e.pathname,e.protocol,r)}return"string"==typeof t.search&&(e.search=r6(t.search,r)),"string"==typeof t.hash&&(e.hash=r9(t.hash,r)),e}function nw(e){return e.replace(/([+*?:{}()\\])/g,"\\$1")}function nk(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}function nC(e,t){t.delimiter??="/#?",t.prefixes??="./",t.sensitive??=!1,t.strict??=!1,t.end??=!0,t.start??=!0,t.endsWith="";let r=`[^${nk(t.delimiter)}]+?`,n=/[$_\u200C\u200D\p{ID_Continue}]/u,i="";for(let o=0;o<e.length;++o){let a=e[o];if(3===a.type){if(3===a.modifier){i+=nw(a.value);continue}i+=`{${nw(a.value)}}${rJ(a.modifier)}`;continue}let s=a.hasCustomName(),l=!!a.suffix.length||!!a.prefix.length&&(1!==a.prefix.length||!t.prefixes.includes(a.prefix)),c=o>0?e[o-1]:null,u=o<e.length-1?e[o+1]:null;if(!l&&s&&1===a.type&&3===a.modifier&&u&&!u.prefix.length&&!u.suffix.length)if(3===u.type){let e=u.value.length>0?u.value[0]:"";l=n.test(e)}else l=!u.hasCustomName();if(!l&&!a.prefix.length&&c&&3===c.type){let e=c.value[c.value.length-1];l=t.prefixes.includes(e)}l&&(i+="{"),i+=nw(a.prefix),s&&(i+=`:${a.name}`),2===a.type?i+=`(${a.value})`:1===a.type?s||(i+=`(${r})`):0===a.type&&(s||c&&3!==c.type&&3===c.modifier&&!l&&""===a.prefix?i+="(.*)":i+="*"),1===a.type&&s&&a.suffix.length&&n.test(a.suffix[0])&&(i+="\\"),i+=nw(a.suffix),l&&(i+="}"),3!==a.modifier&&(i+=rJ(a.modifier))}return i}rF(nv,"extractValues"),rF(ny,"processBaseURLString"),rF(nb,"applyInit"),rF(nw,"escapePatternString"),rF(nk,"escapeRegexpString"),rF(nC,"partsToPattern");var nE=class{#e;#t={};#r={};#n={};#i={};#o=!1;constructor(e={},t,r){try{let n,i;if("string"==typeof t?n=t:r=t,"string"==typeof e){let t=new nm(e);if(t.parse(),e=t.result,void 0===n&&"string"!=typeof e.protocol)throw TypeError("A base URL must be provided for a relative constructor string.");e.baseURL=n}else{if(!e||"object"!=typeof e)throw TypeError("parameter 1 is not of type 'string' and cannot convert to dictionary.");if(n)throw TypeError("parameter 1 is not of type 'string'.")}typeof r>"u"&&(r={ignoreCase:!1});let o={ignoreCase:!0===r.ignoreCase};for(i of(this.#e=nb({pathname:"*",protocol:"*",username:"*",password:"*",hostname:"*",port:"*",search:"*",hash:"*"},e,!0),ni(this.#e.protocol)===this.#e.port&&(this.#e.port=""),ng)){if(!(i in this.#e))continue;let e={},t=this.#e[i];switch(this.#r[i]=[],i){case"protocol":Object.assign(e,rY),e.encodePart=no;break;case"username":Object.assign(e,rY),e.encodePart=na;break;case"password":Object.assign(e,rY),e.encodePart=ns;break;case"hostname":Object.assign(e,rZ),r5(t)?e.encodePart=nc:e.encodePart=nl;break;case"port":Object.assign(e,rY),e.encodePart=nu;break;case"pathname":r3(this.#t.protocol)?(Object.assign(e,rQ,o),e.encodePart=nh):(Object.assign(e,rY,o),e.encodePart=nd);break;case"search":Object.assign(e,rY,o),e.encodePart=nf;break;case"hash":Object.assign(e,rY,o),e.encodePart=np}try{this.#i[i]=rW(t,e),this.#t[i]=rX(this.#i[i],this.#r[i],e),this.#n[i]=nC(this.#i[i],e),this.#o=this.#o||this.#i[i].some(e=>2===e.type)}catch{throw TypeError(`invalid ${i} pattern '${this.#e[i]}'.`)}}}catch(e){throw TypeError(`Failed to construct 'URLPattern': ${e.message}`)}}get[Symbol.toStringTag](){return"URLPattern"}test(e={},t){let r,n={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if("string"!=typeof e&&t)throw TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return!1;try{n="object"==typeof e?nb(n,e,!1):nb(n,nv(e,t),!1)}catch{return!1}for(r of ng)if(!this.#t[r].exec(n[r]))return!1;return!0}exec(e={},t){let r,n={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if("string"!=typeof e&&t)throw TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return;try{n="object"==typeof e?nb(n,e,!1):nb(n,nv(e,t),!1)}catch{return null}let i={};for(r of(t?i.inputs=[e,t]:i.inputs=[e],ng)){let e=this.#t[r].exec(n[r]);if(!e)return null;let t={};for(let[n,i]of this.#r[r].entries())if("string"==typeof i||"number"==typeof i){let r=e[n+1];t[i]=r}i[r]={input:n[r]??"",groups:t}}return i}static compareComponent(e,t,r){let n=rF((e,t)=>{for(let r of["type","modifier","prefix","value","suffix"]){if(e[r]<t[r])return -1;if(e[r]!==t[r])return 1}return 0},"comparePart"),i=new rU(3,"","","","",3),o=new rU(0,"","","","",3),a=rF((e,t)=>{let r=0;for(;r<Math.min(e.length,t.length);++r){let i=n(e[r],t[r]);if(i)return i}return e.length===t.length?0:n(e[r]??i,t[r]??i)},"comparePartList");return t.#n[e]||r.#n[e]?t.#n[e]&&!r.#n[e]?a(t.#i[e],[o]):a(!t.#n[e]&&r.#n[e]?[o]:t.#i[e],r.#i[e]):0}get protocol(){return this.#n.protocol}get username(){return this.#n.username}get password(){return this.#n.password}get hostname(){return this.#n.hostname}get port(){return this.#n.port}get pathname(){return this.#n.pathname}get search(){return this.#n.search}get hash(){return this.#n.hash}get hasRegExpGroups(){return this.#o}};rF(nE,"URLPattern"),globalThis.URLPattern||(globalThis.URLPattern=nE);var nx=Object.defineProperty;let nA=e=>`/v1/users/${e}/guides`;var n_=Object.defineProperty,nR=(e,t,r)=>t in e?n_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,nS=(e,t,r)=>nR(e,"symbol"!=typeof t?t+"":t,r);let nT="default";class nO{constructor(e){nS(this,"instance"),this.instance=e}async get(){this.instance.failIfNotAuthenticated();let e=await this.instance.client().makeRequest({method:"GET",url:`/v1/users/${this.instance.userId}`});return this.handleResponse(e)}async identify(e={}){this.instance.failIfNotAuthenticated();let t=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}`,params:e});return this.handleResponse(t)}async getAllPreferences(){this.instance.failIfNotAuthenticated();let e=await this.instance.client().makeRequest({method:"GET",url:`/v1/users/${this.instance.userId}/preferences`});return this.handleResponse(e)}async getPreferences(e={}){this.instance.failIfNotAuthenticated();let t=e.preferenceSet||nT,r=await this.instance.client().makeRequest({method:"GET",url:`/v1/users/${this.instance.userId}/preferences/${t}`,params:{tenant:e.tenant}});return this.handleResponse(r)}async setPreferences(e,t={}){this.instance.failIfNotAuthenticated();let r=t.preferenceSet||nT,n=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/preferences/${r}`,data:e});return this.handleResponse(n)}async getChannelData(e){this.instance.failIfNotAuthenticated();let t=await this.instance.client().makeRequest({method:"GET",url:`/v1/users/${this.instance.userId}/channel_data/${e.channelId}`});return this.handleResponse(t)}async setChannelData({channelId:e,channelData:t}){this.instance.failIfNotAuthenticated();let r=await this.instance.client().makeRequest({method:"PUT",url:`/v1/users/${this.instance.userId}/channel_data/${e}`,data:{data:t}});return this.handleResponse(r)}async getGuides(e,t){let r=await this.instance.client().makeRequest({method:"GET",url:`${nA(this.instance.userId)}/${e}`,params:t});return this.handleResponse(r)}async markGuideStepAs(e,t){let r=await this.instance.client().makeRequest({method:"PUT",url:`${nA(this.instance.userId)}/messages/${t.message_id}/${e}`,data:t});return this.handleResponse(r)}handleResponse(e){if("error"===e.statusCode)throw Error(e.error||e.body);return e.body}}var nL=Object.defineProperty,nj=(e,t,r)=>t in e?nL(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,nI=(e,t,r)=>nj(e,"symbol"!=typeof t?t+"":t,r);class nN{constructor(e,t={}){if(nI(this,"host"),nI(this,"apiClient",null),nI(this,"userId"),nI(this,"userToken"),nI(this,"logLevel"),nI(this,"tokenExpirationTimer",null),nI(this,"feeds",new V(this)),nI(this,"objects",new rR(this)),nI(this,"preferences",new rI(this)),nI(this,"slack",new rB(this)),nI(this,"msTeams",new rE(this)),nI(this,"user",new nO(this)),nI(this,"messages",new ry(this)),this.apiKey=e,this.host=t.host||"https://api.knock.app",this.logLevel=t.logLevel,this.log("Initialized Knock instance"),this.apiKey&&this.apiKey.startsWith("sk_"))throw Error("[Knock] You are using your secret API key on the client. Please use the public key.")}client(){return this.apiClient||(this.apiClient=this.createApiClient()),this.apiClient}authenticate(e,t,r){let n=!1;this.apiClient&&(this.userId!==e||this.userToken!==t)&&(this.log("userId or userToken changed; reinitializing connections"),this.feeds.teardownInstances(),this.teardown(),n=!0),this.userId=e,this.userToken=t,this.log(`Authenticated with userId ${e}`),this.userToken&&(null==r?void 0:r.onUserTokenExpiring)instanceof Function&&this.maybeScheduleUserTokenExpiration(r.onUserTokenExpiring,r.timeBeforeExpirationInMs),n&&(this.apiClient=this.createApiClient(),this.feeds.reinitializeInstances(),this.log("Reinitialized real-time connections"))}failIfNotAuthenticated(){if(!this.isAuthenticated())throw Error("Not authenticated. Please call `authenticate` first.")}isAuthenticated(e=!1){return e?!!(this.userId&&this.userToken):!!this.userId}teardown(){var e;this.tokenExpirationTimer&&clearTimeout(this.tokenExpirationTimer),null!=(e=this.apiClient)&&e.socket&&this.apiClient.socket.isConnected()&&this.apiClient.socket.disconnect()}log(e,t=!1){("debug"===this.logLevel||t)&&console.log(`[Knock] ${e}`)}createApiClient(){return new rp({apiKey:this.apiKey,host:this.host,userToken:this.userToken})}async maybeScheduleUserTokenExpiration(e,t=3e4){if(!this.userToken)return;let r=function(e,t){let r;if("string"!=typeof e)throw new q("Invalid token specified: must be a string");t||(t={});let n=+(!0!==t.header),i=e.split(".")[n];if("string"!=typeof i)throw new q(`Invalid token specified: missing part #${n+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(i)}catch(e){throw new q(`Invalid token specified: invalid base64 for part #${n+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new q(`Invalid token specified: invalid json for part #${n+1} (${e.message})`)}}(this.userToken),n=(r.exp??0)*1e3,i=Date.now();if(n&&n>i){let o=n-t-i;this.tokenExpirationTimer=setTimeout(async()=>{let n=await e(this.userToken,r);"string"==typeof n&&this.authenticate(this.userId,n,{onUserTokenExpiring:e,timeBeforeExpirationInMs:t})},o)}}}var nP=r(6356);function nM(e){let t=(0,p.useRef)();return(0,p.useMemo)(()=>{let r=t.current;return r&&nP(e,r)?r:(t.current=e,e)},[e])}function nB(e,t,r,n={}){e.authenticate(t,r,{onUserTokenExpiring:null==n?void 0:n.onUserTokenExpiring,timeBeforeExpirationInMs:null==n?void 0:n.timeBeforeExpirationInMs})}let nD=p.createContext(null),nF=({apiKey:e,host:t,logLevel:r,userId:n,userToken:i,onUserTokenExpiring:o,timeBeforeExpirationInMs:a,children:s,i18n:l})=>{let c=function(e,t,r,n={}){let i=p.useRef(),o=nM(n);return p.useMemo(()=>{let n=i.current;if(n&&n.isAuthenticated()&&(n.userId!==t||n.userToken!==r))return nB(n,t,r,o),n;n&&n.teardown();let a=new nN(e,{host:o.host,logLevel:o.logLevel});return nB(a,t,r,o),i.current=a,a},[e,t,r,o])}(e??"",n,i,p.useMemo(()=>({host:t,onUserTokenExpiring:o,timeBeforeExpirationInMs:a,logLevel:r}),[t,o,a,r]));return p.createElement(nD.Provider,{value:{knock:c}},p.createElement(v,{i18n:l},s))},nU=()=>{let e=p.useContext(nD);if(!e)throw Error("useKnockClient must be used within a KnockProvider");return e.knock};var nz=(e=>(e.All="all",e.Read="read",e.Unseen="unseen",e.Unread="unread",e))(nz||{});let n$=Symbol.for("constructDateFrom");function nV(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&n$ in e?e[n$](t):e instanceof Date?new e.constructor(t):new Date(t)}function nq(e,t){return nV(t||e,e)}let nW={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},nH=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,nK=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,nG=/^([+-])(\d{2})(?::?(\d{2}))?$/;function nJ(e){return e?parseInt(e):1}function nX(e){return e&&parseFloat(e.replace(",","."))||0}let nY=[31,null,31,30,31,30,31,31,30,31,30,31];function nZ(e){return e%400==0||e%4==0&&e%100!=0}function nQ(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];let i=nV.bind(null,e||r.find(e=>"object"==typeof e));return r.map(i)}function n0(e){let t=nq(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}function n1(e,t){let r=nq(e,null==t?void 0:t.in);return r.setHours(0,0,0,0),r}function n2(e,t,r){let[n,i]=nQ(null==r?void 0:r.in,e,t),o=n1(n),a=n1(i);return Math.round((o-n0(o)-(a-n0(a)))/864e5)}function n5(e,t,r){let[n,i]=nQ(null==r?void 0:r.in,e,t);return 12*(n.getFullYear()-i.getFullYear())+(n.getMonth()-i.getMonth())}function n4(e,t){return Math.trunc(nq(e,null==t?void 0:t.in).getMonth()/3)+1}function n3(e,t,r){let[n,i]=nQ(null==r?void 0:r.in,e,t);return 4*(n.getFullYear()-i.getFullYear())+(n4(n)-n4(i))}let n9={};function n6(e,t){var r,n,i,o,a,s,l,c;let u=null!=(c=null!=(l=null!=(s=null!=(a=null==t?void 0:t.weekStartsOn)?a:null==t||null==(n=t.locale)||null==(r=n.options)?void 0:r.weekStartsOn)?s:n9.weekStartsOn)?l:null==(o=n9.locale)||null==(i=o.options)?void 0:i.weekStartsOn)?c:0,h=nq(e,null==t?void 0:t.in),d=h.getDay();return h.setDate(h.getDate()-(7*(d<u)+d-u)),h.setHours(0,0,0,0),h}function n8(e,t,r){let[n,i]=nQ(null==r?void 0:r.in,e,t),o=n6(n,r),a=n6(i,r);return Math.round((o-n0(o)-(a-n0(a)))/6048e5)}function n7(e,t,r){let[n,i]=nQ(null==r?void 0:r.in,e,t);return n.getFullYear()-i.getFullYear()}function ie(e){return t=>{let r=(e?Math[e]:Math.trunc)(t);return 0===r?0:r}}function it(e,t,r){let[n,i]=nQ(null==r?void 0:r.in,e,t);return ie(null==r?void 0:r.roundingMethod)((n-i)/36e5)}function ir(e,t,r){let n=(nq(e)-nq(t))/6e4;return ie(null==r?void 0:r.roundingMethod)(n)}function ii(e,t,r){let n=(nq(e)-nq(t))/1e3;return ie(null==r?void 0:r.roundingMethod)(n)}function io(e,t){return void 0!==e?e:t}var ia=r(80375);let{useDebugValue:is}=p,{useSyncExternalStoreWithSelector:il}=ia,ic=!1,iu=e=>e;function ih(e,t=iu,r){r&&!ic&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),ic=!0);let n=il(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return is(n),n}let id=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?createStore(e):e,r=(e,r)=>ih(t,e,r);return Object.assign(r,t),r},ip=p.createContext(void 0),im=({feedId:e,children:t,defaultFeedOptions:r={},colorMode:n="light"})=>{var i;let o;try{o=nU()}catch{throw Error("KnockFeedProvider must be used within a KnockProvider.")}let a=function(e,t,r={}){let n=(0,p.useRef)(),i=nM(r);return(0,p.useMemo)(()=>(n.current&&n.current.dispose(),n.current=e.feeds.initialize(t,i),n.current.store.subscribe(e=>{var t;return null==(t=null==n?void 0:n.current)?void 0:t.store.setState(e)}),n.current.listenForUpdates(),n.current),[e,t,i])}(o,e??"",r),s=(i=a,e=>ih(i.store,e??(e=>e)));return p.createElement(ip.Provider,{key:function(e,t,r={}){return[e,t,r.source,r.tenant,r.has_tenant,r.archived].filter(e=>null!=e).join("-")}(o.userId,e??"",r),value:{knock:o,feedClient:a,useFeedStore:s,colorMode:n}},t)},ig=()=>{let e=p.useContext(ip);if(!e)throw Error("useKnockFeed must be used within a KnockFeedProvider");return e},iv=p.createContext(void 0);function iy(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[r,n]of e)if(!t.has(r)||!Object.is(n,t.get(r)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let r of e)if(!t.has(r))return!1;return!0}let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let n=0;n<r.length;n++)if(!Object.prototype.hasOwnProperty.call(t,r[n])||!Object.is(e[r[n]],t[r[n]]))return!1;return!0}let ib=()=>{let e=p.useContext(iv);if(!e)throw Error("useGuide must be used within a KnockGuideProvider");return e};function iw(){let{translations:e,locale:t}=(0,p.useContext)(g);return{locale:t,t:t=>e[t]||m.en.translations[t]}}p.createContext(null);let ik="MS_TEAMS_TEAMS";p.createContext(null);let iC="SLACK_CHANNELS",iE=({color:e="rgba(0,0,0,0.4)",speed:t="medium",gap:r=4,thickness:n=4,size:i="1em",...o})=>p.createElement("svg",{height:i,width:i,...o,style:{animationDuration:`${"fast"===t?600:"slow"===t?900:750}ms`},className:"__react-svg-spinner_circle",role:"img","aria-labelledby":"title desc",viewBox:"0 0 32 32"},p.createElement("title",{id:"title"},"Circle loading spinner"),p.createElement("desc",{id:"desc"},'Image of a partial circle indicating "loading."'),p.createElement("style",{dangerouslySetInnerHTML:{__html:`
      .__react-svg-spinner_circle{
          transition-property: transform;
          animation-name: __react-svg-spinner_infinite-spin;
          animation-iteration-count: infinite;
          animation-timing-function: linear;
      }
      @keyframes __react-svg-spinner_infinite-spin {
          from {transform: rotate(0deg)}
          to {transform: rotate(360deg)}
      }
    `}}),p.createElement("circle",{role:"presentation",cx:16,cy:16,r:14-n/2,stroke:e,fill:"none",strokeWidth:n,strokeDasharray:2*Math.PI*(11-r),strokeLinecap:"round"})),ix=({hasLabel:e})=>p.createElement("div",{className:`rnf-button-spinner rnf-button-spinner--${e?"with-label":"without-label"}`},p.createElement(iE,null)),iA=({variant:e="primary",loadingText:t,isLoading:r=!1,isDisabled:n=!1,isFullWidth:i=!1,onClick:o,children:a})=>{let{colorMode:s}=ig(),l=["rnf-button",`rnf-button--${e}`,i?"rnf-button--full-width":"",r?"rnf-button--is-loading":"",`rnf-button--${s}`].join(" "),c=t||p.createElement("span",{className:"rnf-button__button-text-hidden"},a);return p.createElement("button",{onClick:o,className:l,disabled:r||n,type:"button"},r&&p.createElement(ix,{hasLabel:!!t}),r?c:a)},i_=({children:e})=>p.createElement("div",{className:"rnf-button-group"},e),iR=({width:e=24,height:t=24,"aria-hidden":r})=>p.createElement("svg",{width:e,viewBox:"0 0 24 24",fill:"none",height:t,"aria-hidden":r},p.createElement("path",{d:"M20.0474 16.4728C18.8436 14.9996 17.9938 14.2496 17.9938 10.1879C17.9938 6.46832 16.0944 5.14317 14.5311 4.49957C14.3235 4.41426 14.128 4.21832 14.0647 4.00504C13.7905 3.07176 13.0217 2.24957 11.9999 2.24957C10.978 2.24957 10.2088 3.07223 9.93736 4.00598C9.87408 4.2216 9.67861 4.41426 9.47096 4.49957C7.9058 5.1441 6.0083 6.46457 6.0083 10.1879C6.00596 14.2496 5.15611 14.9996 3.95237 16.4728C3.45362 17.0832 3.89049 17.9996 4.76283 17.9996H19.2416C20.1092 17.9996 20.5433 17.0803 20.0474 16.4728Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),p.createElement("path",{d:"M14.9999 17.9988V18.7488C14.9999 19.5445 14.6838 20.3075 14.1212 20.8701C13.5586 21.4327 12.7955 21.7488 11.9999 21.7488C11.2042 21.7488 10.4412 21.4327 9.87856 20.8701C9.31595 20.3075 8.99988 19.5445 8.99988 18.7488V17.9988",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})),iS=({width:e=16,height:t=16,"aria-hidden":r})=>p.createElement("svg",{width:e,height:t,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":r},p.createElement("path",{d:"M14 8.00012C14 4.68762 11.3125 2.00012 7.99997 2.00012C4.68747 2.00012 1.99997 4.68762 1.99997 8.00012C1.99997 11.3126 4.68747 14.0001 7.99997 14.0001C11.3125 14.0001 14 11.3126 14 8.00012Z",stroke:"currentColor",strokeWidth:"1.5",strokeMiterlimit:"10"}),p.createElement("path",{d:"M10.9999 5.5004L6.79994 10.5004L4.99994 8.5004",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})),iT=({width:e=8,height:t=6,"aria-hidden":r})=>p.createElement("svg",{width:e,height:t,viewBox:"0 0 8 6",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":r},p.createElement("path",{d:"M1.74994 1.87512L3.99994 4.12512L6.24994 1.87512",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})),iO=({width:e=14,height:t=14,"aria-hidden":r})=>p.createElement("svg",{width:e,height:t,viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":r},p.createElement("path",{d:"M7.00012 0.499939C3.41606 0.499939 0.500122 3.41588 0.500122 6.99994C0.500122 10.584 3.41606 13.4999 7.00012 13.4999C10.5842 13.4999 13.5001 10.584 13.5001 6.99994C13.5001 3.41588 10.5842 0.499939 7.00012 0.499939ZM9.35356 8.6465C9.40194 8.69247 9.44063 8.74766 9.46735 8.80881C9.49407 8.86997 9.50828 8.93585 9.50913 9.00259C9.50999 9.06932 9.49747 9.13555 9.47233 9.19737C9.44718 9.25919 9.40992 9.31535 9.36273 9.36254C9.31553 9.40973 9.25937 9.447 9.19755 9.47214C9.13573 9.49729 9.0695 9.5098 9.00277 9.50895C8.93604 9.50809 8.87015 9.49389 8.809 9.46717C8.74784 9.44045 8.69265 9.40176 8.64668 9.35337L7.00012 7.70712L5.35356 9.35337C5.25903 9.44318 5.13315 9.49251 5.00277 9.49084C4.87239 9.48918 4.74782 9.43664 4.65562 9.34444C4.56342 9.25224 4.51088 9.12767 4.50921 8.99729C4.50755 8.86691 4.55687 8.74103 4.64668 8.6465L6.29293 6.99994L4.64668 5.35338C4.55687 5.25884 4.50755 5.13297 4.50921 5.00259C4.51088 4.87221 4.56342 4.74764 4.65562 4.65544C4.74782 4.56324 4.87239 4.5107 5.00277 4.50903C5.13315 4.50736 5.25903 4.55669 5.35356 4.6465L7.00012 6.29275L8.64668 4.6465C8.74121 4.55669 8.86709 4.50736 8.99747 4.50903C9.12785 4.5107 9.25242 4.56324 9.34462 4.65544C9.43682 4.74764 9.48936 4.87221 9.49103 5.00259C9.4927 5.13297 9.44337 5.25884 9.35356 5.35338L7.70731 6.99994L9.35356 8.6465Z",fill:"currentColor"}));var iL=r(80797);let ij=()=>{},iI=()=>{let{colorMode:e}=ig(),{t}=iw();return p.createElement("div",{className:`rnf-empty-feed rnf-empty-feed--${e}`},p.createElement("div",{className:"rnf-empty-feed__inner"},p.createElement("h2",{className:"rnf-empty-feed__header"},t("emptyFeedTitle")),p.createElement("p",{className:"rnf-empty-feed__body"},t("emptyFeedBody"))))};function iN(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function iP(e){var t=iN(e).Element;return e instanceof t||e instanceof Element}function iM(e){var t=iN(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function iB(e){if("undefined"==typeof ShadowRoot)return!1;var t=iN(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var iD=Math.max,iF=Math.min,iU=Math.round;function iz(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function i$(){return!/^((?!chrome|android).)*safari/i.test(iz())}function iV(e,t,r){void 0===t&&(t=!1),void 0===r&&(r=!1);var n=e.getBoundingClientRect(),i=1,o=1;t&&iM(e)&&(i=e.offsetWidth>0&&iU(n.width)/e.offsetWidth||1,o=e.offsetHeight>0&&iU(n.height)/e.offsetHeight||1);var a=(iP(e)?iN(e):window).visualViewport,s=!i$()&&r,l=(n.left+(s&&a?a.offsetLeft:0))/i,c=(n.top+(s&&a?a.offsetTop:0))/o,u=n.width/i,h=n.height/o;return{width:u,height:h,top:c,right:l+u,bottom:c+h,left:l,x:l,y:c}}function iq(e){var t=iN(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function iW(e){return e?(e.nodeName||"").toLowerCase():null}function iH(e){return((iP(e)?e.ownerDocument:e.document)||window.document).documentElement}function iK(e){return iV(iH(e)).left+iq(e).scrollLeft}function iG(e){return iN(e).getComputedStyle(e)}function iJ(e){var t=iG(e),r=t.overflow,n=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+i+n)}function iX(e){var t=iV(e),r=e.offsetWidth,n=e.offsetHeight;return 1>=Math.abs(t.width-r)&&(r=t.width),1>=Math.abs(t.height-n)&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function iY(e){return"html"===iW(e)?e:e.assignedSlot||e.parentNode||(iB(e)?e.host:null)||iH(e)}function iZ(e,t){void 0===t&&(t=[]);var r,n=function e(t){return["html","body","#document"].indexOf(iW(t))>=0?t.ownerDocument.body:iM(t)&&iJ(t)?t:e(iY(t))}(e),i=n===(null==(r=e.ownerDocument)?void 0:r.body),o=iN(n),a=i?[o].concat(o.visualViewport||[],iJ(n)?n:[]):n,s=t.concat(a);return i?s:s.concat(iZ(iY(a)))}function iQ(e){return iM(e)&&"fixed"!==iG(e).position?e.offsetParent:null}function i0(e){for(var t=iN(e),r=iQ(e);r&&["table","td","th"].indexOf(iW(r))>=0&&"static"===iG(r).position;)r=iQ(r);return r&&("html"===iW(r)||"body"===iW(r)&&"static"===iG(r).position)?t:r||function(e){var t=/firefox/i.test(iz());if(/Trident/i.test(iz())&&iM(e)&&"fixed"===iG(e).position)return null;var r=iY(e);for(iB(r)&&(r=r.host);iM(r)&&0>["html","body"].indexOf(iW(r));){var n=iG(r);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||t&&"filter"===n.willChange||t&&n.filter&&"none"!==n.filter)return r;r=r.parentNode}return null}(e)||t}var i1="bottom",i2="right",i5="left",i4="auto",i3=["top",i1,i2,i5],i9="start",i6="viewport",i8="popper",i7=i3.reduce(function(e,t){return e.concat([t+"-"+i9,t+"-end"])},[]),oe=[].concat(i3,[i4]).reduce(function(e,t){return e.concat([t,t+"-"+i9,t+"-end"])},[]),ot=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],or={placement:"bottom",modifiers:[],strategy:"absolute"};function on(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var oi={passive:!0};function oo(e){return e.split("-")[0]}function oa(e){return e.split("-")[1]}function os(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function ol(e){var t,r=e.reference,n=e.element,i=e.placement,o=i?oo(i):null,a=i?oa(i):null,s=r.x+r.width/2-n.width/2,l=r.y+r.height/2-n.height/2;switch(o){case"top":t={x:s,y:r.y-n.height};break;case i1:t={x:s,y:r.y+r.height};break;case i2:t={x:r.x+r.width,y:l};break;case i5:t={x:r.x-n.width,y:l};break;default:t={x:r.x,y:r.y}}var c=o?os(o):null;if(null!=c){var u="y"===c?"height":"width";switch(a){case i9:t[c]=t[c]-(r[u]/2-n[u]/2);break;case"end":t[c]=t[c]+(r[u]/2-n[u]/2)}}return t}var oc={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ou(e){var t,r,n,i,o,a,s,l=e.popper,c=e.popperRect,u=e.placement,h=e.variation,d=e.offsets,f=e.position,p=e.gpuAcceleration,m=e.adaptive,g=e.roundOffsets,v=e.isFixed,y=d.x,b=void 0===y?0:y,w=d.y,k=void 0===w?0:w,C="function"==typeof g?g({x:b,y:k}):{x:b,y:k};b=C.x,k=C.y;var E=d.hasOwnProperty("x"),x=d.hasOwnProperty("y"),A=i5,_="top",R=window;if(m){var S=i0(l),T="clientHeight",O="clientWidth";S===iN(l)&&"static"!==iG(S=iH(l)).position&&"absolute"===f&&(T="scrollHeight",O="scrollWidth"),("top"===u||(u===i5||u===i2)&&"end"===h)&&(_=i1,k-=(v&&S===R&&R.visualViewport?R.visualViewport.height:S[T])-c.height,k*=p?1:-1),(u===i5||("top"===u||u===i1)&&"end"===h)&&(A=i2,b-=(v&&S===R&&R.visualViewport?R.visualViewport.width:S[O])-c.width,b*=p?1:-1)}var L=Object.assign({position:f},m&&oc),j=!0===g?(t={x:b,y:k},r=iN(l),n=t.x,i=t.y,{x:iU(n*(o=r.devicePixelRatio||1))/o||0,y:iU(i*o)/o||0}):{x:b,y:k};return(b=j.x,k=j.y,p)?Object.assign({},L,((s={})[_]=x?"0":"",s[A]=E?"0":"",s.transform=1>=(R.devicePixelRatio||1)?"translate("+b+"px, "+k+"px)":"translate3d("+b+"px, "+k+"px, 0)",s)):Object.assign({},L,((a={})[_]=x?k+"px":"",a[A]=E?b+"px":"",a.transform="",a))}var oh={left:"right",right:"left",bottom:"top",top:"bottom"};function od(e){return e.replace(/left|right|bottom|top/g,function(e){return oh[e]})}var of={start:"end",end:"start"};function op(e){return e.replace(/start|end/g,function(e){return of[e]})}function om(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&iB(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function og(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ov(e,t,r){var n,i,o,a,s,l,c,u,h,d;return t===i6?og(function(e,t){var r=iN(e),n=iH(e),i=r.visualViewport,o=n.clientWidth,a=n.clientHeight,s=0,l=0;if(i){o=i.width,a=i.height;var c=i$();(c||!c&&"fixed"===t)&&(s=i.offsetLeft,l=i.offsetTop)}return{width:o,height:a,x:s+iK(e),y:l}}(e,r)):iP(t)?((n=iV(t,!1,"fixed"===r)).top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n):og((i=iH(e),a=iH(i),s=iq(i),l=null==(o=i.ownerDocument)?void 0:o.body,c=iD(a.scrollWidth,a.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),u=iD(a.scrollHeight,a.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),h=-s.scrollLeft+iK(i),d=-s.scrollTop,"rtl"===iG(l||a).direction&&(h+=iD(a.clientWidth,l?l.clientWidth:0)-c),{width:c,height:u,x:h,y:d}))}function oy(){return{top:0,right:0,bottom:0,left:0}}function ob(e){return Object.assign({},oy(),e)}function ow(e,t){return t.reduce(function(t,r){return t[r]=e,t},{})}function ok(e,t){void 0===t&&(t={});var r,n,i,o,a,s,l,c,u=t,h=u.placement,d=void 0===h?e.placement:h,f=u.strategy,p=void 0===f?e.strategy:f,m=u.boundary,g=u.rootBoundary,v=u.elementContext,y=void 0===v?i8:v,b=u.altBoundary,w=u.padding,k=void 0===w?0:w,C=ob("number"!=typeof k?k:ow(k,i3)),E=e.rects.popper,x=e.elements[void 0!==b&&b?y===i8?"reference":i8:y],A=(r=iP(x)?x:x.contextElement||iH(e.elements.popper),n=void 0===m?"clippingParents":m,i=void 0===g?i6:g,l=(s=[].concat("clippingParents"===n?(o=iZ(iY(r)),!iP(a=["absolute","fixed"].indexOf(iG(r).position)>=0&&iM(r)?i0(r):r)?[]:o.filter(function(e){return iP(e)&&om(e,a)&&"body"!==iW(e)})):[].concat(n),[i]))[0],(c=s.reduce(function(e,t){var n=ov(r,t,p);return e.top=iD(n.top,e.top),e.right=iF(n.right,e.right),e.bottom=iF(n.bottom,e.bottom),e.left=iD(n.left,e.left),e},ov(r,l,p))).width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c),_=iV(e.elements.reference),R=ol({reference:_,element:E,strategy:"absolute",placement:d}),S=og(Object.assign({},E,R)),T=y===i8?S:_,O={top:A.top-T.top+C.top,bottom:T.bottom-A.bottom+C.bottom,left:A.left-T.left+C.left,right:T.right-A.right+C.right},L=e.modifiersData.offset;if(y===i8&&L){var j=L[d];Object.keys(O).forEach(function(e){var t=[i2,i1].indexOf(e)>=0?1:-1,r=["top",i1].indexOf(e)>=0?"y":"x";O[e]+=j[r]*t})}return O}function oC(e,t,r){return iD(e,iF(t,r))}function oE(e,t,r){return void 0===r&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function ox(e){return["top",i2,i1,i5].some(function(t){return e[t]>=0})}var oA=function(e){void 0===e&&(e={});var t=e,r=t.defaultModifiers,n=void 0===r?[]:r,i=t.defaultOptions,o=void 0===i?or:i;return function(e,t,r){void 0===r&&(r=o);var i,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},or,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:s,setOptions:function(r){var i,a,c,d,f,p,m="function"==typeof r?r(s.options):r;h(),s.options=Object.assign({},o,s.options,m),s.scrollParents={reference:iP(e)?iZ(e):e.contextElement?iZ(e.contextElement):[],popper:iZ(t)};var g=(a=Object.keys(i=[].concat(n,s.options.modifiers).reduce(function(e,t){var r=e[t.name];return e[t.name]=r?Object.assign({},r,t,{options:Object.assign({},r.options,t.options),data:Object.assign({},r.data,t.data)}):t,e},{})).map(function(e){return i[e]}),c=new Map,d=new Set,f=[],a.forEach(function(e){c.set(e.name,e)}),a.forEach(function(e){d.has(e.name)||function e(t){d.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!d.has(t)){var r=c.get(t);r&&e(r)}}),f.push(t)}(e)}),p=f,ot.reduce(function(e,t){return e.concat(p.filter(function(e){return e.phase===t}))},[]));return s.orderedModifiers=g.filter(function(e){return e.enabled}),s.orderedModifiers.forEach(function(e){var t=e.name,r=e.options,n=e.effect;if("function"==typeof n){var i=n({state:s,name:t,instance:u,options:void 0===r?{}:r});l.push(i||function(){})}}),u.update()},forceUpdate:function(){if(!c){var e=s.elements,t=e.reference,r=e.popper;if(on(t,r)){s.rects={reference:(n=i0(r),i="fixed"===s.options.strategy,o=iM(n),d=iM(n)&&(l=iU((a=n.getBoundingClientRect()).width)/n.offsetWidth||1,h=iU(a.height)/n.offsetHeight||1,1!==l||1!==h),f=iH(n),p=iV(t,d,i),m={scrollLeft:0,scrollTop:0},g={x:0,y:0},(o||!o&&!i)&&(("body"!==iW(n)||iJ(f))&&(m=function(e){return e!==iN(e)&&iM(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:iq(e)}(n)),iM(n)?(g=iV(n,!0),g.x+=n.clientLeft,g.y+=n.clientTop):f&&(g.x=iK(f))),{x:p.left+m.scrollLeft-g.x,y:p.top+m.scrollTop-g.y,width:p.width,height:p.height}),popper:iX(r)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(e){return s.modifiersData[e.name]=Object.assign({},e.data)});for(var n,i,o,a,l,h,d,f,p,m,g,v=0;v<s.orderedModifiers.length;v++){if(!0===s.reset){s.reset=!1,v=-1;continue}var y=s.orderedModifiers[v],b=y.fn,w=y.options,k=void 0===w?{}:w,C=y.name;"function"==typeof b&&(s=b({state:s,options:k,name:C,instance:u})||s)}}}},update:(i=function(){return new Promise(function(e){u.forceUpdate(),e(s)})},function(){return a||(a=new Promise(function(e){Promise.resolve().then(function(){a=void 0,e(i())})})),a}),destroy:function(){h(),c=!0}};if(!on(e,t))return u;function h(){l.forEach(function(e){return e()}),l=[]}return u.setOptions(r).then(function(e){!c&&r.onFirstUpdate&&r.onFirstUpdate(e)}),u}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,r=e.instance,n=e.options,i=n.scroll,o=void 0===i||i,a=n.resize,s=void 0===a||a,l=iN(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach(function(e){e.addEventListener("scroll",r.update,oi)}),s&&l.addEventListener("resize",r.update,oi),function(){o&&c.forEach(function(e){e.removeEventListener("scroll",r.update,oi)}),s&&l.removeEventListener("resize",r.update,oi)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,r=e.name;t.modifiersData[r]=ol({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,r=e.options,n=r.gpuAcceleration,i=r.adaptive,o=r.roundOffsets,a=void 0===o||o,s={placement:oo(t.placement),variation:oa(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===n||n,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ou(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===i||i,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ou(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var r=t.styles[e]||{},n=t.attributes[e]||{},i=t.elements[e];iM(i)&&iW(i)&&(Object.assign(i.style,r),Object.keys(n).forEach(function(e){var t=n[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(e){var n=t.elements[e],i=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:r[e]).reduce(function(e,t){return e[t]="",e},{});iM(n)&&iW(n)&&(Object.assign(n.style,o),Object.keys(i).forEach(function(e){n.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,r=e.options,n=e.name,i=r.offset,o=void 0===i?[0,0]:i,a=oe.reduce(function(e,r){var n,i,a,s,l,c;return e[r]=(n=t.rects,a=[i5,"top"].indexOf(i=oo(r))>=0?-1:1,l=(s="function"==typeof o?o(Object.assign({},n,{placement:r})):o)[0],c=s[1],l=l||0,c=(c||0)*a,[i5,i2].indexOf(i)>=0?{x:c,y:l}:{x:l,y:c}),e},{}),s=a[t.placement],l=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var i=r.mainAxis,o=void 0===i||i,a=r.altAxis,s=void 0===a||a,l=r.fallbackPlacements,c=r.padding,u=r.boundary,h=r.rootBoundary,d=r.altBoundary,f=r.flipVariations,p=void 0===f||f,m=r.allowedAutoPlacements,g=t.options.placement,v=oo(g)===g,y=l||(v||!p?[od(g)]:function(e){if(oo(e)===i4)return[];var t=od(e);return[op(e),t,op(t)]}(g)),b=[g].concat(y).reduce(function(e,r){var n,i,o,a,s,l,d,f,g,v,y,b;return e.concat(oo(r)===i4?(i=(n={placement:r,boundary:u,rootBoundary:h,padding:c,flipVariations:p,allowedAutoPlacements:m}).placement,o=n.boundary,a=n.rootBoundary,s=n.padding,l=n.flipVariations,f=void 0===(d=n.allowedAutoPlacements)?oe:d,0===(y=(v=(g=oa(i))?l?i7:i7.filter(function(e){return oa(e)===g}):i3).filter(function(e){return f.indexOf(e)>=0})).length&&(y=v),Object.keys(b=y.reduce(function(e,r){return e[r]=ok(t,{placement:r,boundary:o,rootBoundary:a,padding:s})[oo(r)],e},{})).sort(function(e,t){return b[e]-b[t]})):r)},[]),w=t.rects.reference,k=t.rects.popper,C=new Map,E=!0,x=b[0],A=0;A<b.length;A++){var _=b[A],R=oo(_),S=oa(_)===i9,T=["top",i1].indexOf(R)>=0,O=T?"width":"height",L=ok(t,{placement:_,boundary:u,rootBoundary:h,altBoundary:d,padding:c}),j=T?S?i2:i5:S?i1:"top";w[O]>k[O]&&(j=od(j));var I=od(j),N=[];if(o&&N.push(L[R]<=0),s&&N.push(L[j]<=0,L[I]<=0),N.every(function(e){return e})){x=_,E=!1;break}C.set(_,N)}if(E)for(var P=p?3:1,M=function(e){var t=b.find(function(t){var r=C.get(t);if(r)return r.slice(0,e).every(function(e){return e})});if(t)return x=t,"break"},B=P;B>0&&"break"!==M(B);B--);t.placement!==x&&(t.modifiersData[n]._skip=!0,t.placement=x,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name,i=r.mainAxis,o=r.altAxis,a=r.boundary,s=r.rootBoundary,l=r.altBoundary,c=r.padding,u=r.tether,h=void 0===u||u,d=r.tetherOffset,f=void 0===d?0:d,p=ok(t,{boundary:a,rootBoundary:s,padding:c,altBoundary:l}),m=oo(t.placement),g=oa(t.placement),v=!g,y=os(m),b="x"===y?"y":"x",w=t.modifiersData.popperOffsets,k=t.rects.reference,C=t.rects.popper,E="function"==typeof f?f(Object.assign({},t.rects,{placement:t.placement})):f,x="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),A=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,_={x:0,y:0};if(w){if(void 0===i||i){var R,S="y"===y?"top":i5,T="y"===y?i1:i2,O="y"===y?"height":"width",L=w[y],j=L+p[S],I=L-p[T],N=h?-C[O]/2:0,P=g===i9?k[O]:C[O],M=g===i9?-C[O]:-k[O],B=t.elements.arrow,D=h&&B?iX(B):{width:0,height:0},F=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:oy(),U=F[S],z=F[T],$=oC(0,k[O],D[O]),V=v?k[O]/2-N-$-U-x.mainAxis:P-$-U-x.mainAxis,q=v?-k[O]/2+N+$+z+x.mainAxis:M+$+z+x.mainAxis,W=t.elements.arrow&&i0(t.elements.arrow),H=W?"y"===y?W.clientTop||0:W.clientLeft||0:0,K=null!=(R=null==A?void 0:A[y])?R:0,G=oC(h?iF(j,L+V-K-H):j,L,h?iD(I,L+q-K):I);w[y]=G,_[y]=G-L}if(void 0!==o&&o){var J,X,Y="x"===y?"top":i5,Z="x"===y?i1:i2,Q=w[b],ee="y"===b?"height":"width",et=Q+p[Y],er=Q-p[Z],en=-1!==["top",i5].indexOf(m),ei=null!=(X=null==A?void 0:A[b])?X:0,eo=en?et:Q-k[ee]-C[ee]-ei+x.altAxis,ea=en?Q+k[ee]+C[ee]-ei-x.altAxis:er,es=h&&en?(J=oC(eo,Q,ea))>ea?ea:J:oC(h?eo:et,Q,h?ea:er);w[b]=es,_[b]=es-Q}t.modifiersData[n]=_}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,r=e.state,n=e.name,i=e.options,o=r.elements.arrow,a=r.modifiersData.popperOffsets,s=oo(r.placement),l=os(s),c=[i5,i2].indexOf(s)>=0?"height":"width";if(o&&a){var u,h=(u=i.padding,ob("number"!=typeof(u="function"==typeof u?u(Object.assign({},r.rects,{placement:r.placement})):u)?u:ow(u,i3))),d=iX(o),f="y"===l?"top":i5,p="y"===l?i1:i2,m=r.rects.reference[c]+r.rects.reference[l]-a[l]-r.rects.popper[c],g=a[l]-r.rects.reference[l],v=i0(o),y=v?"y"===l?v.clientHeight||0:v.clientWidth||0:0,b=h[f],w=y-d[c]-h[p],k=y/2-d[c]/2+(m/2-g/2),C=oC(b,k,w);r.modifiersData[n]=((t={})[l]=C,t.centerOffset=C-k,t)}},effect:function(e){var t=e.state,r=e.options.element,n=void 0===r?"[data-popper-arrow]":r;if(null!=n)("string"!=typeof n||(n=t.elements.popper.querySelector(n)))&&om(t.elements.popper,n)&&(t.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,r=e.name,n=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,a=ok(t,{elementContext:"reference"}),s=ok(t,{altBoundary:!0}),l=oE(a,n),c=oE(s,i,o),u=ox(l),h=ox(c);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":h})}}]});let o_=({item:e})=>{let{colorMode:t,feedClient:r}=ig(),{t:n}=iw(),[i,o]=(0,p.useState)(!1),a=(0,p.useRef)(null),s=(0,p.useRef)(null),l=(0,p.useCallback)(t=>{t.preventDefault(),t.stopPropagation(),r.markAsArchived(e)},[e]);return(0,p.useEffect)(()=>{if(a.current&&s.current&&i){let e=oA(a.current,s.current,{placement:"top-end",modifiers:[{name:"offset",options:{offset:[0,8]}}]});return()=>{e.destroy()}}},[i]),p.createElement("button",{ref:a,onClick:l,onMouseEnter:()=>o(!0),onMouseLeave:()=>o(!1),type:"button","aria-label":n("archiveNotification"),className:`rnf-archive-notification-btn rnf-archive-notification-btn--${t}`},p.createElement(iO,{"aria-hidden":!0}),i&&p.createElement("div",{ref:s,className:`rnf-tooltip rnf-tooltip--${t}`},n("archiveNotification")))},oR=({name:e,src:t})=>p.createElement("div",{className:"rnf-avatar"},t?p.createElement("img",{src:t,alt:e,className:"rnf-avatar__image"}):p.createElement("span",{className:"rnf-avatar__initials"},function(e){let[t,r]=e.split(" ");return t&&r?`${t.charAt(0)}${r.charAt(0)}`:t?t.charAt(0):""}(e)));function oS(e){e&&""!==e&&setTimeout(()=>window.location.assign(e),200)}let oT=p.forwardRef(({item:e,onItemClick:t,onButtonClick:r,avatar:n,children:i,archiveButton:o},a)=>{var s;let{feedClient:l,colorMode:c}=ig(),{locale:u}=iw(),h=(0,p.useMemo)(()=>e.blocks.reduce((e,t)=>({...e,[t.name]:t}),{}),[e]),d=null==(s=h.action_url)?void 0:s.rendered,f=h.actions,m=p.useCallback(()=>(l.markAsInteracted(e,{type:"cell_click",action:d}),t?t(e):oS(d)),[e,d,t,l]),g=p.useCallback((t,n)=>(l.markAsInteracted(e,{type:"button_click",name:n.name,label:n.label,action:n.action}),r?r(e,n):oS(n.action)),[r,l,e]),v=p.useCallback(e=>{"Enter"===e.key&&(e.stopPropagation(),m())},[m]),y=e.actors[0];return p.createElement("div",{ref:a,className:`rnf-notification-cell rnf-notification-cell--${c}`,onClick:m,onKeyDown:v,tabIndex:0},p.createElement("div",{className:"rnf-notification-cell__inner"},!e.read_at&&p.createElement("div",{className:"rnf-notification-cell__unread-dot"}),io(n,y&&"name"in y&&y.name&&p.createElement(oR,{name:y.name,src:y.avatar})),p.createElement("div",{className:"rnf-notification-cell__content-outer"},h.body&&p.createElement("div",{className:"rnf-notification-cell__content",dangerouslySetInnerHTML:{__html:h.body.rendered}}),f&&p.createElement("div",{className:"rnf-notification-cell__button-group"},p.createElement(i_,null,f.buttons.map((e,t)=>p.createElement(iA,{variant:0===t?"primary":"secondary",key:e.name,onClick:t=>g(t,e)},e.label)))),i&&p.createElement("div",{className:"rnf-notification-cell__child-content"},i),p.createElement("span",{className:"rnf-notification-cell__timestamp"},function(e,t={}){try{let r=function(e,t){var r;let n,i,o=()=>nV(void 0,NaN),a=(r=void 0,2),s=function(e){let t,r={},n=e.split(nW.dateTimeDelimiter);if(n.length>2)return r;if(/:/.test(n[0])?t=n[0]:(r.date=n[0],t=n[1],nW.timeZoneDelimiter.test(r.date)&&(r.date=e.split(nW.timeZoneDelimiter)[0],t=e.substr(r.date.length,e.length))),t){let e=nW.timezone.exec(t);e?(r.time=t.replace(e[1],""),r.timezone=e[1]):r.time=t}return r}(e);if(s.date){let e=function(e,t){let r=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),n=e.match(r);if(!n)return{year:NaN,restDateString:""};let i=n[1]?parseInt(n[1]):null,o=n[2]?parseInt(n[2]):null;return{year:null===o?i:100*o,restDateString:e.slice((n[1]||n[2]).length)}}(s.date,a);n=function(e,t){var r,n,i,o,a,s,l,c;if(null===t)return new Date(NaN);let u=e.match(nH);if(!u)return new Date(NaN);let h=!!u[4],d=nJ(u[1]),f=nJ(u[2])-1,p=nJ(u[3]),m=nJ(u[4]),g=nJ(u[5])-1;if(h){return(r=0,n=m,i=g,n>=1&&n<=53&&i>=0&&i<=6)?function(e,t,r){let n=new Date(0);n.setUTCFullYear(e,0,4);let i=n.getUTCDay()||7;return n.setUTCDate(n.getUTCDate()+((t-1)*7+r+1-i)),n}(t,m,g):new Date(NaN)}{let e=new Date(0);return(o=t,a=f,s=p,a>=0&&a<=11&&s>=1&&s<=(nY[a]||(nZ(o)?29:28))&&(l=t,(c=d)>=1&&c<=(nZ(l)?366:365)))?(e.setUTCFullYear(t,f,Math.max(d,p)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!n||isNaN(+n))return o();let l=+n,c=0;if(s.time&&isNaN(c=function(e){var t,r,n;let i=e.match(nK);if(!i)return NaN;let o=nX(i[1]),a=nX(i[2]),s=nX(i[3]);return(t=o,r=a,n=s,24===t?0===r&&0===n:n>=0&&n<60&&r>=0&&r<60&&t>=0&&t<25)?36e5*o+6e4*a+1e3*s:NaN}(s.time)))return o();if(s.timezone){if(isNaN(i=function(e){var t,r;if("Z"===e)return 0;let n=e.match(nG);if(!n)return 0;let i="+"===n[1]?-1:1,o=parseInt(n[2]),a=n[3]&&parseInt(n[3])||0;return(t=0,(r=a)>=0&&r<=59)?i*(36e5*o+6e4*a):NaN}(s.timezone)))return o()}else{let e=new Date(l+c),r=nq(0,null==t?void 0:t.in);return r.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),r.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),r}return nq(l+c+i,null==t?void 0:t.in)}(e);return function(e,t,r){let n,i=0,[o,a]=nQ(null==r?void 0:r.in,e,t);if(null==r?void 0:r.unit)"second"===(n=null==r?void 0:r.unit)?i=ii(o,a):"minute"===n?i=ir(o,a):"hour"===n?i=it(o,a):"day"===n?i=n2(o,a):"week"===n?i=n8(o,a):"month"===n?i=n5(o,a):"quarter"===n?i=n3(o,a):"year"===n&&(i=n7(o,a));else{let e=ii(o,a);60>Math.abs(e)?(i=ii(o,a),n="second"):3600>Math.abs(e)?(i=ir(o,a),n="minute"):86400>Math.abs(e)&&1>Math.abs(n2(o,a))?(i=it(o,a),n="hour"):604800>Math.abs(e)&&(i=n2(o,a))&&7>Math.abs(i)?n="day":2629746>Math.abs(e)?(i=n8(o,a),n="week"):7889238>Math.abs(e)?(i=n5(o,a),n="month"):0x1e18558>Math.abs(e)&&4>n3(o,a)?(i=n3(o,a),n="quarter"):(i=n7(o,a),n="year")}return new Intl.RelativeTimeFormat(null==r?void 0:r.locale,{numeric:"auto",...r}).format(i,n)}(r,new Date,{locale:t.locale})}catch{return e}}(e.inserted_at,{locale:u}))),io(o,p.createElement(o_,{item:e}))))}),oO=({children:e,value:t,onChange:r})=>{let{colorMode:n}=ig();return p.createElement("div",{className:`rnf-dropdown rnf-dropdown--${n}`},p.createElement("select",{"aria-label":"Select notification filter",value:t,onChange:r},e),p.createElement(iT,{"aria-hidden":!0}))},oL=({onClick:e})=>{let{useFeedStore:t,feedClient:r,colorMode:n}=ig(),{t:i}=iw(),o=t(e=>e.items.filter(e=>!e.read_at)),a=t(e=>e.metadata.unread_count),s=p.useCallback(t=>{r.markAllAsRead(),e&&e(t,o)},[r,o,e]);return p.createElement("button",{className:`rnf-mark-all-as-read rnf-mark-all-as-read--${n}`,disabled:0===a,onClick:s,type:"button"},i("markAllAsRead"),p.createElement(iS,{"aria-hidden":!0}))},oj=[nz.All,nz.Unread,nz.Read],oI=({onMarkAllAsReadClick:e,filterStatus:t,setFilterStatus:r})=>{let{t:n}=iw();return p.createElement("header",{className:"rnf-notification-feed__header"},p.createElement("div",{className:"rnf-notification-feed__selector"},p.createElement("span",{className:"rnf-notification-feed__type"},n("notifications")),p.createElement(oO,{value:t,onChange:e=>r(e.target.value)},oj.map(e=>p.createElement("option",{key:e,value:e},n(e))))),p.createElement(oL,{onClick:e}))},oN=e=>p.createElement(oT,{key:e.item.id,...e}),oP=e=>p.createElement(oI,{...e}),oM=({colorMode:e})=>p.createElement("div",{className:"rnf-notification-feed__spinner-container"},p.createElement(iE,{thickness:3,size:"16px",color:"dark"===e?"rgba(255, 255, 255, 0.65)":void 0})),oB=({EmptyComponent:e=p.createElement(iI,null),renderItem:t=oN,onNotificationClick:r,onNotificationButtonClick:n,onMarkAllAsReadClick:i,initialFilterStatus:o=nz.All,header:a,renderHeader:s=oP})=>{let[l,c]=(0,p.useState)(o),{feedClient:u,useFeedStore:h,colorMode:d}=ig(),{settings:f}=function(e){let[t,r]=(0,p.useState)(null),[n,i]=(0,p.useState)(!1);return(0,p.useEffect)(()=>{!async function(){let t=e.knock,n=t.client(),o=`/v1/users/${t.userId}/feeds/${e.feedId}/settings`;i(!0);let a=await n.makeRequest({method:"GET",url:o});a.error||r(a.body),i(!1)}()},[]),{settings:t,loading:n}}(u),{t:m}=iw(),{pageInfo:g,items:v,networkStatus:y}=h(),b=(0,p.useRef)(null);(0,p.useEffect)(()=>{c(o)},[o]),(0,p.useEffect)(()=>{u.fetch({status:l})},[u,l]);let w=0===v.length,E=C(y);return function(e){let t=e.callback??ij,r=e.ref,n=e.offset??0,i=(0,p.useMemo)(()=>iL(t,200),[t]),o=(0,p.useCallback)(()=>{if(r.current){let e=r.current,t=Math.round(e.scrollTop+e.clientHeight);Math.round(e.scrollHeight-n)<=t&&i()}},[i]);(0,p.useEffect)(()=>{let e;return r.current&&(e=r.current,r.current.addEventListener("scroll",o)),()=>{e&&e.removeEventListener("scroll",o)}},[o])}({ref:b,callback:(0,p.useCallback)(()=>{!E&&g.after&&u.fetchNextPage()},[E,g,u]),offset:70}),p.createElement("div",{className:`rnf-notification-feed rnf-notification-feed--${d}`},a||s({setFilterStatus:c,filterStatus:l,onMarkAllAsReadClick:i}),p.createElement("div",{className:"rnf-notification-feed__container",ref:b},y===k.loading&&p.createElement(oM,{colorMode:d}),p.createElement("div",{className:"rnf-notification-feed__feed-items-container"},y!==k.loading&&v.map(e=>t({item:e,onItemClick:r,onButtonClick:n}))),y===k.fetchMore&&p.createElement(oM,{colorMode:d}),!E&&w&&e),(null==f?void 0:f.features.branding_required)&&p.createElement("div",{className:"rnf-notification-feed__knock-branding"},p.createElement("a",{href:"https://knock.app?utm_source=powered-by-knock&utm_medium=referral&utm_campaign=knock-branding-feed",target:"_blank"},m("poweredBy")||"Powered by Knock")))},oD=({store:e,feedClient:t})=>{e.metadata.unseen_count>0&&t.markAllAsSeen()},oF=({isVisible:e,onOpen:t=oD,onClose:r,buttonRef:n,closeOnClickOutside:i=!0,placement:o="bottom-end",...a})=>{let{t:s}=iw(),{colorMode:l,feedClient:c,useFeedStore:u}=ig(),h=u(),{ref:d}=function(e,t,r){let n=(0,p.useRef)(null),i=e=>{"Escape"===e.key&&t(e)},o=e=>{var i,o;r.closeOnClickOutside&&(i=n.current,o=e.target,!(i&&(i===o||i.contains(o))))&&t(e)};return(0,p.useEffect)(()=>(e&&(document.addEventListener("keydown",i,!0),document.addEventListener("click",o,!0)),()=>{document.removeEventListener("keydown",i,!0),document.removeEventListener("click",o,!0)}),[e]),{ref:n}}(e,r,{closeOnClickOutside:i});return(0,p.useEffect)(()=>{e&&t&&t({store:h,feedClient:c})},[e,t,h,c]),(0,p.useEffect)(()=>{if(n.current&&d.current){let e=oA(n.current,d.current,{strategy:"fixed",placement:o,modifiers:[{name:"offset",options:{offset:[0,8]}}]});return()=>{e.destroy()}}},[n,d,o]),p.createElement("div",{className:`rnf-notification-feed-popover rnf-notification-feed-popover--${l}`,style:{visibility:e?"visible":"hidden",opacity:+!!e},ref:d,role:"dialog","aria-label":s("notifications"),tabIndex:-1},p.createElement("div",{className:"rnf-notification-feed-popover__inner"},p.createElement(oB,{...a})))},oU=({badgeCountType:e="unseen"})=>{let{useFeedStore:t}=ig(),r=t(t=>(function(e,t){switch(e){case"all":return t.total_count;case"unread":return t.unread_count;case"unseen":return t.unseen_count}})(e,t.metadata));return 0!==r?p.createElement("div",{className:"rnf-unseen-badge"},p.createElement("span",{className:"rnf-unseen-badge__count"},r>9?"9+":r)):null},oz=p.forwardRef(({onClick:e,badgeCountType:t},r)=>{let{colorMode:n}=ig();return p.createElement("button",{className:`rnf-notification-icon-button rnf-notification-icon-button--${n}`,"aria-label":"Open notification feed",ref:r,onClick:e},p.createElement(iR,{"aria-hidden":!0}),p.createElement(oU,{badgeCountType:t}))});var o$=r(49973);let oV=e=>{let t;try{t=new URL(e)}catch{return!1}return"http:"===t.protocol||"https:"===t.protocol},oq=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-banner",t),...r},e);oq.displayName="BannerView.Root";let oW=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-banner__message",t),...r},e);oW.displayName="BannerView.Content";let oH=({title:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-banner__title",t),...r},e);oH.displayName="BannerView.Title";let oK=({body:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-banner__body",t),dangerouslySetInnerHTML:{__html:e},...r});oK.displayName="BannerView.Body";let oG=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-banner__actions",t),...r},e);oG.displayName="BannerView.Actions";let oJ=({text:e,action:t,className:r,...n})=>p.createElement("button",{className:(0,o$.A)("knock-guide-banner__action",r),...n},e);oJ.displayName="BannerView.PrimaryButton";let oX=({text:e,action:t,className:r,...n})=>p.createElement("button",{className:(0,o$.A)("knock-guide-banner__action knock-guide-banner__action--secondary",r),...n},e);oX.displayName="BannerView.SecondaryButton";let oY=({className:e,...t})=>p.createElement("button",{className:(0,o$.A)("knock-guide-banner__close",e),...t},p.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},p.createElement("g",{fill:"#60646C",fillRule:"evenodd",clipRule:"evenodd"},p.createElement("path",{d:"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"}),p.createElement("path",{d:"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"}))));oY.displayName="BannerView.DismissButton";let oZ=({content:e,colorMode:t="light",onDismiss:r,onButtonClick:n})=>p.createElement(oq,{"data-knock-color-mode":t},p.createElement(oW,null,p.createElement(oH,{title:e.title}),p.createElement(oK,{body:e.body})),p.createElement(oG,null,e.secondary_button&&p.createElement(oX,{text:e.secondary_button.text,action:e.secondary_button.action,onClick:t=>{if(n){let{text:r,action:i}=e.secondary_button;n(t,{name:"secondary_button",text:r,action:i})}}}),e.primary_button&&p.createElement(oJ,{text:e.primary_button.text,action:e.primary_button.action,onClick:t=>{if(n){let{text:r,action:i}=e.primary_button;n(t,{name:"primary_button",text:r,action:i})}}}),e.dismissible&&p.createElement(oY,{onClick:r})));oZ.displayName="BannerView.Default",Object.assign({},{Default:oZ,Root:oq,Content:oW,Title:oH,Body:oK,Actions:oG,PrimaryButton:oJ,SecondaryButton:oX,DismissButton:oY});let oQ=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-card",t),...r},e);oQ.displayName="CardView.Root";let o0=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-card__message",t),...r},e);o0.displayName="CardView.Content";let o1=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-card__header",t),...r},e);o1.displayName="CardView.Header";let o2=({headline:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-card__headline",t),...r},e);o2.displayName="CardView.Headline";let o5=({title:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-card__title",t),...r},e);o5.displayName="CardView.Title";let o4=({body:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-card__body",t),dangerouslySetInnerHTML:{__html:e},...r});o4.displayName="CardView.Body";let o3=({children:e,className:t,alt:r,...n})=>p.createElement("img",{className:(0,o$.A)("knock-guide-card__img",t),alt:r||"",...n},e);o3.displayName="CardView.Img";let o9=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-card__actions",t),...r},e);o9.displayName="CardView.Actions";let o6=({text:e,action:t,className:r,...n})=>p.createElement("button",{className:(0,o$.A)("knock-guide-card__action",r),...n},e);o6.displayName="CardView.PrimaryButton";let o8=({text:e,action:t,className:r,...n})=>p.createElement("button",{className:(0,o$.A)("knock-guide-card__action knock-guide-card__action--secondary",r),...n},e);o8.displayName="CardView.SecondaryButton";let o7=({className:e,...t})=>p.createElement("button",{className:(0,o$.A)("knock-guide-card__close",e),...t},p.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},p.createElement("g",{fill:"#60646C",fillRule:"evenodd",clipRule:"evenodd"},p.createElement("path",{d:"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"}),p.createElement("path",{d:"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"}))));o7.displayName="CardView.DismissButton";let ae=({content:e,colorMode:t="light",onDismiss:r,onButtonClick:n,onImageClick:i})=>p.createElement(oQ,{"data-knock-color-mode":t},p.createElement(o0,null,p.createElement(o1,null,p.createElement(o2,{headline:e.headline}),e.dismissible&&p.createElement(o7,{onClick:r})),p.createElement(o5,{title:e.title}),p.createElement(o4,{body:e.body})),e.image&&p.createElement("a",{href:oV(e.image.action)?e.image.action:void 0,target:"_blank"},p.createElement(o3,{src:e.image.url,alt:e.image.alt,onClick:t=>{i&&i(t,e.image)}})),(e.primary_button||e.secondary_button)&&p.createElement(o9,null,e.primary_button&&p.createElement(o6,{text:e.primary_button.text,action:e.primary_button.action,onClick:t=>{if(n){let{text:r,action:i}=e.primary_button;n(t,{name:"primary_button",text:r,action:i})}}}),e.secondary_button&&p.createElement(o8,{text:e.secondary_button.text,action:e.secondary_button.action,onClick:t=>{if(n){let{text:r,action:i}=e.secondary_button;n(t,{name:"secondary_button",text:r,action:i})}}})));ae.displayName="CardView.Default",Object.assign({},{Default:ae,Root:oQ,Content:o0,Title:o5,Body:o4,Img:o3,Actions:o9,PrimaryButton:o6,SecondaryButton:o8,DismissButton:o7});var at=r(50817);let ar=({children:e,onOpenChange:t,...r})=>p.createElement(at.bL,{defaultOpen:!0,onOpenChange:t,...r},p.createElement(at.ZL,null,e));ar.displayName="ModalView.Root";let an=p.forwardRef(({className:e,...t},r)=>p.createElement(at.hJ,{className:(0,o$.A)("knock-guide-modal__overlay",e),ref:r,...t}));an.displayName="ModalView.Overlay";let ai=p.forwardRef(({children:e,className:t,...r},n)=>p.createElement(at.UC,{className:(0,o$.A)("knock-guide-modal",t),ref:n,...r},e));ai.displayName="ModalView.Content";let ao=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-modal__header",t),...r},e);ao.displayName="ModalView.Header";let aa=({title:e,className:t,...r})=>p.createElement(at.hE,{className:(0,o$.A)("knock-guide-modal__title",t),...r},e);aa.displayName="ModalView.Title";let as=({body:e,className:t,...r})=>p.createElement(at.VY,{className:(0,o$.A)("knock-guide-modal__body",t),dangerouslySetInnerHTML:{__html:e},...r});as.displayName="ModalView.Body";let al=({children:e,className:t,alt:r,...n})=>p.createElement("img",{className:(0,o$.A)("knock-guide-modal__img",t),alt:r||"",...n},e);al.displayName="ModalView.Img";let ac=({children:e,className:t,...r})=>p.createElement("div",{className:(0,o$.A)("knock-guide-modal__actions",t),...r},e);ac.displayName="ModalView.Actions";let au=({text:e,action:t,className:r,...n})=>p.createElement("button",{className:(0,o$.A)("knock-guide-modal__action",r),...n},e);au.displayName="ModalView.PrimaryButton";let ah=({text:e,action:t,className:r,...n})=>p.createElement("button",{className:(0,o$.A)("knock-guide-modal__action knock-guide-modal__action--secondary",r),...n},e);ah.displayName="ModalView.SecondaryButton";let ad=({className:e,...t})=>p.createElement(at.bm,{className:(0,o$.A)("knock-guide-modal__close",e),...t},p.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},p.createElement("g",{fill:"#60646C",fillRule:"evenodd",clipRule:"evenodd"},p.createElement("path",{d:"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"}),p.createElement("path",{d:"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"}))));ad.displayName="ModalView.Close";let af=({content:e,colorMode:t="light",onOpenChange:r,onDismiss:n,onButtonClick:i,onImageClick:o})=>p.createElement(ar,{onOpenChange:r},p.createElement(an,null),p.createElement(ai,{"data-knock-color-mode":t,onPointerDownOutside:n},p.createElement(ao,null,p.createElement(aa,{title:e.title}),e.dismissible&&p.createElement(ad,{onClick:n})),p.createElement(as,{body:e.body}),e.image&&p.createElement("a",{href:oV(e.image.action)?e.image.action:void 0,target:"_blank"},p.createElement(al,{src:e.image.url,alt:e.image.alt,onClick:t=>{o&&o(t,e.image)}})),(e.primary_button||e.secondary_button)&&p.createElement(ac,null,e.secondary_button&&p.createElement(ah,{text:e.secondary_button.text,action:e.secondary_button.action,onClick:t=>{if(i){let{text:r,action:n}=e.secondary_button;i(t,{name:"secondary_button",text:r,action:n})}}}),e.primary_button&&p.createElement(au,{text:e.primary_button.text,action:e.primary_button.action,onClick:t=>{if(i){let{text:r,action:n}=e.primary_button;i(t,{name:"primary_button",text:r,action:n})}}}))));af.displayName="ModalView.Default",Object.assign({},{Default:af,Root:ar,Overlay:an,Content:ai,Title:aa,Body:as,Img:al,Actions:ac,PrimaryButton:au,SecondaryButton:ah,Close:ad});var ap=r(53356),am=r(6024),ag={},av=e=>{var{fileScope:t,css:r}=e,n=t.packageName?[t.packageName,t.filePath].join("/"):t.filePath,i=ag[n];if(!i){var o=document.createElement("style");t.packageName&&o.setAttribute("data-package",t.packageName),o.setAttribute("data-file",t.filePath),o.setAttribute("type","text/css"),i=ag[n]=o,document.head.appendChild(o)}i.innerHTML=r},ay=r(17988),ab=r.n(ay),aw=class{constructor(e){let{failure:t,gotoFn:r,output:n}=this._buildTables(e);this.gotoFn=r,this.output=n,this.failure=t}_buildTables(e){let t={0:{}},r={},n=0;for(let i of e){let e=0;for(let o of i)t[e]&&o in t[e]?e=t[e][o]:(n++,t[e][o]=n,t[n]={},e=n,r[n]=[]);r[e].push(i)}let i={},o=[];for(let e in t[0]){let r=t[0][e];i[r]=0,o.push(r)}for(;o.length>0;){let e=o.shift();if(void 0!==e)for(let n in t[e]){let a=t[e][n];o.push(a);let s=i[e];for(;s>0&&!(n in t[s]);)s=i[s];if(n in t[s]){let e=t[s][n];i[a]=e,r[a]=[...r[a],...r[e]]}else i[a]=0}}return{gotoFn:t,output:r,failure:i}}search(e){let t=0,r=[];for(let n=0;n<e.length;n++){let i=e[n];for(;t>0&&!(i in this.gotoFn[t]);)t=this.failure[t];if(i in this.gotoFn[t]&&(t=this.gotoFn[t][i],this.output[t].length>0)){let e=this.output[t];r.push([n,e])}}return r}match(e){let t=0;for(let r=0;r<e.length;r++){let n=e[r];for(;t>0&&!(n in this.gotoFn[t]);)t=this.failure[t];if(n in this.gotoFn[t]&&(t=this.gotoFn[t][n],this.output[t].length>0))return!0}return!1}},ak=[{appendCss:()=>{},registerClassName:()=>{},onEndFileScope:()=>{},registerComposition:()=>{},markCompositionUsed:()=>{},getIdentOption:()=>"short"}],aC=()=>{if(ak.length<1)throw Error("No adapter configured");return ak[ak.length-1]},aE=!1;function ax(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}!function(e){e.Attribute="attribute",e.Pseudo="pseudo",e.PseudoElement="pseudo-element",e.Tag="tag",e.Universal="universal",e.Adjacent="adjacent",e.Child="child",e.Descendant="descendant",e.Parent="parent",e.Sibling="sibling",e.ColumnCombinator="column-combinator"}(s||(s={})),function(e){e.Any="any",e.Element="element",e.End="end",e.Equals="equals",e.Exists="exists",e.Hyphen="hyphen",e.Not="not",e.Start="start"}(l||(l={}));let aA=/^[^\\#]?(?:\\(?:[\da-f]{1,6}\s?|.)|[\w\-\u00b0-\uFFFF])+/,a_=/\\([\da-f]{1,6}\s?|(\s)|.)/gi,aR=new Map([[126,l.Element],[94,l.Start],[36,l.End],[42,l.Any],[33,l.Not],[124,l.Hyphen]]),aS=new Set(["has","not","matches","is","where","host","host-context"]),aT=new Set(["contains","icontains"]);function aO(e,t,r){let n=parseInt(t,16)-65536;return n!=n||r?t:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320)}function aL(e){return e.replace(a_,aO)}function aj(e){return 39===e||34===e}function aI(e){return 32===e||9===e||10===e||12===e||13===e}function aN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aN(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let aM=function e(t){return r.withOptions=r=>e(aP(aP({},t),r)),r;function r(e,...n){let i="string"==typeof e?[e]:e.raw,{escapeSpecialCharacters:o=Array.isArray(e)}=t,a="";for(let e=0;e<i.length;e++){let t=i[e];o&&(t=t.replace(/\\\n[ \t]*/g,"").replace(/\\`/g,"`").replace(/\\\$/g,"$").replace(/\\\{/g,"{")),a+=t,e<n.length&&(a+=n[e])}let s=a.split("\n"),l=null;for(let e of s){let t=e.match(/^(\s+)\S+/);if(t){let e=t[1].length;l=l?Math.min(l,e):e}}if(null!==l){let e=l;a=s.map(t=>" "===t[0]||"	"===t[0]?t.slice(e):t).join("\n")}return a=a.trim(),o&&(a=a.replace(/\\n/g,"\n")),a}}({});var aB=function(){return(aB=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function aD(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}function aF(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a}var aU=/(\u000D|\u000C|\u000D\u000A)/g,az=/[\u0000\uD800-\uDFFF]/g,a$=/(\/\*)[\s\S]*?(\*\/)/g,aV=function(e,t){void 0===t&&(t=0),e=(e=e.replace(aU,"\n").replace(az,"�")).replace(a$,"");for(var r=[];t<e.length;t+=1){var n=e.charCodeAt(t);if(9===n||32===n||10===n){for(var i=e.charCodeAt(++t);9===i||32===i||10===i;)i=e.charCodeAt(++t);t-=1,r.push({type:"<whitespace-token>"})}else if(34===n){var o=aq(e,t);if(null===o)return null;var a=aF(o,2),s=a[0],l=a[1];r.push({type:"<string-token>",value:l}),t=s}else if(35===n){if(t+1<e.length){var c=e.charCodeAt(t+1);if(95===c||c>=65&&c<=90||c>=97&&c<=122||c>=128||c>=48&&c<=57||92===c&&t+2<e.length&&10!==e.charCodeAt(t+2)){var u=aW(e,t+1)?"id":"unrestricted",o=aJ(e,t+1);if(null!==o){var h=aF(o,2),s=h[0],l=h[1];r.push({type:"<hash-token>",value:l.toLowerCase(),flag:u}),t=s;continue}}}r.push({type:"<delim-token>",value:n})}else if(39===n){var o=aq(e,t);if(null===o)return null;var d=aF(o,2),s=d[0],l=d[1];r.push({type:"<string-token>",value:l}),t=s}else if(40===n)r.push({type:"<(-token>"});else if(41===n)r.push({type:"<)-token>"});else if(43===n){var f=aK(e,t);if(null===f)r.push({type:"<delim-token>",value:n});else{var p=aF(f,2),s=p[0],m=p[1];"<dimension-token>"===m[0]?r.push({type:"<dimension-token>",value:m[1],unit:m[2].toLowerCase(),flag:"number"}):"<number-token>"===m[0]?r.push({type:m[0],value:m[1],flag:m[2]}):r.push({type:m[0],value:m[1],flag:"number"}),t=s}}else if(44===n)r.push({type:"<comma-token>"});else if(45===n){var g=aK(e,t);if(null!==g){var v=aF(g,2),s=v[0],m=v[1];"<dimension-token>"===m[0]?r.push({type:"<dimension-token>",value:m[1],unit:m[2].toLowerCase(),flag:"number"}):"<number-token>"===m[0]?r.push({type:m[0],value:m[1],flag:m[2]}):r.push({type:m[0],value:m[1],flag:"number"}),t=s;continue}if(t+2<e.length){var c=e.charCodeAt(t+1),y=e.charCodeAt(t+2);if(45===c&&62===y){r.push({type:"<CDC-token>"}),t+=2;continue}}var o=aZ(e,t);if(null!==o){var b=aF(o,3),s=b[0],l=b[1],w=b[2];r.push({type:w,value:l}),t=s;continue}r.push({type:"<delim-token>",value:n})}else if(46===n){var g=aK(e,t);if(null===g)r.push({type:"<delim-token>",value:n});else{var k=aF(g,2),s=k[0],m=k[1];"<dimension-token>"===m[0]?r.push({type:"<dimension-token>",value:m[1],unit:m[2].toLowerCase(),flag:"number"}):"<number-token>"===m[0]?r.push({type:m[0],value:m[1],flag:m[2]}):r.push({type:m[0],value:m[1],flag:"number"}),t=s;continue}}else if(58===n)r.push({type:"<colon-token>"});else if(59===n)r.push({type:"<semicolon-token>"});else if(60===n){if(t+3<e.length){var c=e.charCodeAt(t+1),y=e.charCodeAt(t+2),C=e.charCodeAt(t+3);if(33===c&&45===y&&45===C){r.push({type:"<CDO-token>"}),t+=3;continue}}r.push({type:"<delim-token>",value:n})}else if(64===n){var o=aX(e,t+1);if(null!==o){var E=aF(o,2),s=E[0],l=E[1];r.push({type:"<at-keyword-token>",value:l.toLowerCase()}),t=s;continue}r.push({type:"<delim-token>",value:n})}else if(91===n)r.push({type:"<[-token>"});else if(92===n){var o=aH(e,t);if(null===o)return null;var x=aF(o,2),s=x[0],l=x[1];e=e.slice(0,t)+l+e.slice(s+1),t-=1}else if(93===n)r.push({type:"<]-token>"});else if(123===n)r.push({type:"<{-token>"});else if(125===n)r.push({type:"<}-token>"});else if(n>=48&&n<=57){var o=aK(e,t),A=aF(o,2),s=A[0],m=A[1];"<dimension-token>"===m[0]?r.push({type:"<dimension-token>",value:m[1],unit:m[2].toLowerCase(),flag:"number"}):"<number-token>"===m[0]?r.push({type:m[0],value:m[1],flag:m[2]}):r.push({type:m[0],value:m[1],flag:"number"}),t=s}else if(95===n||n>=65&&n<=90||n>=97&&n<=122||n>=128){var o=aZ(e,t);if(null===o)return null;var _=aF(o,3),s=_[0],l=_[1],w=_[2];r.push({type:w,value:l}),t=s}else r.push({type:"<delim-token>",value:n})}return r.push({type:"<EOF-token>"}),r},aq=function(e,t){if(e.length<=t+1)return null;for(var r=e.charCodeAt(t),n=[],i=t+1;i<e.length;i+=1){var o=e.charCodeAt(i);if(o===r)return[i,String.fromCharCode.apply(null,n)];if(92===o){var a=aH(e,i);if(null===a)return null;var s=aF(a,2),l=s[0],c=s[1];n.push(c),i=l}else{if(10===o)return null;n.push(o)}}return null},aW=function(e,t){if(e.length<=t)return!1;var r=e.charCodeAt(t);if(45===r){if(e.length<=t+1)return!1;var n=e.charCodeAt(t+1);return 45===n||95===n||!!(n>=65)&&!!(n<=90)||!!(n>=97)&&!!(n<=122)||!!(n>=128)||92===n&&!(e.length<=t+2)&&10!==e.charCodeAt(t+2)}if(95===r||r>=65&&r<=90||r>=97&&r<=122||r>=128)return!0;if(92!==r)return!1;if(e.length<=t+1)return!1;var n=e.charCodeAt(t+1);return 10!==n},aH=function(e,t){if(e.length<=t+1||92!==e.charCodeAt(t))return null;var r=e.charCodeAt(t+1);if(10===r)return null;if((!(r>=48)||!(r<=57))&&(!(r>=65)||!(r<=70))&&(!(r>=97)||!(r<=102)))return[t+1,r];for(var n=[r],i=Math.min(t+7,e.length),o=t+2;o<i;o+=1){var a=e.charCodeAt(o);if(a>=48&&a<=57||a>=65&&a<=70||a>=97&&a<=102)n.push(a);else break}if(o<e.length){var s=e.charCodeAt(o);(9===s||32===s||10===s)&&(o+=1)}return[o-1,parseInt(String.fromCharCode.apply(null,n),16)]},aK=function(e,t){var r=aG(e,t);if(null===r)return null;var n=aF(r,3),i=n[0],o=n[1],a=n[2],s=aX(e,i+1);if(null!==s){var l=aF(s,2);return[l[0],["<dimension-token>",o,l[1]]]}return i+1<e.length&&37===e.charCodeAt(i+1)?[i+1,["<percentage-token>",o]]:[i,["<number-token>",o,a]]},aG=function(e,t){if(e.length<=t)return null;var r="integer",n=[],i=e.charCodeAt(t);for((43===i||45===i)&&(t+=1,45===i&&n.push(45));t<e.length;){var o=e.charCodeAt(t);if(o>=48&&o<=57)n.push(o),t+=1;else break}if(t+1<e.length){var a=e.charCodeAt(t),s=e.charCodeAt(t+1);if(46===a&&s>=48&&s<=57)for(n.push(a,s),r="number",t+=2;t<e.length;){var o=e.charCodeAt(t);if(o>=48&&o<=57)n.push(o),t+=1;else break}}if(t+1<e.length){var a=e.charCodeAt(t),s=e.charCodeAt(t+1),l=e.charCodeAt(t+2);if(69===a||101===a){var c=s>=48&&s<=57;if(c||(43===s||45===s)&&l>=48&&l<=57)for(r="number",c?(n.push(69,s),t+=2):(45===s?n.push(69,45,l):n.push(69,l),t+=3);t<e.length;){var o=e.charCodeAt(t);if(o>=48&&o<=57)n.push(o),t+=1;else break}}}var u=String.fromCharCode.apply(null,n),h="number"===r?parseFloat(u):parseInt(u);return -0===h&&(h=0),Number.isNaN(h)?null:[t-1,h,r]},aJ=function(e,t){if(e.length<=t)return null;for(var r=[],n=e.charCodeAt(t);t<e.length;n=e.charCodeAt(++t)){if(45===n||95===n||n>=65&&n<=90||n>=97&&n<=122||n>=128||n>=48&&n<=57){r.push(n);continue}var i=aH(e,t);if(null!==i){var o=aF(i,2),a=o[0],s=o[1];r.push(s),t=a;continue}break}return 0===t?null:[t-1,String.fromCharCode.apply(null,r)]},aX=function(e,t){if(e.length<=t||!aW(e,t))return null;for(var r=[],n=e.charCodeAt(t);t<e.length;n=e.charCodeAt(++t)){if(45===n||95===n||n>=65&&n<=90||n>=97&&n<=122||n>=128||n>=48&&n<=57){r.push(n);continue}var i=aH(e,t);if(null!==i){var o=aF(i,2),a=o[0],s=o[1];r.push(s),t=a;continue}break}return[t-1,String.fromCharCode.apply(null,r)]},aY=function(e,t){for(var r=e.charCodeAt(t);9===r||32===r||10===r;)r=e.charCodeAt(++t);for(var n=[],i=!1;t<e.length;){if(41===r)return[t,String.fromCharCode.apply(null,n)];if(34===r||39===r||40===r)break;if(9===r||32===r||10===r)i||0===n.length||(i=!0);else if(92===r){var o=aH(e,t);if(null===o||i)return null;var a=aF(o,2),s=a[0],l=a[1];n.push(l),t=s}else{if(i)return null;n.push(r)}r=e.charCodeAt(++t)}return null},aZ=function(e,t){var r=aX(e,t);if(null===r)return null;var n=aF(r,2),i=n[0],o=n[1];if("url"===o.toLowerCase()){if(e.length>i+1){var a=e.charCodeAt(i+1);if(40===a){for(var s=2;i+s<e.length;s+=1){var l=e.charCodeAt(i+s);if(34===l||39===l)break;if(9!==l&&32!==l&&10!==l){var c=aY(e,i+s);if(null===c)return null;var u=aF(c,2);return[u[0],u[1],"<url-token>"]}}return[i+1,o.toLowerCase(),"<function-token>"]}}}else if(e.length>i+1){var a=e.charCodeAt(i+1);if(40===a)return[i+1,o.toLowerCase(),"<function-token>"]}return[i,o.toLowerCase(),"<ident-token>"]},aQ=function(e){for(var t=e.length-1;t>=0;t--)e[t]=a0(e[t]);return e},a0=function(e){if(null===e.mediaCondition)return e;var t=a1(e.mediaCondition);return null===t.operator&&1===t.children.length&&"children"in t.children[0]&&(t=t.children[0]),{mediaPrefix:e.mediaPrefix,mediaType:e.mediaType,mediaCondition:t}},a1=function e(t){for(var r=t.children.length-1;r>=0;r--){var n=t.children[r];if(!("context"in n)){var i=e(n);if(null===i.operator&&1===i.children.length)t.children[r]=i.children[0];else if(i.operator===t.operator&&("and"===i.operator||"or"===i.operator)){for(var o=[r,1],a=0;a<i.children.length;a++)o.push(i.children[a]);t.children.splice.apply(t.children,o)}}}return t},a2=function(e,t){return t instanceof Error?Error("".concat(t.message.trim(),"\n").concat(e.trim())):Error(e.trim())},a5=function(e){var t=aV(e.trim());if(null===t)throw a2("Failed tokenizing");var r=0,n=t.length-1;if("<at-keyword-token>"===t[0].type&&"media"===t[0].value){if("<whitespace-token>"!==t[1].type)throw a2("Expected whitespace after media");r=2;for(var i=2;i<t.length-1;i++){var o=t[i];if("<{-token>"===o.type){n=i;break}if("<semicolon-token>"===o.type)throw a2("Expected '{' in media query but found ';'")}}return a3(t=t.slice(r,n))},a4=function(e){for(var t=[],r=!1,n=0;n<e.length;n++)"<whitespace-token>"===e[n].type?(r=!0,t.length>0&&(t[t.length-1].wsAfter=!0)):(t.push(aB(aB({},e[n]),{wsBefore:r,wsAfter:!1})),r=!1);return t},a3=function(e){for(var t,r,n=[[]],i=0;i<e.length;i++){var o=e[i];"<comma-token>"===o.type?n.push([]):n[n.length-1].push(o)}var a=n.map(a4);if(1===a.length&&0===a[0].length)return[{mediaCondition:null,mediaPrefix:null,mediaType:"all"}];var s=a.map(function(e){return 0===e.length?null:a9(e)}),l=[];try{for(var c=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(s),u=c.next();!u.done;u=c.next()){var h=u.value;null!==h&&l.push(h)}}catch(e){t={error:e}}finally{try{u&&!u.done&&(r=c.return)&&r.call(c)}finally{if(t)throw t.error}}if(0===l.length)throw a2("No valid media queries");return l},a9=function(e){var t=e[0];if("<(-token>"===t.type)try{return{mediaPrefix:null,mediaType:"all",mediaCondition:a6(e,!0)}}catch(e){throw a2("Expected media condition after '('",e)}if("<ident-token>"===t.type){var r=null,n=void 0,i=t.value;("only"===i||"not"===i)&&(r=i);var o=+(null!==r);if(e.length<=o)throw a2("Expected extra token in media query");var a=e[o];if("<ident-token>"===a.type){var s=a.value;if("all"===s)n="all";else if("print"===s||"screen"===s)n=s;else if("tty"===s||"tv"===s||"projection"===s||"handheld"===s||"braille"===s||"embossed"===s||"aural"===s||"speech"===s)r="not"===r?null:"not",n="all";else throw a2("Unknown ident '".concat(s,"' in media query"))}else if("not"===r&&"<(-token>"===a.type){var l=[{type:"<(-token>",wsBefore:!1,wsAfter:!1}];l.push.apply(l,e),l.push({type:"<)-token>",wsBefore:!1,wsAfter:!1});try{return{mediaPrefix:null,mediaType:"all",mediaCondition:a6(l,!0)}}catch(e){throw a2("Expected media condition after '('",e)}}else throw a2("Invalid media query");if(o+1===e.length)return{mediaPrefix:r,mediaType:n,mediaCondition:null};if(o+4<e.length){var c=e[o+1];if("<ident-token>"===c.type&&"and"===c.value)try{return{mediaPrefix:r,mediaType:n,mediaCondition:a6(e.slice(o+2),!1)}}catch(e){throw a2("Expected media condition after 'and'",e)}throw a2("Expected 'and' after media prefix")}throw a2("Expected media condition after media prefix")}throw a2("Expected media condition or media prefix")},a6=function e(t,r,n){if(void 0===n&&(n=null),t.length<3||"<(-token>"!==t[0].type||"<)-token>"!==t[t.length-1].type)throw Error("Invalid media condition");for(var i,o=t.length-1,a=0,s=0,l=0;l<t.length;l++){var c=t[l];if("<(-token>"===c.type?(s+=1,a=Math.max(a,s)):"<)-token>"===c.type&&(s-=1),0===s){o=l;break}}if(0!==s)throw Error("Mismatched parens\nInvalid media condition");var u=t.slice(0,o+1);if(i=1===a?a8(u):"<ident-token>"===u[1].type&&"not"===u[1].value?e(u.slice(2,-1),!0,"not"):e(u.slice(1,-1),!0),o===t.length-1)return{operator:n,children:[i]};var h=t[o+1];if("<ident-token>"!==h.type)throw Error("Invalid operator\nInvalid media condition");if(null!==n&&n!==h.value)throw Error("'".concat(h.value,"' and '").concat(n,"' must not be at same level\nInvalid media condition"));if("or"!==h.value||r){if("and"!==h.value&&"or"!==h.value)throw Error("Invalid operator: '".concat(h.value,"'\nInvalid media condition"))}else throw Error("Cannot use 'or' at top level of a media query\nInvalid media condition");var d=e(t.slice(o+2),r,h.value);return{operator:h.value,children:[i].concat(d.children)}},a8=function(e){if(e.length<3||"<(-token>"!==e[0].type||"<)-token>"!==e[e.length-1].type)throw Error("Invalid media feature");for(var t=[e[0]],r=1;r<e.length;r++){if(r<e.length-2){var n=e[r],i=e[r+1],o=e[r+2];if("<number-token>"===n.type&&n.value>0&&"<delim-token>"===i.type&&47===i.value&&"<number-token>"===o.type&&o.value>0){t.push({type:"<ratio-token>",numerator:n.value,denominator:o.value,wsBefore:n.wsBefore,wsAfter:o.wsAfter}),r+=2;continue}}t.push(e[r])}var a=t[1];if("<ident-token>"===a.type&&3===t.length)return{context:"boolean",feature:a.value};if(5===t.length&&"<ident-token>"===t[1].type&&"<colon-token>"===t[2].type){var s=t[3];if("<number-token>"===s.type||"<dimension-token>"===s.type||"<ratio-token>"===s.type||"<ident-token>"===s.type){var l=t[1].value,c=null,u=l.slice(0,4);return"min-"===u?(c="min",l=l.slice(4)):"max-"===u&&(c="max",l=l.slice(4)),s.wsBefore,s.wsAfter,{context:"value",prefix:c,feature:l,value:aD(s,["wsBefore","wsAfter"])}}}else if(t.length>=5)try{var h=a7(t);return{context:"range",feature:h.featureName,range:h}}catch(e){throw a2("Invalid media feature",e)}throw Error("Invalid media feature")},a7=function(e){if(e.length<5||"<(-token>"!==e[0].type||"<)-token>"!==e[e.length-1].type)throw Error("Invalid range");var t,r,n,i,o={leftToken:null,leftOp:null,featureName:"",rightOp:null,rightToken:null},a="<number-token>"===e[1].type||"<dimension-token>"===e[1].type||"<ratio-token>"===e[1].type||"<ident-token>"===e[1].type&&"infinite"===e[1].value;if("<delim-token>"===e[2].type){if(60===e[2].value)"<delim-token>"!==e[3].type||61!==e[3].value||e[3].wsBefore?o[a?"leftOp":"rightOp"]="<":o[a?"leftOp":"rightOp"]="<=";else if(62===e[2].value)"<delim-token>"!==e[3].type||61!==e[3].value||e[3].wsBefore?o[a?"leftOp":"rightOp"]=">":o[a?"leftOp":"rightOp"]=">=";else if(61===e[2].value)o[a?"leftOp":"rightOp"]="=";else throw Error("Invalid range");if(a)o.leftToken=e[1];else if("<ident-token>"===e[1].type)o.featureName=e[1].value;else throw Error("Invalid range");var s=2+(null!=(r=null==(t=o[a?"leftOp":"rightOp"])?void 0:t.length)?r:0),l=e[s];if(a)if("<ident-token>"===l.type){if(o.featureName=l.value,e.length>=7){var c=e[s+1],u=e[s+2];if("<delim-token>"===c.type){var h=c.value;if(60===h)"<delim-token>"!==u.type||61!==u.value||u.wsBefore?o.rightOp="<":o.rightOp="<=";else if(62===h)"<delim-token>"!==u.type||61!==u.value||u.wsBefore?o.rightOp=">":o.rightOp=">=";else throw Error("Invalid range");var d=e[s+1+(null!=(i=null==(n=o.rightOp)?void 0:n.length)?i:0)];o.rightToken=d}else throw Error("Invalid range")}else if(s+2!==e.length)throw Error("Invalid range")}else throw Error("Invalid range");else o.rightToken=l;var f=null,p=o.leftToken,m=o.leftOp,g=o.featureName,v=o.rightOp,y=o.rightToken,b=null;if(null!==p)if("<ident-token>"===p.type){var w=p.type,k=p.value;"infinite"===k&&(b={type:w,value:k})}else("<number-token>"===p.type||"<dimension-token>"===p.type||"<ratio-token>"===p.type)&&(p.wsBefore,p.wsAfter,b=aD(p,["wsBefore","wsAfter"]));var C=null;if(null!==y)if("<ident-token>"===y.type){var w=y.type,k=y.value;"infinite"===k&&(C={type:w,value:k})}else("<number-token>"===y.type||"<dimension-token>"===y.type||"<ratio-token>"===y.type)&&(y.wsBefore,y.wsAfter,C=aD(y,["wsBefore","wsAfter"]));if(null!==b&&null!==C)if(("<"===m||"<="===m)&&("<"===v||"<="===v))f={leftToken:b,leftOp:m,featureName:g,rightOp:v,rightToken:C};else if((">"===m||">="===m)&&(">"===v||">="===v))f={leftToken:b,leftOp:m,featureName:g,rightOp:v,rightToken:C};else throw Error("Invalid range");else null===b&&null===m&&null!==v&&null!==C?f={leftToken:b,leftOp:m,featureName:g,rightOp:v,rightToken:C}:null!==b&&null!==m&&null===v&&null===C&&(f={leftToken:b,leftOp:m,featureName:g,rightOp:v,rightToken:C});return f}throw Error("Invalid range")};function se(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function st(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?se(Object(r),!0).forEach(function(t){!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:String(n))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):se(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sr(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function sn(e,t){for(var r in e)t(e[r],r)}function si(e,t){var r={};for(var n in e)-1===t.indexOf(n)&&(r[n]=e[n]);return r}var so=(e,t)=>{var r,n=()=>{var r=RegExp(".".concat(ab()(t,{isIdentifier:!0}).replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")),"g");return e.replace(r,"&")};try{r=function(e){let t=[],r=function e(t,r,n){let i=[];function o(e){let t=r.slice(n+e).match(aA);if(!t)throw Error(`Expected name, found ${r.slice(n)}`);let[i]=t;return n+=e+i.length,aL(i)}function a(e){for(n+=e;n<r.length&&aI(r.charCodeAt(n));)n++}function c(){let e=n+=1,t=1;for(;t>0&&n<r.length;n++)40!==r.charCodeAt(n)||u(n)?41===r.charCodeAt(n)&&!u(n)&&t--:t++;if(t)throw Error("Parenthesis not matched");return aL(r.slice(e,n-1))}function u(e){let t=0;for(;92===r.charCodeAt(--e);)t++;return(1&t)==1}function h(){if(i.length>0&&function(e){switch(e.type){case s.Adjacent:case s.Child:case s.Descendant:case s.Parent:case s.Sibling:case s.ColumnCombinator:return!0;default:return!1}}(i[i.length-1]))throw Error("Did not expect successive traversals.")}function d(e){if(i.length>0&&i[i.length-1].type===s.Descendant){i[i.length-1].type=e;return}h(),i.push({type:e})}function f(e,t){i.push({type:s.Attribute,name:e,action:t,value:o(1),namespace:null,ignoreCase:"quirks"})}function p(){if(i.length&&i[i.length-1].type===s.Descendant&&i.pop(),0===i.length)throw Error("Empty sub-selector");t.push(i)}if(a(0),r.length===n)return n;e:for(;n<r.length;){let t=r.charCodeAt(n);switch(t){case 32:case 9:case 10:case 12:case 13:(0===i.length||i[0].type!==s.Descendant)&&(h(),i.push({type:s.Descendant})),a(1);break;case 62:d(s.Child),a(1);break;case 60:d(s.Parent),a(1);break;case 126:d(s.Sibling),a(1);break;case 43:d(s.Adjacent),a(1);break;case 46:f("class",l.Element);break;case 35:f("id",l.Equals);break;case 91:{let e;a(1);let t=null;124===r.charCodeAt(n)?e=o(1):r.startsWith("*|",n)?(t="*",e=o(2)):(e=o(0),124===r.charCodeAt(n)&&61!==r.charCodeAt(n+1)&&(t=e,e=o(1))),a(0);let c=l.Exists,h=aR.get(r.charCodeAt(n));if(h){if(c=h,61!==r.charCodeAt(n+1))throw Error("Expected `=`");a(2)}else 61===r.charCodeAt(n)&&(c=l.Equals,a(1));let d="",f=null;if("exists"!==c){if(aj(r.charCodeAt(n))){let e=r.charCodeAt(n),t=n+1;for(;t<r.length&&(r.charCodeAt(t)!==e||u(t));)t+=1;if(r.charCodeAt(t)!==e)throw Error("Attribute value didn't end");d=aL(r.slice(n+1,t)),n=t+1}else{let e=n;for(;n<r.length&&(!aI(r.charCodeAt(n))&&93!==r.charCodeAt(n)||u(n));)n+=1;d=aL(r.slice(e,n))}a(0);let e=32|r.charCodeAt(n);115===e?(f=!1,a(1)):105===e&&(f=!0,a(1))}if(93!==r.charCodeAt(n))throw Error("Attribute selector didn't terminate");n+=1;let p={type:s.Attribute,name:e,action:c,value:d,namespace:t,ignoreCase:f};i.push(p);break}case 58:{if(58===r.charCodeAt(n+1)){i.push({type:s.PseudoElement,name:o(2).toLowerCase(),data:40===r.charCodeAt(n)?c():null});continue}let t=o(1).toLowerCase(),a=null;if(40===r.charCodeAt(n))if(aS.has(t)){if(aj(r.charCodeAt(n+1)))throw Error(`Pseudo-selector ${t} cannot be quoted`);if(n=e(a=[],r,n+1),41!==r.charCodeAt(n))throw Error(`Missing closing parenthesis in :${t} (${r})`);n+=1}else{if(a=c(),aT.has(t)){let e=a.charCodeAt(0);e===a.charCodeAt(a.length-1)&&aj(e)&&(a=a.slice(1,-1))}a=aL(a)}i.push({type:s.Pseudo,name:t,data:a});break}case 44:p(),i=[],a(1);break;default:{let e;if(r.startsWith("/*",n)){let e=r.indexOf("*/",n+2);if(e<0)throw Error("Comment was not terminated");n=e+2,0===i.length&&a(0);break}let l=null;if(42===t)n+=1,e="*";else if(124===t){if(e="",124===r.charCodeAt(n+1)){d(s.ColumnCombinator),a(2);break}}else if(aA.test(r.slice(n)))e=o(0);else break e;124===r.charCodeAt(n)&&124!==r.charCodeAt(n+1)&&(l=e,42===r.charCodeAt(n+1)?(e="*",n+=2):e=o(1)),i.push("*"===e?{type:s.Universal,namespace:l}:{type:s.Tag,name:e,namespace:l})}}}return p(),n}(t,`${e}`,0);if(r<e.length)throw Error(`Unmatched selector: ${e.slice(r)}`);return t}(e)}catch(e){throw Error("Invalid selector: ".concat(n()))}r.forEach(e=>{try{for(var r=e.length-1;r>=-1;r--){if(!e[r])throw Error();var i=e[r];if("child"===i.type||"parent"===i.type||"sibling"===i.type||"adjacent"===i.type||"descendant"===i.type)throw Error();if("attribute"===i.type&&"class"===i.name&&i.value===t)return}}catch(e){throw Error(aM(c||(c=ax(["\n        Invalid selector: ","\n    \n        Style selectors must target the '&' character (along with any modifiers), e.g. "," or ",".\n        \n        This is to ensure that each style block only affects the styling of a single class.\n        \n        If your selector is targeting another class, you should move it to the style definition for that class, e.g. given we have styles for 'parent' and 'child' elements, instead of adding a selector of ",") to 'parent', you should add "," to 'child').\n        \n        If your selector is targeting something global, use the 'globalStyle' function instead, e.g. if you wanted to write ",", you should instead write 'globalStyle(",", { ... })'\n      "])),n(),"`${parent} &`","`${parent} &:hover`","`& ${child}`","`${parent} &`","`& h1`","`${parent} h1`"))}})};class sa{constructor(){this.ruleset=new Map,this.precedenceLookup=new Map}findOrCreateCondition(e){var t=this.ruleset.get(e);return t||(t={query:e,rules:[],children:new sa},this.ruleset.set(e,t)),t}getConditionalRulesetByPath(e){var t=this;for(var r of e)t=t.findOrCreateCondition(r).children;return t}addRule(e,t,r){var n=this.getConditionalRulesetByPath(r).findOrCreateCondition(t);if(!n)throw Error("Failed to add conditional rule");n.rules.push(e)}addConditionPrecedence(e,t){for(var r=this.getConditionalRulesetByPath(e),n=0;n<t.length;n++){var i,o=t[n],a=null!=(i=r.precedenceLookup.get(o))?i:new Set;for(var s of t.slice(n+1))a.add(s);r.precedenceLookup.set(o,a)}}isCompatible(e){for(var[t,r]of this.precedenceLookup.entries())for(var n of r){var i;if(null!=(i=e.precedenceLookup.get(n))&&i.has(t))return!1}for(var{query:o,children:a}of e.ruleset.values()){var s=this.ruleset.get(o);if(s&&!s.children.isCompatible(a))return!1}return!0}merge(e){for(var{query:t,rules:r,children:n}of e.ruleset.values()){var i=this.ruleset.get(t);i?(i.rules.push(...r),i.children.merge(n)):this.ruleset.set(t,{query:t,rules:r,children:n})}for(var[o,a]of e.precedenceLookup.entries()){var s,l=null!=(s=this.precedenceLookup.get(o))?s:new Set;this.precedenceLookup.set(o,new Set([...l,...a]))}}mergeIfCompatible(e){return!!this.isCompatible(e)&&(this.merge(e),!0)}getSortedRuleset(){var e=this,t=[],r=function(r){var i=e.ruleset.get(n);if(!i)throw Error("Can't find condition for ".concat(n));var o=t.findIndex(e=>r.has(e.query));o>-1?t.splice(o,0,i):t.push(i)};for(var[n,i]of this.precedenceLookup.entries())r(i);return t}renderToArray(){var e=[];for(var{query:t,rules:r,children:n}of this.getSortedRuleset()){var i={};for(var o of r)i[o.selector]=st(st({},i[o.selector]),o.rule);Object.assign(i,...n.renderToArray()),e.push({[t]:i})}return e}}var ss={":-moz-any-link":!0,":-moz-full-screen":!0,":-moz-placeholder":!0,":-moz-read-only":!0,":-moz-read-write":!0,":-ms-fullscreen":!0,":-ms-input-placeholder":!0,":-webkit-any-link":!0,":-webkit-full-screen":!0,"::-moz-color-swatch":!0,"::-moz-list-bullet":!0,"::-moz-list-number":!0,"::-moz-page-sequence":!0,"::-moz-page":!0,"::-moz-placeholder":!0,"::-moz-progress-bar":!0,"::-moz-range-progress":!0,"::-moz-range-thumb":!0,"::-moz-range-track":!0,"::-moz-scrolled-page-sequence":!0,"::-moz-selection":!0,"::-ms-backdrop":!0,"::-ms-browse":!0,"::-ms-check":!0,"::-ms-clear":!0,"::-ms-fill-lower":!0,"::-ms-fill-upper":!0,"::-ms-fill":!0,"::-ms-reveal":!0,"::-ms-thumb":!0,"::-ms-ticks-after":!0,"::-ms-ticks-before":!0,"::-ms-tooltip":!0,"::-ms-track":!0,"::-ms-value":!0,"::-webkit-backdrop":!0,"::-webkit-calendar-picker-indicator":!0,"::-webkit-inner-spin-button":!0,"::-webkit-input-placeholder":!0,"::-webkit-meter-bar":!0,"::-webkit-meter-even-less-good-value":!0,"::-webkit-meter-inner-element":!0,"::-webkit-meter-optimum-value":!0,"::-webkit-meter-suboptimum-value":!0,"::-webkit-outer-spin-button":!0,"::-webkit-progress-bar":!0,"::-webkit-progress-inner-element":!0,"::-webkit-progress-inner-value":!0,"::-webkit-progress-value":!0,"::-webkit-resizer":!0,"::-webkit-scrollbar-button":!0,"::-webkit-scrollbar-corner":!0,"::-webkit-scrollbar-thumb":!0,"::-webkit-scrollbar-track-piece":!0,"::-webkit-scrollbar-track":!0,"::-webkit-scrollbar":!0,"::-webkit-search-cancel-button":!0,"::-webkit-search-results-button":!0,"::-webkit-slider-runnable-track":!0,"::-webkit-slider-thumb":!0,"::after":!0,"::backdrop":!0,"::before":!0,"::cue":!0,"::file-selector-button":!0,"::first-letter":!0,"::first-line":!0,"::grammar-error":!0,"::marker":!0,"::placeholder":!0,"::selection":!0,"::spelling-error":!0,"::target-text":!0,"::view-transition-group":!0,"::view-transition-image-pair":!0,"::view-transition-new":!0,"::view-transition-old":!0,"::view-transition":!0,":active":!0,":after":!0,":any-link":!0,":before":!0,":blank":!0,":checked":!0,":default":!0,":defined":!0,":disabled":!0,":empty":!0,":enabled":!0,":first-child":!0,":first-letter":!0,":first-line":!0,":first-of-type":!0,":first":!0,":focus-visible":!0,":focus-within":!0,":focus":!0,":fullscreen":!0,":hover":!0,":in-range":!0,":indeterminate":!0,":invalid":!0,":last-child":!0,":last-of-type":!0,":left":!0,":link":!0,":only-child":!0,":only-of-type":!0,":optional":!0,":out-of-range":!0,":placeholder-shown":!0,":read-only":!0,":read-write":!0,":required":!0,":right":!0,":root":!0,":scope":!0,":target":!0,":valid":!0,":visited":!0},sl=Object.keys(ss),sc=(e,t)=>Error(aM(u||(u=ax(['\n    Invalid media query: "','"\n\n    ',"\n\n    Read more on MDN: https://developer.mozilla.org/en-US/docs/Web/CSS/Media_Queries/Using_media_queries\n  "])),e,t)),su=e=>{if("@media "===e)throw sc(e,"Query is empty");try{aQ(a5(e))}catch(t){throw sc(e,t.message)}},sh=["vars"],sd=["content"],sf="__DECLARATION",sp={animationIterationCount:!0,borderImage:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,initialLetter:!0,lineClamp:!0,lineHeight:!0,maxLines:!0,opacity:!0,order:!0,orphans:!0,scale:!0,tabSize:!0,WebkitLineClamp:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,maskBorder:!0,maskBorderOutset:!0,maskBorderSlice:!0,maskBorderWidth:!0,shapeImageThreshold:!0,stopOpacity:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},sm=[...sl,"@layer","@media","@supports","@container","selectors"];class sg{constructor(e,t){this.rules=[],this.conditionalRulesets=[new sa],this.fontFaceRules=[],this.keyframesRules=[],this.propertyRules=[],this.localClassNamesMap=new Map(e.map(e=>[e,e])),this.localClassNamesSearch=new aw(e),this.layers=new Map,this.composedClassLists=t.map(e=>{var{identifier:t,classList:r}=e;return{identifier:t,regex:RegExp("(".concat(r,")"),"g")}}).reverse()}processCssObj(e){if("fontFace"===e.type)return void this.fontFaceRules.push(e.rule);if("property"===e.type)return void this.propertyRules.push(e);if("keyframes"===e.type){e.rule=Object.fromEntries(Object.entries(e.rule).map(e=>{var[t,r]=e;return[t,this.transformVars(this.transformProperties(r))]})),this.keyframesRules.push(e);return}if(this.currConditionalRuleset=new sa,"layer"===e.type){var t="@layer ".concat(e.name);this.addLayer([t])}else{var r=si(e.rule,sm);this.addRule({selector:e.selector,rule:r}),this.transformLayer(e,e.rule["@layer"]),this.transformMedia(e,e.rule["@media"]),this.transformSupports(e,e.rule["@supports"]),this.transformContainer(e,e.rule["@container"]),this.transformSimplePseudos(e,e.rule),this.transformSelectors(e,e.rule)}this.conditionalRulesets[this.conditionalRulesets.length-1].mergeIfCompatible(this.currConditionalRuleset)||this.conditionalRulesets.push(this.currConditionalRuleset)}addConditionalRule(e,t){var r=this.transformVars(this.transformProperties(e.rule)),n=this.transformSelector(e.selector);if(!this.currConditionalRuleset)throw Error("Couldn't add conditional rule");var i=t[t.length-1],o=t.slice(0,t.length-1);this.currConditionalRuleset.addRule({selector:n,rule:r},i,o)}addRule(e){var t=this.transformVars(this.transformProperties(e.rule)),r=this.transformSelector(e.selector);this.rules.push({selector:r,rule:t})}addLayer(e){var t=e.join(" - ");this.layers.set(t,e)}transformProperties(e){return this.transformContent(this.pixelifyProperties(e))}pixelifyProperties(e){return sn(e,(t,r)=>{"number"!=typeof t||0===t||sp[r]||(e[r]="".concat(t,"px"))}),e}transformVars(e){var{vars:t}=e,r=sr(e,sh);return t?st(st({},function(e,t){var r={};for(var n in e)r[t(e[n],n)]=e[n];return r}(t,(e,t)=>{var r;return(r=t.match(/^var\((.*)\)$/))?r[1]:t})),r):r}transformContent(e){var{content:t}=e,r=sr(e,sd);return void 0===t?r:st({content:(Array.isArray(t)?t:[t]).map(e=>e&&(e.includes('"')||e.includes("'")||/^([A-Za-z\-]+\([^]*|[^]*-quote|inherit|initial|none|normal|revert|unset)(\s|$)/.test(e))?e:'"'.concat(e,'"'))},r)}transformClassname(e){return".".concat(ab()(e,{isIdentifier:!0}))}transformSelector(e){var t=e,r=function(e){t=t.replace(i,()=>(!function(){aC().markCompositionUsed(...arguments)}(e),e))};for(var{identifier:n,regex:i}of this.composedClassLists)r(n);if(this.localClassNamesMap.has(t))return this.transformClassname(t);for(var o=this.localClassNamesSearch.search(t),a=t.length,s=o.length-1;s>=0;s--){var[l,[c]]=o[s],u=l-c.length+1;a<=l||(a=u,"."!==t[u-1]&&(t=function(e,t,r,n){var i=e.slice(0,t),o=e.slice(r);return"".concat(i).concat(n).concat(o)}(t,u,l+1,this.transformClassname(c))))}return t}transformSelectors(e,t,r){sn(t.selectors,(t,n)=>{if("local"!==e.type)throw Error("Selectors are not allowed within ".concat("global"===e.type?'"globalStyle"':'"selectors"'));var i=this.transformSelector(n.replace(RegExp("&","g"),e.selector));so(i,e.selector);var o={selector:i,rule:si(t,sm)};r?this.addConditionalRule(o,r):this.addRule(o);var a={type:"selector",selector:i,rule:t};this.transformLayer(a,t["@layer"],r),this.transformSupports(a,t["@supports"],r),this.transformMedia(a,t["@media"],r),this.transformContainer(a,t["@container"],r)})}transformMedia(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(t)for(var[i,o]of(null==(r=this.currConditionalRuleset)||r.addConditionPrecedence(n,Object.keys(t).map(e=>"@media ".concat(e))),Object.entries(t))){var a="@media ".concat(i);su(a);var s=[...n,a];this.addConditionalRule({selector:e.selector,rule:si(o,sm)},s),"local"===e.type&&(this.transformSimplePseudos(e,o,s),this.transformSelectors(e,o,s)),this.transformLayer(e,o["@layer"],s),this.transformSupports(e,o["@supports"],s),this.transformContainer(e,o["@container"],s)}}transformContainer(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];t&&(null==(r=this.currConditionalRuleset)||r.addConditionPrecedence(n,Object.keys(t).map(e=>"@container ".concat(e))),sn(t,(t,r)=>{var i=[...n,"@container ".concat(r)];this.addConditionalRule({selector:e.selector,rule:si(t,sm)},i),"local"===e.type&&(this.transformSimplePseudos(e,t,i),this.transformSelectors(e,t,i)),this.transformLayer(e,t["@layer"],i),this.transformSupports(e,t["@supports"],i),this.transformMedia(e,t["@media"],i)}))}transformLayer(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];t&&(null==(r=this.currConditionalRuleset)||r.addConditionPrecedence(n,Object.keys(t).map(e=>"@layer ".concat(e))),sn(t,(t,r)=>{var i=[...n,"@layer ".concat(r)];this.addLayer(i),this.addConditionalRule({selector:e.selector,rule:si(t,sm)},i),"local"===e.type&&(this.transformSimplePseudos(e,t,i),this.transformSelectors(e,t,i)),this.transformMedia(e,t["@media"],i),this.transformSupports(e,t["@supports"],i),this.transformContainer(e,t["@container"],i)}))}transformSupports(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];t&&(null==(r=this.currConditionalRuleset)||r.addConditionPrecedence(n,Object.keys(t).map(e=>"@supports ".concat(e))),sn(t,(t,r)=>{var i=[...n,"@supports ".concat(r)];this.addConditionalRule({selector:e.selector,rule:si(t,sm)},i),"local"===e.type&&(this.transformSimplePseudos(e,t,i),this.transformSelectors(e,t,i)),this.transformLayer(e,t["@layer"],i),this.transformMedia(e,t["@media"],i),this.transformContainer(e,t["@container"],i)}))}transformSimplePseudos(e,t,r){for(var n of Object.keys(t))if(ss[n]){if("local"!==e.type)throw Error("Simple pseudos are not valid in ".concat("global"===e.type?'"globalStyle"':'"selectors"'));r?this.addConditionalRule({selector:"".concat(e.selector).concat(n),rule:t[n]},r):this.addRule({conditions:r,selector:"".concat(e.selector).concat(n),rule:t[n]})}}toCss(){var e=[];for(var t of this.fontFaceRules)e.push(sv({"@font-face":t}));for(var r of this.propertyRules)e.push(sv({["@property ".concat(r.name)]:r.rule}));for(var n of this.keyframesRules)e.push(sv({["@keyframes ".concat(n.name)]:n.rule}));for(var i of this.layers.values()){var[o,...a]=i.reverse(),s={[o]:sf};for(var l of a)s={[l]:s};e.push(sv(s))}for(var c of this.rules)e.push(sv({[c.selector]:c.rule}));for(var u of this.conditionalRulesets)for(var h of u.renderToArray())e.push(sv(h));return e.filter(Boolean)}}function sv(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=[],n=function(n){var i=e[n];i&&Array.isArray(i)?r.push(...i.map(e=>sv({[n]:e},t))):i&&"object"==typeof i?0!==Object.keys(i).length&&r.push("".concat(t).concat(n," {\n").concat(sv(i,t+"  "),"\n").concat(t,"}")):i===sf?r.push("".concat(t).concat(n,";")):r.push("".concat(t).concat(n.startsWith("--")?n:n.replace(/([A-Z])/g,"-$1").replace(/^ms-/,"-ms-").toLowerCase(),": ").concat(i,";"))};for(var i of Object.keys(e))n(i);return r.join("\n")}var sy=r(37811);let sb="object"==typeof performance&&performance&&"function"==typeof performance.now?performance:Date,sw=new Set,sk="object"==typeof sy&&sy?sy:{},sC=(e,t,r,n)=>{"function"==typeof sk.emitWarning?sk.emitWarning(e,t,r,n):console.error(`[${r}] ${t}: ${e}`)},sE=globalThis.AbortController,sx=globalThis.AbortSignal;if(void 0===sE){sx=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(e,t){this._onabort.push(t)}},sE=class{constructor(){t()}signal=new sx;abort(e){if(!this.signal.aborted){for(let t of(this.signal.reason=e,this.signal.aborted=!0,this.signal._onabort))t(e);this.signal.onabort?.(e)}}};let e=sk.env?.LRU_CACHE_IGNORE_AC_WARNING!=="1",t=()=>{e&&(e=!1,sC("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}let sA=e=>!sw.has(e);Symbol("type");let s_=e=>e&&e===Math.floor(e)&&e>0&&isFinite(e),sR=e=>s_(e)?e<=256?Uint8Array:e<=65536?Uint16Array:e<=0x100000000?Uint32Array:e<=Number.MAX_SAFE_INTEGER?sS:null:null;class sS extends Array{constructor(e){super(e),this.fill(0)}}class sT{heap;length;static #O=!1;static create(e){let t=sR(e);if(!t)return[];sT.#O=!0;let r=new sT(e,t);return sT.#O=!1,r}constructor(e,t){if(!sT.#O)throw TypeError("instantiate Stack using Stack.create(n)");this.heap=new t(e),this.length=0}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}}class sO{#L;#j;#I;#N;#P;#M;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#B;#D;#F;#U;#z;#$;#V;#q;#W;#H;#K;#G;#J;#X;#Y;#Z;#Q;static unsafeExposeInternals(e){return{starts:e.#J,ttls:e.#X,sizes:e.#G,keyMap:e.#F,keyList:e.#U,valList:e.#z,next:e.#$,prev:e.#V,get head(){return e.#q},get tail(){return e.#W},free:e.#H,isBackgroundFetch:t=>e.#ee(t),backgroundFetch:(t,r,n,i)=>e.#et(t,r,n,i),moveToTail:t=>e.#er(t),indexes:t=>e.#en(t),rindexes:t=>e.#ei(t),isStale:t=>e.#eo(t)}}get max(){return this.#L}get maxSize(){return this.#j}get calculatedSize(){return this.#D}get size(){return this.#B}get fetchMethod(){return this.#P}get memoMethod(){return this.#M}get dispose(){return this.#I}get disposeAfter(){return this.#N}constructor(e){let{max:t=0,ttl:r,ttlResolution:n=1,ttlAutopurge:i,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:s,dispose:l,disposeAfter:c,noDisposeOnSet:u,noUpdateTTL:h,maxSize:d=0,maxEntrySize:f=0,sizeCalculation:p,fetchMethod:m,memoMethod:g,noDeleteOnFetchRejection:v,noDeleteOnStaleGet:y,allowStaleOnFetchRejection:b,allowStaleOnFetchAbort:w,ignoreFetchAbort:k}=e;if(0!==t&&!s_(t))throw TypeError("max option must be a nonnegative integer");let C=t?sR(t):Array;if(!C)throw Error("invalid max value: "+t);if(this.#L=t,this.#j=d,this.maxEntrySize=f||this.#j,this.sizeCalculation=p,this.sizeCalculation){if(!this.#j&&!this.maxEntrySize)throw TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if("function"!=typeof this.sizeCalculation)throw TypeError("sizeCalculation set to non-function")}if(void 0!==g&&"function"!=typeof g)throw TypeError("memoMethod must be a function if defined");if(this.#M=g,void 0!==m&&"function"!=typeof m)throw TypeError("fetchMethod must be a function if specified");if(this.#P=m,this.#Z=!!m,this.#F=new Map,this.#U=Array(t).fill(void 0),this.#z=Array(t).fill(void 0),this.#$=new C(t),this.#V=new C(t),this.#q=0,this.#W=0,this.#H=sT.create(t),this.#B=0,this.#D=0,"function"==typeof l&&(this.#I=l),"function"==typeof c?(this.#N=c,this.#K=[]):(this.#N=void 0,this.#K=void 0),this.#Y=!!this.#I,this.#Q=!!this.#N,this.noDisposeOnSet=!!u,this.noUpdateTTL=!!h,this.noDeleteOnFetchRejection=!!v,this.allowStaleOnFetchRejection=!!b,this.allowStaleOnFetchAbort=!!w,this.ignoreFetchAbort=!!k,0!==this.maxEntrySize){if(0!==this.#j&&!s_(this.#j))throw TypeError("maxSize must be a positive integer if specified");if(!s_(this.maxEntrySize))throw TypeError("maxEntrySize must be a positive integer if specified");this.#ea()}if(this.allowStale=!!s,this.noDeleteOnStaleGet=!!y,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=s_(n)||0===n?n:1,this.ttlAutopurge=!!i,this.ttl=r||0,this.ttl){if(!s_(this.ttl))throw TypeError("ttl must be a positive integer if specified");this.#es()}if(0===this.#L&&0===this.ttl&&0===this.#j)throw TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#L&&!this.#j){let e="LRU_CACHE_UNBOUNDED";sA(e)&&(sw.add(e),sC("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",e,sO))}}getRemainingTTL(e){return this.#F.has(e)?1/0:0}#es(){let e=new sS(this.#L),t=new sS(this.#L);this.#X=e,this.#J=t,this.#el=(r,n,i=sb.now())=>{if(t[r]=0!==n?i:0,e[r]=n,0!==n&&this.ttlAutopurge){let e=setTimeout(()=>{this.#eo(r)&&this.#ec(this.#U[r],"expire")},n+1);e.unref&&e.unref()}},this.#eu=r=>{t[r]=0!==e[r]?sb.now():0},this.#eh=(i,o)=>{if(e[o]){let a=e[o],s=t[o];if(!a||!s)return;i.ttl=a,i.start=s,i.now=r||n();let l=i.now-s;i.remainingTTL=a-l}};let r=0,n=()=>{let e=sb.now();if(this.ttlResolution>0){r=e;let t=setTimeout(()=>r=0,this.ttlResolution);t.unref&&t.unref()}return e};this.getRemainingTTL=i=>{let o=this.#F.get(i);if(void 0===o)return 0;let a=e[o],s=t[o];return a&&s?a-((r||n())-s):1/0},this.#eo=i=>{let o=t[i],a=e[i];return!!a&&!!o&&(r||n())-o>a}}#eu=()=>{};#eh=()=>{};#el=()=>{};#eo=()=>!1;#ea(){let e=new sS(this.#L);this.#D=0,this.#G=e,this.#ed=t=>{this.#D-=e[t],e[t]=0},this.#ef=(e,t,r,n)=>{if(this.#ee(t))return 0;if(!s_(r))if(n){if("function"!=typeof n)throw TypeError("sizeCalculation must be a function");if(!s_(r=n(t,e)))throw TypeError("sizeCalculation return invalid (expect positive integer)")}else throw TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return r},this.#ep=(t,r,n)=>{if(e[t]=r,this.#j){let r=this.#j-e[t];for(;this.#D>r;)this.#em(!0)}this.#D+=e[t],n&&(n.entrySize=r,n.totalCalculatedSize=this.#D)}}#ed=e=>{};#ep=(e,t,r)=>{};#ef=(e,t,r,n)=>{if(r||n)throw TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#en({allowStale:e=this.allowStale}={}){if(this.#B)for(let t=this.#W;this.#eg(t)&&((e||!this.#eo(t))&&(yield t),t!==this.#q);)t=this.#V[t]}*#ei({allowStale:e=this.allowStale}={}){if(this.#B)for(let t=this.#q;this.#eg(t)&&((e||!this.#eo(t))&&(yield t),t!==this.#W);)t=this.#$[t]}#eg(e){return void 0!==e&&this.#F.get(this.#U[e])===e}*entries(){for(let e of this.#en())void 0===this.#z[e]||void 0===this.#U[e]||this.#ee(this.#z[e])||(yield[this.#U[e],this.#z[e]])}*rentries(){for(let e of this.#ei())void 0===this.#z[e]||void 0===this.#U[e]||this.#ee(this.#z[e])||(yield[this.#U[e],this.#z[e]])}*keys(){for(let e of this.#en()){let t=this.#U[e];void 0===t||this.#ee(this.#z[e])||(yield t)}}*rkeys(){for(let e of this.#ei()){let t=this.#U[e];void 0===t||this.#ee(this.#z[e])||(yield t)}}*values(){for(let e of this.#en())void 0===this.#z[e]||this.#ee(this.#z[e])||(yield this.#z[e])}*rvalues(){for(let e of this.#ei())void 0===this.#z[e]||this.#ee(this.#z[e])||(yield this.#z[e])}[Symbol.iterator](){return this.entries()}[Symbol.toStringTag]="LRUCache";find(e,t={}){for(let r of this.#en()){let n=this.#z[r],i=this.#ee(n)?n.__staleWhileFetching:n;if(void 0!==i&&e(i,this.#U[r],this))return this.get(this.#U[r],t)}}forEach(e,t=this){for(let r of this.#en()){let n=this.#z[r],i=this.#ee(n)?n.__staleWhileFetching:n;void 0!==i&&e.call(t,i,this.#U[r],this)}}rforEach(e,t=this){for(let r of this.#ei()){let n=this.#z[r],i=this.#ee(n)?n.__staleWhileFetching:n;void 0!==i&&e.call(t,i,this.#U[r],this)}}purgeStale(){let e=!1;for(let t of this.#ei({allowStale:!0}))this.#eo(t)&&(this.#ec(this.#U[t],"expire"),e=!0);return e}info(e){let t=this.#F.get(e);if(void 0===t)return;let r=this.#z[t],n=this.#ee(r)?r.__staleWhileFetching:r;if(void 0===n)return;let i={value:n};if(this.#X&&this.#J){let e=this.#X[t],r=this.#J[t];e&&r&&(i.ttl=e-(sb.now()-r),i.start=Date.now())}return this.#G&&(i.size=this.#G[t]),i}dump(){let e=[];for(let t of this.#en({allowStale:!0})){let r=this.#U[t],n=this.#z[t],i=this.#ee(n)?n.__staleWhileFetching:n;if(void 0===i||void 0===r)continue;let o={value:i};if(this.#X&&this.#J){o.ttl=this.#X[t];let e=sb.now()-this.#J[t];o.start=Math.floor(Date.now()-e)}this.#G&&(o.size=this.#G[t]),e.unshift([r,o])}return e}load(e){for(let[t,r]of(this.clear(),e)){if(r.start){let e=Date.now()-r.start;r.start=sb.now()-e}this.set(t,r.value,r)}}set(e,t,r={}){if(void 0===t)return this.delete(e),this;let{ttl:n=this.ttl,start:i,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:s}=r,{noUpdateTTL:l=this.noUpdateTTL}=r,c=this.#ef(e,t,r.size||0,a);if(this.maxEntrySize&&c>this.maxEntrySize)return s&&(s.set="miss",s.maxEntrySizeExceeded=!0),this.#ec(e,"set"),this;let u=0===this.#B?void 0:this.#F.get(e);if(void 0===u)u=0===this.#B?this.#W:0!==this.#H.length?this.#H.pop():this.#B===this.#L?this.#em(!1):this.#B,this.#U[u]=e,this.#z[u]=t,this.#F.set(e,u),this.#$[this.#W]=u,this.#V[u]=this.#W,this.#W=u,this.#B++,this.#ep(u,c,s),s&&(s.set="add"),l=!1;else{this.#er(u);let r=this.#z[u];if(t!==r){if(this.#Z&&this.#ee(r)){r.__abortController.abort(Error("replaced"));let{__staleWhileFetching:t}=r;void 0!==t&&!o&&(this.#Y&&this.#I?.(t,e,"set"),this.#Q&&this.#K?.push([t,e,"set"]))}else!o&&(this.#Y&&this.#I?.(r,e,"set"),this.#Q&&this.#K?.push([r,e,"set"]));if(this.#ed(u),this.#ep(u,c,s),this.#z[u]=t,s){s.set="replace";let e=r&&this.#ee(r)?r.__staleWhileFetching:r;void 0!==e&&(s.oldValue=e)}}else s&&(s.set="update")}if(0===n||this.#X||this.#es(),this.#X&&(l||this.#el(u,n,i),s&&this.#eh(s,u)),!o&&this.#Q&&this.#K){let e,t=this.#K;for(;e=t?.shift();)this.#N?.(...e)}return this}pop(){try{for(;this.#B;){let e=this.#z[this.#q];if(this.#em(!0),this.#ee(e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(void 0!==e)return e}}finally{if(this.#Q&&this.#K){let e,t=this.#K;for(;e=t?.shift();)this.#N?.(...e)}}}#em(e){let t=this.#q,r=this.#U[t],n=this.#z[t];return this.#Z&&this.#ee(n)?n.__abortController.abort(Error("evicted")):(this.#Y||this.#Q)&&(this.#Y&&this.#I?.(n,r,"evict"),this.#Q&&this.#K?.push([n,r,"evict"])),this.#ed(t),e&&(this.#U[t]=void 0,this.#z[t]=void 0,this.#H.push(t)),1===this.#B?(this.#q=this.#W=0,this.#H.length=0):this.#q=this.#$[t],this.#F.delete(r),this.#B--,t}has(e,t={}){let{updateAgeOnHas:r=this.updateAgeOnHas,status:n}=t,i=this.#F.get(e);if(void 0!==i){let e=this.#z[i];if(this.#ee(e)&&void 0===e.__staleWhileFetching)return!1;if(!this.#eo(i))return r&&this.#eu(i),n&&(n.has="hit",this.#eh(n,i)),!0;n&&(n.has="stale",this.#eh(n,i))}else n&&(n.has="miss");return!1}peek(e,t={}){let{allowStale:r=this.allowStale}=t,n=this.#F.get(e);if(void 0===n||!r&&this.#eo(n))return;let i=this.#z[n];return this.#ee(i)?i.__staleWhileFetching:i}#et(e,t,r,n){let i=void 0===t?void 0:this.#z[t];if(this.#ee(i))return i;let o=new sE,{signal:a}=r;a?.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});let s={signal:o.signal,options:r,context:n},l=(n,i=!1)=>{let{aborted:a}=o.signal,l=r.ignoreFetchAbort&&void 0!==n;return(r.status&&(a&&!i?(r.status.fetchAborted=!0,r.status.fetchError=o.signal.reason,l&&(r.status.fetchAbortIgnored=!0)):r.status.fetchResolved=!0),!a||l||i)?(this.#z[t]===u&&(void 0===n?u.__staleWhileFetching?this.#z[t]=u.__staleWhileFetching:this.#ec(e,"fetch"):(r.status&&(r.status.fetchUpdated=!0),this.set(e,n,s.options))),n):c(o.signal.reason)},c=n=>{let{aborted:i}=o.signal,a=i&&r.allowStaleOnFetchAbort,s=a||r.allowStaleOnFetchRejection,l=s||r.noDeleteOnFetchRejection;if(this.#z[t]===u&&(l&&void 0!==u.__staleWhileFetching?a||(this.#z[t]=u.__staleWhileFetching):this.#ec(e,"fetch")),s)return r.status&&void 0!==u.__staleWhileFetching&&(r.status.returnedStale=!0),u.__staleWhileFetching;if(u.__returned===u)throw n};r.status&&(r.status.fetchDispatched=!0);let u=new Promise((t,n)=>{let a=this.#P?.(e,i,s);a&&a instanceof Promise&&a.then(e=>t(void 0===e?void 0:e),n),o.signal.addEventListener("abort",()=>{(!r.ignoreFetchAbort||r.allowStaleOnFetchAbort)&&(t(void 0),r.allowStaleOnFetchAbort&&(t=e=>l(e,!0)))})}).then(l,e=>(r.status&&(r.status.fetchRejected=!0,r.status.fetchError=e),c(e))),h=Object.assign(u,{__abortController:o,__staleWhileFetching:i,__returned:void 0});return void 0===t?(this.set(e,h,{...s.options,status:void 0}),t=this.#F.get(e)):this.#z[t]=h,h}#ee(e){return!!this.#Z&&!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof sE}async fetch(e,t={}){let{allowStale:r=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:s=0,sizeCalculation:l=this.sizeCalculation,noUpdateTTL:c=this.noUpdateTTL,noDeleteOnFetchRejection:u=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:h=this.allowStaleOnFetchRejection,ignoreFetchAbort:d=this.ignoreFetchAbort,allowStaleOnFetchAbort:f=this.allowStaleOnFetchAbort,context:p,forceRefresh:m=!1,status:g,signal:v}=t;if(!this.#Z)return g&&(g.fetch="get"),this.get(e,{allowStale:r,updateAgeOnGet:n,noDeleteOnStaleGet:i,status:g});let y={allowStale:r,updateAgeOnGet:n,noDeleteOnStaleGet:i,ttl:o,noDisposeOnSet:a,size:s,sizeCalculation:l,noUpdateTTL:c,noDeleteOnFetchRejection:u,allowStaleOnFetchRejection:h,allowStaleOnFetchAbort:f,ignoreFetchAbort:d,status:g,signal:v},b=this.#F.get(e);if(void 0===b){g&&(g.fetch="miss");let t=this.#et(e,b,y,p);return t.__returned=t}{let t=this.#z[b];if(this.#ee(t)){let e=r&&void 0!==t.__staleWhileFetching;return g&&(g.fetch="inflight",e&&(g.returnedStale=!0)),e?t.__staleWhileFetching:t.__returned=t}let i=this.#eo(b);if(!m&&!i)return g&&(g.fetch="hit"),this.#er(b),n&&this.#eu(b),g&&this.#eh(g,b),t;let o=this.#et(e,b,y,p),a=void 0!==o.__staleWhileFetching&&r;return g&&(g.fetch=i?"stale":"refresh",a&&i&&(g.returnedStale=!0)),a?o.__staleWhileFetching:o.__returned=o}}async forceFetch(e,t={}){let r=await this.fetch(e,t);if(void 0===r)throw Error("fetch() returned undefined");return r}memo(e,t={}){let r=this.#M;if(!r)throw Error("no memoMethod provided to constructor");let{context:n,forceRefresh:i,...o}=t,a=this.get(e,o);if(!i&&void 0!==a)return a;let s=r(e,a,{options:o,context:n});return this.set(e,s,o),s}get(e,t={}){let{allowStale:r=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,status:o}=t,a=this.#F.get(e);if(void 0!==a){let t=this.#z[a],s=this.#ee(t);return(o&&this.#eh(o,a),this.#eo(a))?(o&&(o.get="stale"),s)?(o&&r&&void 0!==t.__staleWhileFetching&&(o.returnedStale=!0),r?t.__staleWhileFetching:void 0):(i||this.#ec(e,"expire"),o&&r&&(o.returnedStale=!0),r?t:void 0):(o&&(o.get="hit"),s)?t.__staleWhileFetching:(this.#er(a),n&&this.#eu(a),t)}o&&(o.get="miss")}#ev(e,t){this.#V[t]=e,this.#$[e]=t}#er(e){e!==this.#W&&(e===this.#q?this.#q=this.#$[e]:this.#ev(this.#V[e],this.#$[e]),this.#ev(this.#W,e),this.#W=e)}delete(e){return this.#ec(e,"delete")}#ec(e,t){let r=!1;if(0!==this.#B){let n=this.#F.get(e);if(void 0!==n)if(r=!0,1===this.#B)this.#ey(t);else{this.#ed(n);let r=this.#z[n];if(this.#ee(r)?r.__abortController.abort(Error("deleted")):(this.#Y||this.#Q)&&(this.#Y&&this.#I?.(r,e,t),this.#Q&&this.#K?.push([r,e,t])),this.#F.delete(e),this.#U[n]=void 0,this.#z[n]=void 0,n===this.#W)this.#W=this.#V[n];else if(n===this.#q)this.#q=this.#$[n];else{let e=this.#V[n];this.#$[e]=this.#$[n];let t=this.#$[n];this.#V[t]=this.#V[n]}this.#B--,this.#H.push(n)}}if(this.#Q&&this.#K?.length){let e,t=this.#K;for(;e=t?.shift();)this.#N?.(...e)}return r}clear(){return this.#ey("delete")}#ey(e){for(let t of this.#ei({allowStale:!0})){let r=this.#z[t];if(this.#ee(r))r.__abortController.abort(Error("deleted"));else{let n=this.#U[t];this.#Y&&this.#I?.(r,n,e),this.#Q&&this.#K?.push([r,n,e])}}if(this.#F.clear(),this.#z.fill(void 0),this.#U.fill(void 0),this.#X&&this.#J&&(this.#X.fill(0),this.#J.fill(0)),this.#G&&this.#G.fill(0),this.#q=0,this.#W=0,this.#H.length=0,this.#D=0,this.#B=0,this.#Q&&this.#K){let e,t=this.#K;for(;e=t?.shift();)this.#N?.(...e)}}}r(73940);var sL=new Set,sj=[],sI=[];aE||(e=>{if(!e)throw Error('No adapter provided when calling "setAdapter"');aE=!0,ak.push(e)})({appendCss:e=>{sI.push(e)},registerClassName:e=>{sL.add(e)},registerComposition:e=>{sj.push(e)},markCompositionUsed:()=>{},onEndFileScope:e=>{av({fileScope:e,css:(function(e){var{localClassNames:t,cssObjs:r,composedClassLists:n}=e,i=new sg(t,n);for(var o of r)i.processCssObj(o);return i.toCss()})({localClassNames:Array.from(sL),composedClassLists:sj,cssObjs:sI}).join("\n")}),sI=[]},getIdentOption:()=>"short"});var sN=(e,t)=>{for(var r=t-1;r>=0;){if("/"===e[r])return r;r--}return -1},sP=e=>{var t,r=e.lastIndexOf(".css");if(-1===r)return"";var n=sN(e,r);if(t=e.slice(n+1,r),-1===n)return t;var i=sN(e,n-1),o=e.slice(i+1,n);return"index"!==t?t:o},sM=(()=>{var e=new sO({max:500});return t=>{var r=e.get(t);if(r)return r;var n=sP(t);return e.set(t,n),n}})();function sB(e){return e.match(/^[0-9]/)?"_".concat(e):e}function sD(e){var t=getIdentOption(),{debugId:r,debugFileName:n=!0}=_objectSpread2(_objectSpread2({},"string"==typeof e?{debugId:e}:null),"object"==typeof e?e:null),i=getAndIncrementRefCounter().toString(36),{filePath:o,packageName:a}=getFileScope(),s=hash(a?"".concat(a).concat(o):o),l="".concat(s).concat(i);if("debug"===t){var c=function(e){var{debugId:t,debugFileName:r}=e,n=t?[t.replace(/\s/g,"_")]:[];if(r){var{filePath:i}=getFileScope(),o=sM(i);o&&n.unshift(o)}return n.join("_")}({debugId:r,debugFileName:n});return c&&(l="".concat(c,"__").concat(l)),sB(l)}if("function"==typeof t){if(!(l=t({hash:l,debugId:r,filePath:o,packageName:a})).match(/^[A-Z_][0-9A-Z_-]+$/i))throw Error('Identifier function returned invalid indentifier: "'.concat(l,'"'));return l}return sB(l)}var sF=e=>walkObject(e,()=>"");function sU(e,t,r){var n=[...Array(t).keys()].map(()=>"  ").join(""),i="".concat(r||" ").concat(n).concat(e);return"-"===r?pc.red(i):"+"===r?pc.green(i):i}var sz=e=>{var{syntax:t,inherits:r,initialValue:n}=e;return _objectSpread2({syntax:'"'.concat(Array.isArray(t)?t.join(" | "):t,'"'),inherits:r?"true":"false"},null!=n?{initialValue:n}:{})},s$={},sV=(e,t)=>_objectSpread2(_objectSpread2({},e),t);let sq={tokens:{"border-style":{solid:"solid",dashed:"dashed"},color:{transparent:"transparent",white:"#ffffff",black:"#000000",surface:{1:{light:"#ffffff",dark:"#18191b"},2:{light:"#f9f9f8",dark:"#111110"}},alpha:{white:{1:"rgba(255, 255, 255, 0.05)",2:"rgba(255, 255, 255, 0.1)",3:"rgba(255, 255, 255, 0.15)",4:"rgba(255, 255, 255, 0.2)",5:"rgba(255, 255, 255, 0.3)",6:"rgba(255, 255, 255, 0.4)",7:"rgba(255, 255, 255, 0.5)",8:"rgba(255, 255, 255, 0.6)",9:"rgba(255, 255, 255, 0.7)",10:"rgba(255, 255, 255, 0.8)",11:"rgba(255, 255, 255, 0.9)",12:"rgba(255, 255, 255, 0.95)"},black:{1:"rgba(0, 0, 0, 0.05)",2:"rgba(0, 0, 0, 0.1)",3:"rgba(0, 0, 0, 0.15)",4:"rgba(0, 0, 0, 0.2)",5:"rgba(0, 0, 0, 0.3)",6:"rgba(0, 0, 0, 0.4)",7:"rgba(0, 0, 0, 0.5)",8:"rgba(0, 0, 0, 0.6)",9:"rgba(0, 0, 0, 0.7)",10:"rgba(0, 0, 0, 0.8)",11:"rgba(0, 0, 0, 0.9)",12:"rgba(0, 0, 0, 0.95)"}},gray:{1:{light:"#fcfcfd",dark:"#111113"},2:{light:"#f9f9fb",dark:"#18191b"},3:{light:"#f0f0f3",dark:"#212225"},4:{light:"#e8e8ec",dark:"#272a2d"},5:{light:"#e0e1e6",dark:"#2e3135"},6:{light:"#d9d9e0",dark:"#363a3f"},7:{light:"#cdced6",dark:"#43484e"},8:{light:"#b9bbc6",dark:"#5a6169"},9:{light:"#8b8d98",dark:"#696e77"},10:{light:"#80838d",dark:"#777b84"},11:{light:"#60646c",dark:"#b0b4ba"},12:{light:"#1c2024",dark:"#edeef0"}},beige:{1:{light:"#fdfdfc",dark:"#111110"},2:{light:"#f9f9f8",dark:"#191918"},3:{light:"#f1f0ef",dark:"#222221"},4:{light:"#e9e8e6",dark:"#2a2a28"},5:{light:"#e2e1de",dark:"#31312e"},6:{light:"#dad9d6",dark:"#3b3a37"},7:{light:"#cfceca",dark:"#494844"},8:{light:"#bcbbb5",dark:"#62605b"},9:{light:"#8d8d86",dark:"#6f6d66"},10:{light:"#82827c",dark:"#7c7b74"},11:{light:"#63635e",dark:"#b5b3ad"},12:{light:"#21201c",dark:"#eeeeec"}},accent:{1:{light:"#fffcfc",dark:"#181111"},2:{light:"#fff8f7",dark:"#1f1513"},3:{light:"#feebe7",dark:"#391714"},4:{light:"#ffdcd3",dark:"#4e1511"},5:{light:"#ffcdc2",dark:"#5e1c16"},6:{light:"#fdbdaf",dark:"#6e2920"},7:{light:"#f5a898",dark:"#853a2d"},8:{light:"#ec8e7b",dark:"#ac4d39"},9:{light:"#e54d2e",dark:"#e54d2e"},10:{light:"#dd4425",dark:"#ec6142"},11:{light:"#d13415",dark:"#ff977d"},12:{light:"#5c271f",dark:"#fbd3cb"}},green:{1:{light:"#fbfefd",dark:"#0d1512"},2:{light:"#f4fbf7",dark:"#121c18"},3:{light:"#e6f7ed",dark:"#0f2e22"},4:{light:"#d6f1e3",dark:"#0b3b2c"},5:{light:"#c3e9d7",dark:"#114837"},6:{light:"#acdec8",dark:"#1b5745"},7:{light:"#8bceb6",dark:"#246854"},8:{light:"#56ba9f",dark:"#2a7e68"},9:{light:"#29a383",dark:"#29a383"},10:{light:"#26997b",dark:"#27b08b"},11:{light:"#208368",dark:"#1fd8a4"},12:{light:"#1d3b31",dark:"#adf0d4"}},yellow:{1:{light:"#FEFDFB",dark:"#16120C"},2:{light:"#FEFBE9",dark:"#1D180F"},3:{light:"#FFF7C2",dark:"#302008"},4:{light:"#FFEE9C",dark:"#3F2700"},5:{light:"#FBE577",dark:"#4D3000"},6:{light:"#F3D673",dark:"#5C3D05"},7:{light:"#E9C162",dark:"#714F19"},8:{light:"#F3D673",dark:"#8F6424"},9:{light:"#FFC53D",dark:"#FFC53D"},10:{light:"#FFBA18",dark:"#FFD60A"},11:{light:"#AB6400",dark:"#FFCA16"},12:{light:"#4F3422",dark:"#FFE7B3"}},blue:{1:{light:"#fdfdfe",dark:"#11131f"},2:{light:"#f7f9ff",dark:"#141726"},3:{light:"#edf2fe",dark:"#182449"},4:{light:"#e1e9ff",dark:"#1d2e62"},5:{light:"#d2deff",dark:"#253974"},6:{light:"#c1d0ff",dark:"#304384"},7:{light:"#abbdf9",dark:"#3a4f97"},8:{light:"#8da4ef",dark:"#435db1"},9:{light:"#3e63dd",dark:"#3e63dd"},10:{light:"#3358d4",dark:"#5472e4"},11:{light:"#3a5bc7",dark:"#9eb1ff"},12:{light:"#1f2d5c",dark:"#d6e1ff"}},red:{1:{light:"#fffcfd",dark:"#191113"},2:{light:"#fff7f8",dark:"#1e1517"},3:{light:"#feeaed",dark:"#3a141e"},4:{light:"#ffdce1",dark:"#4e1325"},5:{light:"#ffced6",dark:"#5e1a2e"},6:{light:"#f8bfc8",dark:"#6f2539"},7:{light:"#efacb8",dark:"#883447"},8:{light:"#e592a3",dark:"#b3445a"},9:{light:"#e54666",dark:"#e54666"},10:{light:"#dc3b5d",dark:"#ec5a72"},11:{light:"#ca244d",dark:"#ff949d"},12:{light:"#64172b",dark:"#fed2e1"}},purple:{1:{light:"#fdfcfe",dark:"#14121f"},2:{light:"#faf8ff",dark:"#1b1525"},3:{light:"#f4f0fe",dark:"#291f43"},4:{light:"#ebe4ff",dark:"#33255b"},5:{light:"#e1d9ff",dark:"#3c2e69"},6:{light:"#d4cafe",dark:"#473876"},7:{light:"#c2b5f5",dark:"#56468b"},8:{light:"#aa99ec",dark:"#6958ad"},9:{light:"#654dc4",dark:"#6e56cf"},10:{light:"#654dc4",dark:"#7d66d9"},11:{light:"#6550b9",dark:"#baa7ff"},12:{light:"#2f265f",dark:"#e2ddfe"}}},rounded:{0:"0px",1:"0.125rem",2:"0.25rem",3:"0.375rem",4:"0.5rem",5:"0.75rem",6:"1rem",full:"9999px"},shadow:{0:"0px 0px 0px 0px rgba(0, 0, 0, 0.00)",inner:"0px 5px 2px 0px rgba(28, 32, 36, 0.01) inset, 0px 3px 2px 0px rgba(28, 32, 36, 0.03) inset, 0px 1px 1px 0px rgba(28, 32, 36, 0.05) inset, 0px 0px 1px 0px rgba(28, 32, 36, 0.06) inset",1:"0px 5px 2px 0px rgba(28, 32, 36, 0.01), 0px 3px 2px 0px rgba(28, 32, 36, 0.03), 0px 1px 1px 0px rgba(28, 32, 36, 0.05), 0px 0px 1px 0px rgba(28, 32, 36, 0.06)",2:"0px 16px 7px 0px rgba(28, 32, 36, 0.01), 0px 9px 6px 0px rgba(28, 32, 36, 0.03), 0px 4px 4px 0px rgba(28, 32, 36, 0.05), 0px 1px 2px 0px rgba(28, 32, 36, 0.06)",3:"0px 29px 12px 0px rgba(28, 32, 36, 0.01), 0px 16px 10px 0px rgba(28, 32, 36, 0.03), 0px 7px 7px 0px rgba(28, 32, 36, 0.05), 0px 2px 4px 0px rgba(28, 32, 36, 0.06)"},spacing:{px:"1px",0:"0px",1:"0.25rem",2:"0.5rem",3:"0.75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem",140:"35rem",160:"40rem",full:"100%",auto:"auto"},family:{sans:"Inter, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif",mono:"Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace"},leading:{0:"1rem",1:"1rem",2:"1.25rem",3:"1.5rem",4:"1.75rem",5:"1.75rem",6:"2rem",7:"2.25rem",8:"2.5rem",9:"3.5rem","code-0":"1.0rem","code-1":"1.0rem","code-2":"1.25rem","code-3":"1.5rem","code-4":"1.75rem","code-5":"1.75rem","code-6":"2.0rem","code-7":"2.25rem","code-8":"2.5rem","code-9":"3.0rem"},tracking:{0:"0.25%",1:"0.25%",2:"0",3:"0",4:"-0.25%",5:"-0.5%",6:"-0.625%",7:"-0.75%",8:"-1%",9:"-2.5%"},text:{0:"0.6875rem",1:"0.75rem",2:"0.875rem",3:"1rem",4:"1.125rem",5:"1.25rem",6:"1.5rem",7:"1.875rem",8:"2.25rem",9:"3rem","code-0":"0.625rem","code-1":"0.688rem","code-2":"0.812rem","code-4":"1.062rem","code-5":"1.188rem","code-6":"1.438rem","code-7":"1.75rem","code-8":"2.125rem","code-9":"2.875rem"},weight:{regular:"400",medium:"500","semi-bold":"600"},breakpoint:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},zIndex:{hidden:"-1",base:"0",auto:"auto",dropdown:"1000",sticky:"1100",banner:"1200",overlay:"1300",modal:"1400",popover:"1500",skipLink:"1600",toast:"1700",tooltip:"1800"}}};sq.tokens.breakpoint.sm,sq.tokens.breakpoint.md,sq.tokens.breakpoint.lg,sq.tokens.breakpoint.xl,sq.tokens.breakpoint["2xl"];let sW=({currentValueOfCssVar:e="0 0 0 0",value:t,direction:r})=>{let[n,i,o,a]=e.split(" "),s={top:n,right:i,bottom:o,left:a};return"top"===r&&(s.top=t),"right"===r&&(s.right=t),"bottom"===r&&(s.bottom=t),"left"===r&&(s.left=t),"all"===r&&(s.top=t,s.right=t,s.bottom=t,s.left=t),"x"===r&&(s.left=t,s.right=t),"y"===r&&(s.top=t,s.bottom=t),"side-top"===r&&(s.top=t,s.right=t),"side-bottom"===r&&(s.bottom=t,s.left=t),"side-left"===r&&(s.top=t,s.left=t),"side-right"===r&&(s.right=t,s.bottom=t),Object.values(s).join(" ")},sH=e=>{let{cssVars:t}=e;if(!(null!=e&&e.props)||0===Object.keys(e.props).length)return{styleProp:{},otherProps:{},interactive:!1};let{style:r={},...n}=e.props,i=r,o={},a=!1;return Object.keys(n).forEach(e=>{let r=null==t?void 0:t[e];if(!r)return void Object.assign(o,{[e]:n[e]});let s=null==n?void 0:n[e];if(!s)return void Object.assign(i,{[e]:n[e]});let l=r.value.replace("VARIABLE",s),c=r.cssVar;if(r.interactive&&(a=!0),r.direction){let e=sW({currentValueOfCssVar:null==i?void 0:i[c],value:l,direction:r.direction});i={...i,[c]:e};return}i={...i,[c]:l}}),{styleProp:i,otherProps:o,interactive:a}},sK=e=>p.useMemo(()=>sH(e),[e]),sG={display:{cssVar:"--display",value:"VARIABLE"},backgroundColor:{cssVar:"--background-color",value:"var(--tgph-VARIABLE)"},hover_backgroundColor:{cssVar:"--hover_backgroundColor",value:"var(--tgph-VARIABLE)",interactive:!0},focus_backgroundColor:{cssVar:"--focus_backgroundColor",value:"var(--tgph-VARIABLE)",interactive:!0},active_backgroundColor:{cssVar:"--active_backgroundColor",interactive:!0,value:"var(--tgph-VARIABLE)"},borderStyle:{cssVar:"--border-style",value:"var(--tgph-border-style-VARIABLE)"},padding:{cssVar:"--padding",value:"var(--tgph-spacing-VARIABLE)",direction:"all"},paddingX:{cssVar:"--padding",value:"var(--tgph-spacing-VARIABLE)",direction:"x"},paddingY:{cssVar:"--padding",value:"var(--tgph-spacing-VARIABLE)",direction:"y"},paddingTop:{cssVar:"--padding",value:"var(--tgph-spacing-VARIABLE)",direction:"top"},paddingBottom:{cssVar:"--padding",value:"var(--tgph-spacing-VARIABLE)",direction:"bottom"},paddingLeft:{cssVar:"--padding",value:"var(--tgph-spacing-VARIABLE)",direction:"left"},paddingRight:{cssVar:"--padding",value:"var(--tgph-spacing-VARIABLE)",direction:"right"},margin:{cssVar:"--margin",value:"var(--tgph-spacing-VARIABLE)",direction:"all"},marginX:{cssVar:"--margin",value:"var(--tgph-spacing-VARIABLE)",direction:"x"},marginY:{cssVar:"--margin",value:"var(--tgph-spacing-VARIABLE)",direction:"y"},marginTop:{cssVar:"--margin",value:"var(--tgph-spacing-VARIABLE)",direction:"top"},marginBottom:{cssVar:"--margin",value:"var(--tgph-spacing-VARIABLE)",direction:"bottom"},marginLeft:{cssVar:"--margin",value:"var(--tgph-spacing-VARIABLE)",direction:"left"},marginRight:{cssVar:"--margin",value:"var(--tgph-spacing-VARIABLE)",direction:"right"},borderColor:{cssVar:"--border-color",value:"var(--tgph-VARIABLE)"},borderWidth:{cssVar:"--border-width",value:"var(--tgph-spacing-VARIABLE)"},borderTopWidth:{cssVar:"--border-width",value:"var(--tgph-spacing-VARIABLE)",direction:"top"},borderBottomWidth:{cssVar:"--border-width",value:"var(--tgph-spacing-VARIABLE)",direction:"bottom"},borderLeftWidth:{cssVar:"--border-width",value:"var(--tgph-spacing-VARIABLE)",direction:"left"},borderRightWidth:{cssVar:"--border-width",value:"var(--tgph-spacing-VARIABLE)",direction:"right"},borderRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)"},borderTopLeftRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"top"},borderTopRightRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"right"},borderBottomLeftRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"left"},borderBottomRightRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"bottom"},borderTopRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"side-top"},borderBottomRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"side-bottom"},borderLeftRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"side-left"},borderRightRadius:{cssVar:"--border-radius",value:"var(--tgph-rounded-VARIABLE)",direction:"side-right"},boxShadow:{cssVar:"--box-shadow",value:"var(--tgph-shadow-VARIABLE)"},width:{cssVar:"--width",value:"var(--tgph-spacing-VARIABLE)"},height:{cssVar:"--height",value:"var(--tgph-spacing-VARIABLE)"},minWidth:{cssVar:"--min-width",value:"var(--tgph-spacing-VARIABLE)"},minHeight:{cssVar:"--min-height",value:"var(--tgph-spacing-VARIABLE)"},maxWidth:{cssVar:"--max-width",value:"var(--tgph-spacing-VARIABLE)"},maxHeight:{cssVar:"--max-height",value:"var(--tgph-spacing-VARIABLE)"},zIndex:{cssVar:"--z-index",value:"var(--tgph-zIndex-VARIABLE)"},position:{cssVar:"--position",value:"VARIABLE"},top:{cssVar:"--top",value:"var(--tgph-spacing-VARIABLE)"},left:{cssVar:"--left",value:"var(--tgph-spacing-VARIABLE)"},right:{cssVar:"--right",value:"var(--tgph-spacing-VARIABLE)"},bottom:{cssVar:"--bottom",value:"var(--tgph-spacing-VARIABLE)"},overflow:{cssVar:"--overflow",value:"VARIABLE"}},sJ={border:sG.borderWidth,borderX:sG.borderLeftWidth,borderY:sG.borderTopWidth,bg:sG.backgroundColor,p:sG.padding,m:sG.margin,px:sG.paddingX,py:sG.paddingY,pt:sG.paddingTop,pb:sG.paddingBottom,pl:sG.paddingLeft,pr:sG.paddingRight,mx:sG.marginX,my:sG.marginY,mt:sG.marginTop,mb:sG.marginBottom,ml:sG.marginLeft,mr:sG.marginRight,shadow:sG.boxShadow,w:sG.width,h:sG.height,minW:sG.minWidth,minH:sG.minHeight,maxW:sG.maxWidth,maxH:sG.maxHeight,rounded:sG.borderRadius,roundedTopLeft:sG.borderTopLeftRadius,roundedTopRight:sG.borderTopRightRadius,roundedBottomLeft:sG.borderBottomLeftRadius,roundedBottomRight:sG.borderBottomRightRadius,roundedTop:sG.borderTopRadius,roundedBottom:sG.borderBottomRadius,roundedLeft:sG.borderLeftRadius,roundedRight:sG.borderRightRadius,borderTop:sG.borderTopWidth,borderBottom:sG.borderBottomWidth,borderLeft:sG.borderLeftWidth,borderRight:sG.borderRightWidth},sX={...sG,...sJ},sY=({as:e,className:t,tgphRef:r,children:n,...i})=>{let{styleProp:o,otherProps:a,interactive:s}=sK({props:i,cssVars:sX});return(0,am.jsx)(e||"div",{className:(0,o$.A)("tgph-box",s&&"tgph-box--interactive",t),style:o,...a,ref:r,children:n})},sZ={flexDirection:{cssVar:"--direction",value:"VARIABLE"},flexWrap:{cssVar:"--wrap",value:"VARIABLE"},justifyContent:{cssVar:"--justify",value:"VARIABLE"},alignItems:{cssVar:"--align",value:"VARIABLE"},gap:{cssVar:"--gap",value:"var(--tgph-spacing-VARIABLE)"},direction:{cssVar:"--direction",value:"VARIABLE"},align:{cssVar:"--align",value:"VARIABLE"},justify:{cssVar:"--justify",value:"VARIABLE"},wrap:{cssVar:"--wrap",value:"VARIABLE"}},sQ=({className:e,...t})=>{let{styleProp:r,otherProps:n}=sK({props:t,cssVars:sZ});return(0,am.jsx)(sY,{className:(0,o$.A)("tgph-stack",e),style:r,...n})},s0={color:{cssVar:"--color",value:"var(--tgph-VARIABLE)"},fontSize:{cssVar:"--font-size",value:"var(--tgph-text-VARIABLE)"},weight:{cssVar:"--weight",value:"var(--tgph-weight-VARIABLE)"},leading:{cssVar:"--leading",value:"var(--tgph-leading-VARIABLE)"},tracking:{cssVar:"--tracking",value:"var(--tgph-tracking-VARIABLE)"},align:{cssVar:"--text-align",value:"VARIABLE"},family:{cssVar:"--font-family",value:"var(--tgph-family-VARIABLE)"},textOverflow:{cssVar:"--text-overflow",value:"VARIABLE"}},s1={default:"gray-12",gray:"gray-11",red:"red-11",beige:"beige-11",blue:"blue-11",green:"green-11",yellow:"yellow-11",purple:"purple-11",accent:"accent-11",white:"white",black:"black",disabled:"gray-9"},s2=({as:e,size:t="2",weight:r="regular",align:n="left",color:i,className:o,internal_optionalAs:a,...s})=>{if(!e)throw Error("as prop is required");let{styleProp:l,otherProps:c}=sK({props:{color:s1[i],fontSize:t,tracking:t,leading:t,weight:r,align:n,...s},cssVars:s0});return(0,am.jsx)(sY,{as:e,className:(0,o$.A)("tgph-text",o),display:"inline",style:l,...c})},s5={0:"0.8125rem",1:"0.874rem",2:"var(--tgph-spacing-4)",3:"1.125rem",4:"var(--tgph-spacing-5)",5:"1.375rem",6:"1.625rem",7:"var(--tgph-spacing-8)",8:"var(--tgph-spacing-9)",9:"var(--tgph-spacing-12)"},s4={primary:{default:"var(--tgph-gray-12)",gray:"var(--tgph-gray-11)",accent:"var(--tgph-accent-11)",beige:"var(--tgph-beige-11)",blue:"var(--tgph-blue-11)",green:"var(--tgph-green-11)",yellow:"var(--tgph-yellow-11)",purple:"var(--tgph-purple-11)",red:"var(--tgph-red-11)",white:"var(--tgph-white)",disabled:"var(--tgph-gray-9)",black:"var(--tgph-black)"},secondary:{default:"var(--tgph-gray-11)",gray:"var(--tgph-gray-10)",accent:"var(--tgph-accent-10)",beige:"var(--tgph-beige-10)",blue:"var(--tgph-blue-10)",green:"var(--tgph-green-10)",yellow:"var(--tgph-yellow-10)",purple:"var(--tgph-purple-10)",red:"var(--tgph-red-10)",white:"var(--tgph-white)",disabled:"var(--tgph-gray-8)",black:"var(--tgph-black)"}},s3=({as:e,size:t="2",color:r="default",variant:n="primary",icon:i,alt:o,className:a,style:s,...l})=>{if(!i)throw Error("@telegraph/icon: icon prop is required");if(!o&&!l["aria-hidden"])throw Error("@telegraph/icon: alt prop is required");return(0,am.jsx)(s2,{as:e||"span",className:(0,o$.A)("tgph-icon",a),"data-button-icon":!0,style:{"--height":s5[t],"--width":s5[t],"--color":s4[n][r],...s},...l,children:i&&(0,am.jsx)(i,{"aria-label":o,width:"100%",height:"100%",display:"block"})})};var s9=r(13859),s6=r(6341),s8=r(98064);function s7(e){let t=function(e){let t=p.forwardRef((e,t)=>{let{children:r,...n}=e;if(p.isValidElement(r)){var i;let e,o,a=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{o(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==p.Fragment&&(s.ref=t?(0,s8.t)(t,a):a),p.cloneElement(r,s)}return p.Children.count(r)>1?p.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=p.forwardRef((e,r)=>{let{children:n,...i}=e,o=p.Children.toArray(n),a=o.find(lt);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:p.Children.count(e)>1?p.Children.only(null):p.isValidElement(e)?e.props.children:null);return(0,am.jsx)(t,{...i,ref:r,children:p.isValidElement(e)?p.cloneElement(e,void 0,n):null})}return(0,am.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var le=Symbol("radix.slottable");function lt(e){return p.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===le}var lr=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=s7(`Primitive.${t}`),n=p.forwardRef((e,n)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,am.jsx)(i?r:t,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function ln(e,t){e&&s6.flushSync(()=>e.dispatchEvent(t))}var li=r(72336),lo=r(48237),la="dismissableLayer.update",ls=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ll=p.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:s,onInteractOutside:l,onDismiss:c,...u}=e,d=p.useContext(ls),[f,m]=p.useState(null),g=null!=(n=null==f?void 0:f.ownerDocument)?n:null==(r=globalThis)?void 0:r.document,[,v]=p.useState({}),y=(0,s8.s)(t,e=>m(e)),b=Array.from(d.layers),[w]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),k=b.indexOf(w),C=f?b.indexOf(f):-1,E=d.layersWithOutsidePointerEventsDisabled.size>0,x=C>=k,A=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,li.c)(e),i=p.useRef(!1),o=p.useRef(()=>{});return p.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){lu("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",o.current),o.current=t,r.addEventListener("click",o.current,{once:!0})):t()}else r.removeEventListener("click",o.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",o.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...d.branches].some(e=>e.contains(t));x&&!r&&(null==a||a(e),null==l||l(e),e.defaultPrevented||null==c||c())},g),_=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,li.c)(e),i=p.useRef(!1);return p.useEffect(()=>{let e=e=>{e.target&&!i.current&&lu("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...d.branches].some(e=>e.contains(t))&&(null==s||s(e),null==l||l(e),e.defaultPrevented||null==c||c())},g);return(0,lo.U)(e=>{C===d.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},g),p.useEffect(()=>{if(f)return i&&(0===d.layersWithOutsidePointerEventsDisabled.size&&(h=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),lc(),()=>{i&&1===d.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=h)}},[f,g,i,d]),p.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),lc())},[f,d]),p.useEffect(()=>{let e=()=>v({});return document.addEventListener(la,e),()=>document.removeEventListener(la,e)},[]),(0,am.jsx)(lr.div,{...u,ref:y,style:{pointerEvents:E?x?"auto":"none":void 0,...e.style},onFocusCapture:(0,s9.m)(e.onFocusCapture,_.onFocusCapture),onBlurCapture:(0,s9.m)(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:(0,s9.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function lc(){let e=new CustomEvent(la);document.dispatchEvent(e)}function lu(e,t,r,n){let{discrete:i}=n,o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),i?ln(o,a):o.dispatchEvent(a)}ll.displayName="DismissableLayer",p.forwardRef((e,t)=>{let r=p.useContext(ls),n=p.useRef(null),i=(0,s8.s)(t,n);return p.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,am.jsx)(lr.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var lh=r(84268),ld=p.forwardRef((e,t)=>{var r,n;let{container:i,...o}=e,[a,s]=p.useState(!1);(0,lh.N)(()=>s(!0),[]);let l=i||a&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return l?s6.createPortal((0,am.jsx)(lr.div,{...o,ref:t}),l):null});ld.displayName="Portal";var lf=r(17691),lp=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=s7(`Primitive.${t}`),n=p.forwardRef((e,n)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,am.jsx)(i?r:t,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),lm=p.forwardRef((e,t)=>(0,am.jsx)(lp.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));lm.displayName="VisuallyHidden";let lg=Symbol.for("react.forward_ref"),lv=(e,t)=>{let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{o(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}},ly=({children:e,...t},r)=>e?p.Children.toArray(e).map(e=>{if(p.isValidElement(e)){let n=e.$$typeof,i=e.type.$$typeof,o=e.props,a=o.tgphRef;return n===lg||i===lg?p.cloneElement(e,{...lv(t,o),tgphRef:a||r,ref:a||r}):p.cloneElement(e,{...lv(t,o),tgphRef:a||r})}return e}):null,lb=p.forwardRef(({children:e,...t},r)=>ly({children:e,...t},r)),lw=({value:e,determinateValue:t,minDurationMs:r=1e3})=>{let[n,i]=p.useState(e),o=p.useRef(null),a=p.useRef(null),s=()=>{o.current&&(clearTimeout(o.current),o.current=null)},l=p.useCallback(()=>{if(e===t)s(),i(t),a.current=Date.now();else if(null!==a.current){let t=r-(Date.now()-a.current);t>0?(s(),o.current=setTimeout(()=>{i(e),a.current=null},t)):(i(e),a.current=null)}else i(e)},[e,t,r]);return p.useEffect(()=>(l(),s),[e,l]),n},lk={default_buttonShadowColor:{cssVar:"--box-shadow",value:"inset 0 0 0 1px var(--tgph-VARIABLE)"},hover_buttonShadowColor:{cssVar:"--tgph-button-hover-shadow",value:"inset 0 0 0 1px var(--tgph-VARIABLE)"},focus_buttonShadowColor:{cssVar:"--tgph-button-focus-shadow",value:"inset 0 0 0 1px var(--tgph-VARIABLE)"},active_buttonShadowColor:{cssVar:"--tgph-button-active-shadow",value:"inset 0 0 0 1px var(--tgph-VARIABLE)"}},lC={solid:{default:{backgroundColor:"gray-12",hover_backgroundColor:"gray-11",focus_backgroundColor:"gray-10",active_backgroundColor:"gray-10"},accent:{backgroundColor:"accent-9",hover_backgroundColor:"accent-10",focus_backgroundColor:"accent-11",active_backgroundColor:"accent-11"},red:{backgroundColor:"red-9",hover_backgroundColor:"red-10",focus_backgroundColor:"red-11",active_backgroundColor:"red-11"},gray:{backgroundColor:"gray-9",hover_backgroundColor:"gray-10",focus_backgroundColor:"gray-11",active_backgroundColor:"gray-11"},green:{backgroundColor:"green-9",hover_backgroundColor:"green-10",focus_backgroundColor:"green-11",active_backgroundColor:"green-11"},blue:{backgroundColor:"blue-9",hover_backgroundColor:"blue-10",focus_backgroundColor:"blue-11",active_backgroundColor:"blue-11"},yellow:{backgroundColor:"yellow-9",hover_backgroundColor:"yellow-10",focus_backgroundColor:"yellow-11",active_backgroundColor:"yellow-11"},purple:{backgroundColor:"purple-9",hover_backgroundColor:"purple-10",focus_backgroundColor:"purple-11",active_backgroundColor:"purple-11"}},soft:{default:{backgroundColor:"gray-3",hover_backgroundColor:"gray-4",focus_backgroundColor:"gray-5",active_backgroundColor:"gray-5"},gray:{backgroundColor:"gray-3",hover_backgroundColor:"gray-4",focus_backgroundColor:"gray-5",active_backgroundColor:"gray-5"},red:{backgroundColor:"red-3",hover_backgroundColor:"red-4",focus_backgroundColor:"red-5",active_backgroundColor:"red-5"},accent:{backgroundColor:"accent-3",hover_backgroundColor:"accent-4",focus_backgroundColor:"accent-5",active_backgroundColor:"accent-5"},green:{backgroundColor:"green-3",hover_backgroundColor:"green-4",focus_backgroundColor:"green-5",active_backgroundColor:"green-5"},blue:{backgroundColor:"blue-3",hover_backgroundColor:"blue-4",focus_backgroundColor:"blue-5",active_backgroundColor:"blue-5"},yellow:{backgroundColor:"yellow-3",hover_backgroundColor:"yellow-4",focus_backgroundColor:"yellow-5",active_backgroundColor:"yellow-5"},purple:{backgroundColor:"purple-3",hover_backgroundColor:"purple-4",focus_backgroundColor:"purple-5",active_backgroundColor:"purple-5"}},outline:{default:{backgroundColor:"surface-1",default_buttonShadowColor:"gray-6",hover_buttonShadowColor:"gray-7",focus_buttonShadowColor:"gray-8",active_buttonShadowColor:"gray-8"},gray:{backgroundColor:"surface-1",default_buttonShadowColor:"gray-6",hover_buttonShadowColor:"gray-7",focus_buttonShadowColor:"gray-8",active_buttonShadowColor:"gray-8"},red:{backgroundColor:"surface-1",default_buttonShadowColor:"red-6",hover_buttonShadowColor:"red-7",focus_buttonShadowColor:"red-8",active_buttonShadowColor:"red-8"},accent:{backgroundColor:"surface-1",default_buttonShadowColor:"accent-6",hover_buttonShadowColor:"accent-7",focus_buttonShadowColor:"accent-8",active_buttonShadowColor:"accent-8"},green:{backgroundColor:"surface-1",default_buttonShadowColor:"green-6",hover_buttonShadowColor:"green-7",focus_buttonShadowColor:"green-8",active_buttonShadowColor:"green-8"},blue:{backgroundColor:"surface-1",default_buttonShadowColor:"blue-6",hover_buttonShadowColor:"blue-7",focus_buttonShadowColor:"blue-8",active_buttonShadowColor:"blue-8"},yellow:{backgroundColor:"surface-1",default_buttonShadowColor:"yellow-6",hover_buttonShadowColor:"yellow-7",focus_buttonShadowColor:"yellow-8",active_buttonShadowColor:"yellow-8"},purple:{backgroundColor:"surface-1",default_buttonShadowColor:"purple-6",hover_buttonShadowColor:"purple-7",focus_buttonShadowColor:"purple-8",active_buttonShadowColor:"purple-8"}},ghost:{default:{backgroundColor:"transparent",hover_backgroundColor:"gray-3",focus_backgroundColor:"gray-4",active_backgroundColor:"gray-4"},gray:{backgroundColor:"transparent",hover_backgroundColor:"gray-3",focus_backgroundColor:"gray-4",active_backgroundColor:"gray-4"},red:{backgroundColor:"transparent",hover_backgroundColor:"red-3",focus_backgroundColor:"red-4",active_backgroundColor:"red-4"},accent:{backgroundColor:"transparent",hover_backgroundColor:"accent-3",focus_backgroundColor:"accent-4",active_backgroundColor:"accent-4"},green:{backgroundColor:"transparent",hover_backgroundColor:"green-3",focus_backgroundColor:"green-4",active_backgroundColor:"green-4"},blue:{backgroundColor:"transparent",hover_backgroundColor:"blue-3",focus_backgroundColor:"blue-4",active_backgroundColor:"blue-4"},yellow:{backgroundColor:"transparent",hover_backgroundColor:"yellow-3",focus_backgroundColor:"yellow-4",active_backgroundColor:"yellow-4"},purple:{backgroundColor:"transparent",hover_backgroundColor:"purple-3",focus_backgroundColor:"purple-4",active_backgroundColor:"purple-4"}}},lE={default:{0:{w:"auto",h:"5",gap:"1",px:"1",rounded:"2"},1:{w:"auto",h:"6",gap:"1",px:"2",rounded:"2"},2:{w:"auto",h:"8",gap:"2",px:"3",rounded:"2"},3:{w:"auto",h:"10",gap:"3",px:"4",rounded:"3"}},"icon-only":{0:{w:"5",h:"5",gap:"0",px:"0",rounded:"2"},1:{w:"6",h:"6",gap:"0",px:"0",rounded:"2"},2:{w:"8",h:"8",gap:"0",px:"0",rounded:"2"},3:{w:"10",h:"10",gap:"0",px:"0",rounded:"3"}}},lx={0:"0",1:"1",2:"2",3:"3"},lA={solid:{default:"white",gray:"white",red:"white",accent:"white",green:"white",blue:"white",yellow:"white",purple:"white",disabled:"disabled"},soft:{default:"default",gray:"gray",red:"red",accent:"accent",green:"green",blue:"blue",yellow:"yellow",purple:"purple",disabled:"disabled"},outline:{default:"default",gray:"gray",red:"red",accent:"accent",green:"green",blue:"blue",yellow:"yellow",purple:"purple",disabled:"disabled"},ghost:{default:"default",gray:"gray",red:"red",accent:"accent",green:"green",blue:"blue",yellow:"yellow",purple:"purple",disabled:"disabled"}},l_={0:"0",1:"1",2:"2",3:"3"},lR={solid:{default:"white",gray:"white",red:"white",accent:"white",green:"white",blue:"white",yellow:"white",purple:"white",disabled:"disabled"},soft:{default:"default",accent:"accent",gray:"gray",red:"red",green:"green",blue:"blue",yellow:"yellow",purple:"purple",disabled:"disabled"},outline:{default:"default",accent:"accent",gray:"gray",red:"red",green:"green",blue:"blue",yellow:"yellow",purple:"purple",disabled:"disabled"},ghost:{default:"gray",accent:"accent",gray:"gray",red:"red",green:"green",blue:"blue",yellow:"yellow",purple:"purple",disabled:"disabled"}},lS={default:"secondary","icon-only":"primary"},lT=p.createContext({variant:"solid",size:"2",color:"default",state:"default",layout:"default",active:!1}),lO=e=>e.disabled?"disabled":"loading"===e.state?"loading":e.active?"active":e.state,lL=({as:e,variant:t="solid",size:r="2",color:n="default",state:i="default",active:o=!1,disabled:a,className:s,children:l,style:c,...u})=>{let h=lw({value:lO({state:i,disabled:a,active:o}),determinateValue:"loading",minDurationMs:1200}),{styleProp:d,otherProps:f}=sK({props:{...lC[t][n],style:c},cssVars:lk}),m=p.useMemo(()=>{var e;let t=p.Children.toArray(l);if((null==t?void 0:t.length)===1&&p.isValidElement(t[0])){let r=t[0];if(null!=(e=null==r?void 0:r.props)&&e.icon)return"icon-only"}return"default"},[l]);return(0,am.jsx)(lT.Provider,{value:{variant:t,size:r,color:n,state:h,layout:m,active:o},children:(0,am.jsxs)(sQ,{as:(a?"button":e)||"button",className:(0,o$.A)("tgph-button",s),display:"inline-flex",align:"center",justify:"center",...lE[m][r],style:d,"data-tgph-button":!0,"data-tgph-button-layout":m,"data-tgph-button-state":h,"data-tgph-button-variant":t,"data-tgph-button-color":n,disabled:"disabled"===h||"loading"===h,...f,...u,children:["loading"===h&&(0,am.jsx)(lj,{icon:ap.wmz,"aria-hidden":!0,"data-tgph-button-loading-icon":!0}),l]})})},lj=({size:e,color:t,variant:r,icon:n,alt:i,"aria-hidden":o,internal_iconType:a,...s})=>{let l=p.useContext(lT),c={size:e??l_[l.size],color:t??lR[l.variant]["disabled"===l.state?"disabled":l.color],variant:r??lS[l.layout]};return"loading"===l.state&&"leading"===a?null:(0,am.jsx)(s3,{icon:n,"data-button-icon":!0,"data-button-icon-color":c.color,...i?{alt:i}:{"aria-hidden":o},...c,...s})},lI=({as:e,color:t,size:r,weight:n="medium",style:i,...o})=>{let a=p.useContext(lT),s=t??lA[a.variant]["disabled"===a.state?"disabled":a.color];return(0,am.jsx)(s2,{as:e||"span",color:s,size:r??lx[a.size],weight:n,internal_optionalAs:!0,"data-button-text":!0,"data-button-text-color":s,style:{textDecoration:"none",whiteSpace:"nowrap",...i},...o})},lN=({leadingIcon:e,trailingIcon:t,icon:r,children:n,...i})=>{let o=e||r;return(0,am.jsxs)(lL,{...i,children:[o&&(0,am.jsx)(lj,{...o,internal_iconType:"leading"}),n&&(0,am.jsx)(lI,{children:n}),t&&(0,am.jsx)(lj,{...t,internal_iconType:"trailing"})]})};function lP(...e){return p.useCallback(function(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}(...e),e)}Object.assign(lN,{Root:lL,Icon:lj,Text:lI});var lM=r(89840);let lB={Container:{1:"h-6 pl-0 rounded-2",2:"h-8 pl-0 rounded-2",3:"h-10 pl-0 rounded-3"},Input:{1:"px-1",2:"px-2",3:"px-3"},Text:{1:"1",2:"2",3:"3"},Slot:{1:"[&>[data-tgph-button]]:rounded-1",2:"[&>[data-tgph-button]]:rounded-1",3:"[&>[data-tgph-button]]:rounded-2"},SlotLeading:{1:"order-1 pl-1",2:"order-1 pl-2",3:"order-1 pl-3"},SlotTrailing:{1:"order-3 pr-1",2:"order-3 pr-2",3:"order-3 pr-3"}},lD={Container:{default:{outline:"bg-surface-1 border-gray-6 hover:border-gray-7 focus-within:!border-blue-8",ghost:"bg-transparent border-transparent hover:bg-gray-3 focus-within:!bg-gray-4 focus-within:!border-blue-8"},disabled:{outline:"cursor-not-allowed bg-gray-2 border-gray-2 [&_[data-tgph-icon]]:text-gray-8 placeholder:text-gray-9 [&>input]:text-gray-9",ghost:"cursor-not-allowed bg-transparent border border-transparent [&_[data-tgph-icon]]:text-gray-8 placeholder:text-gray-9 [&>input]:text-gray-9"},error:{outline:"bg-surface-1 border-red-6",ghost:"bg-transparent border-red-6"}}},lF=p.createContext({state:"default",size:"2",variant:"outline"}),lU=({as:e="input",size:t="2",variant:r="outline",textProps:n={size:lB.Text[t],color:"default"},className:i,disabled:o,errored:a,children:s,tgphRef:l,...c})=>{let u=p.useRef(null),h=lP(l,u),d=o?"disabled":a?"error":"default";return(0,am.jsx)(lF.Provider,{value:{size:t,variant:r,state:d},children:(0,am.jsxs)(sY,{className:(0,o$.A)("box-border flex items-center transition-all","border-[1px] border-solid text-gray-12 placeholder:text-gray-10",lD.Container[d][r],lB.Container[t]),onPointerDown:e=>{if(e.target.closest("button, a"))return void e.preventDefault();let t=u.current;t&&requestAnimationFrame(()=>{t.focus()})},children:[(0,am.jsx)(s2,{as:e,className:(0,o$.A)("appearance-none border-none shadow-0 outline-0 bg-transparent","[font-family:inherit] h-full w-full","order-2",lB.Input[t],i),disabled:o,...n,...c,tgphRef:h}),s]})})},lz=p.forwardRef(({position:e="leading",...t},r)=>{let n=p.useContext(lF);return(0,am.jsx)(sY,{className:(0,o$.A)("group box-border flex items-center justify-center h-full","[&>[data-tgph-button]]:w-full [&>[data-tgph-button]]:h-auto","[&>[data-tgph-button-layout='icon-only']]:aspect-square [&>[data-tgph-button-layout='icon-only']]:p-0","[&>[data-tgph-button-layout='default']]:px-2","[&:has([data-tgph-button-layout='icon-only'])]:aspect-square","[&:has([data-tgph-button-layout='icon-only'])]:!p-1","leading"===e&&lB.SlotLeading[n.size],"trailing"===e&&lB.SlotTrailing[n.size],lB.Slot[t.size??n.size]),children:(0,am.jsx)(lM.DX,{size:n.size,...t,ref:r})})}),l$=({LeadingComponent:e,TrailingComponent:t,...r})=>(0,am.jsxs)(lU,{...r,children:[e&&(0,am.jsx)(lz,{position:"leading",children:e}),t&&(0,am.jsx)(lz,{position:"trailing",children:t})]});Object.assign(l$,{Root:lU,Slot:lz});var lV=r(40865),lq=r(91373),lW=r(73192),lH=r(48733);function lK(e){let t=e+"CollectionProvider",[r,n]=(0,lH.A)(t),[i,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=p.useRef(null),o=p.useRef(new Map).current;return(0,am.jsx)(i,{scope:t,itemMap:o,collectionRef:n,children:r})};a.displayName=t;let s=e+"CollectionSlot",l=s7(s),c=p.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=o(s,r),a=(0,s8.s)(t,i.collectionRef);return(0,am.jsx)(l,{ref:a,children:n})});c.displayName=s;let u=e+"CollectionItemSlot",h="data-radix-collection-item",d=s7(u),f=p.forwardRef((e,t)=>{let{scope:r,children:n,...i}=e,a=p.useRef(null),s=(0,s8.s)(t,a),l=o(u,r);return p.useEffect(()=>(l.itemMap.set(a,{ref:a,...i}),()=>void l.itemMap.delete(a))),(0,am.jsx)(d,{...{[h]:""},ref:s,children:n})});return f.displayName=u,[{Provider:a,Slot:c,ItemSlot:f},function(t){let r=o(e+"CollectionConsumer",t);return p.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var lG=new WeakMap;function lJ(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=lX(t),i=n>=0?n:r+n;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function lX(e){return e!=e||0===e?0:Math.trunc(e)}d=new WeakMap;var lY=r(85532),lZ=r(16279),lQ="focusScope.autoFocusOnMount",l0="focusScope.autoFocusOnUnmount",l1={bubbles:!1,cancelable:!0},l2=p.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:i,onUnmountAutoFocus:o,...a}=e,[s,l]=p.useState(null),c=(0,li.c)(i),u=(0,li.c)(o),h=p.useRef(null),d=(0,s8.s)(t,e=>l(e)),f=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(n){let e=function(e){if(f.paused||!s)return;let t=e.target;s.contains(t)?h.current=t:l3(h.current,{select:!0})},t=function(e){if(f.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||l3(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&l3(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,f.paused]),p.useEffect(()=>{if(s){l9.add(f);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(lQ,l1);s.addEventListener(lQ,c),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(l3(n,{select:t}),document.activeElement!==r)return}(l5(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&l3(s))}return()=>{s.removeEventListener(lQ,c),setTimeout(()=>{let t=new CustomEvent(l0,l1);s.addEventListener(l0,u),s.dispatchEvent(t),t.defaultPrevented||l3(null!=e?e:document.body,{select:!0}),s.removeEventListener(l0,u),l9.remove(f)},0)}}},[s,c,u,f]);let m=p.useCallback(e=>{if(!r&&!n||f.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[n,o]=function(e){let t=l5(e);return[l4(t,e),l4(t.reverse(),e)]}(t);n&&o?e.shiftKey||i!==o?e.shiftKey&&i===n&&(e.preventDefault(),r&&l3(o,{select:!0})):(e.preventDefault(),r&&l3(n,{select:!0})):i===t&&e.preventDefault()}},[r,n,f.paused]);return(0,am.jsx)(lr.div,{tabIndex:-1,...a,ref:d,onKeyDown:m})});function l5(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function l4(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function l3(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}l2.displayName="FocusScope";var l9=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=l6(e,t)).unshift(t)},remove(t){var r;null==(r=(e=l6(e,t))[0])||r.resume()}}}();function l6(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var l8=r(29823),l7=r(84421),ce=r(37484),ct=p.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...o}=e;return(0,am.jsx)(lr.svg,{...o,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,am.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ct.displayName="Arrow";var cr=r(42189),cn="Popper",[ci,co]=(0,lH.A)(cn),[ca,cs]=ci(cn),cl=e=>{let{__scopePopper:t,children:r}=e,[n,i]=p.useState(null);return(0,am.jsx)(ca,{scope:t,anchor:n,onAnchorChange:i,children:r})};cl.displayName=cn;var cc="PopperAnchor",cu=p.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...i}=e,o=cs(cc,r),a=p.useRef(null),s=(0,s8.s)(t,a);return p.useEffect(()=>{o.onAnchorChange((null==n?void 0:n.current)||a.current)}),n?null:(0,am.jsx)(lr.div,{...i,ref:s})});cu.displayName=cc;var ch="PopperContent",[cd,cf]=ci(ch),cp=p.forwardRef((e,t)=>{var r,n,i,o,a,s,l,c;let{__scopePopper:u,side:h="bottom",sideOffset:d=0,align:f="center",alignOffset:m=0,arrowPadding:g=0,avoidCollisions:v=!0,collisionBoundary:y=[],collisionPadding:b=0,sticky:w="partial",hideWhenDetached:k=!1,updatePositionStrategy:C="optimized",onPlaced:E,...x}=e,A=cs(ch,u),[_,R]=p.useState(null),S=(0,s8.s)(t,e=>R(e)),[T,O]=p.useState(null),L=(0,cr.X)(T),j=null!=(l=null==L?void 0:L.width)?l:0,I=null!=(c=null==L?void 0:L.height)?c:0,N="number"==typeof b?b:{top:0,right:0,bottom:0,left:0,...b},P=Array.isArray(y)?y:[y],M=P.length>0,B={padding:N,boundary:P.filter(cy),altBoundary:M},{refs:D,floatingStyles:F,placement:U,isPositioned:z,middlewareData:$}=(0,l7.we)({strategy:"fixed",placement:h+("center"!==f?"-"+f:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,ce.ll)(...t,{animationFrame:"always"===C})},elements:{reference:A.anchor},middleware:[(0,l7.cY)({mainAxis:d+I,alignmentAxis:m}),v&&(0,l7.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===w?(0,l7.ER)():void 0,...B}),v&&(0,l7.UU)({...B}),(0,l7.Ej)({...B,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:i}=e,{width:o,height:a}=r.reference,s=t.floating.style;s.setProperty("--radix-popper-available-width","".concat(n,"px")),s.setProperty("--radix-popper-available-height","".concat(i,"px")),s.setProperty("--radix-popper-anchor-width","".concat(o,"px")),s.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),T&&(0,l7.UE)({element:T,padding:g}),cb({arrowWidth:j,arrowHeight:I}),k&&(0,l7.jD)({strategy:"referenceHidden",...B})]}),[V,q]=cw(U),W=(0,li.c)(E);(0,lh.N)(()=>{z&&(null==W||W())},[z,W]);let H=null==(r=$.arrow)?void 0:r.x,K=null==(n=$.arrow)?void 0:n.y,G=(null==(i=$.arrow)?void 0:i.centerOffset)!==0,[J,X]=p.useState();return(0,lh.N)(()=>{_&&X(window.getComputedStyle(_).zIndex)},[_]),(0,am.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:z?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:J,"--radix-popper-transform-origin":[null==(o=$.transformOrigin)?void 0:o.x,null==(a=$.transformOrigin)?void 0:a.y].join(" "),...(null==(s=$.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,am.jsx)(cd,{scope:u,placedSide:V,onArrowChange:O,arrowX:H,arrowY:K,shouldHideArrow:G,children:(0,am.jsx)(lr.div,{"data-side":V,"data-align":q,...x,ref:S,style:{...x.style,animation:z?void 0:"none"}})})})});cp.displayName=ch;var cm="PopperArrow",cg={top:"bottom",right:"left",bottom:"top",left:"right"},cv=p.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=cf(cm,r),o=cg[i.placedSide];return(0,am.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,am.jsx)(ct,{...n,ref:t,style:{...n.style,display:"block"}})})});function cy(e){return null!==e}cv.displayName=cm;var cb=e=>({name:"transformOrigin",options:e,fn(t){var r,n,i,o,a;let{placement:s,rects:l,middlewareData:c}=t,u=(null==(r=c.arrow)?void 0:r.centerOffset)!==0,h=u?0:e.arrowWidth,d=u?0:e.arrowHeight,[f,p]=cw(s),m={start:"0%",center:"50%",end:"100%"}[p],g=(null!=(o=null==(n=c.arrow)?void 0:n.x)?o:0)+h/2,v=(null!=(a=null==(i=c.arrow)?void 0:i.y)?a:0)+d/2,y="",b="";return"bottom"===f?(y=u?m:"".concat(g,"px"),b="".concat(-d,"px")):"top"===f?(y=u?m:"".concat(g,"px"),b="".concat(l.floating.height+d,"px")):"right"===f?(y="".concat(-d,"px"),b=u?m:"".concat(v,"px")):"left"===f&&(y="".concat(l.floating.width+d,"px"),b=u?m:"".concat(v,"px")),{data:{x:y,y:b}}}});function cw(e){let[t,r="center"]=e.split("-");return[t,r]}var ck=r(64714),cC="rovingFocusGroup.onEntryFocus",cE={bubbles:!1,cancelable:!0},cx="RovingFocusGroup",[cA,c_,cR]=lK(cx),[cS,cT]=(0,lH.A)(cx,[cR]),[cO,cL]=cS(cx),cj=p.forwardRef((e,t)=>(0,am.jsx)(cA.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,am.jsx)(cA.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,am.jsx)(cI,{...e,ref:t})})}));cj.displayName=cx;var cI=p.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:i=!1,dir:o,currentTabStopId:a,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:l,onEntryFocus:c,preventScrollOnEntryFocus:u=!1,...h}=e,d=p.useRef(null),f=(0,s8.s)(t,d),m=(0,lY.jH)(o),[g,v]=(0,lf.i)({prop:a,defaultProp:null!=s?s:null,onChange:l,caller:cx}),[y,b]=p.useState(!1),w=(0,li.c)(c),k=c_(r),C=p.useRef(!1),[E,x]=p.useState(0);return p.useEffect(()=>{let e=d.current;if(e)return e.addEventListener(cC,w),()=>e.removeEventListener(cC,w)},[w]),(0,am.jsx)(cO,{scope:r,orientation:n,dir:m,loop:i,currentTabStopId:g,onItemFocus:p.useCallback(e=>v(e),[v]),onItemShiftTab:p.useCallback(()=>b(!0),[]),onFocusableItemAdd:p.useCallback(()=>x(e=>e+1),[]),onFocusableItemRemove:p.useCallback(()=>x(e=>e-1),[]),children:(0,am.jsx)(lr.div,{tabIndex:y||0===E?-1:0,"data-orientation":n,...h,ref:f,style:{outline:"none",...e.style},onMouseDown:(0,s9.m)(e.onMouseDown,()=>{C.current=!0}),onFocus:(0,s9.m)(e.onFocus,e=>{let t=!C.current;if(e.target===e.currentTarget&&t&&!y){let t=new CustomEvent(cC,cE);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);cB([e.find(e=>e.active),e.find(e=>e.id===g),...e].filter(Boolean).map(e=>e.ref.current),u)}}C.current=!1}),onBlur:(0,s9.m)(e.onBlur,()=>b(!1))})})}),cN="RovingFocusGroupItem",cP=p.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:i=!1,tabStopId:o,children:a,...s}=e,l=(0,l8.B)(),c=o||l,u=cL(cN,r),h=u.currentTabStopId===c,d=c_(r),{onFocusableItemAdd:f,onFocusableItemRemove:m,currentTabStopId:g}=u;return p.useEffect(()=>{if(n)return f(),()=>m()},[n,f,m]),(0,am.jsx)(cA.ItemSlot,{scope:r,id:c,focusable:n,active:i,children:(0,am.jsx)(lr.span,{tabIndex:h?0:-1,"data-orientation":u.orientation,...s,ref:t,onMouseDown:(0,s9.m)(e.onMouseDown,e=>{n?u.onItemFocus(c):e.preventDefault()}),onFocus:(0,s9.m)(e.onFocus,()=>u.onItemFocus(c)),onKeyDown:(0,s9.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void u.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return cM[i]}(e,u.orientation,u.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=d().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=u.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>cB(r))}}),children:"function"==typeof a?a({isCurrentTabStop:h,hasTabStop:null!=g}):a})})});cP.displayName=cN;var cM={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function cB(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var cD=r(11712),cF=r(10345),cU=["Enter"," "],cz=["ArrowUp","PageDown","End"],c$=["ArrowDown","PageUp","Home",...cz],cV={ltr:[...cU,"ArrowRight"],rtl:[...cU,"ArrowLeft"]},cq={ltr:["ArrowLeft"],rtl:["ArrowRight"]},cW="Menu",[cH,cK,cG]=lK(cW),[cJ,cX]=(0,lH.A)(cW,[cG,co,cT]),cY=co(),cZ=cT(),[cQ,c0]=cJ(cW),[c1,c2]=cJ(cW),c5=e=>{let{__scopeMenu:t,open:r=!1,children:n,dir:i,onOpenChange:o,modal:a=!0}=e,s=cY(t),[l,c]=p.useState(null),u=p.useRef(!1),h=(0,li.c)(o),d=(0,lY.jH)(i);return p.useEffect(()=>{let e=()=>{u.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>u.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,am.jsx)(cl,{...s,children:(0,am.jsx)(cQ,{scope:t,open:r,onOpenChange:h,content:l,onContentChange:c,children:(0,am.jsx)(c1,{scope:t,onClose:p.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:u,dir:d,modal:a,children:n})})})};c5.displayName=cW;var c4=p.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=cY(r);return(0,am.jsx)(cu,{...i,...n,ref:t})});c4.displayName="MenuAnchor";var[c3,c9]=cJ("MenuPortal",{forceMount:void 0}),c6="MenuContent",[c8,c7]=cJ(c6),ue=p.forwardRef((e,t)=>{let r=c9(c6,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,o=c0(c6,e.__scopeMenu),a=c2(c6,e.__scopeMenu);return(0,am.jsx)(cH.Provider,{scope:e.__scopeMenu,children:(0,am.jsx)(ck.C,{present:n||o.open,children:(0,am.jsx)(cH.Slot,{scope:e.__scopeMenu,children:a.modal?(0,am.jsx)(ut,{...i,ref:t}):(0,am.jsx)(ur,{...i,ref:t})})})})}),ut=p.forwardRef((e,t)=>{let r=c0(c6,e.__scopeMenu),n=p.useRef(null),i=(0,s8.s)(t,n);return p.useEffect(()=>{let e=n.current;if(e)return(0,cD.Eq)(e)},[]),(0,am.jsx)(ui,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,s9.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ur=p.forwardRef((e,t)=>{let r=c0(c6,e.__scopeMenu);return(0,am.jsx)(ui,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),un=s7("MenuContent.ScrollLock"),ui=p.forwardRef((e,t)=>{let{__scopeMenu:r,loop:n=!1,trapFocus:i,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:s,onEntryFocus:l,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:h,onInteractOutside:d,onDismiss:f,disableOutsideScroll:m,...g}=e,v=c0(c6,r),y=c2(c6,r),b=cY(r),w=cZ(r),k=cK(r),[C,E]=p.useState(null),x=p.useRef(null),A=(0,s8.s)(t,x,v.onContentChange),_=p.useRef(0),R=p.useRef(""),S=p.useRef(0),T=p.useRef(null),O=p.useRef("right"),L=p.useRef(0),j=m?cF.A:p.Fragment,I=e=>{var t,r;let n=R.current+e,i=k().filter(e=>!e.disabled),o=document.activeElement,a=null==(t=i.find(e=>e.ref.current===o))?void 0:t.textValue,s=function(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=r?e.indexOf(r):-1,a=(n=Math.max(o,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}(i.map(e=>e.textValue),n,a),l=null==(r=i.find(e=>e.textValue===s))?void 0:r.ref.current;!function e(t){R.current=t,window.clearTimeout(_.current),""!==t&&(_.current=window.setTimeout(()=>e(""),1e3))}(n),l&&setTimeout(()=>l.focus())};p.useEffect(()=>()=>window.clearTimeout(_.current),[]),(0,lZ.Oh)();let N=p.useCallback(e=>{var t,r;return O.current===(null==(t=T.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let a=t[e],s=t[o],l=a.x,c=a.y,u=s.x,h=s.y;c>n!=h>n&&r<(u-l)*(n-c)/(h-c)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,null==(r=T.current)?void 0:r.area)},[]);return(0,am.jsx)(c8,{scope:r,searchRef:R,onItemEnter:p.useCallback(e=>{N(e)&&e.preventDefault()},[N]),onItemLeave:p.useCallback(e=>{var t;N(e)||(null==(t=x.current)||t.focus(),E(null))},[N]),onTriggerLeave:p.useCallback(e=>{N(e)&&e.preventDefault()},[N]),pointerGraceTimerRef:S,onPointerGraceIntentChange:p.useCallback(e=>{T.current=e},[]),children:(0,am.jsx)(j,{...m?{as:un,allowPinchZoom:!0}:void 0,children:(0,am.jsx)(l2,{asChild:!0,trapped:i,onMountAutoFocus:(0,s9.m)(o,e=>{var t;e.preventDefault(),null==(t=x.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:(0,am.jsx)(ll,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:h,onInteractOutside:d,onDismiss:f,children:(0,am.jsx)(cj,{asChild:!0,...w,dir:y.dir,orientation:"vertical",loop:n,currentTabStopId:C,onCurrentTabStopIdChange:E,onEntryFocus:(0,s9.m)(l,e=>{y.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,am.jsx)(cp,{role:"menu","aria-orientation":"vertical","data-state":uk(v.open),"data-radix-menu-content":"",dir:y.dir,...b,...g,ref:A,style:{outline:"none",...g.style},onKeyDown:(0,s9.m)(g.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&I(e.key));let i=x.current;if(e.target!==i||!c$.includes(e.key))return;e.preventDefault();let o=k().filter(e=>!e.disabled).map(e=>e.ref.current);cz.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,s9.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(_.current),R.current="")}),onPointerMove:(0,s9.m)(e.onPointerMove,ux(e=>{let t=e.target,r=L.current!==e.clientX;e.currentTarget.contains(t)&&r&&(O.current=e.clientX>L.current?"right":"left",L.current=e.clientX)}))})})})})})})});ue.displayName=c6;var uo=p.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,am.jsx)(lr.div,{role:"group",...n,ref:t})});uo.displayName="MenuGroup",p.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,am.jsx)(lr.div,{...n,ref:t})}).displayName="MenuLabel";var ua="MenuItem",us="menu.itemSelect",ul=p.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:n,...i}=e,o=p.useRef(null),a=c2(ua,e.__scopeMenu),s=c7(ua,e.__scopeMenu),l=(0,s8.s)(t,o),c=p.useRef(!1);return(0,am.jsx)(uc,{...i,ref:l,disabled:r,onClick:(0,s9.m)(e.onClick,()=>{let e=o.current;if(!r&&e){let t=new CustomEvent(us,{bubbles:!0,cancelable:!0});e.addEventListener(us,e=>null==n?void 0:n(e),{once:!0}),ln(e,t),t.defaultPrevented?c.current=!1:a.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),c.current=!0},onPointerUp:(0,s9.m)(e.onPointerUp,e=>{var t;c.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,s9.m)(e.onKeyDown,e=>{let t=""!==s.searchRef.current;r||t&&" "===e.key||cU.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ul.displayName=ua;var uc=p.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:n=!1,textValue:i,...o}=e,a=c7(ua,r),s=cZ(r),l=p.useRef(null),c=(0,s8.s)(t,l),[u,h]=p.useState(!1),[d,f]=p.useState("");return p.useEffect(()=>{let e=l.current;if(e){var t;f((null!=(t=e.textContent)?t:"").trim())}},[o.children]),(0,am.jsx)(cH.ItemSlot,{scope:r,disabled:n,textValue:null!=i?i:d,children:(0,am.jsx)(cP,{asChild:!0,...s,focusable:!n,children:(0,am.jsx)(lr.div,{role:"menuitem","data-highlighted":u?"":void 0,"aria-disabled":n||void 0,"data-disabled":n?"":void 0,...o,ref:c,onPointerMove:(0,s9.m)(e.onPointerMove,ux(e=>{n?a.onItemLeave(e):(a.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,s9.m)(e.onPointerLeave,ux(e=>a.onItemLeave(e))),onFocus:(0,s9.m)(e.onFocus,()=>h(!0)),onBlur:(0,s9.m)(e.onBlur,()=>h(!1))})})})});p.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...i}=e;return(0,am.jsx)(um,{scope:e.__scopeMenu,checked:r,children:(0,am.jsx)(ul,{role:"menuitemcheckbox","aria-checked":uC(r)?"mixed":r,...i,ref:t,"data-state":uE(r),onSelect:(0,s9.m)(i.onSelect,()=>null==n?void 0:n(!!uC(r)||!r),{checkForDefaultPrevented:!1})})})}).displayName="MenuCheckboxItem";var uu="MenuRadioGroup",[uh,ud]=cJ(uu,{value:void 0,onValueChange:()=>{}});p.forwardRef((e,t)=>{let{value:r,onValueChange:n,...i}=e,o=(0,li.c)(n);return(0,am.jsx)(uh,{scope:e.__scopeMenu,value:r,onValueChange:o,children:(0,am.jsx)(uo,{...i,ref:t})})}).displayName=uu;var uf="MenuRadioItem";p.forwardRef((e,t)=>{let{value:r,...n}=e,i=ud(uf,e.__scopeMenu),o=r===i.value;return(0,am.jsx)(um,{scope:e.__scopeMenu,checked:o,children:(0,am.jsx)(ul,{role:"menuitemradio","aria-checked":o,...n,ref:t,"data-state":uE(o),onSelect:(0,s9.m)(n.onSelect,()=>{var e;return null==(e=i.onValueChange)?void 0:e.call(i,r)},{checkForDefaultPrevented:!1})})})}).displayName=uf;var up="MenuItemIndicator",[um,ug]=cJ(up,{checked:!1});p.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...i}=e,o=ug(up,r);return(0,am.jsx)(ck.C,{present:n||uC(o.checked)||!0===o.checked,children:(0,am.jsx)(lr.span,{...i,ref:t,"data-state":uE(o.checked)})})}).displayName=up,p.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,am.jsx)(lr.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})}).displayName="MenuSeparator",p.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=cY(r);return(0,am.jsx)(cv,{...i,...n,ref:t})}).displayName="MenuArrow";var[uv,uy]=cJ("MenuSub"),ub="MenuSubTrigger";p.forwardRef((e,t)=>{let r=c0(ub,e.__scopeMenu),n=c2(ub,e.__scopeMenu),i=uy(ub,e.__scopeMenu),o=c7(ub,e.__scopeMenu),a=p.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:l}=o,c={__scopeMenu:e.__scopeMenu},u=p.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return p.useEffect(()=>u,[u]),p.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),l(null)}},[s,l]),(0,am.jsx)(c4,{asChild:!0,...c,children:(0,am.jsx)(uc,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":uk(r.open),...e,ref:(0,s8.t)(t,i.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,s9.m)(e.onPointerMove,ux(t=>{o.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||a.current||(o.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{r.onOpenChange(!0),u()},100)))})),onPointerLeave:(0,s9.m)(e.onPointerLeave,ux(e=>{var t,n;u();let i=null==(t=r.content)?void 0:t.getBoundingClientRect();if(i){let t=null==(n=r.content)?void 0:n.dataset.side,a="right"===t,l=i[a?"left":"right"],c=i[a?"right":"left"];o.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:i.top},{x:c,y:i.top},{x:c,y:i.bottom},{x:l,y:i.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>o.onPointerGraceIntentChange(null),300)}else{if(o.onTriggerLeave(e),e.defaultPrevented)return;o.onPointerGraceIntentChange(null)}})),onKeyDown:(0,s9.m)(e.onKeyDown,t=>{let i=""!==o.searchRef.current;if(!e.disabled&&(!i||" "!==t.key)&&cV[n.dir].includes(t.key)){var a;r.onOpenChange(!0),null==(a=r.content)||a.focus(),t.preventDefault()}})})})}).displayName=ub;var uw="MenuSubContent";function uk(e){return e?"open":"closed"}function uC(e){return"indeterminate"===e}function uE(e){return uC(e)?"indeterminate":e?"checked":"unchecked"}function ux(e){return t=>"mouse"===t.pointerType?e(t):void 0}p.forwardRef((e,t)=>{let r=c9(c6,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,o=c0(c6,e.__scopeMenu),a=c2(c6,e.__scopeMenu),s=uy(uw,e.__scopeMenu),l=p.useRef(null),c=(0,s8.s)(t,l);return(0,am.jsx)(cH.Provider,{scope:e.__scopeMenu,children:(0,am.jsx)(ck.C,{present:n||o.open,children:(0,am.jsx)(cH.Slot,{scope:e.__scopeMenu,children:(0,am.jsx)(ui,{id:s.contentId,"aria-labelledby":s.triggerId,...i,ref:c,align:"start",side:"rtl"===a.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;a.isUsingKeyboardRef.current&&(null==(t=l.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,s9.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&o.onOpenChange(!1)}),onEscapeKeyDown:(0,s9.m)(e.onEscapeKeyDown,e=>{a.onClose(),e.preventDefault()}),onKeyDown:(0,s9.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=cq[a.dir].includes(e.key);if(t&&r){var n;o.onOpenChange(!1),null==(n=s.trigger)||n.focus(),e.preventDefault()}})})})})})}).displayName=uw;let uA=p.createContext({initialAnimateComplete:!1,motionElements:[],setMotionElements:()=>{},presenceMap:[],previousPresenceMap:[]}),u_=({motionKey:e,exitDuration:t})=>{let{presenceMap:r,setMotionElements:n,previousPresenceMap:i,initialAnimateComplete:o}=p.useContext(uA);return p.useEffect(()=>{if(!(!e||!t))return n(r=>r.filter(t=>e===t.motionKey).length>0?r:[...r,{motionKey:e,exitDuration:t}]),()=>{n(t=>t.filter(t=>e===t.motionKey))}},[t,e,n]),{presence:p.useMemo(()=>{var t,n;return(null==(t=r.find(t=>t["tgph-motion-key"]===e))?void 0:t.value)===(null==(n=i.find(t=>t["tgph-motion-key"]===e))?void 0:n.value)},[e,r,i]),initialAnimateComplete:o}},uR=({presenceMap:e,children:t})=>{let[r,n]=p.useState(!1),[i,o]=p.useState(t),[a,s]=p.useState([]),[l,c]=p.useState(e);return p.useEffect(()=>{let r;return(null==e?void 0:e.some(e=>{var t;return e.value!==(null==(t=null==l?void 0:l.find(t=>t["tgph-motion-key"]===e["tgph-motion-key"]))?void 0:t.value)}))||(null==e?void 0:e.length)!==(null==l?void 0:l.length)?r=setTimeout(()=>{o(t),c(e)},Math.max(...a.map(e=>e.exitDuration))):(o(t),c(e)),()=>r&&clearTimeout(r)},[t,a,e,l]),p.useEffect(()=>{r||n(!0)},[r]),(0,am.jsx)(uA.Provider,{value:{presenceMap:e,previousPresenceMap:l,initialAnimateComplete:r,motionElements:a,setMotionElements:s},children:i})},uS={"ease-in-out":"ease-in-out","ease-in":"ease-in","ease-out":"ease-out",linear:"linear",spring:"linear(0, 0.25, 1)"},uT=uS["ease-in-out"],uO=e=>e&&(null==uS?void 0:uS[e])||uT,uL=({as:e,status:t="initial",className:r,initial:n,animate:i,exit:o,transition:a,initializeWithAnimation:s=!0,skipAnimation:l=!1,style:c,children:u,"tgph-motion-key":h,onAnimationComplete:d,tgphRef:f,...m})=>{let{presence:g,initialAnimateComplete:v}=u_({motionKey:h||"",exitDuration:(null==a?void 0:a.duration)||0}),[y,b]=p.useState(!1===s&&!1===v?i:n),[w,k]=p.useState(!1===s&&!1===v?"animate":"initial");p.useEffect(()=>{!1===g&&"animate"===w&&k("exit")},[g,w]),p.useEffect(()=>{"initial"===t&&k("animate"),"exit"===t&&k("exit")},[t]),p.useEffect(()=>{let e;return"initial"===w&&(e=setTimeout(()=>{b(n)},10)),"animate"===w&&(e=setTimeout(()=>{b(i),d&&setTimeout(()=>{d()},10)},10)),"exit"===w&&(e=setTimeout(()=>{b(o)},10)),()=>e&&clearTimeout(e)},[w,t,n,i,o,d]);let C=!1===s&&!1===v?0:null==a?void 0:a.duration;return(0,am.jsx)(e||"div",{className:function(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}("tgph-motion",r),style:{...!l&&{"--motion-opacity":null==y?void 0:y.opacity,"--motion-scale":null==y?void 0:y.scale,"--motion-x":"number"==typeof(null==y?void 0:y.x)?`${null==y?void 0:y.x}px`:"string"==typeof(null==y?void 0:y.x)?null==y?void 0:y.x:null,"--motion-y":"number"==typeof(null==y?void 0:y.y)?`${null==y?void 0:y.y}px`:"string"==typeof(null==y?void 0:y.y)?null==y?void 0:y.y:null,"--motion-rotate":"number"==typeof(null==y?void 0:y.rotate)?`${null==y?void 0:y.rotate}deg`:null,"--motion-transition-duration":`${C}ms`,"--motion-transition-type":uO(null==a?void 0:a.type)},...c},...m,ref:f,children:u})};uL.displayName="Motion";let uj={div:p.forwardRef((e,t)=>(0,am.jsx)(uL,{as:"div",...e,tgphRef:t||e.tgphRef})),span:p.forwardRef((e,t)=>(0,am.jsx)(uL,{as:"span",...e,tgphRef:t||e.tgphRef})),button:p.forwardRef((e,t)=>(0,am.jsx)(uL,{as:"button",...e,tgphRef:t||e.tgphRef}))},uI=({variant:e="ghost",size:t="2",h:r="7",gap:n="3",justify:i="space-between",w:o="auto",selected:a,icon:s,leadingIcon:l,leadingComponent:c,trailingIcon:u,trailingComponent:h,...d})=>(0,am.jsxs)(lN.Root,{size:t,variant:e,h:r,gap:n,justify:i,w:o,...d,children:[(0,am.jsxs)(sQ,{gap:n,align:"center",w:"full",children:[(0,am.jsx)(uN,{icon:s,selected:a,leadingIcon:l,leadingComponent:c}),(0,am.jsx)(lN.Text,{weight:(null==d?void 0:d.fontWeight)||"regular",w:"full",overflow:"hidden",textOverflow:"ellipsis",children:d.children})]}),(0,am.jsx)(uP,{trailingIcon:u,trailingComponent:h})]}),uN=({icon:e,selected:t,leadingIcon:r,leadingComponent:n})=>{if(!0===t||!1===t)return(0,am.jsx)(lN.Icon,{as:uj.span,variant:"primary",icon:ap.Jlk,"aria-hidden":!0,initializeWithAnimation:!1,animate:t?{opacity:1,rotate:0,scale:1}:{opacity:0,rotate:-45,scale:.3},transition:{duration:150,type:"spring"},style:{transformOrigin:"center"},display:"block"});let i=r||e;return i?(0,am.jsx)(lN.Icon,{variant:"primary",...i}):n||void 0},uP=({trailingIcon:e,trailingComponent:t})=>e?(0,am.jsx)(lN.Icon,{variant:"primary",...e}):t||void 0,uM=p.createContext({open:!1,setOpen:()=>{}}),uB={};Object.assign(uB,{Root:({open:e,onOpenChange:t,defaultOpen:r,modal:n=!0,children:i,...o})=>{let[a=!1,s]=(0,lf.i)({prop:e,defaultProp:r,onChange:t});return(0,am.jsx)(uM.Provider,{value:{open:a,setOpen:s},children:(0,am.jsx)(c5,{open:a,onOpenChange:s,modal:n,...o,children:i})})},Trigger:({asChild:e=!0,tgphRef:t,children:r,...n})=>{let i=p.useContext(uM);return(0,am.jsx)(c4,{onClick:()=>{i.setOpen(!i.open)},asChild:e,...n,ref:t,children:(0,am.jsx)(lb,{children:r})})},Content:({direction:e="column",gap:t="1",rounded:r="4",py:n="1",border:i="px",shadow:o="2",sideOffset:a=4,children:s,onInteractOutside:l,onKeyDown:c,onCloseAutoFocus:u,tgphRef:h,...d})=>(0,am.jsx)(ue,{onInteractOutside:l,onKeyDown:c,onCloseAutoFocus:u,asChild:!0,sideOffset:a,...d,ref:h,children:(0,am.jsx)(lb,{children:(0,am.jsx)(sQ,{bg:"surface-1",direction:e,gap:t,rounded:r,border:i,py:n,shadow:o,style:{overflowY:"auto"},zIndex:"popover",children:s})})}),Button:({mx:e="1",asChild:t=!0,icon:r,leadingIcon:n,trailingIcon:i,leadingComponent:o,trailingComponent:a,selected:s,tgphRef:l,onClick:c,...u})=>(0,am.jsx)(ul,{...u,asChild:t,ref:l,children:(0,am.jsx)(lb,{children:(0,am.jsx)(uI,{onClick:c,selected:s,leadingIcon:n||r,trailingIcon:i,leadingComponent:o,trailingComponent:a,"data-tgph-menu-button":!0,mx:e,style:{flexShrink:0},...u})})}),Divider:({w:e="full",borderBottom:t="px",...r})=>(0,am.jsx)(sY,{as:"hr",w:e,borderBottom:t,...r})});var uD=r(9379);let uF=(e={})=>{let[t,r]=p.useState((null==e?void 0:e.appearanceOverride)||"light"),n="light"===t?"dark":"light",i=e=>{document&&(r(e),document.documentElement.setAttribute("data-tgph-appearance",e))};return p.useEffect(()=>{if(!document)return;r(document.documentElement.getAttribute("data-tgph-appearance"));let e=new MutationObserver(e=>{for(let t of e)"attributes"===t.type&&"data-tgph-appearance"===t.attributeName&&r(document.documentElement.getAttribute("data-tgph-appearance"))});return e.observe(document.documentElement,{attributes:!0}),()=>{e&&e.disconnect()}},[]),{setAppearance:i,toggleAppearance:()=>{i("light"===t?"dark":"light")},appearance:t,invertedAppearance:n,appearanceProps:{"data-tgph-appearance":t},invertedAppearanceProps:{"data-tgph-appearance":n},lightAppearanceProps:{"data-tgph-appearance":"light"},darkAppearanceProps:{"data-tgph-appearance":"dark"}}},uU=({appearance:e,asChild:t,...r})=>{let{lightAppearanceProps:n,darkAppearanceProps:i}=uF();return(0,am.jsx)(t?lM.DX:"div",{..."light"===e?n:i,...r})},uz={light:{border:"px",shadow:"2"},dark:{}},u$=p.createContext({groupOpen:!1,setGroupOpen:()=>{}}),uV=({open:e,delay:t=600})=>{let r=p.useContext(u$);return p.useEffect(()=>{let n=null;return r.setGroupOpen&&(!0===e&&r.setGroupOpen(!0),!1===e&&(n=setTimeout(()=>{r.setGroupOpen&&r.setGroupOpen(!1)},t))),()=>{n&&clearTimeout(n)}},[e,r,t]),{groupOpen:r.groupOpen}},uq=({delayDuration:e=400,skipDelayDuration:t,disableHoverableContent:r,defaultOpen:n,open:i,onOpenChange:o,"aria-label":a,onEscapeKeyDown:s,onPointerDownOutside:l,forceMount:c,side:u="bottom",sideOffset:h=4,align:d="center",alignOffset:f,avoidCollisions:m,collisionBoundary:g,collisionPadding:v,arrowPadding:y,sticky:b,hideWhenDetached:w,skipAnimation:k,label:C,labelProps:E,enabled:x=!0,appearance:A="dark",triggerRef:_,children:R})=>{let[S,T]=(0,lf.i)({prop:i,onChange:o,defaultProp:n}),{groupOpen:O}=uV({open:!!S,delay:e}),L=p.Children.toArray(R).some(e=>e.props.disabled),j=O||L?0:e,I=!O;return(0,am.jsx)(uD.Kq,{delayDuration:j,skipDelayDuration:t,disableHoverableContent:r,children:(0,am.jsxs)(uD.bL,{open:!1!==x&&S,onOpenChange:T,children:[(0,am.jsx)(uD.l9,{asChild:!0,ref:_,children:(0,am.jsx)(lb,{children:R})}),(0,am.jsx)(uD.ZL,{children:(0,am.jsx)(uD.UC,{"aria-label":a,onEscapeKeyDown:s,onPointerDownOutside:l,forceMount:c,side:u,sideOffset:h,align:d,alignOffset:f,avoidCollisions:m,collisionBoundary:g,collisionPadding:v,arrowPadding:y,sticky:b,hideWhenDetached:w,style:{zIndex:"var(--tgph-zIndex-tooltip)"},children:(0,am.jsx)(uU,{appearance:A,asChild:!0,children:(0,am.jsx)(sQ,{as:uj.div,className:"tgph",skipAnimation:k,initial:I?{opacity:0,scale:.5,...(e=>"top"===e?{y:-5}:"bottom"===e?{y:5}:"left"===e?{x:-5}:"right"===e?{x:5}:void 0)(u)}:{},animate:{opacity:1,scale:1,x:0,y:0},transition:{duration:100,type:"spring"},bg:"gray-1",rounded:"3",py:"2",px:"3",align:"center",justify:"center",style:{transformOrigin:"var(--radix-tooltip-content-transform-origin)"},...E?{labelProps:E}:{},...uz[A],children:"string"==typeof C?(0,am.jsx)(s2,{as:"span",size:"1",children:C}):C})})})})]})})},uW={Root:{0:{h:"5"},1:{h:"6"},2:{h:"8"}}},uH={Root:{0:"1",1:"2",2:"2"},Text:{0:"1",1:"2",2:"2"}},uK={Root:{solid:{default:"gray-12",accent:"accent-9",gray:"gray-9",red:"red-9",blue:"blue-9",green:"green-9",yellow:"yellow-9",purple:"purple-9"},soft:{default:"gray-3",accent:"accent-3",gray:"gray-3",red:"red-3",blue:"blue-3",green:"green-3",yellow:"yellow-3",purple:"purple-3"}},Icon:{solid:{default:"white",gray:"white",red:"white",accent:"white",blue:"white",green:"white",yellow:"white",purple:"white"},soft:{default:"default",gray:"gray",accent:"accent",red:"red",blue:"blue",green:"green",yellow:"yellow",purple:"purple"}},Text:{solid:{default:"white",gray:"white",accent:"white",red:"white",blue:"white",green:"white",yellow:"white",purple:"white"},soft:{default:"default",gray:"gray",accent:"accent",red:"red",blue:"blue",green:"green",yellow:"yellow",purple:"purple"}},Button:{solid:{default:"default",gray:"gray",accent:"accent",red:"red",blue:"blue",green:"green",yellow:"yellow",purple:"purple"},soft:{default:"gray",gray:"gray",accent:"accent",red:"red",blue:"blue",green:"green",yellow:"yellow",purple:"purple"}}},uG=p.createContext({size:"1",color:"default",variant:"soft"}),uJ=({as:e="span",size:t="1",color:r="default",variant:n="soft",className:i,...o})=>(0,am.jsx)(uG.Provider,{value:{size:t,color:r,variant:n},children:(0,am.jsx)(sQ,{as:e,align:"center",rounded:"2",display:"inline-flex",pl:uH.Root[t],backgroundColor:uK.Root[n][r],h:uW.Root[t].h,className:(0,o$.$)("tgph-tag",i),...o,"data-tag":!0})}),uX=({as:e="span",maxW:t="40",overflow:r="hidden",style:n,...i})=>{let o=p.useContext(uG);return(0,am.jsx)(s2,{as:e,size:o.size,color:uK.Text[o.variant][o.color],weight:"medium",mr:uH.Text[o.size],maxW:t,overflow:r,internal_optionalAs:!0,style:{whiteSpace:"nowrap",textOverflow:"ellipsis",...n},...i})},uY=({onClick:e,textToCopy:t,...r})=>{let n=p.useContext(uG),[i,o]=p.useState(!1);return p.useEffect(()=>{if(i){let e=setTimeout(()=>o(!1),2e3);return()=>clearTimeout(e)}},[i]),(0,am.jsx)(uq,{label:"Copy text",children:(0,am.jsxs)(lN.Root,{onClick:r=>{var n;null==e||e(r),o(!0),t&&navigator.clipboard.writeText(t),null==(n=r.currentTarget)||n.blur()},size:n.size,color:uK.Button[n.variant][n.color],variant:n.variant,roundedTopRight:"3",roundedBottomRight:"3",roundedTopLeft:"0",roundedBottomLeft:"0",position:"relative",overflow:"hidden",p:"2",...r,children:[(0,am.jsx)(lN.Icon,{as:uj.span,initializeWithAnimation:!1,animate:{y:i?0:"150%",opacity:1},transition:{duration:150,type:"spring"},icon:ap.Jlk,alt:"Copied text","aria-hidden":!i}),(0,am.jsx)(lN.Icon,{as:uj.span,initializeWithAnimation:!1,animate:{y:i?"-150%":0,opacity:1},transition:{duration:150,type:"spring"},icon:ap.QRo,position:"absolute",alt:"Copy text","aria-hidden":i})]})})},uZ=({...e})=>{let t=p.useContext(uG);return(0,am.jsx)(lN,{size:t.size,color:uK.Button[t.variant][t.color],variant:t.variant,icon:{icon:ap.X,alt:"close"},roundedTopRight:"3",roundedBottomRight:"3",roundedTopLeft:"0",roundedBottomLeft:"0",...e})},uQ=({icon:e,alt:t,"aria-hidden":r,...n})=>{let i=p.useContext(uG);return(0,am.jsx)(s3,{icon:e,size:i.size,color:uK.Icon[i.variant][i.color],mr:"1",roundedTopRight:"3",roundedBottomRight:"3",roundedTopLeft:"0",roundedBottomLeft:"0",...t?{alt:t}:{"aria-hidden":r},...n})},u0=({color:e="default",size:t="1",variant:r="soft",icon:n,onRemove:i,onCopy:o,textToCopy:a,textProps:s={maxW:"40"},children:l,...c})=>(0,am.jsxs)(uJ,{color:e,size:t,variant:r,...c,children:[n&&(0,am.jsx)(uQ,{...n}),(0,am.jsx)(uX,{as:"span",...s,children:l}),i&&(0,am.jsx)(uZ,{onClick:i,icon:{icon:ap.X,alt:"Remove"}}),o&&(0,am.jsx)(uY,{onClick:o,textToCopy:a})]});Object.assign(u0,{Root:uJ,Button:uZ,Text:uX,Icon:uQ,CopyButton:uY});let u1=({tgphRef:e},t=[])=>{let[r,n]=p.useState(!1);return p.useEffect(()=>{if(!e.current)return n(!1);let t=e.current,r=()=>{n(t.scrollWidth>t.clientWidth)};r();let i=new ResizeObserver(r);return i.observe(t),()=>{i.disconnect()}},[e,...t]),{truncated:r}},u2=({label:e,children:t,...r})=>{let n=p.useRef(null),{truncated:i}=u1({tgphRef:n},[t]),o=p.useMemo(()=>"string"==typeof t?t:p.isValidElement(t)?t.props.children:e,[t,e]);return(0,am.jsx)(uq,{label:(0,am.jsx)(s2,{as:"span",size:"1",children:o}),enabled:i,triggerRef:n,...r,children:(0,am.jsx)(lb,{children:t})})},u5={0:"5",1:"6",2:"8",3:"10"},u4=e=>Array.isArray(e),u3=e=>"object"==typeof e&&!Array.isArray(e)||"string"==typeof e||!e,u9=e=>{let t=(e,r=[])=>(p.Children.toArray(e).forEach(e=>{p.isValidElement(e)&&(e.props.value?r.push(e):e.props.children&&t(e.props.children,r))}),r);return t(e).map(e=>{var t;return{value:e.props.value,label:(null==(t=e.props)?void 0:t.label)||e.props.children||e.props.value}})},u6=(e,t)=>{if(e)return!0===t?null==e?void 0:e.value:e},u8=(e,t,r)=>{if(!e||!t||0===t.length)return;let n=t.find(t=>t.value===u6(e,r));if(n)return n},u7=({children:e,value:t,searchQuery:r})=>{let n=he(e);return(null==t?void 0:t.toLowerCase().includes(r.toLowerCase()))||n.some(e=>e.toLowerCase().includes(r.toLowerCase()))},he=e=>{let t=p.Children.toArray(e),r=[];return t.forEach(e=>{"string"==typeof e&&r.push(e),p.isValidElement(e)&&e.props.children&&r.push(...he(e.props.children))}),r},ht=p.createContext({value:""}),hr={Root:({value:e,children:t,...r})=>(0,am.jsx)(ht.Provider,{value:{value:e},children:(0,am.jsx)(u0.Root,{as:uj.span,initializeWithAnimation:!1,initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.5},transition:{duration:100,type:"spring"},"tgph-motion-key":e,size:"1",layout:"position",...r,children:t})}),Text:({children:e,...t})=>{let r=p.useContext(hs),n=p.useContext(ht),i=p.useMemo(()=>{let e=r.options.find(e=>e.value===n.value);if(e)return e.label||e.value;if(!r.value)return;let t=r.value.find(e=>u6(e,r.legacyBehavior)===n.value);if(t)return t},[r.options,r.value,n.value,r.legacyBehavior]);return(0,am.jsx)(u0.Text,{...t,children:e||i})},Button:({children:e,...t})=>{let r=p.useContext(hs),n=p.useContext(ht);return(0,am.jsx)(u0.Button,{icon:{icon:ap.X,alt:`Remove ${n.value}`},onClick:e=>{if(!r.onValueChange)return;let t=r.onValueChange,i=r.value.filter(e=>u6(e,r.legacyBehavior)!==n.value);null==t||t(i),e.stopPropagation(),e.preventDefault()},...t,children:e})},Default:({value:e,children:t,...r})=>(0,am.jsxs)(hr.Root,{value:e,...r,children:[(0,am.jsx)(hr.Text,{children:t}),(0,am.jsx)(hr.Button,{})]})},hn={TriggerIndicator:({icon:e=ap.yQN,"aria-hidden":t=!0,...r})=>{let n=p.useContext(hs);return(0,am.jsx)(lN.Icon,{as:uj.span,animate:{rotate:180*!!n.open},transition:{duration:150,type:"spring"},icon:e,"aria-hidden":t,...r})},TriggerClear:({tooltipProps:e,...t})=>{let r=p.useContext(hs),n=()=>{var e,t;if(u4(r.value)){let e=r.onValueChange;null==e||e([])}if(u3(r.value)){let e=r.onValueChange;null==e||e(void 0)}null==(t=null==(e=r.triggerRef)?void 0:e.current)||t.focus()};return p.useMemo(()=>{var e;return u3(r.value)?r.clearable&&r.value:u4(r.value)?r.clearable&&(null==(e=r.value)?void 0:e.length)>0:void 0},[r.clearable,r.value])?(0,am.jsx)(uq,{label:"Clear field",...e,children:(0,am.jsx)(lN,{type:"button",icon:{icon:ap.X,alt:"Clear field"},size:"1",variant:"ghost",onClick:e=>{r.value&&(e.stopPropagation(),n())},onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.stopPropagation(),n())},"data-tgph-combobox-clear":!0,style:{marginTop:"calc(-1 * var(--tgph-spacing-1)",marginBottom:"calc(-1 * var(--tgph-spacing-1)"},...t})}):null},TriggerText:({children:e,...t})=>{let r=p.useContext(hs),n=p.useMemo(()=>{var e;if(!u3(r.value))return;let t=u8(r.value,r.options,r.legacyBehavior),n=(null==t?void 0:t.label)||(null==t?void 0:t.value)||r.placeholder;return r.legacyBehavior&&(null==(e=null==r?void 0:r.value)?void 0:e.label)||n},[r.value,r.options,r.legacyBehavior,r.placeholder]);return(0,am.jsx)(u2,{children:(0,am.jsx)(lN.Text,{color:r.value?"default":"gray",textOverflow:"ellipsis",overflow:"hidden",...t,children:e||n})})},TriggerPlaceholder:({children:e,...t})=>{let r=p.useContext(hs);return(0,am.jsx)(u2,{children:(0,am.jsx)(lN.Text,{color:"gray",textOverflow:"ellipsis",overflow:"hidden",...t,children:e||r.placeholder})})},TriggerTagsContainer:({children:e})=>{let t=p.useContext(hs);if(!u4(t.value))return null;let r=t.layout||"truncate",n=(t.value.length-2).toString().split("");return(0,am.jsxs)(sQ,{gap:"1",w:"full",wrap:"wrap"===r?"wrap":"nowrap",align:"center",style:{position:"relative",flexGrow:1},children:[(0,am.jsx)(uR,{presenceMap:t.value.map(e=>({"tgph-motion-key":u6(e,t.legacyBehavior)||"",value:!0})),children:e}),(0,am.jsx)(uR,{presenceMap:[{"tgph-motion-key":"combobox-more",value:!0}],children:"truncate"===r&&t.value.length>2&&(0,am.jsx)(sQ,{as:uj.div,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:100,type:"spring"},h:"full",pr:"1",pl:"8",align:"center","aria-label":`${t.value.length-2} more selected`,position:"absolute",right:"0",style:{backgroundImage:"linear-gradient(to left, var(--tgph-surface-1) 0 60%, transparent 90% 100%)"},children:(0,am.jsxs)(s2,{as:"span",size:"1",style:{whiteSpace:"nowrap"},children:["+",(0,am.jsxs)(uR,{presenceMap:n.map(e=>({"tgph-motion-key":e,value:!0})),children:[n.map(e=>(0,am.jsx)(sY,{as:uj.span,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:100,type:"spring"},w:"2",display:"inline-block","tgph-motion-key":e,children:e},e))," "]}),"more"]})})})]})},TriggerTag:hr},hi=["ArrowDown","PageUp","Home"],ho=["ArrowUp","PageDown","End"],ha=["Enter"," "],hs=p.createContext({value:void 0,onValueChange:()=>{},contentId:"",triggerId:"",open:!1,setOpen:()=>{},onOpenToggle:()=>{},clearable:!1,disabled:!1,options:[],legacyBehavior:!1}),hl=()=>{let e=p.useContext(hs);if(e.value&&u4(e.value)){let t=e.layout||"truncate";return 0===e.value.length?(0,am.jsx)(hn.TriggerPlaceholder,{}):(0,am.jsx)(hn.TriggerTagsContainer,{children:e.value.map((r,n)=>{let i=u6(r,e.legacyBehavior);if(i&&("truncate"===t&&n<=1||"wrap"===t))return(0,am.jsx)(lb,{children:(0,am.jsx)(hn.TriggerTag.Default,{value:i})},i)})})}if(e&&u3(e.value))return e.value?(0,am.jsx)(hn.TriggerText,{}):(0,am.jsx)(hn.TriggerPlaceholder,{})},hc=({value:e,label:t,selected:r,onSelect:n,children:i,...o})=>{let a=p.useContext(hs),[s,l]=p.useState(!1),c=a.value,u=!a.searchQuery||u7({children:t||i,value:e,searchQuery:a.searchQuery}),h=u4(c)?c.some(t=>u6(t,a.legacyBehavior)===e):u6(c,a.legacyBehavior)===e,d=r=>{var i,o;if(r.stopPropagation(),!(r.key&&!ha.includes(r.key))){if(r.preventDefault(),!0===a.closeOnSelect&&a.setOpen(!1),n)return n(r);if(u3(c)){let r=a.onValueChange;!0===a.legacyBehavior?null==r||r({value:e,label:t}):null==r||r(e)}else if(u4(c)){let r=a.onValueChange,n=a.value,i=h?n.filter(t=>u6(t,a.legacyBehavior)!==e):[...n,!0===a.legacyBehavior?{value:e,label:t}:e];null==r||r(i)}null==(o=null==(i=a.triggerRef)?void 0:i.current)||o.focus()}};if(u)return(0,am.jsx)(uB.Button,{type:"button",onSelect:d,onKeyDown:d,selected:null===r?null:r??h,onFocus:()=>l(!0),onBlur:()=>l(!1),role:"option","aria-selected":h?"true":"false","data-tgph-combobox-option":!0,"data-tgph-combobox-option-focused":s,"data-tgph-combobox-option-value":e,"data-tgph-combobox-option-label":t,...o,children:t||i||e})};Object.assign({},{Root:({modal:e=!0,closeOnSelect:t=!0,clearable:r=!1,disabled:n=!1,legacyBehavior:i=!1,open:o,onOpenChange:a,defaultOpen:s,value:l,onValueChange:c,errored:u,placeholder:h,layout:d,children:f,...m})=>{let g=p.useId(),v=p.useId(),y=p.useRef(null),b=p.useRef(null),w=p.useRef(null),k=p.useMemo(()=>u9(f),[f]),[C,E]=p.useState(""),[x=!1,A]=(0,lf.i)({prop:o,defaultProp:s,onChange:a}),_=p.useCallback(()=>{A(e=>!e)},[A]);return p.useEffect(()=>{x||E("")},[x]),(0,am.jsx)(hs.Provider,{value:{contentId:g,triggerId:v,value:l,onValueChange:c,placeholder:h,open:x,setOpen:A,onOpenToggle:_,closeOnSelect:t,clearable:r,disabled:n,searchQuery:C,setSearchQuery:E,triggerRef:y,searchRef:b,contentRef:w,errored:u,layout:d,options:k,legacyBehavior:i},children:(0,am.jsx)(uB.Root,{open:x,onOpenChange:A,modal:e,...m,children:f})})},Trigger:({size:e="2",children:t,...r})=>{let n=p.useContext(hs),i=p.useMemo(()=>{if(n.value){if(u3(n.value))return u8(n.value,n.options,n.legacyBehavior);if(u4(n.value))return n.value.map(e=>u8(e,n.options,n.legacyBehavior))}},[n.value,n.options,n.legacyBehavior]),o=p.useCallback(()=>i?u3(i)?(null==i?void 0:i.label)||(null==i?void 0:i.value)||n.placeholder:u4(i)&&i.map(e=>(null==e?void 0:e.label)||(null==e?void 0:e.value)).join(", ")||n.placeholder:n.placeholder,[i,n.placeholder]);return(0,am.jsx)(uB.Trigger,{...r,asChild:!0,onClick:e=>{var t,r;e.preventDefault(),n.onOpenToggle(),null==(r=null==(t=n.triggerRef)?void 0:t.current)||r.focus()},onKeyDown:e=>{if("Tab"===e.key)return void e.stopPropagation();if(ha.includes(e.key))return void e.preventDefault();if("ArrowDown"===e.key){e.stopPropagation(),e.preventDefault(),n.onOpenToggle();return}},tgphRef:n.triggerRef,children:(0,am.jsxs)(lN.Root,{id:n.triggerId,type:"button",bg:"surface-1",variant:"outline",align:"center",minH:u5[e],h:"full",w:"full",py:"1",size:e,color:n.errored?"red":"gray",justify:"space-between",role:"combobox","aria-label":o(),"aria-controls":n.contentId,"aria-expanded":n.open,"aria-haspopup":"listbox","data-tgph-combobox-trigger":!0,"data-tgph-combobox-trigger-open":n.open,disabled:n.disabled,...r,children:[t?"function"==typeof t?t({value:i}):t:(0,am.jsx)(hl,{}),(0,am.jsxs)(sQ,{align:"center",gap:"1",children:[(0,am.jsx)(hn.TriggerClear,{}),(0,am.jsx)(hn.TriggerIndicator,{})]})]})})},Content:({style:e,children:t,tgphRef:r,...n})=>{let i=p.useContext(hs),o=p.useRef(!1),a=lP(r,i.contentRef),s=p.useRef(null),[l,c]=p.useState(0),[u,h]=p.useState(!1),d=p.useCallback(e=>{let t=null==e?void 0:e.getBoundingClientRect();t&&c(t.height),u||h(!0)},[u]);return p.useEffect(()=>{let e=new ResizeObserver(e=>{for(let t of e)d(t.target)});return s.current&&u&&e.observe(s.current),()=>e.disconnect()},[u,d]),p.useEffect(()=>{!0===u&&!1===i.open&&h(!1)},[i.open,u]),p.useEffect(()=>{let e;return i.open&&(e=setTimeout(()=>{d(s.current)},10)),()=>e&&clearTimeout(e)},[i.open,d]),(0,am.jsx)(ld,{asChild:!0,children:(0,am.jsx)(ll,{onEscapeKeyDown:e=>{i.open&&(e.stopPropagation(),e.preventDefault(),i.setOpen(!1))},children:(0,am.jsx)(uB.Content,{className:"tgph",mt:"1",onCloseAutoFocus:e=>{var t,r;o.current||null==(r=null==(t=i.triggerRef)?void 0:t.current)||r.focus(),o.current=!1,e.preventDefault()},bg:"surface-1",style:{width:"var(--tgph-combobox-trigger-width)",transition:"min-height 200ms ease-in-out",minHeight:l?`${l}px`:"0",...e,"--tgph-combobox-content-transform-origin":"var(--radix-popper-transform-origin)","--tgph-combobox-content-available-width":"var(--radix-popper-available-width)","--tgph-combobox-content-available-height":"calc(var(--radix-popper-available-height) - var(--tgph-spacing-8))","--tgph-combobox-trigger-width":"var(--radix-popper-anchor-width)","--tgph-combobox-trigger-height":"var(--radix-popper-anchor-height)"},...n,tgphRef:a,"data-tgph-combobox-content":!0,"data-tgph-combobox-content-open":i.open,role:void 0,"aria-orientation":void 0,onKeyDown:e=>{var t,r,n,o;e.stopPropagation();let a=null==(r=null==(t=i.contentRef)?void 0:t.current)?void 0:r.querySelectorAll("[data-tgph-combobox-option]");document.activeElement===(null==a?void 0:a[0])&&ho.includes(e.key)&&(null==(o=null==(n=i.searchRef)?void 0:n.current)||o.focus())},children:(0,am.jsx)(sQ,{direction:"column",gap:"1",tgphRef:s,children:t})})})})},Options:({...e})=>{let t=p.useContext(hs);return(0,am.jsx)(sQ,{id:t.contentId,direction:"column",gap:"1",style:{overflowY:"auto","--max-height":e.maxHeight?void 0:"calc(var(--tgph-combobox-content-available-height) - var(--tgph-spacing-12))"},role:"listbox",...e})},Option:hc,Search:({label:e="Search",placeholder:t="Search",tgphRef:r,value:n,onValueChange:i,...o})=>{var a;let s=p.useId(),l=p.useContext(hs),c=lP(r,l.searchRef),u=n??l.searchQuery,h=i??l.setSearchQuery;return p.useEffect(()=>{var e;let t=e=>{var t,r;hi.includes(e.key)&&(null==(r=null==(t=l.contentRef)?void 0:t.current)||r.focus({preventScroll:!0})),"Escape"===e.key&&l.setOpen(!1),e.stopPropagation()},r=null==(e=l.searchRef)?void 0:e.current;if(r)return r.addEventListener("keydown",t),()=>{r.removeEventListener("keydown",t)}},[l]),(0,am.jsxs)(sY,{borderBottom:"px",px:"1",pb:"1",children:[(0,am.jsx)(lm,{children:(0,am.jsx)(s2,{as:"label",htmlFor:s,children:e})}),(0,am.jsx)(l$,{id:s,variant:"ghost",placeholder:t,value:u,onChange:e=>{h(e.target.value)},LeadingComponent:(0,am.jsx)(s3,{icon:ap.vji,alt:"Search Icon"}),TrailingComponent:null!=l&&l.searchQuery&&(null==(a=null==l?void 0:l.searchQuery)?void 0:a.length)>0?(0,am.jsx)(lN,{variant:"ghost",color:"gray",icon:{icon:ap.X,alt:"Clear Search Query"},onClick:()=>{var e;return null==(e=l.setSearchQuery)?void 0:e.call(l,"")}}):null,autoFocus:!0,"data-tgph-combobox-search":!0,"aria-controls":l.contentId,...o,tgphRef:c})]})},Empty:({icon:e={icon:ap.vji,alt:"Search Icon"},message:t="No results found",children:r,...n})=>{let i=p.useContext(hs),[o,a]=p.useState(!1);if(p.useEffect(()=>{var e,t;let r=null==(t=null==(e=i.contentRef)?void 0:e.current)?void 0:t.querySelectorAll("[data-tgph-combobox-option]");(null==r?void 0:r.length)===0?a(!0):a(!1)},[i.searchQuery,i.contentRef,r]),o)return(0,am.jsxs)(sQ,{gap:"1",align:"center",justify:"center",w:"full",my:"8","data-tgph-combobox-empty":!0,...n,children:[null===e?(0,am.jsx)(am.Fragment,{}):(0,am.jsx)(s3,{...e}),null===t?(0,am.jsx)(am.Fragment,{}):(0,am.jsx)(s2,{as:"span",children:t})]})},Create:({leadingText:e="Create",values:t,onCreate:r,selected:n=null,legacyBehavior:i=!1,...o})=>{let a=p.useContext(hs),s=p.useCallback(e=>!!t&&(null==t?void 0:t.length)!==0&&t.some(t=>u6(t,i)===e),[t,i]);if(a.searchQuery&&!s(a.searchQuery))return(0,am.jsx)(hc,{leadingIcon:{icon:ap.FWt,"aria-hidden":!0},mx:"1",value:a.searchQuery,label:`${e} "${a.searchQuery}"`,selected:n,onSelect:()=>{var e;r&&a.value&&a.searchQuery&&(r(!0===i?{value:a.searchQuery}:a.searchQuery),null==(e=a.setSearchQuery)||e.call(a,""))},...o})},Primitives:hn})},50252:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("anchor",[["path",{d:"M12 22V8",key:"qkxhtm"}],["path",{d:"M5 12H2a10 10 0 0 0 20 0h-3",key:"1hv3nh"}],["circle",{cx:"12",cy:"5",r:"3",key:"rqqgnr"}]])},50817:(e,t,r)=>{"use strict";r.d(t,{UC:()=>ee,VY:()=>er,ZL:()=>Z,bL:()=>Y,bm:()=>en,hE:()=>et,hJ:()=>Q});var n=r(50628),i=r(13859),o=r(98064),a=r(48733),s=r(29823),l=r(17691),c=r(79447),u=r(9665),h=r(4844),d=r(64714),f=r(64826),p=r(16279),m=r(10345),g=r(11712),v=r(89840),y=r(6024),b="Dialog",[w,k]=(0,a.A)(b),[C,E]=w(b),x=e=>{let{__scopeDialog:t,children:r,open:i,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,u=n.useRef(null),h=n.useRef(null),[d,f]=(0,l.i)({prop:i,defaultProp:null!=o&&o,onChange:a,caller:b});return(0,y.jsx)(C,{scope:t,triggerRef:u,contentRef:h,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:d,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:c,children:r})};x.displayName=b;var A="DialogTrigger";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=E(A,r),s=(0,o.s)(t,a.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":W(a.open),...n,ref:s,onClick:(0,i.m)(e.onClick,a.onOpenToggle)})}).displayName=A;var _="DialogPortal",[R,S]=w(_,{forceMount:void 0}),T=e=>{let{__scopeDialog:t,forceMount:r,children:i,container:o}=e,a=E(_,t);return(0,y.jsx)(R,{scope:t,forceMount:r,children:n.Children.map(i,e=>(0,y.jsx)(d.C,{present:r||a.open,children:(0,y.jsx)(h.Z,{asChild:!0,container:o,children:e})}))})};T.displayName=_;var O="DialogOverlay",L=n.forwardRef((e,t)=>{let r=S(O,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,o=E(O,e.__scopeDialog);return o.modal?(0,y.jsx)(d.C,{present:n||o.open,children:(0,y.jsx)(I,{...i,ref:t})}):null});L.displayName=O;var j=(0,v.TL)("DialogOverlay.RemoveScroll"),I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=E(O,r);return(0,y.jsx)(m.A,{as:j,allowPinchZoom:!0,shards:[i.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":W(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),N="DialogContent",P=n.forwardRef((e,t)=>{let r=S(N,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,o=E(N,e.__scopeDialog);return(0,y.jsx)(d.C,{present:n||o.open,children:o.modal?(0,y.jsx)(M,{...i,ref:t}):(0,y.jsx)(B,{...i,ref:t})})});P.displayName=N;var M=n.forwardRef((e,t)=>{let r=E(N,e.__scopeDialog),a=n.useRef(null),s=(0,o.s)(t,r.contentRef,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,g.Eq)(e)},[]),(0,y.jsx)(D,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=E(N,e.__scopeDialog),i=n.useRef(!1),o=n.useRef(!1);return(0,y.jsx)(D,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,a;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(i.current||null==(a=r.triggerRef.current)||a.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{var n,a;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let s=t.target;(null==(a=r.triggerRef.current)?void 0:a.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),D=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,h=E(N,r),d=n.useRef(null),f=(0,o.s)(t,d);return(0,p.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,y.jsx)(c.qW,{role:"dialog",id:h.contentId,"aria-describedby":h.descriptionId,"aria-labelledby":h.titleId,"data-state":W(h.open),...l,ref:f,onDismiss:()=>h.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:h.titleId}),(0,y.jsx)(X,{contentRef:d,descriptionId:h.descriptionId})]})]})}),F="DialogTitle",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=E(F,r);return(0,y.jsx)(f.sG.h2,{id:i.titleId,...n,ref:t})});U.displayName=F;var z="DialogDescription",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=E(z,r);return(0,y.jsx)(f.sG.p,{id:i.descriptionId,...n,ref:t})});$.displayName=z;var V="DialogClose",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(V,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})});function W(e){return e?"open":"closed"}q.displayName=V;var H="DialogTitleWarning",[K,G]=(0,a.q)(H,{contentName:N,titleName:F,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=G(H),i="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(i))},[i,t]),null},X=e=>{let{contentRef:t,descriptionId:r}=e,i=G("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(i.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},Y=x,Z=T,Q=L,ee=P,et=U,er=$,en=q},52482:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var n=r(50628),i=r(64826),o=r(6024),a="horizontal",s=["horizontal","vertical"],l=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:l=a,...c}=e,u=(r=l,s.includes(r))?l:a;return(0,o.jsx)(i.sG.div,{"data-orientation":u,...n?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},52519:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},53194:()=>{},65581:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},65691:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("settings-2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},67029:e=>{"use strict";let t=new Set(["ENOTFOUND","ENETUNREACH","UNABLE_TO_GET_ISSUER_CERT","UNABLE_TO_GET_CRL","UNABLE_TO_DECRYPT_CERT_SIGNATURE","UNABLE_TO_DECRYPT_CRL_SIGNATURE","UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY","CERT_SIGNATURE_FAILURE","CRL_SIGNATURE_FAILURE","CERT_NOT_YET_VALID","CERT_HAS_EXPIRED","CRL_NOT_YET_VALID","CRL_HAS_EXPIRED","ERROR_IN_CERT_NOT_BEFORE_FIELD","ERROR_IN_CERT_NOT_AFTER_FIELD","ERROR_IN_CRL_LAST_UPDATE_FIELD","ERROR_IN_CRL_NEXT_UPDATE_FIELD","OUT_OF_MEM","DEPTH_ZERO_SELF_SIGNED_CERT","SELF_SIGNED_CERT_IN_CHAIN","UNABLE_TO_GET_ISSUER_CERT_LOCALLY","UNABLE_TO_VERIFY_LEAF_SIGNATURE","CERT_CHAIN_TOO_LONG","CERT_REVOKED","INVALID_CA","PATH_LENGTH_EXCEEDED","INVALID_PURPOSE","CERT_UNTRUSTED","CERT_REJECTED","HOSTNAME_MISMATCH"]);e.exports=e=>!t.has(e&&e.code)},69761:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},73940:e=>{"use strict";var t=function(e){var t,n,i;return!!(t=e)&&"object"==typeof t&&(n=e,"[object RegExp]"!==(i=Object.prototype.toString.call(n))&&"[object Date]"!==i&&n.$$typeof!==r)},r="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?s(Array.isArray(e)?[]:{},e,t):e}function i(e,t,r){return e.concat(t).map(function(e){return n(e,r)})}function o(e){return Object.keys(e).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[])}function a(e,t){try{return t in e}catch(e){return!1}}function s(e,r,l){(l=l||{}).arrayMerge=l.arrayMerge||i,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=n;var c,u,h=Array.isArray(r);return h!==Array.isArray(e)?n(r,l):h?l.arrayMerge(e,r,l):(u={},(c=l).isMergeableObject(e)&&o(e).forEach(function(t){u[t]=n(e[t],c)}),o(r).forEach(function(t){a(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))||(a(e,t)&&c.isMergeableObject(r[t])?u[t]=(function(e,t){if(!t.customMerge)return s;var r=t.customMerge(e);return"function"==typeof r?r:s})(t,c)(e[t],r[t],c):u[t]=n(r[t],c))}),u)}s.all=function(e,t){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,r){return s(e,r,t)},{})},e.exports=s},75022:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},79606:(e,t,r)=>{var n,i=r(37811);!function(o){var a=Object.hasOwnProperty,s=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},l="object"==typeof i&&"function"==typeof i.nextTick,c="function"==typeof Symbol,u="object"==typeof Reflect,h="function"==typeof setImmediate?setImmediate:setTimeout,d=c?u&&"function"==typeof Reflect.ownKeys?Reflect.ownKeys:function(e){var t=Object.getOwnPropertyNames(e);return t.push.apply(t,Object.getOwnPropertySymbols(e)),t}:Object.keys;function f(){this._events={},this._conf&&p.call(this,this._conf)}function p(e){e&&(this._conf=e,e.delimiter&&(this.delimiter=e.delimiter),e.maxListeners!==o&&(this._maxListeners=e.maxListeners),e.wildcard&&(this.wildcard=e.wildcard),e.newListener&&(this._newListener=e.newListener),e.removeListener&&(this._removeListener=e.removeListener),e.verboseMemoryLeak&&(this.verboseMemoryLeak=e.verboseMemoryLeak),e.ignoreErrors&&(this.ignoreErrors=e.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function m(e,t){var r="(node) warning: possible EventEmitter memory leak detected. "+e+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(r+=" Event name: "+t+"."),void 0!==i&&i.emitWarning){var n=Error(r);n.name="MaxListenersExceededWarning",n.emitter=this,n.count=e,i.emitWarning(n)}else console.error(r),console.trace&&console.trace()}var g=function(e,t,r){var n=arguments.length;switch(n){case 0:return[];case 1:return[e];case 2:return[e,t];case 3:return[e,t,r];default:for(var i=Array(n);n--;)i[n]=arguments[n];return i}};function v(e,t){for(var r={},n=e.length,i=t?t.length:0,a=0;a<n;a++)r[e[a]]=a<i?t[a]:o;return r}function y(e,t,r){if(this._emitter=e,this._target=t,this._listeners={},this._listenersCount=0,(r.on||r.off)&&(n=r.on,i=r.off),t.addEventListener?(n=t.addEventListener,i=t.removeEventListener):t.addListener?(n=t.addListener,i=t.removeListener):t.on&&(n=t.on,i=t.off),!n&&!i)throw Error("target does not implement any known event API");if("function"!=typeof n)throw TypeError("on method must be a function");if("function"!=typeof i)throw TypeError("off method must be a function");this._on=n,this._off=i;var n,i,o=e._observers;o?o.push(this):e._observers=[this]}function b(e,t,r,n){var i,s,l,c=Object.assign({},t);if(!e)return c;if("object"!=typeof e)throw TypeError("options must be an object");var u=Object.keys(e),h=u.length;function d(e){throw Error('Invalid "'+i+'" option value'+(e?". Reason: "+e:""))}for(var f=0;f<h;f++){if(i=u[f],!n&&!a.call(t,i))throw Error('Unknown "'+i+'" option');o!==(s=e[i])&&(l=r[i],c[i]=l?l(s,d):s)}return c}function w(e,t){return"function"==typeof e&&e.hasOwnProperty("prototype")||t("value must be a constructor"),e}function k(e){var t="value must be type of "+e.join("|"),r=e.length,n=e[0],i=e[1];return 1===r?function(e,r){if(typeof e===n)return e;r(t)}:2===r?function(e,r){var o=typeof e;if(o===n||o===i)return e;r(t)}:function(n,i){for(var o=typeof n,a=r;a-- >0;)if(o===e[a])return n;i(t)}}Object.assign(y.prototype,{subscribe:function(e,t,r){var n=this,i=this._target,o=this._emitter,a=this._listeners,s=function(){var n=g.apply(null,arguments),a={data:n,name:t,original:e};if(r){!1!==r.call(i,a)&&o.emit.apply(o,[a.name].concat(n));return}o.emit.apply(o,[t].concat(n))};if(a[e])throw Error("Event '"+e+"' is already listening");this._listenersCount++,o._newListener&&o._removeListener&&!n._onNewListener?(this._onNewListener=function(r){r===t&&null===a[e]&&(a[e]=s,n._on.call(i,e,s))},o.on("newListener",this._onNewListener),this._onRemoveListener=function(r){r===t&&!o.hasListeners(r)&&a[e]&&(a[e]=null,n._off.call(i,e,s))},a[e]=null,o.on("removeListener",this._onRemoveListener)):(a[e]=s,n._on.call(i,e,s))},unsubscribe:function(e){var t,r,n,i=this,o=this._listeners,a=this._emitter,s=this._off,l=this._target;if(e&&"string"!=typeof e)throw TypeError("event must be a string");function c(){i._onNewListener&&(a.off("newListener",i._onNewListener),a.off("removeListener",i._onRemoveListener),i._onNewListener=null,i._onRemoveListener=null);var e=A.call(a,i);a._observers.splice(e,1)}if(e){if(!(t=o[e]))return;s.call(l,e,t),delete o[e],--this._listenersCount||c()}else{for(n=(r=d(o)).length;n-- >0;)e=r[n],s.call(l,e,o[e]);this._listeners={},this._listenersCount=0,c()}}});var C=k(["function"]),E=k(["object","function"]);function x(e,t,r){var n,i,o,a=0,s=new e(function(l,c,u){function h(){i&&(i=null),a&&(clearTimeout(a),a=0)}n=!(r=b(r,{timeout:0,overload:!1},{timeout:function(e,t){return("number"!=typeof(e*=1)||e<0||!Number.isFinite(e))&&t("timeout must be a positive number"),e}})).overload&&"function"==typeof e.prototype.cancel&&"function"==typeof u;var d=function(e){h(),l(e)},f=function(e){h(),c(e)};n?t(d,f,u):(i=[function(e){f(e||Error("canceled"))}],t(d,f,function(e){if(o)throw Error("Unable to subscribe on cancel event asynchronously");if("function"!=typeof e)throw TypeError("onCancel callback must be a function");i.push(e)}),o=!0),r.timeout>0&&(a=setTimeout(function(){var e=Error("timeout");e.code="ETIMEDOUT",a=0,s.cancel(e),c(e)},r.timeout))});return n||(s.cancel=function(e){if(i){for(var t=i.length,r=1;r<t;r++)i[r](e);i[0](e),i=null}}),s}function A(e){var t=this._observers;if(!t)return -1;for(var r=t.length,n=0;n<r;n++)if(t[n]._target===e)return n;return -1}function _(e,t,r,n,i){if(!r)return null;if(0===n){var o=typeof t;if("string"===o){var a,s,l=0,c=0,u=this.delimiter,h=u.length;if(-1!==(s=t.indexOf(u))){a=[,,,,,];do a[l++]=t.slice(c,s),c=s+h;while(-1!==(s=t.indexOf(u,c)));a[l++]=t.slice(c),t=a,i=l}else t=[t],i=1}else"object"===o?i=t.length:(t=[t],i=1)}var f,p,m,g,v,y,b,w=null,k=t[n],C=t[n+1];if(n===i)r._listeners&&("function"==typeof r._listeners?e&&e.push(r._listeners):e&&e.push.apply(e,r._listeners),w=[r]);else if("*"===k){for(s=(y=d(r)).length;s-- >0;)"_listeners"!==(f=y[s])&&(b=_(e,t,r[f],n+1,i))&&(w?w.push.apply(w,b):w=b);return w}else if("**"===k){for((v=n+1===i||n+2===i&&"*"===C)&&r._listeners&&(w=_(e,t,r,i,i)),s=(y=d(r)).length;s-- >0;)"_listeners"!==(f=y[s])&&("*"===f||"**"===f?(r[f]._listeners&&!v&&(b=_(e,t,r[f],i,i))&&(w?w.push.apply(w,b):w=b),b=_(e,t,r[f],n,i)):b=f===C?_(e,t,r[f],n+2,i):_(e,t,r[f],n,i),b&&(w?w.push.apply(w,b):w=b));return w}else r[k]&&(w=_(e,t,r[k],n+1,i));if((p=r["*"])&&_(e,t,p,n+1,i),m=r["**"])if(n<i)for(m._listeners&&_(e,t,m,i,i),s=(y=d(m)).length;s-- >0;)"_listeners"!==(f=y[s])&&(f===C?_(e,t,m[f],n+2,i):f===k?_(e,t,m[f],n+1,i):((g={})[f]=m[f],_(e,t,{"**":g},n+1,i)));else m._listeners?_(e,t,m,i,i):m["*"]&&m["*"]._listeners&&_(e,t,m["*"],i,i);return w}function R(e,t,r){var n,i,o=0,a=0,s=this.delimiter,l=s.length;if("string"==typeof e)if(-1!==(n=e.indexOf(s))){i=[,,,,,];do i[o++]=e.slice(a,n),a=n+l;while(-1!==(n=e.indexOf(s,a)));i[o++]=e.slice(a)}else i=[e],o=1;else i=e,o=e.length;if(o>1){for(n=0;n+1<o;n++)if("**"===i[n]&&"**"===i[n+1])return}var c,u=this.listenerTree;for(n=0;n<o;n++)if(u=u[c=i[n]]||(u[c]={}),n===o-1){u._listeners?("function"==typeof u._listeners&&(u._listeners=[u._listeners]),r?u._listeners.unshift(t):u._listeners.push(t),!u._listeners.warned&&this._maxListeners>0&&u._listeners.length>this._maxListeners&&(u._listeners.warned=!0,m.call(this,u._listeners.length,c))):u._listeners=t;break}return!0}function S(e,t,r,n){for(var i,o,a,s,l=d(e),c=l.length,u=e._listeners;c-- >0;)i=e[o=l[c]],a="_listeners"===o?r:r?r.concat(o):[o],s=n||"symbol"==typeof o,u&&t.push(s?a:a.join(this.delimiter)),"object"==typeof i&&S.call(this,i,t,a,s);return t}function T(e){for(var t,r,n,i=d(e),o=i.length;o-- >0;)(t=e[r=i[o]])&&(n=!0,"_listeners"===r||T(t)||delete e[r]);return n}function O(e,t,r){this.emitter=e,this.event=t,this.listener=r}function L(e,t,r){if(!0===r)a=!0;else if(!1===r)n=!0;else{if(!r||"object"!=typeof r)throw TypeError("options should be an object or true");var n=r.async,a=r.promisify,s=r.nextTick,c=r.objectify}if(n||s||a){var u=t,d=t._origin||t;if(s&&!l)throw Error("process.nextTick is not supported");o===a&&(a="AsyncFunction"===t.constructor.name),(t=function(){var e=arguments,t=this,r=this.event;return a?s?Promise.resolve():new Promise(function(e){h(e)}).then(function(){return t.event=r,u.apply(t,e)}):(s?i.nextTick:h)(function(){t.event=r,u.apply(t,e)})})._async=!0,t._origin=d}return[t,c?new O(this,e,t):this]}function j(e){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,p.call(this,e)}O.prototype.off=function(){return this.emitter.off(this.event,this.listener),this},j.EventEmitter2=j,j.prototype.listenTo=function(e,t,r){if("object"!=typeof e)throw TypeError("target musts be an object");var n=this;return r=b(r,{on:o,off:o,reducers:o},{on:C,off:C,reducers:E}),function(t){if("object"!=typeof t)throw TypeError("events must be an object");var i,o,a=r.reducers,s=A.call(n,e);i=-1===s?new y(n,e,r):n._observers[s];for(var l=d(t),c=l.length,u="function"==typeof a,h=0;h<c;h++)o=l[h],i.subscribe(o,t[o]||o,u?a:a&&a[o])}(s(t)?v(t):"string"==typeof t?v(t.split(/\s+/)):t),this},j.prototype.stopListeningTo=function(e,t){var r,n=this._observers;if(!n)return!1;var i=n.length,o=!1;if(e&&"object"!=typeof e)throw TypeError("target should be an object");for(;i-- >0;)r=n[i],e&&r._target!==e||(r.unsubscribe(t),o=!0);return o},j.prototype.delimiter=".",j.prototype.setMaxListeners=function(e){o!==e&&(this._maxListeners=e,this._conf||(this._conf={}),this._conf.maxListeners=e)},j.prototype.getMaxListeners=function(){return this._maxListeners},j.prototype.event="",j.prototype.once=function(e,t,r){return this._once(e,t,!1,r)},j.prototype.prependOnceListener=function(e,t,r){return this._once(e,t,!0,r)},j.prototype._once=function(e,t,r,n){return this._many(e,1,t,r,n)},j.prototype.many=function(e,t,r,n){return this._many(e,t,r,!1,n)},j.prototype.prependMany=function(e,t,r,n){return this._many(e,t,r,!0,n)},j.prototype._many=function(e,t,r,n,i){var o=this;if("function"!=typeof r)throw Error("many only accepts instances of Function");function a(){return 0==--t&&o.off(e,a),r.apply(this,arguments)}return a._origin=r,this._on(e,a,n,i)},j.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||f.call(this);var e,t,r,n,i,o,a,s=arguments[0],l=this.wildcard;if("newListener"===s&&!this._newListener&&!this._events.newListener)return!1;if(l&&(a=s,"newListener"!==s&&"removeListener"!==s&&"object"==typeof s)){if(t=s.length,c){for(r=0;r<t;r++)if("symbol"==typeof s[r]){i=!0;break}}i||(s=s.join(this.delimiter))}var u=arguments.length;if(this._all&&this._all.length)for(r=0,t=(o=this._all.slice()).length;r<t;r++)switch(this.event=s,u){case 1:o[r].call(this,s);break;case 2:o[r].call(this,s,arguments[1]);break;case 3:o[r].call(this,s,arguments[1],arguments[2]);break;default:o[r].apply(this,arguments)}if(l)o=[],_.call(this,o,a,this.listenerTree,0,t);else{if("function"==typeof(o=this._events[s])){switch(this.event=s,u){case 1:o.call(this);break;case 2:o.call(this,arguments[1]);break;case 3:o.call(this,arguments[1],arguments[2]);break;default:for(n=1,e=Array(u-1);n<u;n++)e[n-1]=arguments[n];o.apply(this,e)}return!0}o&&(o=o.slice())}if(o&&o.length){if(u>3)for(n=1,e=Array(u-1);n<u;n++)e[n-1]=arguments[n];for(r=0,t=o.length;r<t;r++)switch(this.event=s,u){case 1:o[r].call(this);break;case 2:o[r].call(this,arguments[1]);break;case 3:o[r].call(this,arguments[1],arguments[2]);break;default:o[r].apply(this,e)}return!0}if(!this.ignoreErrors&&!this._all&&"error"===s)if(arguments[1]instanceof Error)throw arguments[1];else throw Error("Uncaught, unspecified 'error' event.");return!!this._all},j.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||f.call(this);var e,t,r,n,i,o,a,s=arguments[0],l=this.wildcard;if("newListener"===s&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(l&&(o=s,"newListener"!==s&&"removeListener"!==s&&"object"==typeof s)){if(t=s.length,c){for(r=0;r<t;r++)if("symbol"==typeof s[r]){a=!0;break}}a||(s=s.join(this.delimiter))}var u=[],h=arguments.length;if(this._all)for(r=0,t=this._all.length;r<t;r++)switch(this.event=s,h){case 1:u.push(this._all[r].call(this,s));break;case 2:u.push(this._all[r].call(this,s,arguments[1]));break;case 3:u.push(this._all[r].call(this,s,arguments[1],arguments[2]));break;default:u.push(this._all[r].apply(this,arguments))}if(l?(i=[],_.call(this,i,o,this.listenerTree,0)):i=this._events[s],"function"==typeof i)switch(this.event=s,h){case 1:u.push(i.call(this));break;case 2:u.push(i.call(this,arguments[1]));break;case 3:u.push(i.call(this,arguments[1],arguments[2]));break;default:for(n=1,e=Array(h-1);n<h;n++)e[n-1]=arguments[n];u.push(i.apply(this,e))}else if(i&&i.length){if(i=i.slice(),h>3)for(n=1,e=Array(h-1);n<h;n++)e[n-1]=arguments[n];for(r=0,t=i.length;r<t;r++)switch(this.event=s,h){case 1:u.push(i[r].call(this));break;case 2:u.push(i[r].call(this,arguments[1]));break;case 3:u.push(i[r].call(this,arguments[1],arguments[2]));break;default:u.push(i[r].apply(this,e))}}else if(!this.ignoreErrors&&!this._all&&"error"===s)if(arguments[1]instanceof Error)return Promise.reject(arguments[1]);else return Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(u)},j.prototype.on=function(e,t,r){return this._on(e,t,!1,r)},j.prototype.prependListener=function(e,t,r){return this._on(e,t,!0,r)},j.prototype.onAny=function(e){return this._onAny(e,!1)},j.prototype.prependAny=function(e){return this._onAny(e,!0)},j.prototype.addListener=j.prototype.on,j.prototype._onAny=function(e,t){if("function"!=typeof e)throw Error("onAny only accepts instances of Function");return this._all||(this._all=[]),t?this._all.unshift(e):this._all.push(e),this},j.prototype._on=function(e,t,r,n){if("function"==typeof e)return this._onAny(e,t),this;if("function"!=typeof t)throw Error("on only accepts instances of Function");this._events||f.call(this);var i,a=this;return(o!==n&&(t=(i=L.call(this,e,t,n))[0],a=i[1]),this._newListener&&this.emit("newListener",e,t),this.wildcard)?R.call(this,e,t,r):this._events[e]?("function"==typeof this._events[e]&&(this._events[e]=[this._events[e]]),r?this._events[e].unshift(t):this._events[e].push(t),!this._events[e].warned&&this._maxListeners>0&&this._events[e].length>this._maxListeners&&(this._events[e].warned=!0,m.call(this,this._events[e].length,e))):this._events[e]=t,a},j.prototype.off=function(e,t){if("function"!=typeof t)throw Error("removeListener only takes instances of Function");var r,n=[];if(this.wildcard){var i="string"==typeof e?e.split(this.delimiter):e.slice();if(!(n=_.call(this,null,i,this.listenerTree,0)))return this}else{if(!this._events[e])return this;r=this._events[e],n.push({_listeners:r})}for(var o=0;o<n.length;o++){var a=n[o];if(s(r=a._listeners)){for(var l=-1,c=0,u=r.length;c<u;c++)if(r[c]===t||r[c].listener&&r[c].listener===t||r[c]._origin&&r[c]._origin===t){l=c;break}if(l<0)continue;return this.wildcard?a._listeners.splice(l,1):this._events[e].splice(l,1),0===r.length&&(this.wildcard?delete a._listeners:delete this._events[e]),this._removeListener&&this.emit("removeListener",e,t),this}(r===t||r.listener&&r.listener===t||r._origin&&r._origin===t)&&(this.wildcard?delete a._listeners:delete this._events[e],this._removeListener&&this.emit("removeListener",e,t))}return this.listenerTree&&T(this.listenerTree),this},j.prototype.offAny=function(e){var t,r=0,n=0;if(e&&this._all&&this._all.length>0){for(r=0,n=(t=this._all).length;r<n;r++)if(e===t[r]){t.splice(r,1),this._removeListener&&this.emit("removeListenerAny",e);break}}else{if(t=this._all,this._removeListener)for(r=0,n=t.length;r<n;r++)this.emit("removeListenerAny",t[r]);this._all=[]}return this},j.prototype.removeListener=j.prototype.off,j.prototype.removeAllListeners=function(e){if(o===e)return this._events&&f.call(this),this;if(this.wildcard){var t,r=_.call(this,null,e,this.listenerTree,0);if(!r)return this;for(t=0;t<r.length;t++)r[t]._listeners=null;this.listenerTree&&T(this.listenerTree)}else this._events&&(this._events[e]=null);return this},j.prototype.listeners=function(e){var t,r,n,i,a,s=this._events;if(o===e){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!s)return[];for(i=(t=d(s)).length,n=[];i-- >0;)"function"==typeof(r=s[t[i]])?n.push(r):n.push.apply(n,r);return n}if(this.wildcard){if(!(a=this.listenerTree))return[];var l=[],c="string"==typeof e?e.split(this.delimiter):e.slice();return _.call(this,l,c,a,0),l}return s&&(r=s[e])?"function"==typeof r?[r]:r:[]},j.prototype.eventNames=function(e){var t=this._events;return this.wildcard?S.call(this,this.listenerTree,[],null,e):t?d(t):[]},j.prototype.listenerCount=function(e){return this.listeners(e).length},j.prototype.hasListeners=function(e){if(this.wildcard){var t=[],r="string"==typeof e?e.split(this.delimiter):e.slice();return _.call(this,t,r,this.listenerTree,0),t.length>0}var n=this._events,i=this._all;return!!(i&&i.length||n&&(o===e?d(n).length:n[e]))},j.prototype.listenersAny=function(){return this._all?this._all:[]},j.prototype.waitFor=function(e,t){var r=this,n=typeof t;return"number"===n?t={timeout:t}:"function"===n&&(t={filter:t}),x((t=b(t,{timeout:0,filter:o,handleError:!1,Promise:Promise,overload:!1},{filter:C,Promise:w})).Promise,function(n,i,o){function a(){var o=t.filter;if(!o||o.apply(r,arguments))if(r.off(e,a),t.handleError){var s=arguments[0];s?i(s):n(g.apply(null,arguments).slice(1))}else n(g.apply(null,arguments))}o(function(){r.off(e,a)}),r._on(e,a,!1)},{timeout:t.timeout,overload:t.overload})};var I=j.prototype;Object.defineProperties(j,{defaultMaxListeners:{get:function(){return I._maxListeners},set:function(e){if("number"!=typeof e||e<0||Number.isNaN(e))throw TypeError("n must be a non-negative number");I._maxListeners=e},enumerable:!0},once:{value:function(e,t,r){return x((r=b(r,{Promise:Promise,timeout:0,overload:!1},{Promise:w})).Promise,function(r,n,i){if("function"==typeof e.addEventListener){o=function(){r(g.apply(null,arguments))},i(function(){e.removeEventListener(t,o)}),e.addEventListener(t,o,{once:!0});return}var o,a,s=function(){a&&e.removeListener("error",a),r(g.apply(null,arguments))};"error"!==t&&(a=function(r){e.removeListener(t,s),n(r)},e.once("error",a)),i(function(){a&&e.removeListener("error",a),e.removeListener(t,s)}),e.once(t,s)},{timeout:r.timeout,overload:r.overload})},writable:!0,configurable:!0}}),Object.defineProperties(I,{_maxListeners:{value:10,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),o===(n=(function(){return j}).call(t,r,t,e))||(e.exports=n)}()},80375:(e,t,r)=>{"use strict";e.exports=r(28975)},80797:(e,t,r)=>{var n=0/0,i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,s=/^0o[0-7]+$/i,l=parseInt,c="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,u="object"==typeof self&&self&&self.Object===Object&&self,h=c||u||Function("return this")(),d=Object.prototype.toString,f=Math.max,p=Math.min,m=function(){return h.Date.now()};function g(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function v(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==d.call(t))return n;if(g(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=g(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var c=a.test(e);return c||s.test(e)?l(e.slice(2),c?2:8):o.test(e)?n:+e}e.exports=function(e,t,r){var n,i,o,a,s,l,c=0,u=!1,h=!1,d=!0;if("function"!=typeof e)throw TypeError("Expected a function");function y(t){var r=n,o=i;return n=i=void 0,c=t,a=e.apply(o,r)}function b(e){var r=e-l,n=e-c;return void 0===l||r>=t||r<0||h&&n>=o}function w(){var e,r,n,i=m();if(b(i))return k(i);s=setTimeout(w,(e=i-l,r=i-c,n=t-e,h?p(n,o-r):n))}function k(e){return(s=void 0,d&&n)?y(e):(n=i=void 0,a)}function C(){var e,r=m(),o=b(r);if(n=arguments,i=this,l=r,o){if(void 0===s)return c=e=l,s=setTimeout(w,t),u?y(e):a;if(h)return s=setTimeout(w,t),y(l)}return void 0===s&&(s=setTimeout(w,t)),a}return t=v(t)||0,g(r)&&(u=!!r.leading,o=(h="maxWait"in r)?f(v(r.maxWait)||0,t):o,d="trailing"in r?!!r.trailing:d),C.cancel=function(){void 0!==s&&clearTimeout(s),c=0,n=l=i=s=void 0},C.flush=function(){return void 0===s?a:k(m())},C}},80941:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},84525:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(95851).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},90220:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},90404:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},94007:(e,t)=>{t.read=function(e,t,r,n,i){var o,a,s=8*i-n-1,l=(1<<s)-1,c=l>>1,u=-7,h=r?i-1:0,d=r?-1:1,f=e[t+h];for(h+=d,o=f&(1<<-u)-1,f>>=-u,u+=s;u>0;o=256*o+e[t+h],h+=d,u-=8);for(a=o&(1<<-u)-1,o>>=-u,u+=n;u>0;a=256*a+e[t+h],h+=d,u-=8);if(0===o)o=1-c;else{if(o===l)return a?NaN:1/0*(f?-1:1);a+=Math.pow(2,n),o-=c}return(f?-1:1)*a*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var a,s,l,c=8*o-i-1,u=(1<<c)-1,h=u>>1,d=5960464477539062e-23*(23===i),f=n?0:o-1,p=n?1:-1,m=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(s=+!!isNaN(t),a=u):(a=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-a))<1&&(a--,l*=2),a+h>=1?t+=d/l:t+=d*Math.pow(2,1-h),t*l>=2&&(a++,l/=2),a+h>=u?(s=0,a=u):a+h>=1?(s=(t*l-1)*Math.pow(2,i),a+=h):(s=t*Math.pow(2,h-1)*Math.pow(2,i),a=0));i>=8;e[r+f]=255&s,f+=p,s/=256,i-=8);for(a=a<<i|s,c+=i;c>0;e[r+f]=255&a,f+=p,a/=256,c-=8);e[r+f-p]|=128*m}},95851:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(50628);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:u,iconNode:h,...d}=e;return(0,n.createElement)("svg",{ref:t,...a,width:i,height:i,stroke:r,strokeWidth:l?24*Number(s)/Number(i):s,className:o("lucide",c),...d},[...h.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),l=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:l,...c}=r;return(0,n.createElement)(s,{ref:a,iconNode:t,className:o("lucide-".concat(i(e)),l),...c})});return r.displayName="".concat(e),r}}}]);