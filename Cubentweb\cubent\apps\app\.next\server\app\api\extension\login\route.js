(()=>{var e={};e.id=6847,e.ids=[6847],e.modules={1378:(e,r,t)=>{Promise.resolve().then(t.bind(t,86332))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},8963:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=8963,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67098:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,21034,23))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74433:(e,r,t)=>{"use strict";t.d(r,{u:()=>o});var s=t(4520),i=t(76741);let o=e=>{let r="An error occurred";r=e instanceof Error||e&&"object"==typeof e&&"message"in e?e.message:String(e);try{(0,s.captureException)(e),i.R.error(`Parsing error: ${r}`)}catch(e){console.error("Error parsing error:",e)}return r}},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76741:(e,r,t)=>{"use strict";t.d(r,{R:()=>s});let s=t(42870).log},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77470:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>g,serverHooks:()=>v,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{POST:()=>q});var i=t(26142),o=t(94327),n=t(34862),u=t(37838),a=t(1359),c=t(18815),p=t(74433),d=t(76741),l=t(26239),x=t(25);let m=x.z.object({deviceId:x.z.string().min(1,"Device ID is required"),state:x.z.string().min(1,"State parameter is required"),acceptTerms:x.z.boolean().optional()}),q=async e=>{try{let{userId:r}=await (0,u.j)(),s=await (0,a.N)();if(!r||!s)return l.NextResponse.json({error:"Unauthorized",message:"User not authenticated"},{status:401});let i=await e.json(),{deviceId:o,state:n,acceptTerms:p}=m.parse(i),x=await c.database.user.upsert({where:{clerkId:r},update:{email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl},create:{clerkId:r,email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl}});if(p&&!x.termsAccepted&&(x=await c.database.user.update({where:{id:x.id},data:{termsAccepted:!0,termsAcceptedAt:new Date}})),!(x.termsAccepted||p))return l.NextResponse.json({error:"Terms not accepted",message:"User must accept terms to continue"},{status:400});let{randomBytes:q}=await Promise.resolve().then(t.t.bind(t,55511,23)),g=`cubent_ext_${q(32).toString("hex")}`,h=new Date(Date.now()+6e5);await c.database.pendingLogin.deleteMany({where:{OR:[{deviceId:o},{expiresAt:{lt:new Date}}]}}),await c.database.pendingLogin.create({data:{deviceId:o,state:n,token:g,userId:r,expiresAt:h}}),d.R.info("Extension login successful",{userId:x.id,deviceId:o.slice(0,8)+"...",email:x.email});let f=`vscode://cubent.cubent/auth/callback?token=${encodeURIComponent(g)}&state=${encodeURIComponent(n)}`;return l.NextResponse.json({success:!0,token:g,redirectUrl:f,message:"Login successful"})}catch(r){let e=(0,p.u)(r);if(d.R.error("Extension login failed",{error:e}),r instanceof x.z.ZodError)return l.NextResponse.json({error:"Validation error",message:r.errors[0]?.message||"Invalid input"},{status:400});return l.NextResponse.json({error:"Internal server error",message:"Login failed"},{status:500})}},g=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/extension/login/route",pathname:"/api/extension/login",filename:"route",bundlePath:"app/api/extension/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\login\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:v}=g;function w(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,7873,3887,5480,1359,3319,864],()=>t(77470));module.exports=s})();