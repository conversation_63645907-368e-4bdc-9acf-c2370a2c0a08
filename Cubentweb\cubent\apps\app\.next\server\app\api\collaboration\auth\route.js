(()=>{var t={};t.id=7162,t.ids=[7162],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},11997:t=>{"use strict";t.exports=require("punycode")},16698:t=>{"use strict";t.exports=require("node:async_hooks")},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(t,e,i)=>{"use strict";t.exports=i(44870)},27910:t=>{"use strict";t.exports=require("stream")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:t=>{"use strict";t.exports=require("node:fs")},73545:(t,e,i)=>{"use strict";i.r(e),i.d(e,{patchFetch:()=>eY,routeModule:()=>eB,serverHooks:()=>eJ,workAsyncStorage:()=>eF,workUnitAsyncStorage:()=>eW});var r={};i.r(r),i.d(r,{POST:()=>eH});var s=i(26142),o=i(94327),n=i(34862),a=i(1359),h=i(37838),l=Object.defineProperty,d="@liveblocks/core",u="2.24.2",c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{};function p(t){console.error(t)}function f(t,e,i){let r=Symbol.for(t),s=i?`${e||"dev"} (${i})`:e||"dev";c[r]?c[r]===s||p(`Multiple copies of Liveblocks are being loaded in your project. This will cause issues! See https://liveblocks.io/docs/errors/dupes 

Conflicts:
- ${t} ${c[r]} (already loaded)
- ${t} ${s} (trying to load this now)`):c[r]=s,e&&u&&e!==u&&p(`Cross-linked versions of Liveblocks found, which will cause issues! See https://liveblocks.io/docs/errors/cross-linked 

Conflicts:
- ${d} is at ${u}
- ${t} is at ${e}

Always upgrade all Liveblocks packages to the same version number.`)}function m(t){let e=t.editedAt?new Date(t.editedAt):void 0,i=new Date(t.createdAt),r=t.reactions.map(t=>({...t,createdAt:new Date(t.createdAt)}));if(t.body)return{...t,reactions:r,createdAt:i,editedAt:e};{let s=new Date(t.deletedAt);return{...t,reactions:r,createdAt:i,editedAt:e,deletedAt:s}}}function y(t){let e=new Date(t.createdAt),i=new Date(t.updatedAt),r=t.comments.map(t=>m(t));return{...t,createdAt:e,updatedAt:i,comments:r}}function _(t){let e=new Date(t.notifiedAt),i=t.readAt?new Date(t.readAt):null;if("activities"in t){let r=t.activities.map(t=>({...t,createdAt:new Date(t.createdAt)}));return{...t,notifiedAt:e,readAt:i,activities:r}}return{...t,notifiedAt:e,readAt:i}}function w(t){let e=new Date(t.createdAt);return{...t,createdAt:e}}((t,e)=>{for(var i in e)l(t,i,{get:e[i],enumerable:!0})})({},{error:()=>S,errorWithTitle:()=>T,warn:()=>E,warnWithTitle:()=>O});var g="background:#0e0d12;border-radius:9999px;color:#fff;padding:3px 7px;font-family:sans-serif;font-weight:600;";function v(t){return"undefined"==typeof window?console[t]:(e,...i)=>console[t]("%cLiveblocks",g,e,...i)}var E=v("warn"),S=v("error");function b(t){return"undefined"==typeof window?console[t]:(e,i,...r)=>console[t](`%cLiveblocks%c ${e}`,g,"font-weight:600",i,...r)}var O=b("warn"),T=b("error");function A(t){return null!==t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)}function I(t){return A(t)&&"string"==typeof t.startsWith}function k(t){throw Error(t)}function C(t){return Object.entries(t)}function R(t){try{return JSON.parse(t)}catch(t){return}}function P(t){return JSON.parse(JSON.stringify(t))}function D(t){let e={...t};return Object.keys(t).forEach(t=>{void 0===e[t]&&delete e[t]}),e}async function x(t,e,i){let r;return Promise.race([t,new Promise((t,s)=>{r=setTimeout(()=>{s(Error(i))},e)})]).finally(()=>clearTimeout(r))}var N=class t extends Error{response;details;constructor(t,e,i){super(t),this.name="HttpError",this.response=e,this.details=i}static async fromResponse(e){let i,r,s;try{i=await e.text()}catch{}let o=i?R(i):void 0;A(o)&&(r=o);let n="";n||="string"==typeof r?.message?r.message:"",n||="string"==typeof r?.error?r.error:"",void 0===o&&(n||=i||""),n||=e.statusText;try{s=new URL(e.url).pathname}catch{}return new t(n+=void 0!==s?` (got status ${e.status} from ${s})`:` (got status ${e.status})`,e,r)}get status(){return this.response.status}},$=t=>t instanceof N&&t.status>=400&&t.status<500;function L(){let t,e;return[new Promise((i,r)=>{t=i,e=r}),t,e]}function j(){let t=new Set;function e(e){return t.add(e),()=>t.delete(e)}function i(t){let i=e(e=>(i(),t(e)));return i}async function r(t){let i;return new Promise(r=>{i=e(e=>{(void 0===t||t(e))&&r(e)})}).finally(()=>i?.())}return{notify:function(e){let i=!1;for(let r of t)r(e),i=!0;return i},subscribe:e,subscribeOnce:i,count:function(){return t.size},waitUntil:r,dispose(){t.clear()},observable:{subscribe:e,subscribeOnce:i,waitUntil:r}}}var U=t=>t,M=Symbol("kSinks"),K=Symbol("kTrigger"),z=null,q=null;function H(t){if(null!==z)return void t();z=new Set;try{t()}finally{for(let t of z)t[K]();z=null}}function B(t){z||k("Expected to be in an active batch"),z.add(t)}function F(t,e){let i=!1,r={...t};return Object.keys(e).forEach(t=>{let s=e[t];r[t]!==s&&(void 0===s?delete r[t]:r[t]=s,i=!0)}),i?r:t}var W=class{equals;#t;[M];constructor(t){this.equals=t??Object.is,this.#t=j(),this[M]=new Set,this.get=this.get.bind(this),this.subscribe=this.subscribe.bind(this),this.subscribeOnce=this.subscribeOnce.bind(this)}dispose(){this.#t.dispose(),this.#t="(disposed)",this.equals="(disposed)"}get hasWatchers(){if(this.#t.count()>0)return!0;for(let t of this[M])if(t.hasWatchers)return!0;return!1}[K](){for(let t of(this.#t.notify(),this[M]))B(t)}subscribe(t){return 0===this.#t.count()&&this.get(),this.#t.subscribe(t)}subscribeOnce(t){let e=this.subscribe(()=>(e(),t()));return e}waitUntil(){throw Error("waitUntil not supported on Signals")}markSinksDirty(){for(let t of this[M])t.markDirty()}addSink(t){this[M].add(t)}removeSink(t){this[M].delete(t)}asReadonly(){return this}},J=class extends W{#e;constructor(t,e){super(e),this.#e=U(t)}dispose(){super.dispose(),this.#e="(disposed)"}get(){return q?.add(this),this.#e}set(t){H(()=>{"function"==typeof t&&(t=t(this.#e)),this.equals(this.#e,t)||(this.#e=U(t),this.markSinksDirty(),B(this))})}},Y=Symbol(),G=class t extends W{#i;#r;#s;#o;#n;static from(...e){let i=e.pop();if("function"!=typeof i&&k("Invalid .from() call, last argument expected to be a function"),"function"!=typeof e[e.length-1])return new t(e,i);{let r=e.pop();return new t(e,r,i)}}constructor(t,e,i){super(i),this.#r=!0,this.#i=Y,this.#o=t,this.#s=new Set,this.#n=e}dispose(){for(let t of this.#s)t.removeSink(this);this.#i="(disposed)",this.#s="(disposed)",this.#o="(disposed)",this.#n="(disposed)"}get isDirty(){return this.#r}#a(){let t,e=q;q=new Set;try{t=this.#n(...this.#o.map(t=>t.get()))}finally{let t=this.#s;for(let e of(this.#s=new Set,q))this.#s.add(e),t.delete(e);for(let e of t)e.removeSink(this);for(let t of this.#s)t.addSink(this);q=e}return this.#r=!1,!this.equals(this.#i,t)&&(this.#i=t,!0)}markDirty(){this.#r||(this.#r=!0,this.markSinksDirty())}get(){return this.#r&&this.#a(),q?.add(this),this.#i}[K](){this.hasWatchers&&this.#a()&&super[K]()}},V=class extends W{#h;constructor(t){super(),this.#h=t}dispose(){super.dispose(),this.#h="(disposed)"}get(){return q?.add(this),this.#h}mutate(t){H(()=>{let e=!t||t(this.#h);null!==e&&"object"==typeof e&&"then"in e&&k("MutableSignal.mutate() does not support async callbacks"),!1!==e&&(this.markSinksDirty(),B(this))})}};function X(t,e){return null===e||"object"!=typeof e||Array.isArray(e)?e:Object.keys(e).sort().reduce((t,i)=>(t[i]=e[i],t),{})}function Z(t){return JSON.stringify(t,X)}function Q(t){try{return JSON.stringify(t)}catch(e){throw console.error(`Could not stringify: ${e.message}`),console.error(t),e}}var tt=class{input;resolve;reject;promise;constructor(t){this.input=t;let{promise:e,resolve:i,reject:r}=function(){let[t,e,i]=L();return{promise:t,resolve:e,reject:i}}();this.promise=e,this.resolve=i,this.reject=r}},te=(t=21)=>crypto.getRandomValues(new Uint8Array(t)).reduce((t,e)=>t+=(e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e<63?"_":"-","");var ti=/^[a-zA-Z_][a-zA-Z0-9_]*$/;function tr(t){let e=[],i=Object.entries(t),r=[],s=[],o=[];return i.forEach(([t,e])=>{if(!ti.test(t))throw Error("Key must only contain letters, numbers, _");tn(e)?r.push([t,e]):A(e)&&(I(e)?s.push([t,e]):o.push([t,e]))}),e=[...ts(r),...to(s)],o.forEach(([t,i])=>{let r=Object.entries(i),s=[],o=[];r.forEach(([e,i])=>{if(th(e))throw Error("Key cannot be empty");tn(i)?s.push([ta(t,e),i]):I(i)&&o.push([ta(t,e),i])}),e=[...e,...ts(s),...to(o)]}),e.map(({key:t,operator:e,value:i})=>`${t}${e}${tl(i)}`).join(" ")}var ts=t=>{let e=[];return t.forEach(([t,i])=>{e.push({key:t,operator:":",value:i})}),e},to=t=>{let e=[];return t.forEach(([t,i])=>{"startsWith"in i&&"string"==typeof i.startsWith&&e.push({key:t,operator:"^",value:i.startsWith})}),e},tn=t=>"string"==typeof t||"number"==typeof t||"boolean"==typeof t||null===t,ta=(t,e)=>e?`${t}[${tl(e)}]`:t,th=t=>!t||""===t.toString().trim();function tl(t){let e=JSON.stringify(t);return"string"!=typeof t||e.includes("'")?e:`'${e.slice(1,-1).replace(/\\"/g,'"')}'`}function td(t,e,i){let r=new URL(e,t);return void 0!==i&&(r.search=(i instanceof URLSearchParams?i:function(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))null!=r&&e.set(i,r.toString());return e}(i)).toString()),r.toString()}function tu(t,...e){return t.reduce((t,i,r)=>t+encodeURIComponent(e[r-1]??"")+i)}function tc(t,e){throw Error(e)}function tp(t,e="Expected value to be non-nullable"){return t}var tf=class{#l;constructor(t){this.#l=t}get current(){return this.#l}allowPatching(t){let e=this,i=!0;t({...this.#l,patch(t){if(i)for(let i of(e.#l=Object.assign({},e.#l,t),Object.entries(t))){let[t,e]=i;"patch"!==t&&(this[t]=e)}else throw Error("Can no longer patch stale context")}}),i=!1}},tm=1,ty=class{id;#d;#u;#c;#p;#f;#m;events;#y;#_;#w;get #g(){let t=this.#c.values()[Symbol.iterator]().next();if(!t.done)return t.value;throw Error("No states defined yet")}get currentState(){if(null===this.#p)if(0===this.#d)throw Error("Not started yet");else throw Error("Already stopped");return this.#p}start(){if(0!==this.#d)throw Error("State machine has already started");return this.#d=1,this.#p=this.#g,this.#v(null),this}stop(){if(1!==this.#d)throw Error("Cannot stop a state machine that hasn't started yet");this.#E(null),this.#d=2,this.#p=null}constructor(t){this.id=tm++,this.#d=0,this.#p=null,this.#c=new Set,this.#_=new Map,this.#y=[],this.#w=new Set,this.#f=new Map,this.#u=new tf(t),this.#m={didReceiveEvent:j(),willTransition:j(),didIgnoreEvent:j(),willExitState:j(),didEnterState:j()},this.events={didReceiveEvent:this.#m.didReceiveEvent.observable,willTransition:this.#m.willTransition.observable,didIgnoreEvent:this.#m.didIgnoreEvent.observable,willExitState:this.#m.willExitState.observable,didEnterState:this.#m.didEnterState.observable}}get context(){return this.#u.current}addState(t){if(0!==this.#d)throw Error("Already started");return this.#c.add(t),this}onEnter(t,e){if(0!==this.#d)throw Error("Already started");if(this.#_.has(t))throw Error(`enter/exit function for ${t} already exists`);return this.#_.set(t,e),this}onEnterAsync(t,e,i,r,s){return this.onEnter(t,()=>{let t=new AbortController,o=t.signal,n=s?setTimeout(()=>{let t=Error("Timed out");this.#S({type:"ASYNC_ERROR",reason:t},r)},s):void 0,a=!1;return e(this.#u.current,o).then(t=>{o.aborted||(a=!0,this.#S({type:"ASYNC_OK",data:t},i))},t=>{o.aborted||(a=!0,this.#S({type:"ASYNC_ERROR",reason:t},r))}),()=>{clearTimeout(n),a||t.abort()}})}#b(t){let e=[];if("*"===t)for(let t of this.#c)e.push(t);else if(t.endsWith(".*")){let i=t.slice(0,-1);for(let t of this.#c)t.startsWith(i)&&e.push(t)}else this.#c.has(t)&&e.push(t);if(0===e.length)throw Error(`No states match ${JSON.stringify(t)}`);return e}addTransitions(t,e){if(0!==this.#d)throw Error("Already started");for(let i of this.#b(t)){let r=this.#f.get(i);for(let[s,o]of(void 0===r&&(r=new Map,this.#f.set(i,r)),Object.entries(e))){if(r.has(s))throw Error(`Trying to set transition "${s}" on "${i}" (via "${t}"), but a transition already exists there.`);let e=o;if(this.#w.add(s),void 0!==e){let t="function"==typeof e?e:()=>e;r.set(s,t)}}}return this}addTimedTransition(t,e,i){return this.onEnter(t,()=>{let t=setTimeout(()=>{this.#S({type:"TIMER"},i)},"function"==typeof e?e(this.#u.current):e);return()=>{clearTimeout(t)}})}#O(t){return this.#f.get(this.currentState)?.get(t)}#E(t){this.#m.willExitState.notify(this.currentState),this.#u.allowPatching(e=>{t=t??this.#y.length;for(let i=0;i<t;i++)this.#y.pop()?.(e)})}#v(t){let e=function(t,e){let i=t.split(".");if(e<1||e>i.length+1)throw Error("Invalid number of levels");let r=[];e>i.length&&r.push("*");for(let t=i.length-e+1;t<i.length;t++){let e=i.slice(0,t);e.length>0&&r.push(e.join(".")+".*")}return r.push(t),r}(this.currentState,t??this.currentState.split(".").length+1);this.#u.allowPatching(t=>{for(let i of e){let e=this.#_.get(i),r=e?.(t);"function"==typeof r?this.#y.push(r):this.#y.push(null)}}),this.#m.didEnterState.notify(this.currentState)}send(t){if(!this.#w.has(t.type))throw Error(`Invalid event ${JSON.stringify(t.type)}`);if(2===this.#d)return;let e=this.#O(t.type);if(void 0!==e)return this.#S(t,e);this.#m.didIgnoreEvent.notify(t)}#S(t,e){let i,r;this.#m.didReceiveEvent.notify(t);let s=this.currentState,o=("function"==typeof e?e:()=>e)(t,this.#u.current);if(null===o)return void this.#m.didIgnoreEvent.notify(t);if("string"==typeof o?i=o:(i=o.target,r=Array.isArray(o.effect)?o.effect:[o.effect]),!this.#c.has(i))throw Error(`Invalid next state name: ${JSON.stringify(i)}`);this.#m.willTransition.notify({from:s,to:i});let[n,a]=function(t,e){if(t===e)return[0,0];let i=t.split("."),r=e.split("."),s=Math.min(i.length,r.length),o=0;for(;o<s&&i[o]===r[o];o++);return[i.length-o,r.length-o]}(this.currentState,i);if(n>0&&this.#E(n),this.#p=i,void 0!==r){let e=r;this.#u.allowPatching(i=>{for(let r of e)"function"==typeof r?r(i,t):i.patch(r)})}a>0&&this.#v(a)}},t_=(t=>(t[t.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",t[t.USER_JOINED=101]="USER_JOINED",t[t.USER_LEFT=102]="USER_LEFT",t[t.BROADCASTED_EVENT=103]="BROADCASTED_EVENT",t[t.ROOM_STATE=104]="ROOM_STATE",t[t.INITIAL_STORAGE_STATE=200]="INITIAL_STORAGE_STATE",t[t.UPDATE_STORAGE=201]="UPDATE_STORAGE",t[t.REJECT_STORAGE_OP=299]="REJECT_STORAGE_OP",t[t.UPDATE_YDOC=300]="UPDATE_YDOC",t[t.THREAD_CREATED=400]="THREAD_CREATED",t[t.THREAD_DELETED=407]="THREAD_DELETED",t[t.THREAD_METADATA_UPDATED=401]="THREAD_METADATA_UPDATED",t[t.THREAD_UPDATED=408]="THREAD_UPDATED",t[t.COMMENT_CREATED=402]="COMMENT_CREATED",t[t.COMMENT_EDITED=403]="COMMENT_EDITED",t[t.COMMENT_DELETED=404]="COMMENT_DELETED",t[t.COMMENT_REACTION_ADDED=405]="COMMENT_REACTION_ADDED",t[t.COMMENT_REACTION_REMOVED=406]="COMMENT_REACTION_REMOVED",t))(t_||{}),tw=(t=>(t[t.CLOSE_NORMAL=1e3]="CLOSE_NORMAL",t[t.CLOSE_ABNORMAL=1006]="CLOSE_ABNORMAL",t[t.UNEXPECTED_CONDITION=1011]="UNEXPECTED_CONDITION",t[t.TRY_AGAIN_LATER=1013]="TRY_AGAIN_LATER",t[t.INVALID_MESSAGE_FORMAT=4e3]="INVALID_MESSAGE_FORMAT",t[t.NOT_ALLOWED=4001]="NOT_ALLOWED",t[t.MAX_NUMBER_OF_MESSAGES_PER_SECONDS=4002]="MAX_NUMBER_OF_MESSAGES_PER_SECONDS",t[t.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS=4003]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS",t[t.MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP=4004]="MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP",t[t.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM=4005]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM",t[t.ROOM_ID_UPDATED=4006]="ROOM_ID_UPDATED",t[t.KICKED=4100]="KICKED",t[t.TOKEN_EXPIRED=4109]="TOKEN_EXPIRED",t[t.CLOSE_WITHOUT_RETRY=4999]="CLOSE_WITHOUT_RETRY",t))(tw||{});function tg(t){return 4999===t||t>=4e3&&t<4100}function tv(t){return 1013===t||t>=4200&&t<4300}function tE(t){let e=t.currentState;switch(e){case"@ok.connected":case"@ok.awaiting-pong":return"connected";case"@idle.initial":return"initial";case"@auth.busy":case"@auth.backoff":case"@connecting.busy":case"@connecting.backoff":case"@idle.zombie":return t.context.successCount>0?"reconnecting":"connecting";case"@idle.failed":return"disconnected";default:return tc(e,"Unknown state")}}var tS=[250,500,1e3,2e3,4e3,8e3,1e4],tb=tS[0]-1,tO=class extends Error{constructor(t){super(t)}};function tT(t,e){return e.find(e=>e>t)??e[e.length-1]}function tA(t){t.patch({backoffDelay:tT(t.backoffDelay,tS)})}function tI(t){t.patch({backoffDelay:tT(t.backoffDelay,null)})}function tk(t){t.patch({successCount:0})}function tC(t,e){let i=2===t?S:1===t?E:()=>{};return()=>{i(e)}}function tR(t){let e="Connection to Liveblocks websocket server";return i=>{t instanceof Error?E(`${e} could not be established. ${String(t)}`):E(tx(t)?`${e} closed prematurely (code: ${t.code}). Retrying in ${i.backoffDelay}ms.`:`${e} could not be established.`)}}function tP(t){let e=[`code: ${t.code}`];return t.reason&&e.push(`reason: ${t.reason}`),t=>{E(`Connection to Liveblocks websocket server closed (${e.join(", ")}). Retrying in ${t.backoffDelay}ms.`)}}var tD=tC(1,"Connection to WebSocket closed permanently. Won't retry.");function tx(t){return!(t instanceof Error)&&"close"===t.type}var tN=t=>e=>e.patch(t),t$=(t=>(t.Read="room:read",t.Write="room:write",t.PresenceWrite="room:presence:write",t.CommentsWrite="comments:write",t.CommentsRead="comments:read",t))(t$||{});Symbol();j().observable,Date.now();var tL=Symbol("notification-settings-plain");function tj(t){let e={[tL]:{value:t,enumerable:!1}};for(let t of["email","slack","teams","webPush"])e[t]={enumerable:!0,get(){let e=this[tL][t];return void 0===e?(S(`In order to use the '${t}' channel, please set up your project first. For more information: https://liveblocks.io/docs/errors/enable-a-notification-channel`),null):e}};return void 0!==e?Object.create(null,e):Object.create(null)}var tU=tz(0),tM=tz(1),tK=tU+tz(-1);function tz(t){let e=32+(t<0?95+t:t);if(e<32||e>126)throw Error(`Invalid n value: ${t}`);return String.fromCharCode(e)}function tq(t,e){if(void 0!==t&&void 0!==e){var i=t,r=e;if(i<r)return tH(i,r);if(i>r)return tH(r,i);throw Error("Cannot compute value between two equal positions")}if(void 0!==t){var s=t;for(let t=0;t<=s.length-1;t++){let e=s.charCodeAt(t);if(!(e>=126))return s.substring(0,t)+String.fromCharCode(e+1)}return s+tM}if(void 0===e)return tM;{var o=e;let t=o.length-1;for(let e=0;e<=t;e++){let i=o.charCodeAt(e);if(!(i<=32))if(e!==t)return o.substring(0,e+1);else if(33===i)return o.substring(0,e)+tK;else return o.substring(0,e)+String.fromCharCode(i-1)}return tM}}function tH(t,e){let i=0,r=t.length,s=e.length;for(;;){let a=i<r?t.charCodeAt(i):32,h=i<s?e.charCodeAt(i):126;if(a===h){i++;continue}if(h-a!=1){var o,n;return o=t,((n=i)<o.length?o.substring(0,n):o+tU.repeat(n-o.length))+String.fromCharCode(h+a>>1)}{let e=i+1,r=t.substring(0,e);return r.length<e&&(r+=tU.repeat(e-r.length)),r+tH(t.substring(e),"")}}}function tB(t){return!function(t){if(""===t)return!1;let e=t.length-1,i=t.charCodeAt(e);if(i<33||i>126)return!1;for(let i=0;i<e;i++){let e=t.charCodeAt(i);if(e<32||e>126)return!1}return!0}(t)?function(t){let e=[];for(let i=0;i<t.length;i++){let r=t.charCodeAt(i);e.push(r<32?32:r>126?126:r)}for(;e.length>0&&32===e[e.length-1];)e.length--;return e.length>0?String.fromCharCode(...e):tM}(t):t}var tF=(t=>(t[t.INIT=0]="INIT",t[t.SET_PARENT_KEY=1]="SET_PARENT_KEY",t[t.CREATE_LIST=2]="CREATE_LIST",t[t.UPDATE_OBJECT=3]="UPDATE_OBJECT",t[t.CREATE_OBJECT=4]="CREATE_OBJECT",t[t.DELETE_CRDT=5]="DELETE_CRDT",t[t.DELETE_OBJECT_KEY=6]="DELETE_OBJECT_KEY",t[t.CREATE_MAP=7]="CREATE_MAP",t[t.CREATE_REGISTER=8]="CREATE_REGISTER",t))(tF||{});function tW(t,e,i=tB(e)){return Object.freeze({type:"HasParent",node:t,key:e,pos:i})}var tJ=Object.freeze({type:"NoParent"}),tY=class{#T;#A;#I=tJ;_getParentKeyOrThrow(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldKey;default:return tc(this.parent,"Unknown state")}}get _parentPos(){switch(this.parent.type){case"HasParent":return this.parent.pos;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldPos;default:return tc(this.parent,"Unknown state")}}get _pool(){return this.#T}get roomId(){return this.#T?this.#T.roomId:null}get _id(){return this.#A}get parent(){return this.#I}get _parentKey(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":return null;case"Orphaned":return this.parent.oldKey;default:return tc(this.parent,"Unknown state")}}_apply(t,e){if(5===t.type&&"HasParent"===this.parent.type)return this.parent.node._detachChild(this);return{modified:!1}}_setParentLink(t,e){switch(this.parent.type){case"HasParent":if(this.parent.node!==t)throw Error("Cannot set parent: node already has a parent");this.#I=tW(t,e);return;case"Orphaned":case"NoParent":this.#I=tW(t,e);return;default:return tc(this.parent,"Unknown state")}}_attach(t,e){if(this.#A||this.#T)throw Error("Cannot attach node: already attached");e.addNode(t,this),this.#A=t,this.#T=e}_detach(){switch(this.#T&&this.#A&&this.#T.deleteNode(this.#A),this.parent.type){case"HasParent":this.#I=function(t,e=tB(t)){return Object.freeze({type:"Orphaned",oldKey:t,oldPos:e})}(this.parent.key,this.parent.pos);break;case"NoParent":this.#I=tJ;break;case"Orphaned":break;default:tc(this.parent,"Unknown state")}this.#T=void 0}#k;#C;#R;invalidate(){(void 0!==this.#k||void 0!==this.#R)&&(this.#k=void 0,this.#R=void 0,"HasParent"===this.parent.type&&this.parent.node.invalidate())}toTreeNode(t){return(void 0===this.#R||this.#C!==t)&&(this.#C=t,this.#R=this._toTreeNode(t)),this.#R}toImmutable(){return void 0===this.#k&&(this.#k=this._toImmutable()),this.#k}},tG=(t=>(t[t.OBJECT=0]="OBJECT",t[t.LIST=1]="LIST",t[t.MAP=2]="MAP",t[t.REGISTER=3]="REGISTER",t))(tG||{}),tV=class t extends tY{#P;constructor(t){super(),this.#P=t}get data(){return this.#P}static _deserialize([e,i],r,s){let o=new t(i.data);return o._attach(e,s),o}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize register if parentId or parentKey is undefined");return[{type:8,opId:i?.generateOpId(),id:this._id,parentId:t,parentKey:e,data:this.data}]}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveRegister if parent is missing");return{type:3,parentId:tp(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key,data:this.data}}_attachChild(t){throw Error("Method not implemented.")}_detachChild(t){throw Error("Method not implemented.")}_apply(t,e){return super._apply(t,e)}_toTreeNode(t){return{type:"Json",id:this._id??te(),key:t,payload:this.#P}}_toImmutable(){return this.#P}clone(){return P(this.data)}};function tX(t,e){let i=t._parentPos,r=e._parentPos;return i===r?0:i<r?-1:1}var tZ=class t extends tY{#D;#x;#N;constructor(t){let e;for(let i of(super(),this.#D=[],this.#x=new WeakSet,this.#N=new Map,t)){let t=tq(e),r=en(i);r._setParentLink(this,t),this.#D.push(r),e=t}}static _deserialize([e],i,r){let s=new t([]);s._attach(e,r);let o=i.get(e);if(void 0===o)return s;for(let[t,e]of o){let o=et([t,e],i,r);o._setParentLink(s,e.parentKey),s._insertAndSort(o)}return s}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=[],s={id:this._id,opId:i?.generateOpId(),type:2,parentId:t,parentKey:e};for(let t of(r.push(s),this.#D)){let e=t._getParentKeyOrThrow(),s=t4(t._toOps(this._id,e,i),void 0),o=s[0].opId;void 0!==o&&this.#N.set(e,o),r.push(...s)}return r}_insertAndSort(t){this.#D.push(t),this._sortItems()}_sortItems(){this.#D.sort(tX),this.invalidate()}_indexOfPosition(t){return this.#D.findIndex(e=>e._getParentKeyOrThrow()===t)}_attach(t,e){for(let i of(super._attach(t,e),this.#D))i._attach(e.generateId(),e)}_detach(){for(let t of(super._detach(),this.#D))t._detach()}#$(t){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:e,parentKey:i}=t,r=t7(t);r._attach(e,this._pool),r._setParentLink(this,i);let s=t.deletedId,o=this._indexOfPosition(i);if(-1!==o){let e=this.#D[o];if(e._id===s)return e._detach(),this.#D[o]=r,{modified:t0(this,[t1(o,r)]),reverse:[]};{this.#x.add(e),this.#D[o]=r;let i=[t1(o,r)],s=this.#L(t.deletedId);return s&&i.push(s),{modified:t0(this,i),reverse:[]}}}{let e=[],s=this.#L(t.deletedId);return s&&e.push(s),this._insertAndSort(r),e.push(t3(this._indexOfPosition(i),r)),{reverse:[],modified:t0(this,e)}}}#j(t){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let e=[],i=this.#L(t.deletedId);i&&e.push(i);let r=this.#N.get(t.parentKey);if(void 0!==r)if(r!==t.opId)return 0===e.length?{modified:!1}:{modified:t0(this,e),reverse:[]};else this.#N.delete(t.parentKey);let s=this._indexOfPosition(t.parentKey),o=this.#D.find(e=>e._id===t.id);if(void 0!==o){if(o._parentKey===t.parentKey)return{modified:e.length>0&&t0(this,e),reverse:[]};if(-1!==s){this.#x.add(this.#D[s]);let[t]=this.#D.splice(s,1);e.push(t2(s,t))}let i=this.#D.indexOf(o);o._setParentLink(this,t.parentKey),this._sortItems();let r=this.#D.indexOf(o);return r!==i&&e.push(t5(i,r,o)),{modified:e.length>0&&t0(this,e),reverse:[]}}{let i=this._pool.getNode(t.id);if(i&&this.#x.has(i)){i._setParentLink(this,t.parentKey),this.#x.delete(i),this._insertAndSort(i);let r=this.#D.indexOf(i);return{modified:t0(this,[-1===s?t3(r,i):t1(r,i),...e]),reverse:[]}}{-1!==s&&this.#D.splice(s,1);let{newItem:i,newIndex:r}=this.#U(t,t.parentKey);return{modified:t0(this,[-1===s?t3(r,i):t1(r,i),...e]),reverse:[]}}}}#L(t){if(void 0===t||void 0===this._pool)return null;let e=this._pool.getNode(t);if(void 0===e)return null;let i=this._detachChild(e);return!1===i.modified?null:i.modified.updates[0]}#M(t){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let e=tB(t.parentKey),i=this._indexOfPosition(e);-1!==i&&this.#K(i,e);let{newItem:r,newIndex:s}=this.#U(t,e);return{modified:t0(this,[t3(s,r)]),reverse:[]}}#z(t){let e=this.#D.find(e=>e._id===t.id),i=tB(t.parentKey),r=this._indexOfPosition(i);if(e)if(e._parentKey===i)return{modified:!1};else{let t=this.#D.indexOf(e);-1!==r&&this.#K(r,i),e._setParentLink(this,i),this._sortItems();let s=this._indexOfPosition(i);return s===t?{modified:!1}:{modified:t0(this,[t5(t,s,e)]),reverse:[]}}{let e=tp(this._pool).getNode(t.id);if(e&&this.#x.has(e))return e._setParentLink(this,i),this.#x.delete(e),this._insertAndSort(e),{modified:t0(this,[t3(this._indexOfPosition(i),e)]),reverse:[]};{-1!==r&&this.#K(r,i);let{newItem:e,newIndex:s}=this.#U(t,i);return{modified:t0(this,[t3(s,e)]),reverse:[]}}}}#q(t){let{id:e,parentKey:i}=t,r=t7(t);if(this._pool?.getNode(e)!==void 0)return{modified:!1};r._attach(e,tp(this._pool)),r._setParentLink(this,i);let s=this._indexOfPosition(i),o=i;return -1!==s&&(o=tq(this.#D[s]?._parentPos,this.#D[s+1]?._parentPos),r._setParentLink(this,o)),this._insertAndSort(r),{modified:t0(this,[t3(this._indexOfPosition(o),r)]),reverse:[{type:5,id:e}]}}#H(t){let{id:e,parentKey:i}=t,r=t7(t);if(this._pool?.getNode(e)!==void 0)return{modified:!1};this.#N.set(i,tp(t.opId));let s=this._indexOfPosition(i);if(r._attach(e,tp(this._pool)),r._setParentLink(this,i),-1===s)return this._insertAndSort(r),this.#L(t.deletedId),{reverse:[{type:5,id:e}],modified:t0(this,[t3(this._indexOfPosition(i),r)])};{let e=this.#D[s];e._detach(),this.#D[s]=r;let o=t4(e._toOps(tp(this._id),i,this._pool),t.id),n=[t1(s,r)],a=this.#L(t.deletedId);return a&&n.push(a),{modified:t0(this,n),reverse:o}}}_attachChild(t,e){let i;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");return!1!==(i="set"===t.intent?1===e?this.#$(t):2===e?this.#j(t):this.#H(t):1===e?this.#M(t):2===e?this.#z(t):this.#q(t)).modified&&this.invalidate(),i}_detachChild(t){if(t){let e=tp(t._parentKey),i=t._toOps(tp(this._id),e,this._pool),r=this.#D.indexOf(t);if(-1===r)return{modified:!1};let[s]=this.#D.splice(r,1);return this.invalidate(),t._detach(),{modified:t0(this,[t2(r,s)]),reverse:i}}return{modified:!1}}#B(t,e){if(this.#x.has(e))return this.#x.delete(e),e._setParentLink(this,t),this._insertAndSort(e),{modified:t0(this,[t3(this.#D.indexOf(e),e)]),reverse:[]};if(t===e._parentKey)return{modified:!1};let i=this._indexOfPosition(t);if(-1===i){let i=this.#D.indexOf(e);e._setParentLink(this,t),this._sortItems();let r=this.#D.indexOf(e);return r===i?{modified:!1}:{modified:t0(this,[t5(i,r,e)]),reverse:[]}}{this.#D[i]._setParentLink(this,tq(t,this.#D[i+1]?._parentPos));let r=this.#D.indexOf(e);e._setParentLink(this,t),this._sortItems();let s=this.#D.indexOf(e);return s===r?{modified:!1}:{modified:t0(this,[t5(r,s,e)]),reverse:[]}}}#F(t,e){let i=tp(e._parentKey);if(this.#x.has(e)){let i=this._indexOfPosition(t);return this.#x.delete(e),-1!==i&&this.#D[i]._setParentLink(this,tq(t,this.#D[i+1]?._parentPos)),e._setParentLink(this,t),this._insertAndSort(e),{modified:!1}}{if(t===i)return{modified:!1};let r=this.#D.indexOf(e),s=this._indexOfPosition(t);-1!==s&&this.#D[s]._setParentLink(this,tq(t,this.#D[s+1]?._parentPos)),e._setParentLink(this,t),this._sortItems();let o=this.#D.indexOf(e);return r===o?{modified:!1}:{modified:t0(this,[t5(r,o,e)]),reverse:[]}}}#W(t,e){let i=tp(e._parentKey),r=this.#D.indexOf(e),s=this._indexOfPosition(t);-1!==s&&this.#D[s]._setParentLink(this,tq(t,this.#D[s+1]?._parentPos)),e._setParentLink(this,t),this._sortItems();let o=this.#D.indexOf(e);return r===o?{modified:!1}:{modified:t0(this,[t5(r,o,e)]),reverse:[{type:1,id:tp(e._id),parentKey:i}]}}_setChildKey(t,e,i){return 1===i?this.#B(t,e):2===i?this.#F(t,e):this.#W(t,e)}_apply(t,e){return super._apply(t,e)}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveList if parent is missing");return{type:1,parentId:tp(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get length(){return this.#D.length}push(t){return this._pool?.assertStorageIsWritable(),this.insert(t,this.length)}insert(t,e){if(this._pool?.assertStorageIsWritable(),e<0||e>this.#D.length)throw Error(`Cannot insert list item at index "${e}". index should be between 0 and ${this.#D.length}`);let i=tq(this.#D[e-1]?this.#D[e-1]._parentPos:void 0,this.#D[e]?this.#D[e]._parentPos:void 0),r=en(t);if(r._setParentLink(this,i),this._insertAndSort(r),this._pool&&this._id){let t=this._pool.generateId();r._attach(t,this._pool),this._pool.dispatch(r._toOps(this._id,i,this._pool),[{type:5,id:t}],new Map([[this._id,t0(this,[t3(e,r)])]]))}}move(t,e){if(this._pool?.assertStorageIsWritable(),e<0)throw Error("targetIndex cannot be less than 0");if(e>=this.#D.length)throw Error("targetIndex cannot be greater or equal than the list length");if(t<0)throw Error("index cannot be less than 0");if(t>=this.#D.length)throw Error("index cannot be greater or equal than the list length");let i=null,r=null;t<e?(r=e===this.#D.length-1?void 0:this.#D[e+1]._parentPos,i=this.#D[e]._parentPos):(r=this.#D[e]._parentPos,i=0===e?void 0:this.#D[e-1]._parentPos);let s=tq(i,r),o=this.#D[t],n=o._getParentKeyOrThrow();if(o._setParentLink(this,s),this._sortItems(),this._pool&&this._id){let i=new Map([[this._id,t0(this,[t5(t,e,o)])]]);this._pool.dispatch([{type:1,id:tp(o._id),opId:this._pool.generateOpId(),parentKey:s}],[{type:1,id:tp(o._id),parentKey:n}],i)}}delete(t){if(this._pool?.assertStorageIsWritable(),t<0||t>=this.#D.length)throw Error(`Cannot delete list item at index "${t}". index should be between 0 and ${this.#D.length-1}`);let e=this.#D[t];e._detach();let[i]=this.#D.splice(t,1);if(this.invalidate(),this._pool){let r=e._id;if(r){let s=new Map;s.set(tp(this._id),t0(this,[t2(t,i)])),this._pool.dispatch([{id:r,opId:this._pool.generateOpId(),type:5}],e._toOps(tp(this._id),e._getParentKeyOrThrow()),s)}}}clear(){if(this._pool?.assertStorageIsWritable(),this._pool){let t=[],e=[],i=[];for(let r of this.#D){r._detach();let s=r._id;s&&(t.push({type:5,id:s,opId:this._pool.generateOpId()}),e.push(...r._toOps(tp(this._id),r._getParentKeyOrThrow())),i.push(t2(0,r)))}this.#D=[],this.invalidate();let r=new Map;r.set(tp(this._id),t0(this,i)),this._pool.dispatch(t,e,r)}else{for(let t of this.#D)t._detach();this.#D=[],this.invalidate()}}set(t,e){if(this._pool?.assertStorageIsWritable(),t<0||t>=this.#D.length)throw Error(`Cannot set list item at index "${t}". index should be between 0 and ${this.#D.length-1}`);let i=this.#D[t],r=i._getParentKeyOrThrow(),s=i._id;i._detach();let o=en(e);if(o._setParentLink(this,r),this.#D[t]=o,this.invalidate(),this._pool&&this._id){let e=this._pool.generateId();o._attach(e,this._pool);let n=new Map;n.set(this._id,t0(this,[t1(t,o)]));let a=t4(o._toOps(this._id,r,this._pool),s);this.#N.set(r,tp(a[0].opId));let h=t4(i._toOps(this._id,r,void 0),e);this._pool.dispatch(a,h,n)}}toArray(){return this.#D.map(t=>eo(t))}every(t){return this.toArray().every(t)}filter(t){return this.toArray().filter(t)}find(t){return this.toArray().find(t)}findIndex(t){return this.toArray().findIndex(t)}forEach(t){return this.toArray().forEach(t)}get(t){if(!(t<0)&&!(t>=this.#D.length))return eo(this.#D[t])}indexOf(t,e){return this.toArray().indexOf(t,e)}lastIndexOf(t,e){return this.toArray().lastIndexOf(t,e)}map(t){return this.#D.map((e,i)=>t(eo(e),i))}some(t){return this.toArray().some(t)}[Symbol.iterator](){return new tQ(this.#D)}#U(t,e){let i=t7(t);return i._attach(t.id,tp(this._pool)),i._setParentLink(this,e),this._insertAndSort(i),{newItem:i,newIndex:this._indexOfPosition(e)}}#K(t,e){let i=tq(e,this.#D.length>t+1?this.#D[t+1]?._parentPos:void 0);this.#D[t]._setParentLink(this,i)}_toTreeNode(t){return{type:"LiveList",id:this._id??te(),key:t,payload:this.#D.map((t,e)=>t.toTreeNode(e.toString()))}}toImmutable(){return super.toImmutable()}_toImmutable(){return this.#D.map(t=>t.toImmutable())}clone(){return new t(this.#D.map(t=>t.clone()))}},tQ=class{#J;constructor(t){this.#J=t[Symbol.iterator]()}[Symbol.iterator](){return this}next(){let t=this.#J.next();return t.done?{done:!0,value:void 0}:{value:eo(t.value)}}};function t0(t,e){return{node:t,type:"LiveList",updates:e}}function t1(t,e){return{index:t,type:"set",item:e instanceof tV?e.data:e}}function t2(t,e){return{type:"delete",index:t,deletedItem:e instanceof tV?e.data:e}}function t3(t,e){return{index:t,type:"insert",item:e instanceof tV?e.data:e}}function t5(t,e,i){return{type:"move",index:e,item:i instanceof tV?i.data:i,previousIndex:t}}function t4(t,e){return t.map((t,i)=>0===i?{...t,intent:"set",deletedId:e}:t)}var t6=class t extends tY{#Y;#G;constructor(t){if(super(),this.#G=new Map,t){let e=[];for(let[i,r]of t){let t=en(r);t._setParentLink(this,i),e.push([i,t])}this.#Y=new Map(e)}else this.#Y=new Map}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=[],s={id:this._id,opId:i?.generateOpId(),type:7,parentId:t,parentKey:e};for(let[t,e]of(r.push(s),this.#Y))r.push(...e._toOps(this._id,t,i));return r}static _deserialize([e,i],r,s){let o=new t;o._attach(e,s);let n=r.get(e);if(void 0===n)return o;for(let[t,e]of n){let i=et([t,e],r,s);i._setParentLink(o,e.parentKey),o.#Y.set(e.parentKey,i),o.invalidate()}return o}_attach(t,e){for(let[i,r]of(super._attach(t,e),this.#Y))ei(r)&&r._attach(e.generateId(),e)}_attachChild(t,e){let i;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:r,parentKey:s,opId:o}=t,n=t7(t);if(void 0!==this._pool.getNode(r))return{modified:!1};if(2===e){let t=this.#G.get(s);if(t===o)return this.#G.delete(s),{modified:!1};if(void 0!==t)return{modified:!1}}else 1===e&&this.#G.delete(s);let a=this.#Y.get(s);if(a){let t=tp(this._id);i=a._toOps(t,s),a._detach()}else i=[{type:5,id:r}];return n._setParentLink(this,s),n._attach(r,this._pool),this.#Y.set(s,n),this.invalidate(),{modified:{node:this,type:"LiveMap",updates:{[s]:{type:"update"}}},reverse:i}}_detach(){for(let t of(super._detach(),this.#Y.values()))t._detach()}_detachChild(t){let e=tp(this._id),i=tp(t._parentKey),r=t._toOps(e,i,this._pool);for(let[e,i]of this.#Y)i===t&&(this.#Y.delete(e),this.invalidate());return t._detach(),{modified:{node:this,type:"LiveMap",updates:{[i]:{type:"delete"}}},reverse:r}}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveMap if parent is missing");return{type:2,parentId:tp(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get(t){let e=this.#Y.get(t);if(void 0!==e)return eo(e)}set(t,e){this._pool?.assertStorageIsWritable();let i=this.#Y.get(t);i&&i._detach();let r=en(e);if(r._setParentLink(this,t),this.#Y.set(t,r),this.invalidate(),this._pool&&this._id){let e=this._pool.generateId();r._attach(e,this._pool);let s=new Map;s.set(this._id,{node:this,type:"LiveMap",updates:{[t]:{type:"update"}}});let o=r._toOps(this._id,t,this._pool);this.#G.set(t,tp(o[0].opId)),this._pool.dispatch(r._toOps(this._id,t,this._pool),i?i._toOps(this._id,t):[{type:5,id:e}],s)}}get size(){return this.#Y.size}has(t){return this.#Y.has(t)}delete(t){this._pool?.assertStorageIsWritable();let e=this.#Y.get(t);if(void 0===e)return!1;if(e._detach(),this.#Y.delete(t),this.invalidate(),this._pool&&e._id){let i=tp(this._id),r=new Map;r.set(i,{node:this,type:"LiveMap",updates:{[t]:{type:"delete"}}}),this._pool.dispatch([{type:5,id:e._id,opId:this._pool.generateOpId()}],e._toOps(i,t),r)}return!0}entries(){let t=this.#Y.entries();return{[Symbol.iterator](){return this},next(){let e=t.next();return e.done?{done:!0,value:void 0}:{value:[e.value[0],eo(e.value[1])]}}}}[Symbol.iterator](){return this.entries()}keys(){return this.#Y.keys()}values(){let t=this.#Y.values();return{[Symbol.iterator](){return this},next(){let e=t.next();return e.done?{done:!0,value:void 0}:{value:eo(e.value)}}}}forEach(t){for(let e of this)t(e[1],e[0],this)}_toTreeNode(t){return{type:"LiveMap",id:this._id??te(),key:t,payload:Array.from(this.#Y.entries()).map(([t,e])=>e.toTreeNode(t))}}toImmutable(){return super.toImmutable()}_toImmutable(){let t=new Map;for(let[e,i]of this.#Y)t.set(e,i.toImmutable());return U(t)}clone(){return new t(Array.from(this.#Y).map(([t,e])=>[t,e.clone()]))}},t9=class t extends tY{#Y;#V;static #X(t){let e=new Map,i=null;for(let[s,o]of t){var r;if(0!==o.type||void 0!==(r=o).parentId&&void 0!==r.parentKey){let t=[s,o],i=e.get(o.parentId);void 0!==i?i.push(t):e.set(o.parentId,[t])}else i=[s,o]}if(null===i)throw Error("Root can't be null");return[i,e]}static _fromItems(e,i){let[r,s]=t.#X(e);return t._deserialize(r,s,i)}constructor(t={}){super(),this.#V=new Map;let e=D(t);for(let t of Object.keys(e)){let i=e[t];ei(i)&&i._setParentLink(this,t)}this.#Y=new Map(Object.entries(e))}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=i?.generateOpId(),s=[],o={type:4,id:this._id,opId:r,parentId:t,parentKey:e,data:{}};for(let[t,e]of(s.push(o),this.#Y))ei(e)?s.push(...e._toOps(this._id,t,i)):o.data[t]=e;return s}static _deserialize([e,i],r,s){let o=new t(i.data);return o._attach(e,s),this._deserializeChildren(o,r,s)}static _deserializeChildren(t,e,i){let r=e.get(tp(t._id));if(void 0===r)return t;for(let[s,o]of r){let r=function([t,e],i,r){switch(e.type){case 0:return t9._deserialize([t,e],i,r);case 1:return tZ._deserialize([t,e],i,r);case 2:return t6._deserialize([t,e],i,r);case 3:return e.data;default:throw Error("Unexpected CRDT type")}}([s,o],e,i);ee(r)&&r._setParentLink(t,o.parentKey),t.#Y.set(o.parentKey,r),t.invalidate()}return t}_attach(t,e){for(let[i,r]of(super._attach(t,e),this.#Y))ei(r)&&r._attach(e.generateId(),e)}_attachChild(t,e){let i;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:r,opId:s,parentKey:o}=t,n=t8(t);if(void 0!==this._pool.getNode(r))return this.#V.get(o)===s&&this.#V.delete(o),{modified:!1};if(0===e)this.#V.set(o,tp(s));else if(void 0===this.#V.get(o));else if(this.#V.get(o)===s)return this.#V.delete(o),{modified:!1};else return{modified:!1};let a=tp(this._id),h=this.#Y.get(o);return ei(h)?(i=h._toOps(a,o),h._detach()):i=void 0===h?[{type:6,id:a,key:o}]:[{type:3,id:a,data:{[o]:h}}],this.#Y.set(o,n),this.invalidate(),ee(n)&&(n._setParentLink(this,o),n._attach(r,this._pool)),{reverse:i,modified:{node:this,type:"LiveObject",updates:{[o]:{type:"update"}}}}}_detachChild(t){if(t){let e=tp(this._id),i=tp(t._parentKey),r=t._toOps(e,i,this._pool);for(let[e,i]of this.#Y)i===t&&(this.#Y.delete(e),this.invalidate());return t._detach(),{modified:{node:this,type:"LiveObject",updates:{[i]:{type:"delete"}}},reverse:r}}return{modified:!1}}_detach(){for(let t of(super._detach(),this.#Y.values()))ei(t)&&t._detach()}_apply(t,e){return 3===t.type?this.#Z(t,e):6===t.type?this.#Q(t,e):super._apply(t,e)}_serialize(){let t={};for(let[e,i]of this.#Y)ei(i)||(t[e]=i);return"HasParent"===this.parent.type&&this.parent.node._id?{type:0,parentId:this.parent.node._id,parentKey:this.parent.key,data:t}:{type:0,data:t}}#Z(t,e){let i=!1,r=tp(this._id),s=[],o={type:3,id:r,data:{}};for(let e in t.data){let t=this.#Y.get(e);ei(t)?(s.push(...t._toOps(r,e)),t._detach()):void 0!==t?o.data[e]=t:void 0===t&&s.push({type:6,id:r,key:e})}let n={};for(let r in t.data){let s=t.data[r];if(void 0===s)continue;if(e)this.#V.set(r,tp(t.opId));else if(void 0===this.#V.get(r))i=!0;else{if(this.#V.get(r)!==t.opId)continue;this.#V.delete(r);continue}let o=this.#Y.get(r);ei(o)&&o._detach(),i=!0,n[r]={type:"update"},this.#Y.set(r,s),this.invalidate()}return 0!==Object.keys(o.data).length&&s.unshift(o),i?{modified:{node:this,type:"LiveObject",updates:n},reverse:s}:{modified:!1}}#Q(t,e){let i=t.key;if(!1===this.#Y.has(i)||!e&&void 0!==this.#V.get(i))return{modified:!1};let r=this.#Y.get(i),s=tp(this._id),o=[];return ei(r)?(o=r._toOps(s,t.key),r._detach()):void 0!==r&&(o=[{type:3,id:s,data:{[i]:r}}]),this.#Y.delete(i),this.invalidate(),{modified:{node:this,type:"LiveObject",updates:{[t.key]:{type:"delete"}}},reverse:o}}toObject(){return Object.fromEntries(this.#Y)}set(t,e){this._pool?.assertStorageIsWritable(),this.update({[t]:e})}get(t){return this.#Y.get(t)}delete(t){let e;this._pool?.assertStorageIsWritable();let i=this.#Y.get(t);if(void 0===i)return;if(void 0===this._pool||void 0===this._id){ei(i)&&i._detach(),this.#Y.delete(t),this.invalidate();return}ei(i)?(i._detach(),e=i._toOps(this._id,t)):e=[{type:3,data:{[t]:i},id:this._id}],this.#Y.delete(t),this.invalidate();let r=new Map;r.set(this._id,{node:this,type:"LiveObject",updates:{[t]:{type:"delete"}}}),this._pool.dispatch([{type:6,key:t,id:this._id,opId:this._pool.generateOpId()}],e,r)}update(t){if(this._pool?.assertStorageIsWritable(),void 0===this._pool||void 0===this._id){for(let e in t){let i=t[e];if(void 0===i)continue;let r=this.#Y.get(e);ei(r)&&r._detach(),ei(i)&&i._setParentLink(this,e),this.#Y.set(e,i),this.invalidate()}return}let e=[],i=[],r=this._pool.generateOpId(),s={},o={id:this._id,type:3,data:{}},n={};for(let a in t){let h=t[a];if(void 0===h)continue;let l=this.#Y.get(a);if(ei(l)?(i.push(...l._toOps(this._id,a)),l._detach()):void 0===l?i.push({type:6,id:this._id,key:a}):o.data[a]=l,ei(h)){h._setParentLink(this,a),h._attach(this._pool.generateId(),this._pool);let t=h._toOps(this._id,a,this._pool),i=t.find(t=>t.parentId===this._id);i&&this.#V.set(a,tp(i.opId)),e.push(...t)}else s[a]=h,this.#V.set(a,r);this.#Y.set(a,h),this.invalidate(),n[a]={type:"update"}}0!==Object.keys(o.data).length&&i.unshift(o),0!==Object.keys(s).length&&e.unshift({opId:r,id:this._id,type:3,data:s});let a=new Map;a.set(this._id,{node:this,type:"LiveObject",updates:n}),this._pool.dispatch(e,i,a)}toImmutable(){return super.toImmutable()}toTreeNode(t){return super.toTreeNode(t)}_toTreeNode(t){let e=this._id??te();return{type:"LiveObject",id:e,key:t,payload:Array.from(this.#Y.entries()).map(([t,i])=>ei(i)?i.toTreeNode(t):{type:"Json",id:`${e}:${t}`,key:t,payload:i})}}_toImmutable(){let t={};for(let[e,i]of this.#Y)t[e]=ee(i)?i.toImmutable():i;return t}clone(){return new t(Object.fromEntries(Array.from(this.#Y).map(([t,e])=>[t,ee(e)?e.clone():P(e)])))}};function t7(t){return en(t8(t))}function t8(t){switch(t.type){case 8:return t.data;case 4:return new t9(t.data);case 7:return new t6;case 2:return new tZ([]);default:return tc(t,"Unknown creation Op")}}function et([t,e],i,r){switch(e.type){case 0:return t9._deserialize([t,e],i,r);case 1:return tZ._deserialize([t,e],i,r);case 2:return t6._deserialize([t,e],i,r);case 3:return tV._deserialize([t,e],i,r);default:throw Error("Unexpected CRDT type")}}function ee(t){return er(t)||t instanceof t6||es(t)}function ei(t){return ee(t)||t instanceof tV}function er(t){return t instanceof tZ}function es(t){return t instanceof t9}function eo(t){return t instanceof tV?t.data:t instanceof tZ||t instanceof t6||t instanceof t9?t:tc(t,"Unknown AbstractCrdt")}function en(t){return t instanceof t9||t instanceof t6||t instanceof tZ?t:new tV(t)}Symbol.iterator;var ea=(t=>(t[t.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",t[t.BROADCAST_EVENT=103]="BROADCAST_EVENT",t[t.FETCH_STORAGE=200]="FETCH_STORAGE",t[t.UPDATE_STORAGE=201]="UPDATE_STORAGE",t[t.FETCH_YDOC=300]="FETCH_YDOC",t[t.UPDATE_YDOC=301]="UPDATE_YDOC",t))(ea||{});function eh(t,e,i,r,s){if("number"!=typeof e||e<i||void 0!==r&&e>r)throw Error(void 0!==r?`${t} should be between ${s??i} and ${r}.`:`${t} should be at least ${s??i}.`);return e}var el={paragraph:function(t){return"type"in t&&"paragraph"===t.type},text:function(t){return!("type"in t)&&"text"in t&&"string"==typeof t.text},link:function(t){return"type"in t&&"link"===t.type},mention:function(t){return"type"in t&&"mention"===t.type}},ed={paragraph:"block",text:"inline",link:"inline",mention:"inline"},eu={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ec=RegExp(Object.keys(eu).map(t=>`\\${t}`).join("|"),"g"),ep=class{#tt;#te;constructor(t,e){this.#tt=t,this.#te=e}toString(){return this.#tt.reduce((t,e,i)=>t+function(t){if(t instanceof ep)return t.toString();if(Array.isArray(t))return(t.length<=0?new ep([""],[]):new ep(["",...Array(t.length-1).fill(""),""],t)).toString();return String(t).replace(ec,t=>eu[t])}(tp(this.#te[i-1]))+e)}};function ef(t,...e){return new ep(t,e)}var em={_:"\\_","*":"\\*","#":"\\#","`":"\\`","~":"\\~","!":"\\!","|":"\\|","(":"\\(",")":"\\)","{":"\\{","}":"\\}","[":"\\[","]":"\\]"},ey=RegExp(Object.keys(em).map(t=>`\\${t}`).join("|"),"g"),e_=class{#tt;#te;constructor(t,e){this.#tt=t,this.#te=e}toString(){return this.#tt.reduce((t,e,i)=>t+function(t){if(t instanceof e_)return t.toString();if(Array.isArray(t))return(t.length<=0?new e_([""],[]):new e_(["",...Array(t.length-1).fill(""),""],t)).toString();return String(t).replace(ey,t=>em[t])}(tp(this.#te[i-1]))+e)}};function ew(t,...e){return new e_(t,e)}function eg(t){let e={};for(let i in t){let r=t[i];void 0!==r&&(e[i]=ev(r))}return e}function ev(t){if(t instanceof t9)return eg(t.toObject());if(t instanceof tZ)return t.toArray().map(ev);if(t instanceof t6){let e={};for(let[i,r]of t.entries())e[i]=ev(r);return e}return t instanceof tV?t.data:Array.isArray(t)?t.map(ev):A(t)?eg(t):t}function eE(t){if(Array.isArray(t))return new tZ(t.map(eE));if(!A(t))return t;{let e={};for(let i in t){let r=t[i];void 0!==r&&(e[i]=eE(r))}return new t9(e)}}Symbol.iterator;var eS=(t=>(t.Lexical="lexical",t.TipTap="tiptap",t.BlockNote="blocknote",t))(eS||{});async function eb(t){let e=[];for await(let i of t)e.push(i);return e}async function eO(t,e,i){let r=new Set;for await(let s of t){r.size>=i&&await Promise.race(r);let t=(async()=>{try{await e(s)}finally{r.delete(t)}})();r.add(t)}r.size>0&&await Promise.all(r)}f(d,u,"esm"),i(64478),i(35392);var eT=class extends TransformStream{constructor(){let t="";super({transform(e,i){if((t+=e).includes("\n")){let e=t.split("\n");for(let t=0;t<e.length-1;t++)e[t].length>0&&i.enqueue(e[t]);t=e[e.length-1]}},flush(e){t.length>0&&e.enqueue(t)}})}},eA=class extends TransformStream{constructor(){super({transform(t,e){let i=JSON.parse(t);e.enqueue(i)}})}},eI=/^[\w-]+$/;async function ek(){return void 0!==globalThis.fetch?globalThis.fetch:(await i.e(7539).then(i.bind(i,77539))).default}function eC(t){return"string"==typeof t}function eR(t,e){if(!(eC(t)&&t.length>0))throw Error(`Invalid value for field '${e}'. Please provide a non-empty string. For more information: https://liveblocks.io/docs/api-reference/liveblocks-node#authorize`)}function eP(t){return t>=200&&t<300?200:t>=500?503:403}var eD=Object.freeze(["room:write","room:read","room:presence:write","comments:write","comments:read"]),ex=Object.freeze(["room:read","room:presence:write","comments:read"]),eN=Object.freeze(["room:write","comments:write"]),e$=/^([*]|[^*]{1,128}[*]?)$/,eL=class{FULL_ACCESS=eN;READ_ACCESS=ex;#ti;#tr;#ts;#to=!1;#tn=new Map;constructor(t,e,i){eR(e,"userId"),this.#ti=t,this.#tr=e,this.#ts=i}#ta(t){if(this.#to)throw Error("You can no longer change these permissions.");let e=this.#tn.get(t);if(e)return e;if(this.#tn.size>=10)throw Error("You cannot add permissions for more than 10 rooms in a single token");return e=new Set,this.#tn.set(t,e),e}allow(t,e){if("string"!=typeof t)throw Error("Room name or pattern must be a string");if(!e$.test(t))throw Error("Invalid room name or pattern");if(0===e.length)throw Error("Permission list cannot be empty");let i=this.#ta(t);for(let t of e){if(!eD.includes(t))throw Error(`Not a valid permission: ${t}`);i.add(t)}return this}hasPermissions(){return this.#tn.size>0}seal(){if(this.#to)throw Error("You cannot reuse Session instances. Please create a new session every time.");this.#to=!0}serializePermissions(){return Object.fromEntries(Array.from(this.#tn.entries()).map(([t,e])=>[t,Array.from(e)]))}async authorize(){this.seal(),this.hasPermissions()||console.warn("Access tokens without any permission will not be supported soon, you should use wildcards when the client requests a token for resources outside a room. See https://liveblocks.io/docs/errors/liveblocks-client/access-tokens-not-enough-permissions");try{let t=await this.#ti(tu`/v2/authorize-user`,{userId:this.#tr,permissions:this.serializePermissions(),userInfo:this.#ts});return{status:eP(t.status),body:await t.text()}}catch(t){return{status:503,body:'Call to /v2/authorize-user failed. See "error" for more information.',error:t}}}};function ej(t){let e=new Date(t.createdAt),i=t.lastConnectionAt?new Date(t.lastConnectionAt):void 0;return{...t,createdAt:e,lastConnectionAt:i}}var eU=class{#th;#tl;constructor(t){let e=t.secret;!function(t,e){if(!(eC(t)&&t.startsWith("sk_")))throw Error(`Invalid value for field '${e}'. Secret keys must start with 'sk_'. Please provide the secret key from your Liveblocks dashboard at https://liveblocks.io/dashboard/apikeys.`);if(!eI.test(t))throw Error(`Invalid chars found in field '${e}'. Please check that you correctly copied the secret key from your Liveblocks dashboard at https://liveblocks.io/dashboard/apikeys.`)}(e,"secret"),this.#th=e,this.#tl=new URL(function(t){return"string"==typeof t&&t.startsWith("http")?t:"https://api.liveblocks.io"}(t.baseUrl))}async #td(t,e,i){let r=td(this.#tl,t),s={Authorization:`Bearer ${this.#th}`,"Content-Type":"application/json"},o=await ek();return await o(r,{method:"POST",headers:s,body:JSON.stringify(e),signal:i?.signal})}async #tu(t,e,i){let r=td(this.#tl,t),s={Authorization:`Bearer ${this.#th}`,"Content-Type":"application/json"},o=await ek();return await o(r,{method:"PUT",headers:s,body:JSON.stringify(e),signal:i?.signal})}async #tc(t,e,i,r){let s=td(this.#tl,t,i),o={Authorization:`Bearer ${this.#th}`,"Content-Type":"application/octet-stream"},n=await ek();return await n(s,{method:"PUT",headers:o,body:e,signal:r?.signal})}async #tp(t,e){let i=td(this.#tl,t),r={Authorization:`Bearer ${this.#th}`},s=await ek();return await s(i,{method:"DELETE",headers:r,signal:e?.signal})}async #tf(t,e,i){let r=td(this.#tl,t,e),s={Authorization:`Bearer ${this.#th}`},o=await ek();return await o(r,{method:"GET",headers:s,signal:i?.signal})}prepareSession(t,...e){let i=e[0];return new eL(this.#td.bind(this),t,i?.userInfo)}async identifyUser(t,...e){let i=e[0],r=tu`/v2/identify-user`,s="string"==typeof t?t:t.userId,o="string"==typeof t?void 0:t.groupIds;eR(s,"userId");try{let t=await this.#td(r,{userId:s,groupIds:o,userInfo:i?.userInfo});return{status:eP(t.status),body:await t.text()}}catch(t){return{status:503,body:`Call to ${td(this.#tl,r)} failed. See "error" for more information.`,error:t}}}async getRooms(t={},e){let i,r=tu`/v2/rooms`;"string"==typeof t.query?i=t.query:"object"==typeof t.query&&(i=tr(t.query));let s={limit:t.limit,startingAfter:t.startingAfter,userId:t.userId,groupIds:t.groupIds?t.groupIds.join(","):void 0,...Object.fromEntries(Object.entries(t.metadata??{}).map(([t,e])=>[`metadata.${t}`,e])),query:i},o=await this.#tf(r,s,e);if(!o.ok)throw await eM.from(o);let n=await o.json(),a=n.data.map(ej);return{...n,data:a}}async *iterRooms(t,e){let i,{signal:r}=e??{},s=eh("pageSize",e?.pageSize??40,20);for(;;){let{nextCursor:e,data:o}=await this.getRooms({...t,startingAfter:i,limit:s},{signal:r});for(let t of o)yield t;if(!e)break;i=e}}async createRoom(t,e,i){let{defaultAccesses:r,groupsAccesses:s,usersAccesses:o,metadata:n}=e,a=await this.#td(i?.idempotent?tu`/v2/rooms?idempotent`:tu`/v2/rooms`,{id:t,defaultAccesses:r,groupsAccesses:s,usersAccesses:o,metadata:n},i);if(!a.ok)throw await eM.from(a);return ej(await a.json())}async getOrCreateRoom(t,e,i){return await this.createRoom(t,e,{...i,idempotent:!0})}async upsertRoom(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/upsert`,e,i);if(!r.ok)throw await eM.from(r);return ej(await r.json())}async getRoom(t,e){let i=await this.#tf(tu`/v2/rooms/${t}`,void 0,e);if(!i.ok)throw await eM.from(i);return ej(await i.json())}async updateRoom(t,e,i){let{defaultAccesses:r,groupsAccesses:s,usersAccesses:o,metadata:n}=e,a=await this.#td(tu`/v2/rooms/${t}`,{defaultAccesses:r,groupsAccesses:s,usersAccesses:o,metadata:n},i);if(!a.ok)throw await eM.from(a);return ej(await a.json())}async deleteRoom(t,e){let i=await this.#tp(tu`/v2/rooms/${t}`,e);if(!i.ok)throw await eM.from(i)}async getActiveUsers(t,e){let i=await this.#tf(tu`/v2/rooms/${t}/active_users`,void 0,e);if(!i.ok)throw await eM.from(i);return await i.json()}async broadcastEvent(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/broadcast_event`,e,i);if(!r.ok)throw await eM.from(r)}async getStorageDocument(t,e="plain-lson",i){let r=await this.#tf(tu`/v2/rooms/${t}/storage`,{format:e},i);if(!r.ok)throw await eM.from(r);return await r.json()}async #tm(t,e){let i=await this.#td(tu`/v2/rooms/${t}/request-storage-mutation`,{},e);if(!i.ok)throw await eM.from(i);if("application/x-ndjson"!==i.headers.get("content-type"))throw Error("Unexpected response content type");if(null===i.body)throw Error("Unexpected null body in response");let r=i.body.pipeThrough(new TextDecoderStream).pipeThrough(new eT).pipeThrough(new eA)[Symbol.asyncIterator](),s=(await r.next()).value;if(!A(s)||"number"!=typeof s.actor)throw Error("Failed to obtain a unique session");let o=await eb(r);return{actor:s.actor,nodes:o}}async initializeStorageDocument(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/storage`,e,i);if(!r.ok)throw await eM.from(r);return await r.json()}async deleteStorageDocument(t,e){let i=await this.#tp(tu`/v2/rooms/${t}/storage`,e);if(!i.ok)throw await eM.from(i)}async getYjsDocument(t,e={},i){let{format:r,key:s,type:o}=e,n=tu`v2/rooms/${t}/ydoc`,a=await this.#tf(n,{formatting:r?"true":void 0,key:s,type:o},i);if(!a.ok)throw await eM.from(a);return await a.json()}async sendYjsBinaryUpdate(t,e,i={},r){let s=await this.#tc(tu`/v2/rooms/${t}/ydoc`,e,{guid:i.guid},r);if(!s.ok)throw await eM.from(s)}async getYjsDocumentAsBinaryUpdate(t,e={},i){let r=await this.#tf(tu`/v2/rooms/${t}/ydoc-binary`,{guid:e.guid},i);if(!r.ok)throw await eM.from(r);return r.arrayBuffer()}async createSchema(t,e,i){let r=await this.#td(tu`/v2/schemas`,{name:t,body:e},i);if(!r.ok)throw await eM.from(r);let s=await r.json(),o=new Date(s.createdAt),n=new Date(s.updatedAt);return{...s,createdAt:o,updatedAt:n}}async getSchema(t,e){let i=await this.#tf(tu`/v2/schemas/${t}`,void 0,e);if(!i.ok)throw await eM.from(i);let r=await i.json(),s=new Date(r.createdAt),o=new Date(r.updatedAt);return{...r,createdAt:s,updatedAt:o}}async updateSchema(t,e,i){let r=await this.#tu(tu`/v2/schemas/${t}`,{body:e},i);if(!r.ok)throw await eM.from(r);let s=await r.json(),o=new Date(s.createdAt),n=new Date(s.updatedAt);return{...s,createdAt:o,updatedAt:n}}async deleteSchema(t,e){let i=await this.#tp(tu`/v2/schemas/${t}`,e);if(!i.ok)throw await eM.from(i)}async getSchemaByRoomId(t,e){let i=await this.#tf(tu`/v2/rooms/${t}/schema`,void 0,e);if(!i.ok)throw await eM.from(i);let r=await i.json(),s=new Date(r.createdAt),o=new Date(r.updatedAt);return{...r,createdAt:s,updatedAt:o}}async attachSchemaToRoom(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/schema`,{schema:e},i);if(!r.ok)throw await eM.from(r);return await r.json()}async detachSchemaFromRoom(t,e){let i=await this.#tp(tu`/v2/rooms/${t}/schema`,e);if(!i.ok)throw await eM.from(i)}async getThreads(t,e){let i,{roomId:r}=t;"string"==typeof t.query?i=t.query:"object"==typeof t.query&&(i=tr(t.query));let s=await this.#tf(tu`/v2/rooms/${r}/threads`,{query:i},e);if(!s.ok)throw await eM.from(s);let{data:o}=await s.json();return{data:o.map(t=>y(t))}}async getThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#tf(tu`/v2/rooms/${i}/threads/${r}`,void 0,e);if(!s.ok)throw await eM.from(s);return y(await s.json())}async getThreadParticipants(t,e){let{roomId:i,threadId:r}=t,s=await this.#tf(tu`/v2/rooms/${i}/threads/${r}/participants`,void 0,e);if(!s.ok)throw await eM.from(s);return await s.json()}async getThreadSubscriptions(t,e){let{roomId:i,threadId:r}=t,s=await this.#tf(tu`/v2/rooms/${i}/threads/${r}/subscriptions`,void 0,e);if(!s.ok)throw await eM.from(s);let{data:o}=await s.json();return{data:o.map(w)}}async getComment(t,e){let{roomId:i,threadId:r,commentId:s}=t,o=await this.#tf(tu`/v2/rooms/${i}/threads/${r}/comments/${s}`,void 0,e);if(!o.ok)throw await eM.from(o);return m(await o.json())}async createComment(t,e){let{roomId:i,threadId:r,data:s}=t,o=await this.#td(tu`/v2/rooms/${i}/threads/${r}/comments`,{...s,createdAt:s.createdAt?.toISOString()},e);if(!o.ok)throw await eM.from(o);return m(await o.json())}async editComment(t,e){let{roomId:i,threadId:r,commentId:s,data:o}=t,n=await this.#td(tu`/v2/rooms/${i}/threads/${r}/comments/${s}`,{...o,editedAt:o.editedAt?.toISOString()},e);if(!n.ok)throw await eM.from(n);return m(await n.json())}async deleteComment(t,e){let{roomId:i,threadId:r,commentId:s}=t,o=await this.#tp(tu`/v2/rooms/${i}/threads/${r}/comments/${s}`,e);if(!o.ok)throw await eM.from(o)}async createThread(t,e){let{roomId:i,data:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads`,{...r,comment:{...r.comment,createdAt:r.comment.createdAt?.toISOString()}},e);if(!s.ok)throw await eM.from(s);return y(await s.json())}async deleteThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#tp(tu`/v2/rooms/${i}/threads/${r}`,e);if(!s.ok)throw await eM.from(s)}async markThreadAsResolved(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/mark-as-resolved`,{userId:t.data.userId},e);if(!s.ok)throw await eM.from(s);return y(await s.json())}async markThreadAsUnresolved(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/mark-as-unresolved`,{userId:t.data.userId},e);if(!s.ok)throw await eM.from(s);return y(await s.json())}async subscribeToThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/subscribe`,{userId:t.data.userId},e);if(!s.ok)throw await eM.from(s);var o=await s.json();let n=new Date(o.createdAt);return{...o,createdAt:n}}async unsubscribeFromThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/unsubscribe`,{userId:t.data.userId},e);if(!s.ok)throw await eM.from(s)}async editThreadMetadata(t,e){let{roomId:i,threadId:r,data:s}=t,o=await this.#td(tu`/v2/rooms/${i}/threads/${r}/metadata`,{...s,updatedAt:s.updatedAt?.toISOString()},e);if(!o.ok)throw await eM.from(o);return await o.json()}async addCommentReaction(t,e){var i;let{roomId:r,threadId:s,commentId:o,data:n}=t,a=await this.#td(tu`/v2/rooms/${r}/threads/${s}/comments/${o}/add-reaction`,{...n,createdAt:n.createdAt?.toISOString()},e);if(!a.ok)throw await eM.from(a);return{...i=await a.json(),createdAt:new Date(i.createdAt)}}async removeCommentReaction(t,e){let{roomId:i,threadId:r,data:s}=t,o=await this.#td(tu`/v2/rooms/${i}/threads/${r}/comments/${t.commentId}/remove-reaction`,{...s,removedAt:s.removedAt?.toISOString()},e);if(!o.ok)throw await eM.from(o)}async getInboxNotification(t,e){let{userId:i,inboxNotificationId:r}=t,s=await this.#tf(tu`/v2/users/${i}/inbox-notifications/${r}`,void 0,e);if(!s.ok)throw await eM.from(s);return _(await s.json())}async getInboxNotifications(t,e){let i,{userId:r}=t;"string"==typeof t.query?i=t.query:"object"==typeof t.query&&(i=tr(t.query));let s=await this.#tf(tu`/v2/users/${r}/inbox-notifications`,{query:i,limit:t?.limit,startingAfter:t?.startingAfter},e);if(!s.ok)throw await eM.from(s);let o=await s.json();return{...o,data:o.data.map(_)}}async *iterInboxNotifications(t,e){let i,{signal:r}=e??{},s=eh("pageSize",e?.pageSize??50,10);for(;;){let{nextCursor:e,data:o}=await this.getInboxNotifications({...t,startingAfter:i,limit:s},{signal:r});for(let t of o)yield t;if(!e)break;i=e}}async getRoomNotificationSettings(t,e){return this.getRoomSubscriptionSettings(t,e)}async getUserRoomSubscriptionSettings(t,e){let{userId:i,startingAfter:r,limit:s}=t,o=await this.#tf(tu`/v2/users/${i}/room-subscription-settings`,{startingAfter:r,limit:s},e);if(!o.ok)throw await eM.from(o);return await o.json()}async getRoomSubscriptionSettings(t,e){let{userId:i,roomId:r}=t,s=await this.#tf(tu`/v2/rooms/${r}/users/${i}/subscription-settings`,void 0,e);if(!s.ok)throw await eM.from(s);return await s.json()}async updateRoomNotificationSettings(t,e){return this.updateRoomSubscriptionSettings(t,e)}async updateRoomSubscriptionSettings(t,e){let{userId:i,roomId:r,data:s}=t,o=await this.#td(tu`/v2/rooms/${r}/users/${i}/subscription-settings`,s,e);if(!o.ok)throw await eM.from(o);return await o.json()}async deleteRoomNotificationSettings(t,e){return this.deleteRoomSubscriptionSettings(t,e)}async deleteRoomSubscriptionSettings(t,e){let{userId:i,roomId:r}=t,s=await this.#tp(tu`/v2/rooms/${r}/users/${i}/subscription-settings`,e);if(!s.ok)throw await eM.from(s)}async updateRoomId(t,e){let{currentRoomId:i,newRoomId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/update-room-id`,{newRoomId:r},e);if(!s.ok)throw await eM.from(s);return ej(await s.json())}async triggerInboxNotification(t,e){let i=await this.#td(tu`/v2/inbox-notifications/trigger`,t,e);if(!i.ok)throw await eM.from(i)}async deleteInboxNotification(t,e){let{userId:i,inboxNotificationId:r}=t,s=await this.#tp(tu`/v2/users/${i}/inbox-notifications/${r}`,e);if(!s.ok)throw await eM.from(s)}async deleteAllInboxNotifications(t,e){let{userId:i}=t,r=await this.#tp(tu`/v2/users/${i}/inbox-notifications`,e);if(!r.ok)throw await eM.from(r)}async getNotificationSettings(t,e){let{userId:i}=t,r=await this.#tf(tu`/v2/users/${i}/notification-settings`,void 0,e);if(!r.ok)throw await eM.from(r);return tj(await r.json())}async updateNotificationSettings(t,e){let{userId:i,data:r}=t,s=await this.#td(tu`/v2/users/${i}/notification-settings`,r,e);if(!s.ok)throw await eM.from(s);return tj(await s.json())}async deleteNotificationSettings(t,e){let{userId:i}=t,r=await this.#tp(tu`/v2/users/${i}/notification-settings`,e);if(!r.ok)throw await eM.from(r)}async mutateStorage(t,e,i){return this.#ty(t,void 0,e,i)}async massMutateStorage(t,e,i){let r=eh("concurrency",i?.concurrency??8,1,20),s=Math.max(20,4*r),{signal:o}=i??{},n=this.iterRooms(t,{pageSize:s,signal:o}),a={signal:o};await eO(n,t=>this.#ty(t.id,t,e,a),r)}async #ty(t,e,i,r){let s,{signal:o,abort:n}=function(t){let e=new AbortController;return{signal:t?AbortSignal.any([e.signal,t]):e.signal,abort:e.abort.bind(e)}}(r?.signal),a=[],h=performance.now(),l=e=>{if(0===a.length||s)return;let i=performance.now();if(!(e||i-h>200))return;h=i;let r=a;a=[],s=this.#t_(t,[{type:ea.UPDATE_STORAGE,ops:r}],{signal:o}).catch(t=>{n(t)}).finally(()=>{s=void 0})};try{let{actor:r,nodes:s}=await this.#tm(t,{signal:o}),n=function(t,e){let{getCurrentConnectionId:i,onDispatch:r,isStorageWritable:s=()=>!0}=e,o=0,n=0,a=new Map;return{roomId:t,nodes:a,getNode:t=>a.get(t),addNode:(t,e)=>void a.set(t,e),deleteNode:t=>void a.delete(t),generateId:()=>`${i()}:${o++}`,generateOpId:()=>`${i()}:${n++}`,dispatch(t,e,i){r?.(t,e,i)},assertStorageIsWritable:()=>{if(!s())throw Error("Cannot write to storage with a read only user, please ensure the user has write permissions")}}}(t,{getCurrentConnectionId:()=>r,onDispatch:(t,e,i)=>{if(0!==t.length){for(let e of t)a.push(e);l(!1)}}}),h=t9._fromItems(s,n),d=i({room:e,root:h});l(!0),await d}catch(t){throw n(),t}finally{await s,l(!0),await s}}async #t_(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/send-message`,{messages:e},{signal:i?.signal});if(!r.ok)throw await eM.from(r)}},eM=class t extends Error{status;details;constructor(t,e,i){super(t),this.name="LiveblocksError",this.status=e,this.details=i}toString(){let t=`${this.name}: ${this.message} (status ${this.status})`;return this.details&&(t+=`
${this.details}`),t}static async from(e){let i,r=Error();Error.captureStackTrace(r,t.from);let s="An error happened without an error message";try{i=await e.text()}catch{i=s}let o=R(i)??{message:i},n=o.message||s,a=[o.suggestion?`Suggestion: ${String(o.suggestion)}`:void 0,o.docs?`See also: ${String(o.docs)}`:void 0].filter(Boolean).join("\n")||void 0,h=new t(n,e.status,a);return h.stack=r.stack,h}};f("@liveblocks/node","2.24.2","esm");let eK=(0,i(90647).H)().LIVEBLOCKS_SECRET,ez=async({userId:t,orgId:e,userInfo:i})=>{if(!eK)throw Error("LIVEBLOCKS_SECRET is not set");let r=new eU({secret:eK}).prepareSession(t,{userInfo:i});r.allow(`${e}:*`,r.FULL_ACCESS);let{status:s,body:o}=await r.authorize();return new Response(o,{status:s})},eq=["var(--color-red-500)","var(--color-orange-500)","var(--color-amber-500)","var(--color-yellow-500)","var(--color-lime-500)","var(--color-green-500)","var(--color-emerald-500)","var(--color-teal-500)","var(--color-cyan-500)","var(--color-sky-500)","var(--color-blue-500)","var(--color-indigo-500)","var(--color-violet-500)","var(--color-purple-500)","var(--color-fuchsia-500)","var(--color-pink-500)","var(--color-rose-500)"],eH=async()=>{let t=await (0,a.N)(),{orgId:e}=await (0,h.j)();return t&&e?ez({userId:t.id,orgId:e,userInfo:{name:t.fullName??t.emailAddresses.at(0)?.emailAddress??void 0,avatar:t.imageUrl??void 0,color:eq[Math.floor(Math.random()*eq.length)]}}):new Response("Unauthorized",{status:401})},eB=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/collaboration/auth/route",pathname:"/api/collaboration/auth",filename:"route",bundlePath:"app/api/collaboration/auth/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\collaboration\\auth\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:eF,workUnitAsyncStorage:eW,serverHooks:eJ}=eB;function eY(){return(0,n.patchFetch)({workAsyncStorage:eF,workUnitAsyncStorage:eW})}},74075:t=>{"use strict";t.exports=require("zlib")},76760:t=>{"use strict";t.exports=require("node:path")},77598:t=>{"use strict";t.exports=require("node:crypto")},79551:t=>{"use strict";t.exports=require("url")},81630:t=>{"use strict";t.exports=require("http")},89259:()=>{},90647:(t,e,i)=>{"use strict";i.d(e,{H:()=>o});var r=i(71166),s=i(25);let o=()=>(0,r.w)({server:{LIVEBLOCKS_SECRET:s.z.string().startsWith("sk_").optional()},runtimeEnv:{LIVEBLOCKS_SECRET:process.env.LIVEBLOCKS_SECRET}})}};var e=require("../../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),r=e.X(0,[5319,6239,2923,25,7873,3887,1359,1826],()=>i(73545));module.exports=r})();