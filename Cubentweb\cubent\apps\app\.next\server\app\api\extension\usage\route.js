(()=>{var e={};e.id=2205,e.ids=[2205],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12645:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>g});var a=r(26142),n=r(94327),i=r(34862),o=r(37838),u=r(18815),d=r(26239),c=r(25);let l=c.z.object({tokensUsed:c.z.number().min(0),requestsMade:c.z.number().min(0),costAccrued:c.z.number().min(0),date:c.z.string().datetime().optional()});async function g(e){try{let t=e.headers.get("authorization"),s=null;if(t?.startsWith("Bearer ")){let e=t.substring(7);try{let{clerkClient:t}=await r.e(4601).then(r.bind(r,14601)),a=await t();s=(await a.sessions.getSession(e)).userId,console.log("Extension usage tracking - Direct Clerk JWT validation successful:",{userId:s})}catch(t){if(console.log("Extension usage tracking - Direct Clerk JWT validation failed, trying PendingLogin table:",t),!await u.database.pendingLogin.findFirst({where:{token:e,expiresAt:{gt:new Date}}}))return console.error("Extension usage tracking - Token not found in PendingLogin table and direct validation failed"),d.NextResponse.json({error:"Invalid or expired token"},{status:401});try{let{clerkClient:t}=await r.e(4601).then(r.bind(r,14601)),a=await t();s=(await a.sessions.getSession(e)).userId,console.log("Extension usage tracking - PendingLogin validation successful:",{userId:s})}catch(e){return console.error("Extension usage tracking - Invalid session token from PendingLogin:",e),d.NextResponse.json({error:"Invalid token"},{status:401})}}}else s=(await (0,o.j)()).userId;if(!s)return d.NextResponse.json({error:"Unauthorized"},{status:401});let a=await e.json();if(a.modelId&&void 0!==a.cubentUnits){let{modelId:e,provider:t,configName:r,cubentUnits:n,messageCount:i,timestamp:o}=a,c=await u.database.user.findUnique({where:{clerkId:s}});if(!c)return d.NextResponse.json({error:"User not found"},{status:404});await u.database.user.update({where:{id:c.id},data:{cubentUnitsUsed:{increment:n},lastActiveAt:new Date}}),await u.database.usageAnalytics.create({data:{userId:c.id,modelId:e,cubentUnitsUsed:n,requestsMade:i||1,metadata:{provider:t,configName:r,timestamp:o}}});let l=new Date;l.setHours(0,0,0,0);let g=await u.database.usageMetrics.findFirst({where:{userId:c.id,date:{gte:l,lt:new Date(l.getTime()+864e5)}}});return g?await u.database.usageMetrics.update({where:{id:g.id},data:{cubentUnitsUsed:{increment:n},requestsMade:{increment:i||1}}}):await u.database.usageMetrics.create({data:{userId:c.id,cubentUnitsUsed:n,requestsMade:i||1,date:l}}),d.NextResponse.json({success:!0,message:"Cubent Units usage tracked successfully",cubentUnitsUsed:n,totalCubentUnits:c.cubentUnitsUsed+n})}let{tokensUsed:n,requestsMade:i,costAccrued:c,date:g}=l.parse(a),p=await u.database.user.findUnique({where:{clerkId:s}});if(!p)return d.NextResponse.json({error:"User not found"},{status:404});let h=g?new Date(g):new Date,x=await u.database.usageMetrics.findFirst({where:{userId:p.id,date:{gte:new Date(h.getFullYear(),h.getMonth(),h.getDate()),lt:new Date(h.getFullYear(),h.getMonth(),h.getDate()+1)}}});return x?await u.database.usageMetrics.update({where:{id:x.id},data:{tokensUsed:x.tokensUsed+n,requestsMade:x.requestsMade+i,costAccrued:x.costAccrued+c}}):await u.database.usageMetrics.create({data:{userId:p.id,tokensUsed:n,requestsMade:i,costAccrued:c,date:h}}),d.NextResponse.json({success:!0,message:"Usage metrics updated successfully"})}catch(e){return console.error("Usage update error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let{userId:t}=await (0,o.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("days")||"30"),a=new Date;a.setDate(a.getDate()-s);let n=await u.database.user.findUnique({where:{clerkId:t}});if(!n)return d.NextResponse.json({error:"User not found"},{status:404});let i=await u.database.usageMetrics.findMany({where:{userId:n.id,date:{gte:a}},orderBy:{date:"desc"}}),c=i.reduce((e,t)=>({tokensUsed:e.tokensUsed+t.tokensUsed,requestsMade:e.requestsMade+t.requestsMade,costAccrued:e.costAccrued+t.costAccrued}),{tokensUsed:0,requestsMade:0,costAccrued:0});return d.NextResponse.json({totalUsage:c,dailyUsage:i,period:`${s} days`})}catch(e){return console.error("Usage fetch error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/usage/route",pathname:"/api/extension/usage",filename:"route",bundlePath:"app/api/extension/usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\usage\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:f}=h;function b(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>I});var s=r(8741),a=r(62923),n=r(54726),i=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},d=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),c=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var a,n;for(let s of(console.log((a=e,`[clerk debug start: ${a}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),a=r.encode(e).slice(0,4096);return s.decode(a).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((n=e,`[clerk debug end: ${n}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},l=(e,t)=>(...r)=>{let s=("string"==typeof e?c(e,d):e)(),a=t(s);try{let e=a(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var g=r(74365),p=r(37081),h=r(27322),x=r(6264);function w(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return h.r0.stringify(r,{pad:!1})}async function f(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,a=(0,h.hJ)(r.algorithm);if(!a)return{errors:[new x.xy(`Unsupported algorithm ${r.algorithm}`)]};let n=await (0,h.Fh)(t,a,"sign"),i=r.header||{typ:"JWT"};i.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=w(i),u=w(e),d=`${o}.${u}`;try{let e=await h.fA.crypto.subtle.sign(a,n,s.encode(d));return{data:`${d}.${h.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new x.xy(e?.message)]}}}(0,p.C)(h.J0);var b=(0,p.R)(h.iU);(0,p.C)(f),(0,p.C)(h.nk);var m=r(97495),y=r(60606);function U(e,{treatPendingAsSignedOut:t=!0,...r}={}){var a,i,o;let u,d=(0,m.NE)(e,"AuthStatus"),c=(0,m.NE)(e,"AuthToken"),l=(0,m.NE)(e,"AuthMessage"),g=(0,m.NE)(e,"AuthReason"),p=(0,m.NE)(e,"AuthSignature");null==(a=r.logger)||a.debug("headers",{authStatus:d,authMessage:l,authReason:g});let h=(0,m._b)(e,s.AA.Headers.ClerkRequestData),x=(0,y.Kk)(h),w={secretKey:(null==r?void 0:r.secretKey)||x.secretKey||n.rB,publishableKey:x.publishableKey||n.At,apiUrl:n.H$,apiVersion:n.mG,authStatus:d,authMessage:l,authReason:g,treatPendingAsSignedOut:t};if(null==(i=r.logger)||i.debug("auth options",w),d&&d===s.TD.SignedIn){(0,y._l)(c,w.secretKey,p);let e=b(c);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(w,e.raw.text,e.payload)}else u=(0,s.wI)(w);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(w,u.sessionStatus)),u}var A=r(68478);let v=({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>async(a,n)=>{if((0,i.zz)((0,m._b)(a,s.AA.Headers.EnableDebug))&&e.enable(),!(0,m.Zd)(a)){g.M&&(0,y.$K)(a,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,y.$K)(a,t)}return U(a,{...n,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>(r,a)=>((0,i.zz)((0,m._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,y.$K)(r,t),U(r,{...a,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,A.AG)()});let k={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},q=e=>{var t,r;return!!e.headers.get(k.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(k.Headers.NextAction))},j=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||N(e)||S(e)},N=e=>!!e.headers.get(k.Headers.NextUrl)&&!q(e)||D(),D=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},S=e=>!!e.headers.get(k.Headers.NextjsData);var M=r(23056);let I=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,M.TG)(),i=async()=>{if(g.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await v({debugLoggerName:"auth()",noAuthStatusMessage:(0,A.sd)("auth",await i())})(t,{treatPendingAsSignedOut:e}),u=(0,m.NE)(t,"ClerkUrl"),d=(...e)=>{let{returnBackUrl:r}=e[0]||{},i=(0,s.tl)(t),d=i.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||i.cookies.get(s.AA.Cookies.DevBrowser),c=(0,m._b)(t,s.AA.Headers.ClerkRequestData),l=(0,y.Kk)(c);return[(0,s.vH)({redirectAdapter:a.redirect,devBrowserToken:d,baseUrl:i.clerkUrl.toString(),publishableKey:l.publishableKey||n.At,signInUrl:l.signInUrl||n.qW,signUpUrl:l.signUpUrl||n.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=d(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=d(e);return t.redirectToSignUp({returnBackUrl:r})}})};I.protect=async(...e)=>{r(1447);let t=await (0,M.TG)(),s=await I();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:a,request:n}=e;return async(...e)=>{var i,o,u,d,c,l;let g=(null==(i=e[0])?void 0:i.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],p=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(d=e[1])?void 0:d.unauthenticatedUrl),h=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(l=e[1])?void 0:l.unauthorizedUrl),x=()=>h?s(h):a();return"pending"!==r.sessionStatus&&r.userId?g?"function"==typeof g?g(r.has)?r:x():r.has(g)?r:x():r:p?s(p):j(n)?t():a()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:a.notFound,redirect:a.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(12645));module.exports=s})();