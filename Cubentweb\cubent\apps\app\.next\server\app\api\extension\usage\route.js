(()=>{var e={};e.id=2205,e.ids=[2205],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12645:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>U,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>q});var r={};s.r(r),s.d(r,{GET:()=>g,POST:()=>l});var a=s(26142),n=s(94327),i=s(34862),u=s(37838),o=s(18815),d=s(26239),c=s(25);let p=c.z.object({tokensUsed:c.z.number().min(0),requestsMade:c.z.number().min(0),costAccrued:c.z.number().min(0),date:c.z.string().datetime().optional()});async function l(e){try{let t=e.headers.get("authorization"),r=null;if(t?.startsWith("Bearer ")){let e=t.substring(7);try{let{clerkClient:t}=await s.e(4601).then(s.bind(s,14601)),a=await t();r=(await a.sessions.getSession(e)).userId,console.log("Extension usage tracking - Direct Clerk JWT validation successful:",{userId:r})}catch(s){console.log("Extension usage tracking - Direct Clerk JWT validation failed, trying PendingLogin table:",s);let t=await o.database.pendingLogin.findFirst({where:{token:e,expiresAt:{gt:new Date}}});if(!t)return console.error("Extension usage tracking - Token not found in PendingLogin table and direct validation failed"),d.NextResponse.json({error:"Invalid or expired token"},{status:401});r=t.userId,console.log("Extension usage tracking - PendingLogin validation successful:",{userId:r})}}else r=(await (0,u.j)()).userId;if(!r)return d.NextResponse.json({error:"Unauthorized"},{status:401});let a=await e.json();if(a.modelId&&void 0!==a.cubentUnits){let{modelId:e,provider:t,configName:s,cubentUnits:n,messageCount:i,timestamp:u}=a,c=await o.database.user.findUnique({where:{clerkId:r}});if(!c)return d.NextResponse.json({error:"User not found"},{status:404});await o.database.user.update({where:{id:c.id},data:{cubentUnitsUsed:{increment:n},lastActiveAt:new Date}}),await o.database.usageAnalytics.create({data:{userId:c.id,modelId:e,cubentUnitsUsed:n,requestsMade:i||1,metadata:{provider:t,configName:s,timestamp:u}}});let p=new Date;p.setHours(0,0,0,0);let l=await o.database.usageMetrics.findFirst({where:{userId:c.id,date:{gte:p,lt:new Date(p.getTime()+864e5)}}});return l?await o.database.usageMetrics.update({where:{id:l.id},data:{cubentUnitsUsed:{increment:n},requestsMade:{increment:i||1}}}):await o.database.usageMetrics.create({data:{userId:c.id,cubentUnitsUsed:n,requestsMade:i||1,date:p}}),d.NextResponse.json({success:!0,message:"Cubent Units usage tracked successfully",cubentUnitsUsed:n,totalCubentUnits:c.cubentUnitsUsed+n})}let{tokensUsed:n,requestsMade:i,costAccrued:c,date:l}=p.parse(a),g=await o.database.user.findUnique({where:{clerkId:r}});if(!g)return d.NextResponse.json({error:"User not found"},{status:404});let x=l?new Date(l):new Date,w=await o.database.usageMetrics.findFirst({where:{userId:g.id,date:{gte:new Date(x.getFullYear(),x.getMonth(),x.getDate()),lt:new Date(x.getFullYear(),x.getMonth(),x.getDate()+1)}}});return w?await o.database.usageMetrics.update({where:{id:w.id},data:{tokensUsed:w.tokensUsed+n,requestsMade:w.requestsMade+i,costAccrued:w.costAccrued+c}}):await o.database.usageMetrics.create({data:{userId:g.id,tokensUsed:n,requestsMade:i,costAccrued:c,date:x}}),d.NextResponse.json({success:!0,message:"Usage metrics updated successfully"})}catch(e){return console.error("Usage update error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let{userId:t}=await (0,u.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),r=parseInt(s.get("days")||"30"),a=new Date;a.setDate(a.getDate()-r);let n=await o.database.user.findUnique({where:{clerkId:t}});if(!n)return d.NextResponse.json({error:"User not found"},{status:404});let i=await o.database.usageMetrics.findMany({where:{userId:n.id,date:{gte:a}},orderBy:{date:"desc"}}),c=i.reduce((e,t)=>({tokensUsed:e.tokensUsed+t.tokensUsed,requestsMade:e.requestsMade+t.requestsMade,costAccrued:e.costAccrued+t.costAccrued}),{tokensUsed:0,requestsMade:0,costAccrued:0});return d.NextResponse.json({totalUsage:c,dailyUsage:i,period:`${r} days`})}catch(e){return console.error("Usage fetch error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/usage/route",pathname:"/api/extension/usage",filename:"route",bundlePath:"app/api/extension/usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\usage\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:w,workUnitAsyncStorage:q,serverHooks:U}=x;function f(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:q})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(12645));module.exports=r})();