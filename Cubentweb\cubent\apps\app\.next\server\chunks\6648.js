exports.id=6648,exports.ids=[6648],exports.modules={4085:()=>{},6609:(e,t,n)=>{Promise.resolve().then(n.bind(n,79202)),Promise.resolve().then(n.bind(n,34676)),Promise.resolve().then(n.bind(n,12350)),Promise.resolve().then(n.bind(n,73012))},12350:(e,t,n)=>{"use strict";n.d(t,{Bx:()=>E,Yv:()=>k,CG:()=>R,Cn:()=>O,rQ:()=>A,jj:()=>F,Gh:()=>y,sF:()=>N,wZ:()=>I,Uj:()=>_,FX:()=>U,SidebarProvider:()=>S,SidebarTrigger:()=>j,cL:()=>w});var r=n(99730),a=n(57752),s=n(58576),o=n(72795),i=n(89513),d=n(83590),l=n(74938);n(99752),n(85733);var c=n(71803),u=n(88529);function p({...e}){return(0,r.jsx)(c.bL,{"data-slot":"sheet",...e})}function b({...e}){return(0,r.jsx)(c.ZL,{"data-slot":"sheet-portal",...e})}function m({className:e,...t}){return(0,r.jsx)(c.hJ,{"data-slot":"sheet-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function f({className:e,children:t,side:n="right",...a}){return(0,r.jsxs)(b,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(c.UC,{"data-slot":"sheet-content",className:(0,d.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...a,children:[t,(0,r.jsxs)(c.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(u.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,d.cn)("flex flex-col gap-1.5 p-4",e),...t})}function g({className:e,...t}){return(0,r.jsx)(c.hE,{"data-slot":"sheet-title",className:(0,d.cn)("text-foreground font-semibold",e),...t})}function v({className:e,...t}){return(0,r.jsx)(c.VY,{"data-slot":"sheet-description",className:(0,d.cn)("text-muted-foreground text-sm",e),...t})}var x=n(41033);let C=a.createContext(null);function w(){let e=a.useContext(C);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function S({defaultOpen:e=!0,open:t,onOpenChange:n,className:s,style:o,children:i,...l}){let c=function(){let[e,t]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),n=()=>{t(window.innerWidth<768)};return e.addEventListener("change",n),t(window.innerWidth<768),()=>e.removeEventListener("change",n)},[]),!!e}(),[u,p]=a.useState(!1),[b,m]=a.useState(e),f=t??b,h=a.useCallback(e=>{let t="function"==typeof e?e(f):e;n?n(t):m(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[n,f]),g=a.useCallback(()=>c?p(e=>!e):h(e=>!e),[c,h,p]);a.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let v=f?"expanded":"collapsed",w=a.useMemo(()=>({state:v,open:f,setOpen:h,isMobile:c,openMobile:u,setOpenMobile:p,toggleSidebar:g}),[v,f,h,c,u,p,g]);return(0,r.jsx)(C.Provider,{value:w,children:(0,r.jsx)(x.TooltipProvider,{delayDuration:0,children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,d.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...l,children:i})})})}function E({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:a,children:s,...o}){let{isMobile:i,state:l,openMobile:c,setOpenMobile:u}=w();return"none"===n?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,d.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",a),...o,children:s}):i?(0,r.jsx)(p,{open:c,onOpenChange:u,...o,children:(0,r.jsxs)(f,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,r.jsxs)(h,{className:"sr-only",children:[(0,r.jsx)(g,{children:"Sidebar"}),(0,r.jsx)(v,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:s})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,d.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",a),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function j({className:e,onClick:t,...n}){let{toggleSidebar:a}=w();return(0,r.jsxs)(l.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,d.cn)("size-7",e),onClick:e=>{t?.(e),a()},...n,children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function N({className:e,...t}){return(0,r.jsx)("main",{"data-slot":"sidebar-inset",className:(0,d.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function y({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t})}function R({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t})}function k({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function O({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t})}function F({className:e,asChild:t=!1,...n}){let a=t?s.DX:"div";return(0,r.jsx)(a,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,d.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function A({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,d.cn)("w-full text-sm",e),...t})}function I({className:e,...t}){return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",e),...t})}function U({className:e,...t}){return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",e),...t})}let D=(0,o.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function _({asChild:e=!1,isActive:t=!1,variant:n="default",size:a="default",tooltip:o,className:i,...l}){let c=e?s.DX:"button",{isMobile:u,state:p}=w(),b=(0,r.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":a,"data-active":t,className:(0,d.cn)(D({variant:n,size:a}),i),...l});return o?("string"==typeof o&&(o={children:o}),(0,r.jsxs)(x.m_,{children:[(0,r.jsx)(x.k$,{asChild:!0,children:b}),(0,r.jsx)(x.ZI,{side:"right",align:"center",hidden:"collapsed"!==p||u,...o})]})):b}},16337:(e,t,n)=>{Promise.resolve().then(n.bind(n,28076)),Promise.resolve().then(n.bind(n,42394)),Promise.resolve().then(n.bind(n,68741)),Promise.resolve().then(n.bind(n,93122))},16703:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>f});var r=n(94752),a=n(2382),s=n(1359),o=n(37838),i=n(68741),d=n(19257),l=n(93122),c=n(92782);let u=(0,n(18686).H)().ARCJET_KEY,p=async(e,t)=>{if(!u)return;let n=(0,c.Ay)({key:u,characteristics:["ip.src"],rules:[(0,c.a)({mode:"LIVE"})]}),r=t??await (0,c.Em)(),a=n.withRule((0,c.fs)({mode:"LIVE",allow:e})),s=await a.protect(r);if(s.isDenied()){if(console.warn(`Arcjet decision: ${JSON.stringify(s.reason,null,2)}`),s.reason.isBot())throw Error("No bots allowed");if(s.reason.isRateLimit())throw Error("Rate limit exceeded");throw Error("Access denied")}};var b=n(28076),m=n(42394);let f=async({children:e})=>{a._.ARCJET_KEY&&await p(["CATEGORY:PREVIEW"]);let t=await (0,s.N)(),{redirectToSignIn:n}=await (0,o.j)(),c=await (0,d.showBetaFeature)();return t?(0,r.jsx)(l.NotificationsProvider,{userId:t.id,children:(0,r.jsxs)(i.SidebarProvider,{children:[(0,r.jsxs)(m.GlobalSidebar,{children:[c&&(0,r.jsx)("div",{className:"m-4 rounded-full bg-blue-500 p-1.5 text-center text-sm text-white",children:"Beta feature now available"}),e]}),(0,r.jsx)(b.PostHogIdentifier,{})]})}):n()}},19257:(e,t,n)=>{"use strict";n.r(t),n.d(t,{showBetaFeature:()=>d});var r=n(51153);let a=(0,n(28364).H)(),s=a.NEXT_PUBLIC_POSTHOG_KEY&&a.NEXT_PUBLIC_POSTHOG_HOST?new r.f2(a.NEXT_PUBLIC_POSTHOG_KEY,{host:a.NEXT_PUBLIC_POSTHOG_HOST,flushAt:1,flushInterval:0}):null;var o=n(37838),i=n(8392);let d=(e=>(0,i.Jt)({key:e,defaultValue:!1,async decide(){let{userId:t}=await (0,o.j)();return t?(s?await s.isFeatureEnabled(e,t):null)??this.defaultValue:this.defaultValue}}))("showBetaFeature")},28076:(e,t,n)=>{"use strict";n.d(t,{PostHogIdentifier:()=>r});let r=(0,n(6340).registerClientReference)(function(){throw Error("Attempted to call PostHogIdentifier() from the server but PostHogIdentifier is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\components\\posthog-identifier.tsx","PostHogIdentifier")},34676:(e,t,n)=>{"use strict";n.d(t,{GlobalSidebar:()=>I});var r=n(99730),a=n(2233),s=n(53330),o=n(74938),i=n(12350),d=n(83590),l=n(22693),c=n(57752),u=n(51134);n(29745),n(4085);let p=()=>{let[e,t]=(0,c.useState)(!1),n=(0,c.useRef)(null);return(0,u.H)().NEXT_PUBLIC_KNOCK_API_KEY?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.PS,{onClick:()=>t(!e),ref:n}),n.current&&(0,r.jsx)(l.Lq,{buttonRef:n,isVisible:e,onClose:e=>{e.target!==n.current&&t(!1)}})]}):null};var b=n(41434),m=n(39378),f=n(23294),h=n(83088),g=n(25072),v=n(16103),x=n(44487),C=n(70692),w=n(96446),S=n(43653),E=n(1033),j=n(21568),N=n(41265),y=n.n(N),R=n(99752),k=n(72298),O=n(48911);let F=()=>(0,r.jsx)("form",{action:"/search",className:"flex items-center gap-2 px-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute top-px bottom-px left-px flex h-8 w-8 items-center justify-center",children:(0,r.jsx)(k.A,{size:16,className:"text-muted-foreground"})}),(0,r.jsx)(R.p,{type:"text",name:"q",placeholder:"Search",className:"h-auto bg-background py-1.5 pr-3 pl-8 text-xs"}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"absolute top-px right-px bottom-px h-8 w-8",children:(0,r.jsx)(O.A,{size:16,className:"text-muted-foreground"})})]})}),A={user:{name:"shadcn",email:"<EMAIL>",avatar:"/avatars/shadcn.jpg"},navMain:[{title:"Dashboard",url:"/dashboard",icon:b.A},{title:"Profile",url:"/profile",icon:m.A,isActive:!0},{title:"Usage Analytics",url:"/profile/usage",icon:f.A},{title:"Conversations",url:"/conversations",icon:h.A}],navAccount:[{title:"Extension",url:"/profile/extension",icon:g.A},{title:"Billing",url:"/billing",icon:v.A},{title:"Settings",url:"/profile/settings",icon:x.A},{title:"Notifications",url:"/settings/notifications",icon:C.A}],navSecondary:[{title:"Documentation",url:"/docs",icon:w.A},{title:"Help Center",url:"/help",icon:S.A},{title:"Support",url:"#",icon:E.A},{title:"Terms & Privacy",url:"/terms",icon:j.A}]},I=({children:e})=>{let t=(0,i.cL)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.Bx,{variant:"inset",children:[(0,r.jsx)(i.Gh,{children:(0,r.jsx)(i.wZ,{children:(0,r.jsx)(i.FX,{children:(0,r.jsx)("div",{className:(0,d.cn)("h-[36px] overflow-hidden transition-all [&>div]:w-full",t.open?"":"-mx-1"),children:(0,r.jsx)(a.NC,{hidePersonal:!0,afterSelectOrganizationUrl:"/profile"})})})})}),(0,r.jsx)(F,{}),(0,r.jsxs)(i.Yv,{children:[(0,r.jsxs)(i.Cn,{children:[(0,r.jsx)(i.jj,{children:"Dashboard"}),(0,r.jsx)(i.wZ,{children:A.navMain.map(e=>(0,r.jsx)(i.FX,{children:(0,r.jsx)(i.Uj,{asChild:!0,tooltip:e.title,children:(0,r.jsxs)(y(),{href:e.url,children:[(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})]}),(0,r.jsxs)(i.Cn,{children:[(0,r.jsx)(i.jj,{children:"Account"}),(0,r.jsx)(i.wZ,{children:A.navAccount.map(e=>(0,r.jsx)(i.FX,{children:(0,r.jsx)(i.Uj,{asChild:!0,tooltip:e.title,children:(0,r.jsxs)(y(),{href:e.url,children:[(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})]}),(0,r.jsx)(i.Cn,{className:"mt-auto",children:(0,r.jsx)(i.rQ,{children:(0,r.jsx)(i.wZ,{children:A.navSecondary.map(e=>(0,r.jsx)(i.FX,{children:(0,r.jsx)(i.Uj,{asChild:!0,children:(0,r.jsxs)(y(),{href:e.url,children:[(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})})]}),(0,r.jsx)(i.CG,{children:(0,r.jsx)(i.wZ,{children:(0,r.jsxs)(i.FX,{className:"flex items-center gap-2",children:[(0,r.jsx)(a.uF,{showName:!0,appearance:{elements:{rootBox:"flex overflow-hidden w-full",userButtonBox:"flex-row-reverse",userButtonOuterIdentifier:"truncate pl-0"}}}),(0,r.jsxs)("div",{className:"flex shrink-0 items-center gap-px",children:[(0,r.jsx)(s.ModeToggle,{}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"shrink-0",asChild:!0,children:(0,r.jsx)("div",{className:"h-4 w-4",children:(0,r.jsx)(p,{})})})]})]})})})]}),(0,r.jsx)(i.sF,{children:e})]})}},36334:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(46097);let a=async e=>[{type:"image/png",width:1200,height:630,url:(0,r.fillMetadataSegment)(".",await e.params,"opengraph-image.png")+"?1de9f909622c0a32"}]},42394:(e,t,n)=>{"use strict";n.d(t,{GlobalSidebar:()=>r});let r=(0,n(6340).registerClientReference)(function(){throw Error("Attempted to call GlobalSidebar() from the server but GlobalSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\components\\sidebar.tsx","GlobalSidebar")},51134:(e,t,n)=>{"use strict";n.d(t,{H:()=>s});var r=n(79796),a=n(42294);let s=()=>(0,r.w)({server:{KNOCK_SECRET_API_KEY:a.z.string().optional()},client:{NEXT_PUBLIC_KNOCK_API_KEY:a.z.string().optional(),NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:a.z.string().optional()},runtimeEnv:{NEXT_PUBLIC_KNOCK_API_KEY:"",NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:"",KNOCK_SECRET_API_KEY:process.env.KNOCK_SECRET_API_KEY}})},53330:(e,t,n)=>{"use strict";n.d(t,{ModeToggle:()=>m});var r=n(99730),a=n(62458),s=n(48585),o=n(74938);n(57752);var i=n(33187),d=n(83590);function l({...e}){return(0,r.jsx)(i.bL,{"data-slot":"dropdown-menu",...e})}function c({...e}){return(0,r.jsx)(i.l9,{"data-slot":"dropdown-menu-trigger",...e})}function u({className:e,sideOffset:t=4,...n}){return(0,r.jsx)(i.ZL,{children:(0,r.jsx)(i.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...n})})}function p({className:e,inset:t,variant:n="default",...a}){return(0,r.jsx)(i.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}let b=[{label:"Light",value:"light"},{label:"Dark",value:"dark"},{label:"System",value:"system"}],m=()=>{let{setTheme:e}=(0,s.D)();return(0,r.jsxs)(l,{children:[(0,r.jsx)(c,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"shrink-0 text-foreground",children:[(0,r.jsx)(a.gLX,{className:"dark:-rotate-90 h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:scale-0"}),(0,r.jsx)(a.rRK,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsx)(u,{children:b.map(({label:t,value:n})=>(0,r.jsx)(p,{onClick:()=>e(n),children:t},n))})]})}},54526:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(46097);let a=async e=>[{type:"image/png",sizes:"192x192",url:(0,r.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?6b949b4e1bd36892"}]},56940:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(46097);let a=async e=>[{type:"image/png",sizes:"32x32",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.png")+"?0fb4a24cefe3ddc0"}]},68741:(e,t,n)=>{"use strict";n.d(t,{SidebarProvider:()=>a,SidebarTrigger:()=>s});var r=n(6340);(0,r.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","Sidebar"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarContent"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarFooter"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarGroup"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarGroupAction"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarGroupContent"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarGroupLabel"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarHeader"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarInput"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarInset"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenu"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuAction"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuBadge"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuButton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuItem"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuSub"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuSubButton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarMenuSubItem");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarRail"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarSeparator");let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","SidebarTrigger");(0,r.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx","useSidebar")},73012:(e,t,n)=>{"use strict";n.d(t,{NotificationsProvider:()=>d});var r=n(99730),a=n(22693),s=n(51134);let o=(0,s.H)().NEXT_PUBLIC_KNOCK_API_KEY,i=(0,s.H)().NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID,d=({children:e,userId:t})=>o&&i?(0,r.jsx)(a.eb,{apiKey:o,userId:t,children:(0,r.jsx)(a.WL,{feedId:i,children:e})}):e},79202:(e,t,n)=>{"use strict";n.d(t,{PostHogIdentifier:()=>i});var r=n(89620),a=n(2233),s=n(53172),o=n(57752);let i=()=>{let{user:e}=(0,a.Jd)(),t=(0,o.useRef)(!1),n=(0,s.usePathname)(),i=(0,s.useSearchParams)(),d=(0,r.s)();return(0,o.useEffect)(()=>{if(n&&d){let e=window.origin+n;i.toString()&&(e=`${e}?${i.toString()}`),d.capture("$pageview",{$current_url:e})}},[n,i,d]),(0,o.useEffect)(()=>{e&&!t.current&&(d.identify(e.id,{email:e.emailAddresses.at(0)?.emailAddress,firstName:e.firstName,lastName:e.lastName,createdAt:e.createdAt,avatar:e.imageUrl,phoneNumber:e.phoneNumbers.at(0)?.phoneNumber}),t.current=!0)},[e,d]),null}},85733:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>o});var r=n(99730);n(57752);var a=n(41706),s=n(83590);function o({className:e,orientation:t="horizontal",decorative:n=!0,...o}){return(0,r.jsx)(a.b,{"data-slot":"separator-root",decorative:n,orientation:t,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...o})}},93122:(e,t,n)=>{"use strict";n.d(t,{NotificationsProvider:()=>r});let r=(0,n(6340).registerClientReference)(function(){throw Error("Attempted to call NotificationsProvider() from the server but NotificationsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\notifications\\components\\provider.tsx","NotificationsProvider")},99752:(e,t,n)=>{"use strict";n.d(t,{p:()=>s});var r=n(99730);n(57752);var a=n(83590);function s({className:e,type:t,...n}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n})}}};