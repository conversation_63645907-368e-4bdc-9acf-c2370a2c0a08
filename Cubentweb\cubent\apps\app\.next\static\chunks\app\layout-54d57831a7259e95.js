(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{23454:(e,t,o)=>{"use strict";o.d(t,{PostHogProvider:()=>c,s:()=>i.sf});var r=o(6024),n=o(87219),i=o(19921),a=o(50628),s=o(91028),l=o(74822);let d=()=>(0,s.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:l.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:l.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:l.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}}),c=e=>((0,a.useEffect)(()=>{try{let e=d();e.NEXT_PUBLIC_POSTHOG_KEY&&e.NEXT_PUBLIC_POSTHOG_HOST?n.Ay.init(e.NEXT_PUBLIC_POSTHOG_KEY,{api_host:"/ingest",ui_host:e.NEXT_PUBLIC_POSTHOG_HOST,person_profiles:"identified_only",capture_pageview:!1,capture_pageleave:!0}):console.warn("PostHog environment variables not configured. Analytics disabled.")}catch(e){console.warn("PostHog initialization failed:",e)}},[]),(0,r.jsx)(i.so,{client:n.Ay,...e}))},31918:(e,t,o)=>{"use strict";o.d(t,{cn:()=>i}),o(63410);var r=o(49973);o(13957);var n=o(22928);let i=function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.QP)((0,r.$)(t))}},37539:(e,t,o)=>{"use strict";o.d(t,{TooltipProvider:()=>a,ZI:()=>d,k$:()=>l,m_:()=>s});var r=o(6024);o(50628);var n=o(9379),i=o(31918);function a(e){let{delayDuration:t=0,...o}=e;return(0,r.jsx)(n.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...o})}function s(e){let{...t}=e;return(0,r.jsx)(a,{children:(0,r.jsx)(n.bL,{"data-slot":"tooltip",...t})})}function l(e){let{...t}=e;return(0,r.jsx)(n.l9,{"data-slot":"tooltip-trigger",...t})}function d(e){let{className:t,sideOffset:o=0,children:a,...s}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{"data-slot":"tooltip-content",sideOffset:o,className:(0,i.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...s,children:[a,(0,r.jsx)(n.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},39499:(e,t,o)=>{"use strict";o.d(t,{y:()=>n});var r=o(70988);let n=(0,r.createServerReference)("7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a",r.callServer,void 0,r.findSourceMapURL,"invalidateCacheAction")},62429:(e,t,o)=>{Promise.resolve().then(o.bind(o,64353)),Promise.resolve().then(o.t.bind(o,91350,23)),Promise.resolve().then(o.bind(o,43432)),Promise.resolve().then(o.bind(o,93181)),Promise.resolve().then(o.bind(o,67461)),Promise.resolve().then(o.bind(o,40644)),Promise.resolve().then(o.bind(o,30426)),Promise.resolve().then(o.bind(o,94134)),Promise.resolve().then(o.bind(o,31800)),Promise.resolve().then(o.bind(o,72881)),Promise.resolve().then(o.t.bind(o,88224,23)),Promise.resolve().then(o.t.bind(o,9658,23)),Promise.resolve().then(o.t.bind(o,5826,23)),Promise.resolve().then(o.bind(o,13957)),Promise.resolve().then(o.bind(o,23454)),Promise.resolve().then(o.bind(o,70433)),Promise.resolve().then(o.bind(o,89069)),Promise.resolve().then(o.bind(o,37539))},64353:(e,t,o)=>{"use strict";o.d(t,{CrossDomainAuthSync:()=>i});var r=o(12503),n=o(50628);function i(){let{isLoaded:e,isSignedIn:t,user:o}=(0,r.Jd)();return(0,n.useEffect)(()=>{!async function(){if(e)if(t&&o)try{console.log("[CROSS-DOMAIN] Setting auth token for cubent.dev...");let e=await fetch("/api/auth/set-cross-domain-token",{method:"POST",headers:{"Content-Type":"application/json"}});e.ok?console.log("[CROSS-DOMAIN] Auth token set successfully"):console.error("[CROSS-DOMAIN] Failed to set auth token:",e.status)}catch(e){console.error("[CROSS-DOMAIN] Error setting auth token:",e)}else try{console.log("[CROSS-DOMAIN] Clearing auth token for cubent.dev...");let e=await fetch("/api/auth/clear-cross-domain-token",{method:"POST",headers:{"Content-Type":"application/json"}});e.ok?console.log("[CROSS-DOMAIN] Auth token cleared successfully"):console.error("[CROSS-DOMAIN] Failed to clear auth token:",e.status)}catch(e){console.error("[CROSS-DOMAIN] Error clearing auth token:",e)}}()},[e,t,o]),null}},70433:(e,t,o)=>{"use strict";o.d(t,{AuthProvider:()=>s});var r=o(6024),n=o(98766),i=o(5850),a=o(72881);let s=e=>{let{privacyUrl:t,termsUrl:o,helpUrl:s,...l}=e,{resolvedTheme:d}=(0,a.D)(),c="dark"===d?i.dark:void 0;return(0,r.jsx)(n.lJ,{...l,appearance:{layout:{privacyPageUrl:t,termsPageUrl:o,helpPageUrl:s},baseTheme:c,elements:{dividerLine:"bg-border",socialButtonsIconButton:"bg-card",navbarButton:"text-foreground",organizationSwitcherTrigger__open:"bg-background",organizationPreviewMainIdentifier:"text-foreground",organizationSwitcherTriggerIcon:"text-muted-foreground",organizationPreview__organizationSwitcherTrigger:"gap-2",organizationPreviewAvatarContainer:"shrink-0"},variables:{fontFamily:"var(--font-sans)",fontFamilyButtons:"var(--font-sans)",fontWeight:{bold:"var(--font-weight-bold)",normal:"var(--font-weight-normal)",medium:"var(--font-weight-medium)"}}}})}},89069:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>a});var r=o(6024),n=o(72881),i=o(13957);let a=e=>{let{...t}=e,{theme:o="system"}=(0,n.D)();return(0,r.jsx)(i.l,{theme:o,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})}},91350:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,4861,1081,449,9222,3572,2503,9379,3594,3993,1903,2913,4499,7358],()=>t(62429)),_N_E=e.O()}]);