(()=>{var e={};e.id=1023,e.ids=[1023],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>T});var s=r(8741),n=r(62923),i=r(54726),a=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),l=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,i;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((i=e,`[clerk debug end: ${i}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},d=(e,t)=>(...r)=>{let s=("string"==typeof e?l(e,c):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),g=r(37081),x=r(27322),h=r(6264);function f(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return x.r0.stringify(r,{pad:!1})}async function y(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,x.hJ)(r.algorithm);if(!n)return{errors:[new h.xy(`Unsupported algorithm ${r.algorithm}`)]};let i=await (0,x.Fh)(t,n,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=f(a),u=f(e),c=`${o}.${u}`;try{let e=await x.fA.crypto.subtle.sign(n,i,s.encode(c));return{data:`${c}.${x.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new h.xy(e?.message)]}}}(0,g.C)(x.J0);var w=(0,g.R)(x.iU);(0,g.C)(y),(0,g.C)(x.nk);var m=r(97495),b=r(60606);function v(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,a,o;let u,c=(0,m.NE)(e,"AuthStatus"),l=(0,m.NE)(e,"AuthToken"),d=(0,m.NE)(e,"AuthMessage"),p=(0,m.NE)(e,"AuthReason"),g=(0,m.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:c,authMessage:d,authReason:p});let x=(0,m._b)(e,s.AA.Headers.ClerkRequestData),h=(0,b.Kk)(x),f={secretKey:(null==r?void 0:r.secretKey)||h.secretKey||i.rB,publishableKey:h.publishableKey||i.At,apiUrl:i.H$,apiVersion:i.mG,authStatus:c,authMessage:d,authReason:p,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",f),c&&c===s.TD.SignedIn){(0,b._l)(l,f.secretKey,g);let e=w(l);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(f,e.raw.text,e.payload)}else u=(0,s.wI)(f);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(f,u.sessionStatus)),u}var A=r(68478);let S=({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>async(n,i)=>{if((0,a.zz)((0,m._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,m.Zd)(n)){p.M&&(0,b.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,b.$K)(n,t)}return v(n,{...i,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>(r,n)=>((0,a.zz)((0,m._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,b.$K)(r,t),v(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,A.AG)()});let U={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},q=e=>{var t,r;return!!e.headers.get(U.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(U.Headers.NextAction))},j=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||N(e)||E(e)},N=e=>!!e.headers.get(U.Headers.NextUrl)&&!q(e)||k(),k=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},E=e=>!!e.headers.get(U.Headers.NextjsData);var R=r(23056);let T=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,R.TG)(),a=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await S({debugLoggerName:"auth()",noAuthStatusMessage:(0,A.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),u=(0,m.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,s.tl)(t),c=a.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||a.cookies.get(s.AA.Cookies.DevBrowser),l=(0,m._b)(t,s.AA.Headers.ClerkRequestData),d=(0,b.Kk)(l);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:c,baseUrl:a.clerkUrl.toString(),publishableKey:d.publishableKey||i.At,signInUrl:d.signInUrl||i.qW,signUpUrl:d.signUpUrl||i.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};T.protect=async(...e)=>{r(1447);let t=await (0,R.TG)(),s=await T();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:i}=e;return async(...e)=>{var a,o,u,c,l,d;let p=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],g=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),x=(null==(l=e[0])?void 0:l.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),h=()=>x?s(x):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:h():r.has(p)?r:h():r:g?s(g):j(i)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86262:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>x,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>g});var n=r(26142),i=r(94327),a=r(34862),o=r(37838),u=r(18815),c=r(26239),l=r(25);let d=l.z.object({extensionSettings:l.z.record(l.z.any()).optional(),preferences:l.z.record(l.z.any()).optional()});async function p(){try{let{userId:e}=await (0,o.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e},select:{extensionSettings:!0,preferences:!0}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});return c.NextResponse.json({extensionSettings:t.extensionSettings||{},preferences:t.preferences||{}})}catch(e){return console.error("Settings fetch error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let{userId:t}=await (0,o.j)();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),{extensionSettings:s,preferences:n}=d.parse(r),i=await u.database.user.findUnique({where:{clerkId:t}});if(!i)return c.NextResponse.json({error:"User not found"},{status:404});let a={};void 0!==s&&(a.extensionSettings=s),void 0!==n&&(a.preferences=n);let l=await u.database.user.update({where:{id:i.id},data:a,select:{extensionSettings:!0,preferences:!0}});return c.NextResponse.json({success:!0,extensionSettings:l.extensionSettings,preferences:l.preferences})}catch(e){return console.error("Settings update error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/settings/route",pathname:"/api/extension/settings",filename:"route",bundlePath:"app/api/extension/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:y}=x;function w(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(86262));module.exports=s})();