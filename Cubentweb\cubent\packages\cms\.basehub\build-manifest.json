{"generatedAt": "2025-06-26T16:58:35.143Z", "sdkVersion": "8.2.7", "inputHash": "e315d5c41804e7b8cb68b4a9cd14ee9f", "schemaHash": "861511b50df8d472344217c1986c324e", "resolvedRef": {"repoHash": "57ec52db", "type": "branch", "ref": "main", "createSuggestedBranchLink": null, "id": "KluwvFPvKCxusUOmSQG4q", "name": "main", "git": null, "createdAt": "2025-06-16T00:30:26.760Z", "archivedAt": null, "archivedBy": null, "headCommitId": "qNNz4p8JMipdRXk4579YJ", "isDefault": true, "deletedAt": null, "workingRootBlockId": "a8Oul5Re6jsffvG4Ab5XZ"}}