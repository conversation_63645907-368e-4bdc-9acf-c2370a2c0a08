(()=>{var e={};e.id=9663,e.ids=[9663],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21514:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>d});var n=r(26142),i=r(94327),o=r(34862),a=r(37838),u=r(18815),c=r(26239);async function l(){try{let{userId:e}=await (0,a.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});let r=new Date,s=new Date(r.getFullYear(),r.getMonth(),1),n=await u.database.usageMetrics.aggregate({where:{userId:t.id,date:{gte:s}},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),i=t.subscriptionTier||"FREE",o={FREE:{name:"Free",monthlyTokenLimit:1e4,monthlyRequestLimit:100,monthlyCostLimit:5,features:["Basic AI assistance","Limited models"]},PRO:{name:"Pro",monthlyTokenLimit:1e5,monthlyRequestLimit:1e3,monthlyCostLimit:50,features:["Advanced AI assistance","All models","Priority support"]},ENTERPRISE:{name:"Enterprise",monthlyTokenLimit:-1,monthlyRequestLimit:-1,monthlyCostLimit:-1,features:["Unlimited usage","Custom models","Dedicated support"]}}[i],l={subscription:{tier:i,status:t.subscriptionStatus||"ACTIVE",name:o.name,features:o.features,limits:{monthlyTokenLimit:o.monthlyTokenLimit,monthlyRequestLimit:o.monthlyRequestLimit,monthlyCostLimit:o.monthlyCostLimit},billingPeriod:{start:s,end:new Date(r.getFullYear(),r.getMonth()+1,0)}},usage:{current:{tokensUsed:n._sum.tokensUsed||0,requestsMade:n._sum.requestsMade||0,costAccrued:n._sum.costAccrued||0},percentages:{tokens:o.monthlyTokenLimit>0?Math.round((n._sum.tokensUsed||0)/o.monthlyTokenLimit*100):0,requests:o.monthlyRequestLimit>0?Math.round((n._sum.requestsMade||0)/o.monthlyRequestLimit*100):0,cost:o.monthlyCostLimit>0?Math.round((n._sum.costAccrued||0)/o.monthlyCostLimit*100):0}},canUpgrade:"ENTERPRISE"!==i,upgradeUrl:"FREE"===i?"/pricing":"/billing"};return c.NextResponse.json(l)}catch(e){return console.error("Subscription fetch error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let{userId:t}=await (0,a.j)();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{tokensRequested:r=0,estimatedCost:s=0}=await e.json(),n=await u.database.user.findUnique({where:{clerkId:t}});if(!n)return c.NextResponse.json({error:"User not found"},{status:404});let i=n.subscriptionTier||"FREE",o=new Date,l=new Date(o.getFullYear(),o.getMonth(),1),d=await u.database.usageMetrics.aggregate({where:{userId:n.id,date:{gte:l}},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),p={FREE:{tokens:1e4,requests:100,cost:5},PRO:{tokens:1e5,requests:1e3,cost:50},ENTERPRISE:{tokens:-1,requests:-1,cost:-1}}[i],h={tokens:d._sum.tokensUsed||0,requests:d._sum.requestsMade||0,cost:d._sum.costAccrued||0},m={tokens:-1===p.tokens||h.tokens+r<=p.tokens,requests:-1===p.requests||h.requests+1<=p.requests,cost:-1===p.cost||h.cost+s<=p.cost},g=m.tokens&&m.requests&&m.cost,x="";return m.tokens?m.requests?m.cost||(x="Monthly cost limit exceeded"):x="Monthly request limit exceeded":x="Monthly token limit exceeded",c.NextResponse.json({allowed:g,reason:g?null:x,limits:p,currentUsage:h,remaining:{tokens:-1===p.tokens?-1:Math.max(0,p.tokens-h.tokens),requests:-1===p.requests?-1:Math.max(0,p.requests-h.requests),cost:-1===p.cost?-1:Math.max(0,p.cost-h.cost)},upgradeRequired:!g&&"ENTERPRISE"!==i})}catch(e){return console.error("Subscription check error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/subscription/route",pathname:"/api/extension/subscription",filename:"route",bundlePath:"app/api/extension/subscription/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\subscription\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:g}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>j});var s=r(8741),n=r(62923),i=r(54726),o=r(87553),a=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),l=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,i;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((i=e,`[clerk debug end: ${i}] (@clerk/nextjs=6.20.0,next=${a.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},d=(e,t)=>(...r)=>{let s=("string"==typeof e?l(e,c):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),h=r(37081),m=r(27322),g=r(6264);function x(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return m.r0.stringify(r,{pad:!1})}async function y(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,m.hJ)(r.algorithm);if(!n)return{errors:[new g.xy(`Unsupported algorithm ${r.algorithm}`)]};let i=await (0,m.Fh)(t,n,"sign"),o=r.header||{typ:"JWT"};o.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let a=x(o),u=x(e),c=`${a}.${u}`;try{let e=await m.fA.crypto.subtle.sign(n,i,s.encode(c));return{data:`${c}.${m.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new g.xy(e?.message)]}}}(0,h.C)(m.J0);var f=(0,h.R)(m.iU);(0,h.C)(y),(0,h.C)(m.nk);var q=r(97495),w=r(60606);function b(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,o,a;let u,c=(0,q.NE)(e,"AuthStatus"),l=(0,q.NE)(e,"AuthToken"),d=(0,q.NE)(e,"AuthMessage"),p=(0,q.NE)(e,"AuthReason"),h=(0,q.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:c,authMessage:d,authReason:p});let m=(0,q._b)(e,s.AA.Headers.ClerkRequestData),g=(0,w.Kk)(m),x={secretKey:(null==r?void 0:r.secretKey)||g.secretKey||i.rB,publishableKey:g.publishableKey||i.At,apiUrl:i.H$,apiVersion:i.mG,authStatus:c,authMessage:d,authReason:p,treatPendingAsSignedOut:t};if(null==(o=r.logger)||o.debug("auth options",x),c&&c===s.TD.SignedIn){(0,w._l)(l,x.secretKey,h);let e=f(l);null==(a=r.logger)||a.debug("jwt",e.raw),u=(0,s.Z5)(x,e.raw.text,e.payload)}else u=(0,s.wI)(x);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(x,u.sessionStatus)),u}var A=r(68478);let k=({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>async(n,i)=>{if((0,o.zz)((0,q._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,q.Zd)(n)){p.M&&(0,w.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,w.$K)(n,t)}return b(n,{...i,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>(r,n)=>((0,o.zz)((0,q._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,w.$K)(r,t),b(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,A.AG)()});let v={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},E=e=>{var t,r;return!!e.headers.get(v.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(v.Headers.NextAction))},R=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||U(e)||T(e)},U=e=>!!e.headers.get(v.Headers.NextUrl)&&!E(e)||S(),S=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},T=e=>!!e.headers.get(v.Headers.NextjsData);var N=r(23056);let j=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,N.TG)(),o=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},a=await k({debugLoggerName:"auth()",noAuthStatusMessage:(0,A.sd)("auth",await o())})(t,{treatPendingAsSignedOut:e}),u=(0,q.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},o=(0,s.tl)(t),c=o.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||o.cookies.get(s.AA.Cookies.DevBrowser),l=(0,q._b)(t,s.AA.Headers.ClerkRequestData),d=(0,w.Kk)(l);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:c,baseUrl:o.clerkUrl.toString(),publishableKey:d.publishableKey||i.At,signInUrl:d.signInUrl||i.qW,signUpUrl:d.signUpUrl||i.sE,sessionStatus:a.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(a,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};j.protect=async(...e)=>{r(1447);let t=await (0,N.TG)(),s=await j();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:i}=e;return async(...e)=>{var o,a,u,c,l,d;let p=(null==(o=e[0])?void 0:o.unauthenticatedUrl)||(null==(a=e[0])?void 0:a.unauthorizedUrl)?void 0:e[0],h=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),m=(null==(l=e[0])?void 0:l.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),g=()=>m?s(m):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:g():r.has(p)?r:g():r:h?s(h):R(i)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(21514));module.exports=s})();