"use strict";exports.id=4601,exports.ids=[4601],exports.modules={12901:(e,t,s)=>{s.d(t,{$:()=>h});var i=s(8741),n=s(23056),l=s(76315),r=s(97495);let a=new(s(16698)).AsyncLocalStorage;var o=s(60606);let h=async()=>{var e,t;let s;try{let e=await (0,n.TG)(),t=(0,r._b)(e,i.AA.Headers.ClerkRequestData);s=(0,o.Kk)(t)}catch(e){if(e&&(0,n.Sz)(e))throw e}let h=null!=(t=null==(e=a.getStore())?void 0:e.get("requestData"))?t:s;return(null==h?void 0:h.secretKey)||(null==h?void 0:h.publishableKey)?(0,l.n)(h):(0,l.n)({})}},14601:(e,t,s)=>{s.d(t,{clerkClient:()=>i.$}),s(26790);var i=s(12901);s(8741)},26790:(e,t,s)=>{s.d(t,{z:()=>T});var i,n,l,r,a,o,h,d,c,u,p,S,f,y,k,m,b,g,v,w=s(45940);s(92867);var K=s(37081);s(27322),s(6264);var V=s(49530),E=s(57136),M=s(94051),R=class{constructor(){(0,M.VK)(this,l),(0,M.VK)(this,i,"clerk_telemetry_throttler"),(0,M.VK)(this,n,864e5)}isEventThrottled(e){if(!(0,M.S7)(this,l,o))return!1;let t=Date.now(),s=(0,M.jq)(this,l,r).call(this,e),h=(0,M.S7)(this,l,a)?.[s];if(!h){let e={...(0,M.S7)(this,l,a),[s]:t};localStorage.setItem((0,M.S7)(this,i),JSON.stringify(e))}if(h&&t-h>(0,M.S7)(this,n)){let e=(0,M.S7)(this,l,a);delete e[s],localStorage.setItem((0,M.S7)(this,i),JSON.stringify(e))}return!!h}};i=new WeakMap,n=new WeakMap,l=new WeakSet,r=function(e){let{sk:t,pk:s,payload:i,...n}=e,l={...i,...n};return JSON.stringify(Object.keys({...i,...n}).sort().map(e=>l[e]))},a=function(){let e=localStorage.getItem((0,M.S7)(this,i));return e?JSON.parse(e):{}},o=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,M.S7)(this,i)),!1}};var q={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},O=class{constructor(e){(0,M.VK)(this,S),(0,M.VK)(this,h),(0,M.VK)(this,d),(0,M.VK)(this,c,{}),(0,M.VK)(this,u,[]),(0,M.VK)(this,p),(0,M.OV)(this,h,{maxBufferSize:e.maxBufferSize??q.maxBufferSize,samplingRate:e.samplingRate??q.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:q.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,M.S7)(this,c).clerkVersion=e.clerkVersion??"":(0,M.S7)(this,c).clerkVersion="",(0,M.S7)(this,c).sdk=e.sdk,(0,M.S7)(this,c).sdkVersion=e.sdkVersion,(0,M.S7)(this,c).publishableKey=e.publishableKey??"";let t=(0,E.q5)(e.publishableKey);t&&((0,M.S7)(this,c).instanceType=t.instanceType),e.secretKey&&((0,M.S7)(this,c).secretKey=e.secretKey.substring(0,16)),(0,M.OV)(this,d,new R)}get isEnabled(){return!("development"!==(0,M.S7)(this,c).instanceType||(0,M.S7)(this,h).disabled||"undefined"!=typeof process&&(0,V.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,M.S7)(this,h).debug||"undefined"!=typeof process&&(0,V.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,M.jq)(this,S,v).call(this,e.event,e.payload);(0,M.jq)(this,S,b).call(this,t.event,t),(0,M.jq)(this,S,f).call(this,t,e.eventSamplingRate)&&((0,M.S7)(this,u).push(t),(0,M.jq)(this,S,k).call(this))}};h=new WeakMap,d=new WeakMap,c=new WeakMap,u=new WeakMap,p=new WeakMap,S=new WeakSet,f=function(e,t){return this.isEnabled&&!this.isDebug&&(0,M.jq)(this,S,y).call(this,e,t)},y=function(e,t){let s=Math.random();return!!(s<=(0,M.S7)(this,h).samplingRate&&(void 0===t||s<=t))&&!(0,M.S7)(this,d).isEventThrottled(e)},k=function(){if("undefined"==typeof window)return void(0,M.jq)(this,S,m).call(this);if((0,M.S7)(this,u).length>=(0,M.S7)(this,h).maxBufferSize){(0,M.S7)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,M.S7)(this,p)),(0,M.jq)(this,S,m).call(this);return}(0,M.S7)(this,p)||("requestIdleCallback"in window?(0,M.OV)(this,p,requestIdleCallback(()=>{(0,M.jq)(this,S,m).call(this)})):(0,M.OV)(this,p,setTimeout(()=>{(0,M.jq)(this,S,m).call(this)},0)))},m=function(){fetch(new URL("/v1/event",(0,M.S7)(this,h).endpoint),{method:"POST",body:JSON.stringify({events:(0,M.S7)(this,u)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,M.OV)(this,u,[])}).catch(()=>void 0)},b=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},g=function(){let e={name:(0,M.S7)(this,c).sdk,version:(0,M.S7)(this,c).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){let s=(0,M.jq)(this,S,g).call(this);return{event:e,cv:(0,M.S7)(this,c).clerkVersion??"",it:(0,M.S7)(this,c).instanceType??"",sdk:s.name,sdkv:s.version,...(0,M.S7)(this,c).publishableKey?{pk:(0,M.S7)(this,c).publishableKey}:{},...(0,M.S7)(this,c).secretKey?{sk:(0,M.S7)(this,c).secretKey}:{},payload:t}};function T(e){let t={...e},s=(0,w.y3)(t),i=(0,w.Bs)({options:t,apiClient:s}),n=new O({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...i,telemetry:n}}(0,K.C)(w.nr)},76315:(e,t,s)=>{s.d(t,{n:()=>r});var i=s(26790),n=s(54726);let l={secretKey:n.rB,publishableKey:n.At,apiUrl:n.H$,apiVersion:n.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:n.Rg,domain:n.V2,isSatellite:n.fS,sdkMetadata:n.tm,telemetry:{disabled:n.nN,debug:n.Mh}},r=e=>(0,i.z)({...l,...e})}};