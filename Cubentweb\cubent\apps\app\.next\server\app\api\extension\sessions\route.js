(()=>{var e={};e.id=6855,e.ids=[6855],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>z});var s=r(8741),n=r(62923),i=r(54726),a=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},d=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),c=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,i;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((i=e,`[clerk debug end: ${i}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},l=(e,t)=>(...r)=>{let s=("string"==typeof e?c(e,d):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),x=r(37081),h=r(27322),A=r(6264);function g(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return h.r0.stringify(r,{pad:!1})}async function f(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,h.hJ)(r.algorithm);if(!n)return{errors:[new A.xy(`Unsupported algorithm ${r.algorithm}`)]};let i=await (0,h.Fh)(t,n,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=g(a),u=g(e),d=`${o}.${u}`;try{let e=await h.fA.crypto.subtle.sign(n,i,s.encode(d));return{data:`${d}.${h.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new A.xy(e?.message)]}}}(0,x.C)(h.J0);var v=(0,x.R)(h.iU);(0,x.C)(f),(0,x.C)(h.nk);var m=r(97495),w=r(60606);function y(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,a,o;let u,d=(0,m.NE)(e,"AuthStatus"),c=(0,m.NE)(e,"AuthToken"),l=(0,m.NE)(e,"AuthMessage"),p=(0,m.NE)(e,"AuthReason"),x=(0,m.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:d,authMessage:l,authReason:p});let h=(0,m._b)(e,s.AA.Headers.ClerkRequestData),A=(0,w.Kk)(h),g={secretKey:(null==r?void 0:r.secretKey)||A.secretKey||i.rB,publishableKey:A.publishableKey||i.At,apiUrl:i.H$,apiVersion:i.mG,authStatus:d,authMessage:l,authReason:p,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",g),d&&d===s.TD.SignedIn){(0,w._l)(c,g.secretKey,x);let e=v(c);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(g,e.raw.text,e.payload)}else u=(0,s.wI)(g);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(g,u.sessionStatus)),u}var b=r(68478);let U=({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>async(n,i)=>{if((0,a.zz)((0,m._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,m.Zd)(n)){p.M&&(0,w.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,w.$K)(n,t)}return y(n,{...i,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>(r,n)=>((0,a.zz)((0,m._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,w.$K)(r,t),y(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,b.AG)()});let j={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},I=e=>{var t,r;return!!e.headers.get(j.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(j.Headers.NextAction))},S=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||q(e)||k(e)},q=e=>!!e.headers.get(j.Headers.NextUrl)&&!I(e)||N(),N=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},k=e=>!!e.headers.get(j.Headers.NextjsData);var R=r(23056);let z=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,R.TG)(),a=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await U({debugLoggerName:"auth()",noAuthStatusMessage:(0,b.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),u=(0,m.NE)(t,"ClerkUrl"),d=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,s.tl)(t),d=a.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||a.cookies.get(s.AA.Cookies.DevBrowser),c=(0,m._b)(t,s.AA.Headers.ClerkRequestData),l=(0,w.Kk)(c);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:d,baseUrl:a.clerkUrl.toString(),publishableKey:l.publishableKey||i.At,signInUrl:l.signInUrl||i.qW,signUpUrl:l.signUpUrl||i.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=d(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=d(e);return t.redirectToSignUp({returnBackUrl:r})}})};z.protect=async(...e)=>{r(1447);let t=await (0,R.TG)(),s=await z();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:i}=e;return async(...e)=>{var a,o,u,d,c,l;let p=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],x=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(d=e[1])?void 0:d.unauthenticatedUrl),h=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(l=e[1])?void 0:l.unauthorizedUrl),A=()=>h?s(h):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:A():r.has(p)?r:A():r:x?s(x):S(i)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>w,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>g,GET:()=>A,PATCH:()=>h,POST:()=>x});var n=r(26142),i=r(94327),a=r(34862),o=r(37838),u=r(18815),d=r(26239),c=r(25);let l=c.z.object({sessionId:c.z.string().min(1),extensionVersion:c.z.string().optional(),vscodeVersion:c.z.string().optional(),platform:c.z.string().optional(),metadata:c.z.record(c.z.any()).optional()}),p=c.z.object({sessionId:c.z.string().min(1),isActive:c.z.boolean().optional(),tokensUsed:c.z.number().int().min(0).optional(),requestsMade:c.z.number().int().min(0).optional(),metadata:c.z.record(c.z.any()).optional()});async function x(e){try{let{userId:t}=await (0,o.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=l.safeParse(r);if(!s.success)return d.NextResponse.json({error:"Invalid session data",details:s.error.errors},{status:400});let{sessionId:n,extensionVersion:i,vscodeVersion:a,platform:c,metadata:p}=s.data,x=await u.database.user.findUnique({where:{clerkId:t}});if(!x)return d.NextResponse.json({error:"User not found"},{status:404});let h=await u.database.extensionSession.upsert({where:{userId_sessionId:{userId:x.id,sessionId:n}},update:{isActive:!0,lastActiveAt:new Date,extensionVersion:i,vscodeVersion:a,platform:c,metadata:p},create:{userId:x.id,sessionId:n,isActive:!0,extensionVersion:i,vscodeVersion:a,platform:c,metadata:p,tokensUsed:0,requestsMade:0,createdAt:new Date,lastActiveAt:new Date}});return d.NextResponse.json({success:!0,session:{id:h.id,sessionId:h.sessionId,isActive:h.isActive,createdAt:h.createdAt,lastActiveAt:h.lastActiveAt}})}catch(e){return console.error("Session creation error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let{userId:t}=await (0,o.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=p.safeParse(r);if(!s.success)return d.NextResponse.json({error:"Invalid session update data",details:s.error.errors},{status:400});let{sessionId:n,isActive:i,tokensUsed:a,requestsMade:c,metadata:l}=s.data,x=await u.database.user.findUnique({where:{clerkId:t}});if(!x)return d.NextResponse.json({error:"User not found"},{status:404});let h={lastActiveAt:new Date};"boolean"==typeof i&&(h.isActive=i),"number"==typeof a&&(h.tokensUsed={increment:a}),"number"==typeof c&&(h.requestsMade={increment:c}),l&&(h.metadata=l);let A=await u.database.extensionSession.update({where:{userId_sessionId:{userId:x.id,sessionId:n}},data:h});return d.NextResponse.json({success:!0,session:{id:A.id,sessionId:A.sessionId,isActive:A.isActive,tokensUsed:A.tokensUsed,requestsMade:A.requestsMade,lastActiveAt:A.lastActiveAt}})}catch(e){return console.error("Session update error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function A(){try{let{userId:e}=await (0,o.j)();if(!e)return d.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return d.NextResponse.json({error:"User not found"},{status:404});let r=await u.database.extensionSession.findMany({where:{userId:t.id},orderBy:{lastActiveAt:"desc"}}),s=r.filter(e=>e.isActive),n=r.length;return d.NextResponse.json({sessions:r.map(e=>({id:e.id,sessionId:e.sessionId,isActive:e.isActive,extensionVersion:e.extensionVersion,vscodeVersion:e.vscodeVersion,platform:e.platform,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,createdAt:e.createdAt,lastActiveAt:e.lastActiveAt,metadata:e.metadata})),summary:{totalSessions:n,activeSessions:s.length,lastActiveSession:r[0]?.lastActiveAt||null}})}catch(e){return console.error("Sessions fetch error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let{userId:t}=await (0,o.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("sessionId"),n="true"===r.get("all"),i=await u.database.user.findUnique({where:{clerkId:t}});if(!i)return d.NextResponse.json({error:"User not found"},{status:404});if(n){let e=await u.database.extensionSession.updateMany({where:{userId:i.id,isActive:!0},data:{isActive:!1,lastActiveAt:new Date}});return d.NextResponse.json({success:!0,message:`Terminated ${e.count} active sessions`,terminatedSessions:e.count})}if(!s)return d.NextResponse.json({error:"Session ID required or use ?all=true to terminate all sessions"},{status:400});{let e=await u.database.extensionSession.update({where:{userId_sessionId:{userId:i.id,sessionId:s}},data:{isActive:!1,lastActiveAt:new Date}});return d.NextResponse.json({success:!0,message:"Session terminated successfully",session:{id:e.id,sessionId:e.sessionId,isActive:e.isActive,lastActiveAt:e.lastActiveAt}})}}catch(e){return console.error("Session termination error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/sessions/route",pathname:"/api/extension/sessions",filename:"route",bundlePath:"app/api/extension/sessions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\sessions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:m,serverHooks:w}=f;function y(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:m})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(94442));module.exports=s})();