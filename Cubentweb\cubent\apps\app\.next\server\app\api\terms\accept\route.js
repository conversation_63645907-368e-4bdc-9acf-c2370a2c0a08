(()=>{var e={};e.id=4082,e.ids=[4082],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>D});var s=r(8741),n=r(62923),a=r(54726),i=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),l=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,a;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((a=e,`[clerk debug end: ${a}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},d=(e,t)=>(...r)=>{let s=("string"==typeof e?l(e,c):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),g=r(37081),h=r(27322),x=r(6264);function m(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return h.r0.stringify(r,{pad:!1})}async function f(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,h.hJ)(r.algorithm);if(!n)return{errors:[new x.xy(`Unsupported algorithm ${r.algorithm}`)]};let a=await (0,h.Fh)(t,n,"sign"),i=r.header||{typ:"JWT"};i.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=m(i),u=m(e),c=`${o}.${u}`;try{let e=await h.fA.crypto.subtle.sign(n,a,s.encode(c));return{data:`${c}.${h.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new x.xy(e?.message)]}}}(0,g.C)(h.J0);var A=(0,g.R)(h.iU);(0,g.C)(f),(0,g.C)(h.nk);var y=r(97495),w=r(60606);function b(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,i,o;let u,c=(0,y.NE)(e,"AuthStatus"),l=(0,y.NE)(e,"AuthToken"),d=(0,y.NE)(e,"AuthMessage"),p=(0,y.NE)(e,"AuthReason"),g=(0,y.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:c,authMessage:d,authReason:p});let h=(0,y._b)(e,s.AA.Headers.ClerkRequestData),x=(0,w.Kk)(h),m={secretKey:(null==r?void 0:r.secretKey)||x.secretKey||a.rB,publishableKey:x.publishableKey||a.At,apiUrl:a.H$,apiVersion:a.mG,authStatus:c,authMessage:d,authReason:p,treatPendingAsSignedOut:t};if(null==(i=r.logger)||i.debug("auth options",m),c&&c===s.TD.SignedIn){(0,w._l)(l,m.secretKey,g);let e=A(l);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(m,e.raw.text,e.payload)}else u=(0,s.wI)(m);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(m,u.sessionStatus)),u}var v=r(68478);let q=({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>async(n,a)=>{if((0,i.zz)((0,y._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,y.Zd)(n)){p.M&&(0,w.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,w.$K)(n,t)}return b(n,{...a,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>(r,n)=>((0,i.zz)((0,y._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,w.$K)(r,t),b(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,v.AG)()});let U={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},S=e=>{var t,r;return!!e.headers.get(U.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(U.Headers.NextAction))},j=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||N(e)||T(e)},N=e=>!!e.headers.get(U.Headers.NextUrl)&&!S(e)||k(),k=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},T=e=>!!e.headers.get(U.Headers.NextjsData);var E=r(23056);let D=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,E.TG)(),i=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await q({debugLoggerName:"auth()",noAuthStatusMessage:(0,v.sd)("auth",await i())})(t,{treatPendingAsSignedOut:e}),u=(0,y.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},i=(0,s.tl)(t),c=i.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||i.cookies.get(s.AA.Cookies.DevBrowser),l=(0,y._b)(t,s.AA.Headers.ClerkRequestData),d=(0,w.Kk)(l);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:c,baseUrl:i.clerkUrl.toString(),publishableKey:d.publishableKey||a.At,signInUrl:d.signInUrl||a.qW,signUpUrl:d.signUpUrl||a.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};D.protect=async(...e)=>{r(1447);let t=await (0,E.TG)(),s=await D();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:a}=e;return async(...e)=>{var i,o,u,c,l,d;let p=(null==(i=e[0])?void 0:i.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],g=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),h=(null==(l=e[0])?void 0:l.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),x=()=>h?s(h):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:x():r.has(p)?r:x():r:g?s(g):j(a)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},93234:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{POST:()=>p});var n=r(26142),a=r(94327),i=r(34862),o=r(37838),u=r(18815),c=r(26239),l=r(25);let d=l.z.object({userId:l.z.string()});async function p(e){try{let{userId:t}=await (0,o.j)();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),{userId:s}=d.parse(r);if(!await u.database.user.findUnique({where:{id:s,clerkId:t}}))return c.NextResponse.json({error:"User not found"},{status:404});let n=await u.database.user.update({where:{id:s},data:{termsAccepted:!0,termsAcceptedAt:new Date}});return c.NextResponse.json({success:!0,message:"Terms accepted successfully",termsAcceptedAt:n.termsAcceptedAt})}catch(e){return console.error("Terms acceptance error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/terms/accept/route",pathname:"/api/terms/accept",filename:"route",bundlePath:"app/api/terms/accept/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\terms\\accept\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:m}=g;function f(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(93234));module.exports=s})();