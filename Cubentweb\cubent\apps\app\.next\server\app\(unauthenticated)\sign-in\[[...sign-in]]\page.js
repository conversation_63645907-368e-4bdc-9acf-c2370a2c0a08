"use strict";(()=>{var e={};e.id=200,e.ids=[200],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},62955:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.default,__next_app__:()=>u,pages:()=>p,routeModule:()=>l,tree:()=>d});var n=t(57864),s=t(94327),i=t(70814),o=t(17984),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);t.d(r,a);let d={children:["",{children:["(unauthenticated)",{children:["sign-in",{children:["[[...sign-in]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68365)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(unauthenticated)\\sign-in\\[[...sign-in]]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,18290)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(unauthenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(unauthenticated)\\sign-in\\[[...sign-in]]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},l=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(unauthenticated)/sign-in/[[...sign-in]]/page",pathname:"/sign-in/[[...sign-in]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68365:(e,r,t)=>{t.r(r),t.d(r,{default:()=>l,metadata:()=>u});var n=t(94752),s=t(34069),i=t(36137),o=t(62923);let a="Welcome back",d="Enter your details to sign in.",p=(0,i.default)(()=>t.e(3026).then(t.bind(t,13026)).then(e=>e.SignIn),{loadableGenerated:{modules:["app\\(unauthenticated)\\sign-in\\[[...sign-in]]\\page.tsx -> @repo/auth/components/sign-in"]}}),u=(0,s.w)({title:a,description:d}),l=async({searchParams:e})=>{let r=await e;r.device_id&&r.state&&(0,o.redirect)(`/login?device_id=${r.device_id}&state=${r.state}`);let t="/auth-success";return r.redirect_url&&(t=`/auth-success?redirect_url=${encodeURIComponent(r.redirect_url)}`),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,n.jsx)("h1",{className:"font-semibold text-2xl tracking-tight",children:a}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:d})]}),(0,n.jsx)(p,{fallbackRedirectUrl:t})]})}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74075:e=>{e.exports=require("zlib")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[5319,2923,25,7873,3319,2644,277,1988,1121,9652,7209,9148],()=>t(62955));module.exports=n})();