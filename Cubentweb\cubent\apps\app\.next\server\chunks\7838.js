"use strict";exports.id=7838,exports.ids=[7838],exports.modules={23056:(e,t,r)=>{r.d(t,{Sz:()=>i,TG:()=>n});var s=r(26239);let i=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),s=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||s||i};async function n(){try{let{headers:e}=await r.e(2644).then(r.bind(r,62644)),t=await e();return new s.NextRequest("https://placeholder.com",{headers:t})}catch(e){if(e&&i(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}},37081:(e,t,r)=>{function s(e){return async(...t)=>{let{data:r,errors:s}=await e(...t);if(s)throw s[0];return r}}function i(e){return(...t)=>{let{data:r,errors:s}=e(...t);if(s)throw s[0];return r}}r.d(t,{C:()=>s,R:()=>i})},37838:(e,t,r)=>{r.d(t,{j:()=>U});var s=r(8741),i=r(62923),n=r(54726),a=r(87553),o=r(3680);let l=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?l(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,l(t)])),null,2)).join(", "),d=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var i,n;for(let s of(console.log((i=e,`[clerk debug start: ${i}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return s.decode(i).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((n=e,`[clerk debug end: ${n}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},h=(e,t)=>(...r)=>{let s=("string"==typeof e?d(e,c):e)(),i=t(s);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var u=r(74365),f=r(37081),p=r(27322),y=r(6264);function g(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return p.r0.stringify(r,{pad:!1})}async function _(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,i=(0,p.hJ)(r.algorithm);if(!i)return{errors:[new y.xy(`Unsupported algorithm ${r.algorithm}`)]};let n=await (0,p.Fh)(t,i,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=g(a),l=g(e),c=`${o}.${l}`;try{let e=await p.fA.crypto.subtle.sign(i,n,s.encode(c));return{data:`${c}.${p.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new y.xy(e?.message)]}}}(0,f.C)(p.J0);var m=(0,f.R)(p.iU);(0,f.C)(_),(0,f.C)(p.nk);var w=r(97495),x=r(60606);function k(e,{treatPendingAsSignedOut:t=!0,...r}={}){var i,a,o;let l,c=(0,w.NE)(e,"AuthStatus"),d=(0,w.NE)(e,"AuthToken"),h=(0,w.NE)(e,"AuthMessage"),u=(0,w.NE)(e,"AuthReason"),f=(0,w.NE)(e,"AuthSignature");null==(i=r.logger)||i.debug("headers",{authStatus:c,authMessage:h,authReason:u});let p=(0,w._b)(e,s.AA.Headers.ClerkRequestData),y=(0,x.Kk)(p),g={secretKey:(null==r?void 0:r.secretKey)||y.secretKey||n.rB,publishableKey:y.publishableKey||n.At,apiUrl:n.H$,apiVersion:n.mG,authStatus:c,authMessage:h,authReason:u,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",g),c&&c===s.TD.SignedIn){(0,x._l)(d,g.secretKey,f);let e=m(d);null==(o=r.logger)||o.debug("jwt",e.raw),l=(0,s.Z5)(g,e.raw.text,e.payload)}else l=(0,s.wI)(g);return t&&"pending"===l.sessionStatus&&(l=(0,s.wI)(g,l.sessionStatus)),l}var v=r(68478);let b=({debugLoggerName:e,noAuthStatusMessage:t})=>h(e,e=>async(i,n)=>{if((0,a.zz)((0,w._b)(i,s.AA.Headers.EnableDebug))&&e.enable(),!(0,w.Zd)(i)){u.M&&(0,x.$K)(i,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,x.$K)(i,t)}return k(i,{...n,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>h(e,e=>(r,i)=>((0,a.zz)((0,w._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,x.$K)(r,t),k(r,{...i,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,v.AG)()});let A={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},S=e=>{var t,r;return!!e.headers.get(A.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(A.Headers.NextAction))},B=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||z(e)||M(e)},z=e=>!!e.headers.get(A.Headers.NextUrl)&&!S(e)||E(),E=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},M=e=>!!e.headers.get(A.Headers.NextjsData);var C=r(23056);let U=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,C.TG)(),a=async()=>{if(u.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await b({debugLoggerName:"auth()",noAuthStatusMessage:(0,v.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),l=(0,w.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,s.tl)(t),c=a.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||a.cookies.get(s.AA.Cookies.DevBrowser),d=(0,w._b)(t,s.AA.Headers.ClerkRequestData),h=(0,x.Kk)(d);return[(0,s.vH)({redirectAdapter:i.redirect,devBrowserToken:c,baseUrl:a.clerkUrl.toString(),publishableKey:h.publishableKey||n.At,signInUrl:h.signInUrl||n.qW,signUpUrl:h.signUpUrl||n.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==l?void 0:l.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};U.protect=async(...e)=>{r(1447);let t=await (0,C.TG)(),s=await U();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:i,request:n}=e;return async(...e)=>{var a,o,l,c,d,h;let u=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],f=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),p=(null==(d=e[0])?void 0:d.unauthorizedUrl)||(null==(h=e[1])?void 0:h.unauthorizedUrl),y=()=>p?s(p):i();return"pending"!==r.sessionStatus&&r.userId?u?"function"==typeof u?u(r.has)?r:y():r.has(u)?r:y():r:f?s(f):B(n)?t():i()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:i.notFound,redirect:i.redirect})(...e)}},60606:(e,t,r)=>{r.d(t,{$K:()=>eh,_l:()=>eu,Kk:()=>ep}),r(8741),r(44401),r(94051);var s=r(21757);r(26239);var i,n,a,o,l,c,d,h=r(65931),u=Object.defineProperty,f=(e,t,r)=>t in e?u(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,p=(null==(i="undefined"!=typeof globalThis?globalThis:void 0)?void 0:i.crypto)||(null==(n="undefined"!=typeof global?global:void 0)?void 0:n.crypto)||(null==(a="undefined"!=typeof window?window:void 0)?void 0:a.crypto)||(null==(o="undefined"!=typeof self?self:void 0)?void 0:o.crypto)||(null==(c=null==(l="undefined"!=typeof frames?frames:void 0)?void 0:l[0])?void 0:c.crypto);d=p?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(p.getRandomValues(new Uint32Array(1))[0]);return new g(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let s=0,i;s<e;s+=4){let e=r(0x100000000*(i||Math.random()));i=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new g(t,e)};var y=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},g=class extends y{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let s=0;s<e;s+=1)t[s>>>2]|=r[s]<<24-s%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=_){return e.stringify(this)}concat(e){let t=this.words,r=e.words,s=this.sigBytes,i=e.sigBytes;if(this.clamp(),s%4)for(let e=0;e<i;e+=1){let i=r[e>>>2]>>>24-e%4*8&255;t[s+e>>>2]|=i<<24-(s+e)%4*8}else for(let e=0;e<i;e+=4)t[s+e>>>2]=r[e>>>2];return this.sigBytes+=i,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>f(e,"symbol"!=typeof t?t+"":t,r))(g,"random",d);var _={stringify(e){let{words:t,sigBytes:r}=e,s=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;s.push((r>>>4).toString(16)),s.push((15&r).toString(16))}return s.join("")},parse(e){let t=e.length,r=[];for(let s=0;s<t;s+=2)r[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new g(r,t/2)}},m={stringify(e){let{words:t,sigBytes:r}=e,s=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;s.push(String.fromCharCode(r))}return s.join("")},parse(e){let t=e.length,r=[];for(let s=0;s<t;s+=1)r[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new g(r,t)}},w={stringify(e){try{return decodeURIComponent(escape(m.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>m.parse(unescape(encodeURIComponent(e)))},x=class extends y{constructor(){super(),this._minBufferSize=0}reset(){this._data=new g,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=w.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:s}=this,i=r.words,n=r.sigBytes,a=n/(4*s),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*s,l=Math.min(4*o,n);if(o){for(let e=0;e<o;e+=s)this._doProcessBlock(i,e);t=i.splice(0,o),r.sigBytes-=l}return new g(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},k=class extends x{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new y,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new v(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},v=class extends y{constructor(e,t){super();let r=new e;this._hasher=r;let s=t;"string"==typeof s&&(s=w.parse(s));let i=r.blockSize,n=4*i;s.sigBytes>n&&(s=r.finalize(t)),s.clamp();let a=s.clone();this._oKey=a;let o=s.clone();this._iKey=o;let l=a.words,c=o.words;for(let e=0;e<i;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;a.sigBytes=n,o.sigBytes=n,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},b=(e,t,r)=>{let s=[],i=0;for(let n=0;n<t;n+=1)if(n%4){let t=r[e.charCodeAt(n-1)]<<n%4*2|r[e.charCodeAt(n)]>>>6-n%4*2;s[i>>>2]|=t<<24-i%4*8,i+=1}return g.create(s,i)},A={stringify(e){let{words:t,sigBytes:r}=e,s=this._map;e.clamp();let i=[];for(let e=0;e<r;e+=3){let n=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)i.push(s.charAt(n>>>6*(3-t)&63))}let n=s.charAt(64);if(n)for(;i.length%4;)i.push(n);return i.join("")},parse(e){let t=e.length,r=this._map,s=this._reverseMap;if(!s){this._reverseMap=[],s=this._reverseMap;for(let e=0;e<r.length;e+=1)s[r.charCodeAt(e)]=e}let i=r.charAt(64);if(i){let r=e.indexOf(i);-1!==r&&(t=r)}return b(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},S=[];for(let e=0;e<64;e+=1)S[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var B=(e,t,r,s,i,n,a)=>{let o=e+(t&r|~t&s)+i+a;return(o<<n|o>>>32-n)+t},z=(e,t,r,s,i,n,a)=>{let o=e+(t&s|r&~s)+i+a;return(o<<n|o>>>32-n)+t},E=(e,t,r,s,i,n,a)=>{let o=e+(t^r^s)+i+a;return(o<<n|o>>>32-n)+t},M=(e,t,r,s,i,n,a)=>{let o=e+(r^(t|~s))+i+a;return(o<<n|o>>>32-n)+t},C=class extends k{_doReset(){this._hash=new g([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let s=t+r,i=e[s];e[s]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00}let r=this._hash.words,s=e[t+0],i=e[t+1],n=e[t+2],a=e[t+3],o=e[t+4],l=e[t+5],c=e[t+6],d=e[t+7],h=e[t+8],u=e[t+9],f=e[t+10],p=e[t+11],y=e[t+12],g=e[t+13],_=e[t+14],m=e[t+15],w=r[0],x=r[1],k=r[2],v=r[3];w=B(w,x,k,v,s,7,S[0]),v=B(v,w,x,k,i,12,S[1]),k=B(k,v,w,x,n,17,S[2]),x=B(x,k,v,w,a,22,S[3]),w=B(w,x,k,v,o,7,S[4]),v=B(v,w,x,k,l,12,S[5]),k=B(k,v,w,x,c,17,S[6]),x=B(x,k,v,w,d,22,S[7]),w=B(w,x,k,v,h,7,S[8]),v=B(v,w,x,k,u,12,S[9]),k=B(k,v,w,x,f,17,S[10]),x=B(x,k,v,w,p,22,S[11]),w=B(w,x,k,v,y,7,S[12]),v=B(v,w,x,k,g,12,S[13]),k=B(k,v,w,x,_,17,S[14]),x=B(x,k,v,w,m,22,S[15]),w=z(w,x,k,v,i,5,S[16]),v=z(v,w,x,k,c,9,S[17]),k=z(k,v,w,x,p,14,S[18]),x=z(x,k,v,w,s,20,S[19]),w=z(w,x,k,v,l,5,S[20]),v=z(v,w,x,k,f,9,S[21]),k=z(k,v,w,x,m,14,S[22]),x=z(x,k,v,w,o,20,S[23]),w=z(w,x,k,v,u,5,S[24]),v=z(v,w,x,k,_,9,S[25]),k=z(k,v,w,x,a,14,S[26]),x=z(x,k,v,w,h,20,S[27]),w=z(w,x,k,v,g,5,S[28]),v=z(v,w,x,k,n,9,S[29]),k=z(k,v,w,x,d,14,S[30]),x=z(x,k,v,w,y,20,S[31]),w=E(w,x,k,v,l,4,S[32]),v=E(v,w,x,k,h,11,S[33]),k=E(k,v,w,x,p,16,S[34]),x=E(x,k,v,w,_,23,S[35]),w=E(w,x,k,v,i,4,S[36]),v=E(v,w,x,k,o,11,S[37]),k=E(k,v,w,x,d,16,S[38]),x=E(x,k,v,w,f,23,S[39]),w=E(w,x,k,v,g,4,S[40]),v=E(v,w,x,k,s,11,S[41]),k=E(k,v,w,x,a,16,S[42]),x=E(x,k,v,w,c,23,S[43]),w=E(w,x,k,v,u,4,S[44]),v=E(v,w,x,k,y,11,S[45]),k=E(k,v,w,x,m,16,S[46]),x=E(x,k,v,w,n,23,S[47]),w=M(w,x,k,v,s,6,S[48]),v=M(v,w,x,k,d,10,S[49]),k=M(k,v,w,x,_,15,S[50]),x=M(x,k,v,w,l,21,S[51]),w=M(w,x,k,v,y,6,S[52]),v=M(v,w,x,k,a,10,S[53]),k=M(k,v,w,x,f,15,S[54]),x=M(x,k,v,w,i,21,S[55]),w=M(w,x,k,v,h,6,S[56]),v=M(v,w,x,k,m,10,S[57]),k=M(k,v,w,x,c,15,S[58]),x=M(x,k,v,w,g,21,S[59]),w=M(w,x,k,v,o,6,S[60]),v=M(v,w,x,k,p,10,S[61]),k=M(k,v,w,x,n,15,S[62]),x=M(x,k,v,w,u,21,S[63]),r[0]=r[0]+w|0,r[1]=r[1]+x|0,r[2]=r[2]+k|0,r[3]=r[3]+v|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;t[s>>>5]|=128<<24-s%32;let i=Math.floor(r/0x100000000);t[(s+64>>>9<<4)+15]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,t[(s+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let n=this._hash,a=n.words;for(let e=0;e<4;e+=1){let t=a[e];a[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return n}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};k._createHelper(C),k._createHmacHelper(C);var U=class extends y{constructor(e){super(),this.cfg=Object.assign(new y,{keySize:4,hasher:C,iterations:1},e)}compute(e,t){let r,{cfg:s}=this,i=s.hasher.create(),n=g.create(),a=n.words,{keySize:o,iterations:l}=s;for(;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(let e=1;e<l;e+=1)r=i.finalize(r),i.reset();n.concat(r)}return n.sigBytes=4*o,n}},j=class extends x{constructor(e,t,r){super(),this.cfg=Object.assign(new y,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?K:I;return{encrypt:(r,s,i)=>t(s).encrypt(e,r,s,i),decrypt:(r,s,i)=>t(s).decrypt(e,r,s,i)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};j._ENC_XFORM_MODE=1,j._DEC_XFORM_MODE=2,j.keySize=4,j.ivSize=4;var R=class extends y{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function D(e,t,r){let s,i=this._iv;i?(s=i,this._iv=void 0):s=this._prevBlock;for(let i=0;i<r;i+=1)e[t+i]^=s[i]}var O=class extends R{};O.Encryptor=class extends O{processBlock(e,t){let r=this._cipher,{blockSize:s}=r;D.call(this,e,t,s),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+s)}},O.Decryptor=class extends O{processBlock(e,t){let r=this._cipher,{blockSize:s}=r,i=e.slice(t,t+s);r.decryptBlock(e,t),D.call(this,e,t,s),this._prevBlock=i}};var F={pad(e,t){let r=4*t,s=r-e.sigBytes%r,i=s<<24|s<<16|s<<8|s,n=[];for(let e=0;e<s;e+=4)n.push(i);let a=g.create(n,s);e.concat(a)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},N=class extends j{constructor(e,t,r){super(e,t,Object.assign({mode:O,padding:F},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:s}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=s.createEncryptor:(e=s.createDecryptor,this._minBufferSize=1),this._mode=e.call(s,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},H=class extends y{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},I=class extends y{static encrypt(e,t,r,s){let i=Object.assign(new y,this.cfg,s),n=e.createEncryptor(r,i),a=n.finalize(t),o=n.cfg;return H.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:n.blockSize,formatter:i.format})}static decrypt(e,t,r,s){let i=t,n=Object.assign(new y,this.cfg,s);return i=this._parse(i,n.format),e.createDecryptor(r,n).finalize(i.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};I.cfg=Object.assign(new y,{format:{stringify(e){let t,{ciphertext:r,salt:s}=e;return(s?g.create([0x53616c74,0x65645f5f]).concat(s).concat(r):r).toString(A)},parse(e){let t,r=A.parse(e),s=r.words;return 0x53616c74===s[0]&&0x65645f5f===s[1]&&(t=g.create(s.slice(2,4)),s.splice(0,4),r.sigBytes-=16),H.create({ciphertext:r,salt:t})}}});var K=class extends I{static encrypt(e,t,r,s){let i=Object.assign(new y,this.cfg,s),n=i.kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=n.iv;let a=I.encrypt.call(this,e,t,n.key,i);return a.mixIn(n),a}static decrypt(e,t,r,s){let i=t,n=Object.assign(new y,this.cfg,s);i=this._parse(i,n.format);let a=n.kdf.execute(r,e.keySize,e.ivSize,i.salt,n.hasher);return n.iv=a.iv,I.decrypt.call(this,e,i,a.key,n)}};K.cfg=Object.assign(I.cfg,{kdf:{execute(e,t,r,s,i){let n,a=s;a||(a=g.random(8)),n=i?U.create({keySize:t+r,hasher:i}).compute(e,a):U.create({keySize:t+r}).compute(e,a);let o=g.create(n.words.slice(t),4*r);return n.sigBytes=4*t,H.create({key:n,iv:o,salt:a})}}});var T=[],$=[],P=[],q=[],J=[],L=[],G=[],X=[],V=[],Z=[],W=[];for(let e=0;e<256;e+=1)e<128?W[e]=e<<1:W[e]=e<<1^283;var Y=0,Q=0;for(let e=0;e<256;e+=1){let e=Q^Q<<1^Q<<2^Q<<3^Q<<4;e=e>>>8^255&e^99,T[Y]=e,$[e]=Y;let t=W[Y],r=W[t],s=W[r],i=257*W[e]^0x1010100*e;P[Y]=i<<24|i>>>8,q[Y]=i<<16|i>>>16,J[Y]=i<<8|i>>>24,L[Y]=i,i=0x1010101*s^65537*r^257*t^0x1010100*Y,G[e]=i<<24|i>>>8,X[e]=i<<16|i>>>16,V[e]=i<<8|i>>>24,Z[e]=i,Y?(Y=t^W[W[W[s^t]]],Q^=W[W[Q]]):Y=Q=1}var ee=[0,1,2,4,8,16,32,64,128,27,54],et=class extends N{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,s=t.sigBytes/4;this._nRounds=s+6;let i=(this._nRounds+1)*4;this._keySchedule=[];let n=this._keySchedule;for(let t=0;t<i;t+=1)t<s?n[t]=r[t]:(e=n[t-1],t%s?s>6&&t%s==4&&(e=T[e>>>24]<<24|T[e>>>16&255]<<16|T[e>>>8&255]<<8|T[255&e]):e=(T[(e=e<<8|e>>>24)>>>24]<<24|T[e>>>16&255]<<16|T[e>>>8&255]<<8|T[255&e])^ee[t/s|0]<<24,n[t]=n[t-s]^e);this._invKeySchedule=[];let a=this._invKeySchedule;for(let t=0;t<i;t+=1){let r=i-t;e=t%4?n[r]:n[r-4],t<4||r<=4?a[t]=e:a[t]=G[T[e>>>24]]^X[T[e>>>16&255]]^V[T[e>>>8&255]]^Z[T[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,P,q,J,L,T)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,G,X,V,Z,$),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,s,i,n,a,o){let l=this._nRounds,c=e[t]^r[0],d=e[t+1]^r[1],h=e[t+2]^r[2],u=e[t+3]^r[3],f=4;for(let e=1;e<l;e+=1){let e=s[c>>>24]^i[d>>>16&255]^n[h>>>8&255]^a[255&u]^r[f];f+=1;let t=s[d>>>24]^i[h>>>16&255]^n[u>>>8&255]^a[255&c]^r[f];f+=1;let o=s[h>>>24]^i[u>>>16&255]^n[c>>>8&255]^a[255&d]^r[f];f+=1;let l=s[u>>>24]^i[c>>>16&255]^n[d>>>8&255]^a[255&h]^r[f];f+=1,c=e,d=t,h=o,u=l}let p=(o[c>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&u])^r[f];f+=1;let y=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[u>>>8&255]<<8|o[255&c])^r[f];f+=1;let g=(o[h>>>24]<<24|o[u>>>16&255]<<16|o[c>>>8&255]<<8|o[255&d])^r[f];f+=1;let _=(o[u>>>24]<<24|o[c>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[f];f+=1,e[t]=p,e[t+1]=y,e[t+2]=g,e[t+3]=_}};et.keySize=8;var er=N._createHelper(et),es=[],ei=class extends k{_doReset(){this._hash=new g([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,s=r[0],i=r[1],n=r[2],a=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)es[r]=0|e[t+r];else{let e=es[r-3]^es[r-8]^es[r-14]^es[r-16];es[r]=e<<1|e>>>31}let l=(s<<5|s>>>27)+o+es[r];r<20?l+=(i&n|~i&a)+0x5a827999:r<40?l+=(i^n^a)+0x6ed9eba1:r<60?l+=(i&n|i&a|n&a)-0x70e44324:l+=(i^n^a)-0x359d3e2a,o=a,a=n,n=i<<30|i>>>2,i=s,s=l}r[0]=r[0]+s|0,r[1]=r[1]+i|0,r[2]=r[2]+n|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return t[s>>>5]|=128<<24-s%32,t[(s+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(s+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},en=(k._createHelper(ei),k._createHmacHelper(ei)),ea=r(54726),eo=r(68478),el=r(97495);let ec="x-middleware-override-headers",ed="x-middleware-request";function eh(e,t){if(!(0,el.Zd)(e))throw Error(t)}function eu(e,t,r){if(!r||en(e,t).toString()!==r)throw Error(eo._t)}let ef="clerk_keyless_dummy_key";function ep(e){if(!e)return{};let t=(0,s.Fj)()?ea.o7||ea.rB:ea.o7||ea.rB||ef;try{return eg(e,t)}catch{if(h.I)try{return eg(e,ef)}catch{ey()}ey()}}function ey(){if((0,s.Fj)())throw Error(eo.mJ);throw Error(eo.RC)}function eg(e,t){return JSON.parse(er.decrypt(e,t).toString(w))}},68478:(e,t,r)=>{r.d(t,{AG:()=>s,RC:()=>o,_t:()=>n,mJ:()=>a,sd:()=>i});let s=()=>i("getAuth"),i=(e="auth",t)=>`Clerk: ${e}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:
- ${t?[...t,""].join("\n- "):" "}clerkMiddleware() is used in your Next.js Middleware.
- Your Middleware matcher is configured to match this route or page.
- If you are using the src directory, make sure the Middleware file is inside of it.

For more details, see https://clerk.com/docs/quickstarts/nextjs
`,n="Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)",a="Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)",o=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`}};