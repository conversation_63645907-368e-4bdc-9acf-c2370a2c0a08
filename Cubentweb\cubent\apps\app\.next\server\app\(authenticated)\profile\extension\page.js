(()=>{var e={};e.id=7183,e.ids=[7183],e.modules={673:(e,t,s)=>{"use strict";s.d(t,{F:()=>a});var r=s(35371);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,a=(e,t)=>s=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:a,defaultVariants:o}=t,c=Object.keys(a).map(e=>{let t=null==s?void 0:s[e],r=null==o?void 0:o[e];if(null===t)return null;let i=n(t)||n(r);return a[e][i]}),l=s&&Object.entries(s).reduce((e,t)=>{let[s,r]=t;return void 0===r||(e[s]=r),e},{});return i(e,c,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:s,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...o,...l}[t]):({...o,...l})[t]===s})?[...e,s,r]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21117:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>l,routeModule:()=>u,tree:()=>c});var r=s(57864),n=s(94327),i=s(70814),a=s(17984),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);s.d(t,o);let c={children:["",{children:["(authenticated)",{children:["profile",{children:["extension",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,37870)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\page.tsx"],d={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(authenticated)/profile/extension/page",pathname:"/profile/extension",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},23979:(e,t,s)=>{Promise.resolve().then(s.bind(s,76426)),Promise.resolve().then(s.bind(s,84158)),Promise.resolve().then(s.t.bind(s,21034,23)),Promise.resolve().then(s.t.bind(s,49499,23)),Promise.resolve().then(s.bind(s,93665))},24700:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(94752);s(23233);var n=s(30409),i=s(673),a=s(58559);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:s,asChild:i=!1,...c}){let l=i?n.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,a.cn)(o({variant:t,size:s,className:e})),...c})}},24767:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(23233);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),a=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:i="",children:a,iconNode:d,...u},p)=>(0,r.createElement)("svg",{ref:p,...l,width:t,height:t,stroke:e,strokeWidth:n?24*Number(s)/Number(t):s,className:o("lucide",i),...!a&&!c(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(a)?a:[a]])),u=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},c)=>(0,r.createElement)(d,{ref:c,iconNode:t,className:o(`lucide-${n(a(e))}`,`lucide-${e}`,s),...i}));return s.displayName=a(e),s}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30409:(e,t,s)=>{"use strict";s.d(t,{DX:()=>a});var r=s(23233);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var i=s(94752),a=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...i}=e;if(r.isValidElement(s)){var a;let e,o,c=(a=s,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),l=function(e,t){let s={...t};for(let r in t){let n=e[r],i=t[r];/^on[A-Z]/.test(r)?n&&i?s[r]=(...e)=>{let t=i(...e);return n(...e),t}:n&&(s[r]=n):"style"===r?s[r]={...n,...i}:"className"===r&&(s[r]=[n,i].filter(Boolean).join(" "))}return{...e,...s}}(i,s.props);return s.type!==r.Fragment&&(l.ref=t?function(...e){return t=>{let s=!1,r=e.map(e=>{let r=n(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():n(e[t],null)}}}}(t,c):c),r.cloneElement(s,l)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:n,...a}=e,o=r.Children.toArray(n),l=o.find(c);if(l){let e=l.props.children,n=o.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,i.jsx)(t,{...a,ref:s,children:n})});return s.displayName=`${e}.Slot`,s}("Slot"),o=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34069:(e,t,s)=>{"use strict";s.d(t,{w:()=>c});var r=s(81121),n=s.n(r);let i="next-forge",a={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,c=({title:e,description:t,image:s,...r})=>{let c=`${e} | ${i}`,l={title:c,description:t,applicationName:i,metadataBase:o?new URL(`https://${o}`):void 0,authors:[a],creator:a.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:c},openGraph:{title:c,description:t,type:"website",siteName:i,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},d=n()(l,r);return s&&d.openGraph&&(d.openGraph.images=[{url:s,width:1200,height:630,alt:e}]),d}},34311:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19161).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},37870:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N,metadata:()=>w});var r=s(94752),n=s(37838),i=s(1359),a=s(18815),o=s(24700),c=s(46954),l=s(70483),d=s(34069),u=s(62923),p=s(49499),h=s.n(p),x=s(44793),m=s(24767);let f=(0,m.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),v=(0,m.A)("unplug",[["path",{d:"m19 5 3-3",key:"yk6iyv"}],["path",{d:"m2 22 3-3",key:"19mgm9"}],["path",{d:"M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z",key:"goz73y"}],["path",{d:"M7.5 13.5 10 11",key:"7xgeeb"}],["path",{d:"M10.5 16.5 13 14",key:"10btkg"}],["path",{d:"m12 6 6 6 2.3-2.3a2.4 2.4 0 0 0 0-3.4l-2.6-2.6a2.4 2.4 0 0 0-3.4 0Z",key:"1snsnr"}]]);var g=s(84158),y=s(76426);let b="Extension Management",j="Manage your VS Code extension connection and settings.",w=(0,d.w)({title:b,description:j}),N=async()=>{let{userId:e}=await (0,n.j)(),t=await (0,i.N)();e&&t||(0,u.redirect)("/sign-in");let s=await a.database.user.findUnique({where:{clerkId:e},include:{extensionSessions:{orderBy:{lastActiveAt:"desc"}}}});s||(0,u.notFound)();let d=s.extensionSessions.filter(e=>e.isActive),p=s.extensionSessions.filter(e=>!e.isActive);return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,r.jsxs)(h(),{href:"/profile",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:b}),(0,r.jsx)("p",{className:"text-muted-foreground",children:j})]})]}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Connection Status"}),(0,r.jsx)(c.BT,{children:"Current status of your VS Code extension connection"})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Extension Status:"}),(0,r.jsx)(l.E,{variant:d.length>0?"default":"secondary",children:d.length>0?"Connected":"Disconnected"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Active Sessions:"}),(0,r.jsx)("span",{children:d.length})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Terms Accepted:"}),(0,r.jsx)(l.E,{variant:s.termsAccepted?"default":"destructive",children:s.termsAccepted?"Yes":"No"})]}),s.lastExtensionSync&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Last Sync:"}),(0,r.jsx)("span",{className:"text-sm",children:new Date(s.lastExtensionSync).toLocaleString()})]}),(0,r.jsx)("div",{className:"pt-4 space-y-2",children:s.termsAccepted?0===d.length?(0,r.jsx)(o.$,{className:"w-full",children:"Connect Extension"}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(o.$,{variant:"outline",className:"w-full",children:[(0,r.jsx)(f,{className:"h-4 w-4 mr-2"}),"Refresh Connection"]}),(0,r.jsxs)(o.$,{variant:"destructive",className:"w-full",children:[(0,r.jsx)(v,{className:"h-4 w-4 mr-2"}),"Disconnect All Sessions"]})]}):(0,r.jsx)(o.$,{asChild:!0,className:"w-full",children:(0,r.jsx)(h(),{href:"/terms",children:"Accept Terms to Connect"})})})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"API Key"}),(0,r.jsx)(c.BT,{children:"Manage your extension API key for secure communication"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(y.ApiKeyManager,{apiKey:s.extensionApiKey,userId:s.id})})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Extension Sessions"}),(0,r.jsx)(c.BT,{children:"View and manage your active and past extension sessions"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(g.ExtensionSessionsList,{activeSessions:d,inactiveSessions:p})})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Connection Instructions"}),(0,r.jsx)(c.BT,{children:"How to connect your VS Code extension to this website"})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-semibold",children:"Automatic Connection (Recommended)"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm text-muted-foreground",children:[(0,r.jsx)("li",{children:"Make sure you have accepted the terms of service on this website"}),(0,r.jsx)("li",{children:'Click the "Connect Extension" button above'}),(0,r.jsx)("li",{children:"VS Code will open automatically and prompt you to connect"}),(0,r.jsx)("li",{children:"Follow the prompts in VS Code to complete the connection"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-semibold",children:"Manual Connection"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm text-muted-foreground",children:[(0,r.jsx)("li",{children:"Open VS Code with the Cubent extension installed"}),(0,r.jsx)("li",{children:"Open the Command Palette (Ctrl+Shift+P / Cmd+Shift+P)"}),(0,r.jsx)("li",{children:'Run the command "Cubent: Connect to Website"'}),(0,r.jsx)("li",{children:"Enter this website URL when prompted"}),(0,r.jsx)("li",{children:"Complete the authentication flow"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-semibold",children:"Troubleshooting"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-muted-foreground",children:[(0,r.jsx)("li",{children:"Make sure you're signed in to the same account in both VS Code and this website"}),(0,r.jsx)("li",{children:"Check that the Cubent extension is installed and up to date"}),(0,r.jsx)("li",{children:"Try refreshing the connection if it appears stuck"}),(0,r.jsx)("li",{children:"Contact support if you continue to experience issues"})]})]})]})]})]})}},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},43903:(e,t,s)=>{"use strict";s.d(t,{ApiKeyManager:()=>h});var r=s(99730),n=s(74938),i=s(99752),a=s(57752),o=s(22683),c=s(19161);let l=(0,c.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),d=(0,c.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var u=s(60628),p=s(71265);function h({apiKey:e,userId:t}){let[s,c]=(0,a.useState)(!1),[h,x]=(0,a.useState)(!1),m=async()=>{if(e)try{await navigator.clipboard.writeText(e),o.toast.success("API key copied to clipboard")}catch(e){o.toast.error("Failed to copy API key")}},f=async()=>{x(!0);try{(await fetch("/api/extension/generate-key",{method:"POST"})).ok?(o.toast.success("New API key generated successfully"),window.location.reload()):o.toast.error("Failed to generate new API key")}catch(e){o.toast.error("Failed to generate new API key")}finally{x(!1)}},v=e?`${e.slice(0,12)}${"*".repeat(e.length-16)}${e.slice(-4)}`:"";return(0,r.jsx)("div",{className:"space-y-4",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Your API Key"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.p,{type:"text",value:s?e:v,readOnly:!0,className:"font-mono text-sm"}),(0,r.jsx)(n.$,{variant:"outline",size:"icon",onClick:()=>c(!s),children:s?(0,r.jsx)(l,{className:"h-4 w-4"}):(0,r.jsx)(d,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"outline",size:"icon",onClick:m,children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsxs)(n.$,{variant:"outline",onClick:f,disabled:h,className:"flex-1",children:[(0,r.jsx)(p.A,{className:`h-4 w-4 mr-2 ${h?"animate-spin":""}`}),h?"Generating...":"Regenerate Key"]})}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,r.jsx)("p",{children:"• Keep your API key secure and don't share it with others"}),(0,r.jsx)("p",{children:"• Regenerating will invalidate the current key"}),(0,r.jsx)("p",{children:"• The extension will need to reconnect after regenerating"})]})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"No API key generated yet. Connect your extension to generate one automatically."}),(0,r.jsxs)(n.$,{onClick:f,disabled:h,className:"w-full",children:[(0,r.jsx)(p.A,{className:`h-4 w-4 mr-2 ${h?"animate-spin":""}`}),h?"Generating...":"Generate API Key"]})]})})}},44708:e=>{"use strict";e.exports=require("node:https")},44793:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(24767).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},46954:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>a});var r=s(94752);s(23233);var n=s(58559);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},48161:e=>{"use strict";e.exports=require("node:os")},49499:(e,t,s)=>{let{createProxy:r}=s(20867);e.exports=r("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\client\\app-dir\\link.js")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59988:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>r.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>r.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>n.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>r.at});var r=s(54841),n=s(44089)},62067:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19161).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70483:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var r=s(94752);s(23233);var n=s(30409),i=s(673),a=s(58559);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:s=!1,...i}){let c=s?n.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,a.cn)(o({variant:t}),e),...i})}},71265:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19161).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76426:(e,t,s)=>{"use strict";s.d(t,{ApiKeyManager:()=>r});let r=(0,s(6340).registerClientReference)(function(){throw Error("Attempted to call ApiKeyManager() from the server but ApiKeyManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\components\\api-key-manager.tsx","ApiKeyManager")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79444:(e,t,s)=>{"use strict";s.d(t,{ExtensionSessionsList:()=>x});var r=s(99730),n=s(74938),i=s(87785),a=s(85733),o=s(57752),c=s(22683),l=s(62067),d=s(19161);let u=(0,d.A)("unplug",[["path",{d:"m19 5 3-3",key:"yk6iyv"}],["path",{d:"m2 22 3-3",key:"19mgm9"}],["path",{d:"M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z",key:"goz73y"}],["path",{d:"M7.5 13.5 10 11",key:"7xgeeb"}],["path",{d:"M10.5 16.5 13 14",key:"10btkg"}],["path",{d:"m12 6 6 6 2.3-2.3a2.4 2.4 0 0 0 0-3.4l-2.6-2.6a2.4 2.4 0 0 0-3.4 0Z",key:"1snsnr"}]]),p=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var h=s(34311);function x({activeSessions:e,inactiveSessions:t}){let[s,d]=(0,o.useState)(null),x=async e=>{d(e);try{(await fetch(`/api/extension/sessions?sessionId=${e}`,{method:"DELETE"})).ok?(c.toast.success("Session disconnected successfully"),window.location.reload()):c.toast.error("Failed to disconnect session")}catch(e){c.toast.error("Failed to disconnect session")}finally{d(null)}},m=e=>new Date(e).toLocaleString(),f=e=>{let t=new Date().getTime()-new Date(e).getTime(),s=Math.floor(t/6e4),r=Math.floor(t/36e5),n=Math.floor(t/864e5);return s<1?"Just now":s<60?`${s}m ago`:r<24?`${r}h ago`:`${n}d ago`};return 0===e.length&&0===t.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)("p",{children:"No extension sessions found."}),(0,r.jsx)("p",{className:"text-sm",children:"Connect your VS Code extension to see sessions here."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[e.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("h3",{className:"font-semibold",children:["Active Sessions (",e.length,")"]})]}),(0,r.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.E,{variant:"default",children:"Active"}),(0,r.jsxs)("span",{className:"font-mono text-sm",children:[e.sessionId.slice(0,8),"..."]})]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("span",{children:["Connected: ",m(e.createdAt)]}),(0,r.jsxs)("span",{children:["Last seen: ",f(e.lastActiveAt)]})]})})]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>x(e.sessionId),disabled:s===e.sessionId,children:[(0,r.jsx)(u,{className:"h-4 w-4 mr-2"}),s===e.sessionId?"Disconnecting...":"Disconnect"]})]},e.id))})]}),e.length>0&&t.length>0&&(0,r.jsx)(a.Separator,{}),t.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("h3",{className:"font-semibold",children:["Past Sessions (",t.length,")"]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[t.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg opacity-60",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.E,{variant:"secondary",children:"Inactive"}),(0,r.jsxs)("span",{className:"font-mono text-sm",children:[e.sessionId.slice(0,8),"..."]})]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("span",{children:["Connected: ",m(e.createdAt)]}),(0,r.jsxs)("span",{children:["Last seen: ",m(e.lastActiveAt)]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Disconnected"})]})]},e.id)),t.length>5&&(0,r.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["... and ",t.length-5," more past sessions"]})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83651:(e,t,s)=>{Promise.resolve().then(s.bind(s,43903)),Promise.resolve().then(s.bind(s,79444)),Promise.resolve().then(s.bind(s,86332)),Promise.resolve().then(s.t.bind(s,41265,23)),Promise.resolve().then(s.bind(s,22683))},83997:e=>{"use strict";e.exports=require("tty")},84158:(e,t,s)=>{"use strict";s.d(t,{ExtensionSessionsList:()=>r});let r=(0,s(6340).registerClientReference)(function(){throw Error("Attempted to call ExtensionSessionsList() from the server but ExtensionSessionsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\components\\extension-sessions-list.tsx","ExtensionSessionsList")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87785:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var r=s(99730);s(57752);var n=s(58576),i=s(72795),a=s(83590);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:s=!1,...i}){let c=s?n.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,a.cn)(o({variant:t}),e),...i})}},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,7873,3887,5480,1359,3319,2644,277,1988,5432,4841,8482,1121,864,7209,6648],()=>s(21117));module.exports=r})();