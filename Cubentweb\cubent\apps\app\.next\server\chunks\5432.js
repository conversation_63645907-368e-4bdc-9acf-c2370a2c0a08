"use strict";exports.id=5432,exports.ids=[5432],exports.modules={3560:(e,t,r)=>{r.d(t,{A:()=>i});let i=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)}},6798:(e,t,r)=>{r.d(t,{M3:()=>o,W2:()=>s,ll:()=>a,t9:()=>n});var i=r(67407);function a(e){return(0,i.A)(e)&&"string"==typeof e.kty}function s(e){return"oct"!==e.kty&&"string"==typeof e.d}function o(e){return"oct"!==e.kty&&void 0===e.d}function n(e){return a(e)&&"oct"===e.kty&&"string"==typeof e.k}},8392:(e,t,r)=>{r.d(t,{Jt:()=>X});var i=r(13690),a=r(77598),s=r(57975),o=r(14295);function n(e){switch(e){case"PS256":case"RS256":case"ES256":case"ES256K":return"sha256";case"PS384":case"RS384":case"ES384":return"sha384";case"PS512":case"RS512":case"ES512":return"sha512";case"Ed25519":case"EdDSA":return;default:throw new o.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}var l=r(46524),c=r(13317);let u=new Map([["ES256","P-256"],["ES256K","secp256k1"],["ES384","P-384"],["ES512","P-521"]]);function d(e,t){let r,i,s,n;if(t instanceof a.KeyObject)r=t.asymmetricKeyType,i=t.asymmetricKeyDetails;else switch(s=!0,t.kty){case"RSA":r="rsa";break;case"EC":r="ec";break;case"OKP":if("Ed25519"===t.crv){r="ed25519";break}if("Ed448"===t.crv){r="ed448";break}throw TypeError("Invalid key for this operation, its crv must be Ed25519 or Ed448");default:throw TypeError("Invalid key for this operation, its kty must be RSA, OKP, or EC")}switch(e){case"Ed25519":if("ed25519"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519");break;case"EdDSA":if(!["ed25519","ed448"].includes(r))throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448");break;case"RS256":case"RS384":case"RS512":if("rsa"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");(0,c.A)(t,e);break;case"PS256":case"PS384":case"PS512":if("rsa-pss"===r){let{hashAlgorithm:t,mgf1HashAlgorithm:r,saltLength:a}=i,s=parseInt(e.slice(-3),10);if(void 0!==t&&(t!==`sha${s}`||r!==t))throw TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}`);if(void 0!==a&&a>s>>3)throw TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}`)}else if("rsa"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss");(0,c.A)(t,e),n={padding:a.constants.RSA_PKCS1_PSS_PADDING,saltLength:a.constants.RSA_PSS_SALTLEN_DIGEST};break;case"ES256":case"ES256K":case"ES384":case"ES512":{if("ec"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ec");let i=(0,l.A)(t),a=u.get(e);if(i!==a)throw TypeError(`Invalid key curve for the algorithm, its curve must be ${a}, got ${i}`);n={dsaEncoding:"ieee-p1363"};break}default:throw new o.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`)}return s?{format:"jwk",key:t,...n}:n?{...n,key:t}:t}var h=r(89605),f=r(25840),p=r(63669),y=r(69472),g=r(6798);function m(e,t,r){if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError((0,p.A)(t,...y.g));return(0,a.createSecretKey)(t)}if(t instanceof a.KeyObject)return t;if((0,h.R)(t))return(0,f.Y)(t,e,r),a.KeyObject.from(t);if(g.ll(t))return e.startsWith("HS")?(0,a.createSecretKey)(Buffer.from(t.k,"base64url")):t;throw TypeError((0,p.A)(t,...y.g,"Uint8Array","JSON Web Key"))}let w=(0,s.promisify)(a.sign),b=async(e,t,r)=>{let i=m(e,t,"sign");if(e.startsWith("HS")){let t=a.createHmac(function(e){switch(e){case"HS256":return"sha256";case"HS384":return"sha384";case"HS512":return"sha512";default:throw new o.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}(e),i);return t.update(r),t.digest()}return w(n(e),r,d(e,i))},v=(0,s.promisify)(a.verify),A=async(e,t,r,i)=>{let s=m(e,t,"verify");if(e.startsWith("HS")){let t=await b(e,s,i);try{return a.timingSafeEqual(r,t)}catch{return!1}}let o=n(e),l=d(e,s);try{return await v(o,i,l,r)}catch{return!1}};var E=r(45271),S=r(61824),_=r(67407),P=r(70556),k=r(49010),C=r(3560),T=r(76285);async function I(e,t,r){let a,s;if(!(0,_.A)(e))throw new o.Ye("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new o.Ye('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new o.Ye("JWS Protected Header incorrect type");if(void 0===e.payload)throw new o.Ye("JWS Payload missing");if("string"!=typeof e.signature)throw new o.Ye("JWS Signature missing or incorrect type");if(void 0!==e.header&&!(0,_.A)(e.header))throw new o.Ye("JWS Unprotected Header incorrect type");let n={};if(e.protected)try{let t=(0,i.D4)(e.protected);n=JSON.parse(E.D0.decode(t))}catch{throw new o.Ye("JWS Protected Header is invalid")}if(!(0,S.A)(n,e.header))throw new o.Ye("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let l={...n,...e.header},c=(0,k.A)(o.Ye,new Map([["b64",!0]]),r?.crit,n,l),u=!0;if(c.has("b64")&&"boolean"!=typeof(u=n.b64))throw new o.Ye('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:d}=l;if("string"!=typeof d||!d)throw new o.Ye('JWS "alg" (Algorithm) Header Parameter missing or invalid');let h=r&&(0,C.A)("algorithms",r.algorithms);if(h&&!h.has(d))throw new o.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(u){if("string"!=typeof e.payload)throw new o.Ye("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new o.Ye("JWS Payload must be a string or an Uint8Array instance");let f=!1;"function"==typeof t?(t=await t(n,e),f=!0,(0,P.I)(d,t,"verify"),(0,g.ll)(t)&&(t=await (0,T.Og)(t,d))):(0,P.I)(d,t,"verify");let p=(0,E.xW)(E.Rd.encode(e.protected??""),E.Rd.encode("."),"string"==typeof e.payload?E.Rd.encode(e.payload):e.payload);try{a=(0,i.D4)(e.signature)}catch{throw new o.Ye("Failed to base64url decode the signature")}if(!await A(d,t,a,p))throw new o.h2;if(u)try{s=(0,i.D4)(e.payload)}catch{throw new o.Ye("Failed to base64url decode the payload")}else s="string"==typeof e.payload?E.Rd.encode(e.payload):e.payload;let y={payload:s};return(void 0!==e.protected&&(y.protectedHeader=n),void 0!==e.header&&(y.unprotectedHeader=e.header),f)?{...y,key:t}:y}async function x(e,t,r){if(e instanceof Uint8Array&&(e=E.D0.decode(e)),"string"!=typeof e)throw new o.Ye("Compact JWS must be a string or Uint8Array");let{0:i,1:a,2:s,length:n}=e.split(".");if(3!==n)throw new o.Ye("Invalid Compact JWS");let l=await I({payload:a,protected:i,signature:s},t,r),c={payload:l.payload,protectedHeader:l.protectedHeader};return"function"==typeof t?{...c,key:l.key}:c}var F=r(44214);class O{_payload;_protectedHeader;_unprotectedHeader;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){let r;if(!this._protectedHeader&&!this._unprotectedHeader)throw new o.Ye("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!(0,S.A)(this._protectedHeader,this._unprotectedHeader))throw new o.Ye("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let a={...this._protectedHeader,...this._unprotectedHeader},s=(0,k.A)(o.Ye,new Map([["b64",!0]]),t?.crit,this._protectedHeader,a),n=!0;if(s.has("b64")&&"boolean"!=typeof(n=this._protectedHeader.b64))throw new o.Ye('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:l}=a;if("string"!=typeof l||!l)throw new o.Ye('JWS "alg" (Algorithm) Header Parameter missing or invalid');(0,P.I)(l,e,"sign");let c=this._payload;n&&(c=E.Rd.encode((0,i.lF)(c))),r=this._protectedHeader?E.Rd.encode((0,i.lF)(JSON.stringify(this._protectedHeader))):E.Rd.encode("");let u=(0,E.xW)(r,E.Rd.encode("."),c),d=await b(l,e,u),h={signature:(0,i.lF)(d),payload:""};return n&&(h.payload=E.D0.decode(c)),this._unprotectedHeader&&(h.header=this._unprotectedHeader),this._protectedHeader&&(h.protected=E.D0.decode(r)),h}}class ${_flattened;constructor(e){this._flattened=new O(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let r=await this._flattened.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}function R(e,t,{cachePromiseRejection:r=!1}={}){let i,a,s=!1;return function(...o){return s&&t(o,i)||(a=e.apply(this,o),!r&&a.catch&&a.catch(()=>s=!1),s=!0,i=o),a}}var H=R((e,t)=>x(e,F.D(t),{algorithms:["HS256"]}),(e,t)=>e[0]===t[0]&&e[1]===t[1],{cachePromiseRejection:!0});async function D(e,t,r){var i,a;let{payload:s}=await H(e,r),[o,n]=s.length===t.length?[s]:(i=s,a=t.length,[i.slice(0,a),i.slice(a)]),l=n?JSON.parse(`[${new TextDecoder().decode(n)}]`):null,c=0;return o.reduce((e,r,i)=>{let a=t[i];if(!a)throw Error(`flags: No flag at index ${i}`);switch(r){case 253:e[a.key]=!1;break;case 254:e[a.key]=!0;break;case 255:e[a.key]=l[c++];break;case 252:e[a.key]=null;break;default:e[a.key]=a.options?.[r]?.value}return e},{})}R((e,t)=>new $(e).setProtectedHeader({alg:"HS256"}).sign(F.D(t)),(e,t)=>e[0].length===t[0].length&&e[0].every((e,r)=>t[0][r]===e)&&e[1]===t[1],{cachePromiseRejection:!0});var j=r(26818);function M(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}var W=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of function(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>M(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>M(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}};Symbol.for("edge-runtime.inspect.custom");var K=R(e=>(0,j.PO)(e),(e,t)=>e[0]===t[0],{cachePromiseRejection:!0});async function L(e){return"string"==typeof e&&""!==e?await K(e)??null:null}async function J(e,t,r=process.env.FLAGS_SECRET){if(!r)throw Error("flags: Can not serialize due to missing secret");return D(t,e,r)}async function U(e,t,r,i=process.env.FLAGS_SECRET){if(!i)throw Error("flags: getPrecomputed was called without a secret. Please set FLAGS_SECRET environment variable.");let a=await J(t,r,i);return Array.isArray(e)?e.map(e=>a[e.key]):a[e.key]}var N=Symbol.for("react.postpone");var B=new WeakMap;function G(e,t,r,i){let a=B.get(e);if(!a)return void B.set(e,new Map([[t,new Map([[r,i]])]]));let s=a.get(t);if(!s)return void a.set(t,new Map([[r,i]]));s.set(r,i)}var V=new WeakMap,q=new WeakMap,z=new WeakMap,Y=new WeakMap;async function Q(e,t,r,i){if(!e)return;if("function"!=typeof e)return e;let a=Y.get(t);if(a)return e(...a);let s=[{headers:r,cookies:i}];return Y.set(t,s),e(...s)}function X(e){var t;let i=function(t){if("function"==typeof e.decide)return e.decide(t);if("function"==typeof e.adapter?.decide)return e.adapter.decide({key:e.key,...t});throw Error(`flags: No decide function provided for ${e.key}`)},a=function(t){return"function"==typeof e.identify?e.identify(t):"function"==typeof e.adapter?.identify?e.adapter.identify(t):e.identify},s=async function(t){let a,s,o;if(t.request){let e=function(e){let t=V.get(e);if(void 0!==t)return t;let r=new Headers;for(let[t,i]of Object.entries(e))Array.isArray(i)?i.forEach(e=>r.append(t,e)):void 0!==i&&r.append(t,i);return V.set(e,r),r}(t.request.headers);a=function(e){let t=q.get(e);if(void 0!==t)return t;let r=j.oF.seal(e);return q.set(e,r),r}(e),s=function(e){let t=z.get(e);if(void 0!==t)return t;let r=j.Ck.seal(new W(e));return z.set(e,r),r}(e),o=t.request.headers}else{let{headers:e,cookies:t}=await r.e(2644).then(r.bind(r,62644)),[i,n]=await Promise.all([e(),t()]);a=i,s=n,o=i}let n=await L(s.get("vercel-flag-overrides")?.value),l=await Q(t.identify,o,a,s),c=JSON.stringify(l)??"",u=function(e,t,r){let i=B.get(e)?.get(t);if(i)return i.get(r)}(a,e.key,c);if(void 0!==u)return(0,j.J_)("method","cached"),await u;if(n&&void 0!==n[e.key]){(0,j.J_)("method","override");let t=n[e.key];return G(a,e.key,c,Promise.resolve(t)),(0,j.HC)(e.key,t,{reason:"override"}),t}let d=(async()=>i({defaultValue:e.defaultValue,headers:a,cookies:s,entities:l}))().then(t=>{if(void 0!==t)return t;if(void 0!==e.defaultValue)return e.defaultValue;throw Error(`flags: Flag "${e.key}" must have a defaultValue or a decide function that returns a value`)},t=>{if(function(e){if("object"==typeof e&&null!==e&&"$$typeof"in e&&e.$$typeof===N)return!0;if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";")[0];return"NEXT_REDIRECT"===t||"DYNAMIC_SERVER_USAGE"===t||"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t||"NEXT_NOT_FOUND"===t}(t))throw t;if(void 0!==e.defaultValue)return console.warn(`flags: Flag "${e.key}" is falling back to its defaultValue after catching the following error`,t),e.defaultValue;throw console.warn(`flags: Flag "${e.key}" could not be evaluated`),t});G(a,e.key,c,d);let h=await d;return e.config?.reportValue!==!1&&(0,j.aC)(e.key,h),h},o=e.origin?e.origin:"function"==typeof e.adapter?.origin?e.adapter.origin(e.key):e.adapter?.origin,n=(0,j.uP)(async(...e)=>{if((0,j.J_)("method","decided"),"string"==typeof e[0]&&Array.isArray(e[1])){let[t,r,i]=e;if(t&&r)return(0,j.J_)("method","precomputed"),U(n,r,t,i)}if(e[0]&&"object"==typeof e[0]&&"headers"in e[0]){let[t]=e;return s({identify:a,request:t})}return s({identify:a,request:void 0})},{name:"flag",isVerboseTrace:!1,attributes:{key:e.key}});return n.key=e.key,n.defaultValue=e.defaultValue,n.origin=o,n.options=Array.isArray(t=e.options)?t.map(e=>"boolean"==typeof e||"number"==typeof e||"string"==typeof e||null===e?{value:e}:e):t,n.description=e.description,n.identify=a?(0,j.uP)(a,{isVerboseTrace:!1,name:"identify",attributes:{key:e.key}}):a,n.decide=(0,j.uP)(i,{isVerboseTrace:!1,name:"decide",attributes:{key:e.key}}),n.run=(0,j.uP)(s,{isVerboseTrace:!1,name:"run",attributes:{key:e.key}}),n}},13317:(e,t,r)=>{r.d(t,{A:()=>a});var i=r(77598);let a=(e,t)=>{let r;try{r=e instanceof i.KeyObject?e.asymmetricKeyDetails?.modulusLength:Buffer.from(e.n,"base64url").byteLength<<3}catch{}if("number"!=typeof r||r<2048)throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)}},13690:(e,t,r)=>{r.d(t,{D4:()=>o,lF:()=>s});var i=r(4573),a=r(45271);let s=e=>i.Buffer.from(e).toString("base64url"),o=e=>new Uint8Array(i.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=a.D0.decode(t)),t}(e),"base64url"))},14295:(e,t,r)=>{r.d(t,{Dp:()=>d,Rb:()=>o,T0:()=>n,Ye:()=>u,aA:()=>c,h2:()=>f,ie:()=>a,n:()=>s,xO:()=>l});class i extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class a extends i{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class s extends i{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class o extends i{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class n extends i{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class l extends i{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class c extends i{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class u extends i{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class d extends i{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class h extends i{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class f extends i{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}},22556:(e,t,r)=>{r.d(t,{A:()=>a});var i=r(57975);let a=e=>i.types.isKeyObject(e)},25840:(e,t,r)=>{function i(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function a(e,t){return e.name===t}function s(e){return parseInt(e.name.slice(4),10)}function o(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}function n(e,t,...r){switch(t){case"HS256":case"HS384":case"HS512":{if(!a(e.algorithm,"HMAC"))throw i("HMAC");let r=parseInt(t.slice(2),10);if(s(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!a(e.algorithm,"RSASSA-PKCS1-v1_5"))throw i("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(s(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!a(e.algorithm,"RSA-PSS"))throw i("RSA-PSS");let r=parseInt(t.slice(2),10);if(s(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==e.algorithm.name&&"Ed448"!==e.algorithm.name)throw i("Ed25519 or Ed448");break;case"Ed25519":if(!a(e.algorithm,"Ed25519"))throw i("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!a(e.algorithm,"ECDSA"))throw i("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw i(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}o(e,r)}function l(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!a(e.algorithm,"AES-GCM"))throw i("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw i(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!a(e.algorithm,"AES-KW"))throw i("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw i(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw i("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!a(e.algorithm,"PBKDF2"))throw i("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!a(e.algorithm,"RSA-OAEP"))throw i("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(s(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}o(e,r)}r.d(t,{$:()=>l,Y:()=>n})},26818:(e,t,r)=>{let i;r.d(t,{oF:()=>ep,Ck:()=>eg,PO:()=>en,HC:()=>ed,aC:()=>eu,J_:()=>ei,uP:()=>ea,uX:()=>ec});var a=r(84297),s=r(44214),o=r(13690),n=r(77598),l=r(14295);let c=(e,t)=>{if(t.length<<3!==function(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new l.T0(`Unsupported JWE Algorithm: ${e}`)}}(e))throw new l.aA("Invalid Initialization Vector length")};var u=r(22556);let d=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new l.T0(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new l.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if((0,u.A)(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new l.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")};var h=r(45271);let f=n.timingSafeEqual;var p=r(89605),y=r(25840),g=r(63669);let m=e=>(i||=new Set((0,n.getCiphers)())).has(e);var w=r(69472);let b=(e,t,r,i,a,s)=>{let o;if((0,p.R)(t))(0,y.$)(t,e,"decrypt"),o=n.KeyObject.from(t);else if(t instanceof Uint8Array||(0,u.A)(t))o=t;else throw TypeError((0,g.A)(t,...w.g,"Uint8Array"));if(!i)throw new l.aA("JWE Initialization Vector missing");if(!a)throw new l.aA("JWE Authentication Tag missing");switch(d(e,o),c(e,i),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,i,a,s){let o,c,d=parseInt(e.slice(1,4),10);(0,u.A)(t)&&(t=t.export());let p=t.subarray(d>>3),y=t.subarray(0,d>>3),g=parseInt(e.slice(-3),10),w=`aes-${d}-cbc`;if(!m(w))throw new l.T0(`alg ${e} is not supported by your javascript runtime`);let b=function(e,t,r,i,a,s){let o=(0,h.xW)(e,t,r,(0,h.mx)(e.length<<3)),l=(0,n.createHmac)(`sha${i}`,a);return l.update(o),l.digest().slice(0,s>>3)}(s,i,r,g,y,d);try{o=f(a,b)}catch{}if(!o)throw new l.xO;try{let e=(0,n.createDecipheriv)(w,p,i);c=(0,h.xW)(e.update(r),e.final())}catch{}if(!c)throw new l.xO;return c}(e,o,r,i,a,s);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,i,a,s){let o=parseInt(e.slice(1,4),10),c=`aes-${o}-gcm`;if(!m(c))throw new l.T0(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,n.createDecipheriv)(c,t,i,{authTagLength:16});e.setAuthTag(a),s.byteLength&&e.setAAD(s,{plaintextLength:r.length});let o=e.update(r);return e.final(),o}catch{throw new l.xO}}(e,o,r,i,a,s);default:throw new l.T0("Unsupported JWE Content Encryption Algorithm")}};var v=r(61824),A=r(67407),E=r(4573);let S=(e,t,r)=>{let i=parseInt(e.slice(1,4),10),a=`aes${i}-wrap`;if(!m(a))throw new l.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`);let s=function(e,t,r){if((0,u.A)(e))return e;if(e instanceof Uint8Array)return(0,n.createSecretKey)(e);if((0,p.R)(e))return(0,y.$)(e,t,r),n.KeyObject.from(e);throw TypeError((0,g.A)(e,...w.g,"Uint8Array"))}(t,e,"unwrapKey");!function(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}(s,e);let o=(0,n.createDecipheriv)(a,s,E.Buffer.alloc(8,166));return(0,h.xW)(o.update(r),o.final())};var _=r(57975),P=r(46524);async function k(e,t,r,i,a=new Uint8Array(0),s=new Uint8Array(0)){let o,l;if((0,p.R)(e))(0,y.$)(e,"ECDH"),o=n.KeyObject.from(e);else if((0,u.A)(e))o=e;else throw TypeError((0,g.A)(e,...w.g));if((0,p.R)(t))(0,y.$)(t,"ECDH","deriveBits"),l=n.KeyObject.from(t);else if((0,u.A)(t))l=t;else throw TypeError((0,g.A)(t,...w.g));let c=(0,h.xW)((0,h.Kp)(h.Rd.encode(r)),(0,h.Kp)(a),(0,h.Kp)(s),(0,h.VS)(i)),d=(0,n.diffieHellman)({privateKey:l,publicKey:o});return(0,h.yI)(d,i,c)}(0,_.promisify)(n.generateKeyPair);let C=e=>["P-256","P-384","P-521","X25519","X448"].includes((0,P.A)(e)),T=(0,_.promisify)(n.pbkdf2),I=async(e,t,r,i,a)=>{!function(e){if(!(e instanceof Uint8Array)||e.length<8)throw new l.aA("PBES2 Salt Input must be 8 or more octets")}(a);let s=(0,h.MT)(e,a),o=parseInt(e.slice(13,16),10)>>3,c=function(e,t){if((0,u.A)(e))return e.export();if(e instanceof Uint8Array)return e;if((0,p.R)(e))return(0,y.$)(e,t,"deriveBits","deriveKey"),n.KeyObject.from(e).export();throw TypeError((0,g.A)(e,...w.g,"Uint8Array"))}(t,e),d=await T(c,s,i,o,`sha${e.slice(8,11)}`);return S(e.slice(-6),d,r)};var x=r(13317);let F=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");(0,x.A)(e,t)},O=(0,_.deprecate)(()=>n.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),$=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return n.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return O();default:return}},R=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}},H=(e,t,r)=>{let i=$(e),a=R(e),s=function(e,t,...r){if((0,u.A)(e))return e;if((0,p.R)(e))return(0,y.$)(e,t,...r),n.KeyObject.from(e);throw TypeError((0,g.A)(e,...w.g))}(t,e,"unwrapKey","decrypt");return F(s,e),(0,n.privateDecrypt)({key:s,oaepHash:a,padding:i},r)},D={};function j(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new l.T0(`Unsupported JWE Algorithm: ${e}`)}}let M=e=>(0,n.randomFillSync)(new Uint8Array(j(e)>>3));var W=r(76285),K=r(70556);async function L(e,t,r,i,a){return b(e.slice(0,7),t,r,i,a,new Uint8Array(0))}async function J(e,t,r,i,a){switch((0,K.A)(e,t,"decrypt"),t=await D.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new l.aA("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new l.aA("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,s;if(!(0,A.A)(i.epk))throw new l.aA('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!C(t))throw new l.T0("ECDH with the provided key is not allowed or not supported by your javascript runtime");let n=await (0,W.Og)(i.epk,e);if(void 0!==i.apu){if("string"!=typeof i.apu)throw new l.aA('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=(0,o.D4)(i.apu)}catch{throw new l.aA("Failed to base64url decode the apu")}}if(void 0!==i.apv){if("string"!=typeof i.apv)throw new l.aA('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{s=(0,o.D4)(i.apv)}catch{throw new l.aA("Failed to base64url decode the apv")}}let c=await k(n,t,"ECDH-ES"===e?i.enc:e,"ECDH-ES"===e?j(i.enc):parseInt(e.slice(-5,-2),10),a,s);if("ECDH-ES"===e)return c;if(void 0===r)throw new l.aA("JWE Encrypted Key missing");return S(e.slice(-6),c,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new l.aA("JWE Encrypted Key missing");return H(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let s;if(void 0===r)throw new l.aA("JWE Encrypted Key missing");if("number"!=typeof i.p2c)throw new l.aA('JOSE Header "p2c" (PBES2 Count) missing or invalid');let n=a?.maxPBES2Count||1e4;if(i.p2c>n)throw new l.aA('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof i.p2s)throw new l.aA('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{s=(0,o.D4)(i.p2s)}catch{throw new l.aA("Failed to base64url decode the p2s")}return I(e,t,r,i.p2c,s)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new l.aA("JWE Encrypted Key missing");return S(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,s;if(void 0===r)throw new l.aA("JWE Encrypted Key missing");if("string"!=typeof i.iv)throw new l.aA('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof i.tag)throw new l.aA('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=(0,o.D4)(i.iv)}catch{throw new l.aA("Failed to base64url decode the iv")}try{s=(0,o.D4)(i.tag)}catch{throw new l.aA("Failed to base64url decode the tag")}return L(e,t,r,a,s)}default:throw new l.T0('Invalid or unsupported "alg" (JWE Algorithm) header value')}}var U=r(49010),N=r(3560);async function B(e,t,r){let i,a,s,n,c,u,d;if(!(0,A.A)(e))throw new l.aA("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new l.aA("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new l.aA("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new l.aA("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new l.aA("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new l.aA("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new l.aA("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new l.aA("JWE AAD incorrect type");if(void 0!==e.header&&!(0,A.A)(e.header))throw new l.aA("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!(0,A.A)(e.unprotected))throw new l.aA("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=(0,o.D4)(e.protected);i=JSON.parse(h.D0.decode(t))}catch{throw new l.aA("JWE Protected Header is invalid")}if(!(0,v.A)(i,e.header,e.unprotected))throw new l.aA("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let f={...i,...e.header,...e.unprotected};if((0,U.A)(l.aA,new Map,r?.crit,i,f),void 0!==f.zip)throw new l.T0('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:p,enc:y}=f;if("string"!=typeof p||!p)throw new l.aA("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof y||!y)throw new l.aA("missing JWE Encryption Algorithm (enc) in JWE Header");let g=r&&(0,N.A)("keyManagementAlgorithms",r.keyManagementAlgorithms),m=r&&(0,N.A)("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(g&&!g.has(p)||!g&&p.startsWith("PBES2"))throw new l.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(m&&!m.has(y))throw new l.Rb('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{a=(0,o.D4)(e.encrypted_key)}catch{throw new l.aA("Failed to base64url decode the encrypted_key")}let w=!1;"function"==typeof t&&(t=await t(i,e),w=!0);try{s=await J(p,t,a,f,r)}catch(e){if(e instanceof TypeError||e instanceof l.aA||e instanceof l.T0)throw e;s=M(y)}if(void 0!==e.iv)try{n=(0,o.D4)(e.iv)}catch{throw new l.aA("Failed to base64url decode the iv")}if(void 0!==e.tag)try{c=(0,o.D4)(e.tag)}catch{throw new l.aA("Failed to base64url decode the tag")}let E=h.Rd.encode(e.protected??"");u=void 0!==e.aad?(0,h.xW)(E,h.Rd.encode("."),h.Rd.encode(e.aad)):E;try{d=(0,o.D4)(e.ciphertext)}catch{throw new l.aA("Failed to base64url decode the ciphertext")}let S={plaintext:await b(y,s,d,n,c,u)};if(void 0!==e.protected&&(S.protectedHeader=i),void 0!==e.aad)try{S.additionalAuthenticatedData=(0,o.D4)(e.aad)}catch{throw new l.aA("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(S.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(S.unprotectedHeader=e.header),w)?{...S,key:t}:S}async function G(e,t,r){if(e instanceof Uint8Array&&(e=h.D0.decode(e)),"string"!=typeof e)throw new l.aA("Compact JWE must be a string or Uint8Array");let{0:i,1:a,2:s,3:o,4:n,length:c}=e.split(".");if(5!==c)throw new l.aA("Invalid Compact JWE");let u=await B({ciphertext:o,iv:s||void 0,protected:i,tag:n||void 0,encrypted_key:a||void 0},t,r),d={plaintext:u.plaintext,protectedHeader:u.protectedHeader};return"function"==typeof t?{...d,key:u.key}:d}let V=e=>Math.floor(e.getTime()/1e3),q=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,z=e=>{let t,r=q.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let i=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(i);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*i);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*i);break;case"day":case"days":case"d":t=Math.round(86400*i);break;case"week":case"weeks":case"w":t=Math.round(604800*i);break;default:t=Math.round(0x1e187e0*i)}return"-"===r[1]||"ago"===r[4]?-t:t},Y=e=>e.toLowerCase().replace(/^application\//,""),Q=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),X=(e,t,r={})=>{let i,a;try{i=JSON.parse(h.D0.decode(t))}catch{}if(!(0,A.A)(i))throw new l.Dp("JWT Claims Set must be a top-level JSON object");let{typ:s}=r;if(s&&("string"!=typeof e.typ||Y(e.typ)!==Y(s)))throw new l.ie('unexpected "typ" JWT header value',i,"typ","check_failed");let{requiredClaims:o=[],issuer:n,subject:c,audience:u,maxTokenAge:d}=r,f=[...o];for(let e of(void 0!==d&&f.push("iat"),void 0!==u&&f.push("aud"),void 0!==c&&f.push("sub"),void 0!==n&&f.push("iss"),new Set(f.reverse())))if(!(e in i))throw new l.ie(`missing required "${e}" claim`,i,e,"missing");if(n&&!(Array.isArray(n)?n:[n]).includes(i.iss))throw new l.ie('unexpected "iss" claim value',i,"iss","check_failed");if(c&&i.sub!==c)throw new l.ie('unexpected "sub" claim value',i,"sub","check_failed");if(u&&!Q(i.aud,"string"==typeof u?[u]:u))throw new l.ie('unexpected "aud" claim value',i,"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=z(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,y=V(p||new Date);if((void 0!==i.iat||d)&&"number"!=typeof i.iat)throw new l.ie('"iat" claim must be a number',i,"iat","invalid");if(void 0!==i.nbf){if("number"!=typeof i.nbf)throw new l.ie('"nbf" claim must be a number',i,"nbf","invalid");if(i.nbf>y+a)throw new l.ie('"nbf" claim timestamp check failed',i,"nbf","check_failed")}if(void 0!==i.exp){if("number"!=typeof i.exp)throw new l.ie('"exp" claim must be a number',i,"exp","invalid");if(i.exp<=y-a)throw new l.n('"exp" claim timestamp check failed',i,"exp","check_failed")}if(d){let e=y-i.iat;if(e-a>("number"==typeof d?d:z(d)))throw new l.n('"iat" claim timestamp check failed (too far in the past)',i,"iat","check_failed");if(e<0-a)throw new l.ie('"iat" claim timestamp check failed (it should be in the past)',i,"iat","check_failed")}return i};async function Z(e,t,r){let i=await G(e,t,r),a=X(i.protectedHeader,i.plaintext,r),{protectedHeader:s}=i;if(void 0!==s.iss&&s.iss!==a.iss)throw new l.ie('replicated "iss" claim header parameter mismatch',a,"iss","mismatch");if(void 0!==s.sub&&s.sub!==a.sub)throw new l.ie('replicated "sub" claim header parameter mismatch',a,"sub","mismatch");if(void 0!==s.aud&&JSON.stringify(s.aud)!==JSON.stringify(a.aud))throw new l.ie('replicated "aud" claim header parameter mismatch',a,"aud","mismatch");let o={payload:a,protectedHeader:s};return"function"==typeof t?{...o,key:i.key}:o}var ee="4.0.1",et=Symbol.for("flags:global-trace"),er=new a.AsyncLocalStorage;function ei(e,t){er.getStore()?.set(e,t)}function ea(e,t={name:e.name}){return function(...r){let i=function(){let e=Reflect.get(globalThis,et);return e?.getTracer("flags",ee)}();return i&&("true"===process.env.VERCEL_FLAGS_TRACE_VERBOSE||!1===t.isVerboseTrace)?er.run(new Map,()=>i.startActiveSpan(t.name,i=>{t.attributes&&i.setAttributes(t.attributes);try{let a=e.apply(this,r);return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then?a.then(e=>{t.attributesSuccess&&i.setAttributes(t.attributesSuccess(e)),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.setStatus({code:1}),i.end()}).catch(e=>{t.attributesError&&i.setAttributes(t.attributesError(e)),i.setStatus({code:2,message:e instanceof Error?e.message:void 0}),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.end()}):(t.attributesSuccess&&i.setAttributes(t.attributesSuccess(a)),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.setStatus({code:1}),i.end()),a}catch(e){throw t.attributesError&&i.setAttributes(t.attributesError(e)),i.setStatus({code:2,message:e instanceof Error?e.message:void 0}),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.end(),e}})):e.apply(this,r)}}var es=(e,t)=>Array.isArray(e)?e.includes(t):e===t;async function eo(e,t,r){if("string"!=typeof e)return;let i=s.D(r);if(32!==i.length)throw Error("flags: Invalid secret, it must be a 256-bit key (32 bytes)");try{let{payload:r}=await Z(e,i);return t(r)?r:void 0}catch{return}}async function en(e,t=process?.env?.FLAGS_SECRET){if(!t)throw Error("flags: Missing FLAGS_SECRET");let r=await eo(e,e=>es(e.pur,"overrides")&&Object.hasOwn(e,"o"),t);return r?.o}async function el(e,t=process?.env?.FLAGS_SECRET){if(!t)throw Error("flags: Missing FLAGS_SECRET");return!!await eo(e,e=>es(e.pur,"proof"),t)}var ec=ea(async function(e,t=process?.env?.FLAGS_SECRET){if(!e)return!1;if(!t)throw Error("flags: verifyAccess was called without a secret. Please set FLAGS_SECRET environment variable.");return await el(e.replace(/^Bearer /i,""),t)},{isVerboseTrace:!1,name:"verifyAccess"});function eu(e,t){let r=Symbol.for("@vercel/request-context"),i=Reflect.get(globalThis,r)?.get();i?.flags?.reportValue(e,t,{sdkVersion:ee})}function ed(e,t,r){let i=Symbol.for("@vercel/request-context"),a=Reflect.get(globalThis,i)?.get();a?.flags?.reportValue(e,t,{sdkVersion:ee,...r})}var eh=class{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}},ef=class e extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new e}},ep=class e extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return eh.get(t,r,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==s)return eh.get(t,s,i)},set(t,r,i,a){if("symbol"==typeof r)return eh.set(t,r,i,a);let s=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===s);return eh.set(t,o??r,i,a)},has(t,r){if("symbol"==typeof r)return eh.has(t,r);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==a&&eh.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return eh.deleteProperty(t,r);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===a||eh.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ef.callable;default:return eh.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(t){return t instanceof Headers?t:new e(t)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}},ey=class e extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new e}},eg=class{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ey.callable;default:return eh.get(e,t,r)}}})}};Symbol.for("next.mutated.cookies")},44214:(e,t,r)=>{r.d(t,{D:()=>a});var i=r(13690);i.lF;let a=i.D4},45271:(e,t,r)=>{r.d(t,{xW:()=>n,yI:()=>f,D0:()=>o,Rd:()=>s,Kp:()=>h,MT:()=>l,VS:()=>d,mx:()=>u});var i=r(77598);let a=(e,t)=>(0,i.createHash)(e).update(t).digest(),s=new TextEncoder,o=new TextDecoder;function n(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let i of e)t.set(i,r),r+=i.length;return t}function l(e,t){return n(s.encode(e),new Uint8Array([0]),t)}function c(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function u(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return c(r,t,0),c(r,e%0x100000000,4),r}function d(e){let t=new Uint8Array(4);return c(t,e),t}function h(e){return n(d(e.length),e)}async function f(e,t,r){let i=Math.ceil((t>>3)/32),s=new Uint8Array(32*i);for(let t=0;t<i;t++){let i=new Uint8Array(4+e.length+r.length);i.set(d(t+1)),i.set(e,4),i.set(r,4+e.length),s.set(await a("sha256",i),32*t)}return s.slice(0,t>>3)}},46524:(e,t,r)=>{r.d(t,{A:()=>d});var i=r(77598),a=r(14295),s=r(89605),o=r(22556),n=r(63669),l=r(69472),c=r(6798);new WeakMap;let u=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new a.T0("Unsupported key curve for this operation")}},d=(e,t)=>{let r;if((0,s.R)(e))r=i.KeyObject.from(e);else if((0,o.A)(e))r=e;else if((0,c.ll)(e))return e.crv;else throw TypeError((0,n.A)(e,...l.g));if("secret"===r.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let e=r.asymmetricKeyDetails.namedCurve;if(t)return e;return u(e)}default:throw TypeError("Invalid asymmetric key type for this operation")}}},49010:(e,t,r)=>{r.d(t,{A:()=>a});var i=r(14295);let a=function(e,t,r,a,s){let o;if(void 0!==s.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let n of(o=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!o.has(n))throw new i.T0(`Extension Header Parameter "${n}" is not recognized`);if(void 0===s[n])throw new e(`Extension Header Parameter "${n}" is missing`);if(o.get(n)&&void 0===a[n])throw new e(`Extension Header Parameter "${n}" MUST be integrity protected`)}return new Set(a.crit)}},51153:(e,t,r)=>{let i;r.d(t,{f2:()=>eF});var a,s,o,n,l,c,u,d,h,f,p,y=r(33873),g=r(73024),m=r(80481);let w="posthog-node";class b{constructor(e,t,r,i){this.name=w,this.name=w,this.setupOnce=function(a,s){a(function(e,{organization:t,projectId:r,prefix:i,severityAllowList:a=["error"]}={}){return s=>{if(!("*"===a||a.includes(s.level)))return s;s.tags||(s.tags={});let o=s.tags[b.POSTHOG_ID_TAG];if(void 0===o)return s;let n=e.options.host??"https://us.i.posthog.com",l=new URL(`/project/${e.apiKey}/person/${o}`,n).toString();s.tags["PostHog Person URL"]=l;let c=s.exception?.values||[],u=c.map(e=>({...e,stacktrace:e.stacktrace?{...e.stacktrace,type:"raw",frames:(e.stacktrace.frames||[]).map(e=>({...e,platform:"node:javascript"}))}:void 0})),d={$exception_message:c[0]?.value||s.message,$exception_type:c[0]?.type,$exception_personURL:l,$exception_level:s.level,$exception_list:u,$sentry_event_id:s.event_id,$sentry_exception:s.exception,$sentry_exception_message:c[0]?.value||s.message,$sentry_exception_type:c[0]?.type,$sentry_tags:s.tags};return t&&r&&(d.$sentry_url=(i||"https://sentry.io/organizations/")+t+"/issues/?project="+r+"&query="+s.event_id),e.capture({event:"$exception",distinctId:o,properties:d}),s}}(e,{organization:t,projectId:s()?.getClient()?.getDsn()?.projectId,prefix:r,severityAllowList:i}))}}}b.POSTHOG_ID_TAG="posthog_distinct_id";let v="0123456789abcdef";class A{constructor(e){this.bytes=e}static ofInner(e){if(16===e.length)return new A(e);throw TypeError("not 128-bit length")}static fromFieldsV7(e,t,r,i){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(r)||!Number.isInteger(i)||e<0||t<0||r<0||i<0||e>0xffffffffffff||t>4095||r>0x3fffffff||i>0xffffffff)throw RangeError("invalid field value");let a=new Uint8Array(16);return a[0]=e/0x10000000000,a[1]=e/0x100000000,a[2]=e/0x1000000,a[3]=e/65536,a[4]=e/256,a[5]=e,a[6]=112|t>>>8,a[7]=t,a[8]=128|r>>>24,a[9]=r>>>16,a[10]=r>>>8,a[11]=r,a[12]=i>>>24,a[13]=i>>>16,a[14]=i>>>8,a[15]=i,new A(a)}static parse(e){let t;switch(e.length){case 32:t=/^[0-9a-f]{32}$/i.exec(e)?.[0];break;case 36:t=/^([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(e)?.slice(1,6).join("");break;case 38:t=/^\{([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})\}$/i.exec(e)?.slice(1,6).join("");break;case 45:t=/^urn:uuid:([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(e)?.slice(1,6).join("")}if(t){let e=new Uint8Array(16);for(let r=0;r<16;r+=4){let i=parseInt(t.substring(2*r,2*r+8),16);e[r+0]=i>>>24,e[r+1]=i>>>16,e[r+2]=i>>>8,e[r+3]=i}return new A(e)}throw SyntaxError("could not parse UUID string")}toString(){let e="";for(let t=0;t<this.bytes.length;t++)e+=v.charAt(this.bytes[t]>>>4),e+=v.charAt(15&this.bytes[t]),(3===t||5===t||7===t||9===t)&&(e+="-");return e}toHex(){let e="";for(let t=0;t<this.bytes.length;t++)e+=v.charAt(this.bytes[t]>>>4),e+=v.charAt(15&this.bytes[t]);return e}toJSON(){return this.toString()}getVariant(){let e=this.bytes[8]>>>4;if(e<0)throw Error("unreachable");if(e<=7)return this.bytes.every(e=>0===e)?"NIL":"VAR_0";if(e<=11)return"VAR_10";if(e<=13)return"VAR_110";if(e<=15)return this.bytes.every(e=>255===e)?"MAX":"VAR_RESERVED";else throw Error("unreachable")}getVersion(){return"VAR_10"===this.getVariant()?this.bytes[6]>>>4:void 0}clone(){return new A(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(let t=0;t<16;t++){let r=this.bytes[t]-e.bytes[t];if(0!==r)return Math.sign(r)}return 0}}class E{constructor(e){this.timestamp=0,this.counter=0,this.random=e??S()}generate(){return this.generateOrResetCore(Date.now(),1e4)}generateOrAbort(){return this.generateOrAbortCore(Date.now(),1e4)}generateOrResetCore(e,t){let r=this.generateOrAbortCore(e,t);return void 0===r&&(this.timestamp=0,r=this.generateOrAbortCore(e,t)),r}generateOrAbortCore(e,t){if(!Number.isInteger(e)||e<1||e>0xffffffffffff)throw RangeError("`unixTsMs` must be a 48-bit positive integer");if(t<0||t>0xffffffffffff)throw RangeError("`rollbackAllowance` out of reasonable range");if(e>this.timestamp)this.timestamp=e,this.resetCounter();else{if(!(e+t>=this.timestamp))return;this.counter++,this.counter>0x3ffffffffff&&(this.timestamp++,this.resetCounter())}return A.fromFieldsV7(this.timestamp,Math.trunc(this.counter/0x40000000),this.counter&0x40000000-1,this.random.nextUint32())}resetCounter(){this.counter=1024*this.random.nextUint32()+(1023&this.random.nextUint32())}generateV4(){let e=new Uint8Array(Uint32Array.of(this.random.nextUint32(),this.random.nextUint32(),this.random.nextUint32(),this.random.nextUint32()).buffer);return e[6]=64|e[6]>>>4,e[8]=128|e[8]>>>2,A.ofInner(e)}}let S=()=>({nextUint32:()=>65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random())}),_=()=>P().toString(),P=()=>(i||(i=new E)).generate();function k(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return C(e,Error)}}function C(e,t){try{return e instanceof t}catch{return!1}}function T(e,t){return Object.prototype.toString.call(e)===`[object ${t}]`}async function I(e,t,r,i){let a=i&&i.mechanism||{handled:!0,type:"generic"},s=function e(t,r,i){let a=function(e,t,r){if(k(t))return t;if(e.synthetic=!0,T(t,"Object")){let e=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let r=e[t];if(k(r))return r}}(t);if(e)return e;let i=function(e){if("name"in e&&"string"==typeof e.name){let t=`'${e.name}' captured as exception`;return"message"in e&&"string"==typeof e.message&&(t+=` with message '${e.message}'`),t}if("message"in e&&"string"==typeof e.message)return e.message;let t=function(e,t=40){let r=Object.keys(function(e){return k(e)?{message:e.message,name:e.name,stack:e.stack,...F(e)}:"undefined"!=typeof Event&&C(e,Event)?{type:e.type,target:O(e.target),currentTarget:O(e.currentTarget),...F(e)}:e}(e));r.sort();let i=r[0];if(!i)return"[object has no keys]";if(i.length>=t)return x(i,t);for(let e=r.length;e>0;e--){let i=r.slice(0,e).join(", ");if(!(i.length>t)){if(e===r.length)return i;return x(i,t)}}return""}(e);if(T(e,"ErrorEvent"))return`Event \`ErrorEvent\` captured as exception with message \`${e.message}\``;let r=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e);return`${r&&"Object"!==r?`'${r}'`:"Object"} captured as exception with keys: ${t}`}(t),a=r?.syntheticException||Error(i);return a.message=i,a}let i=r?.syntheticException||Error(t);return i.message=`${t}`,i}(t,r,i);return a.cause?[a,...e(t,a.cause,i)]:[a]}(a,r,i);return{$exception_list:await Promise.all(s.map(async r=>{let i=await $(e,t,r);return i.value=i.value||"",i.type=i.type||"Error",i.mechanism=a,i}))}}function x(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function F(e){if("object"!=typeof e||null===e)return{};{let t={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}}function O(e){try{return Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}async function $(e,t,r){let i={type:r.name||r.constructor.name,value:r.message};let a=e(r.stack||"",1);for(let e of t)a=await e(a);return a.length&&(i.stacktrace={frames:a,type:"raw"}),i}class R{static async captureException(e,t,r,i,a){let s={...a};i||(s.$process_person_profile=!1);let o=await I(this.stackParser,this.frameModifiers,t,r);e.capture({event:"$exception",distinctId:i||_(),properties:{...o,...s}})}constructor(e,t){this.client=e,this._exceptionAutocaptureEnabled=t.enableExceptionAutocapture||!1,this.startAutocaptureIfEnabled()}startAutocaptureIfEnabled(){if(this.isEnabled()){var e,t,r;let i;e=this.onException.bind(this),t=this.onFatalError.bind(this),global.process.on("uncaughtException",(i=!1,Object.assign(r=>{let a=global.process.listeners("uncaughtException").filter(e=>"domainUncaughtExceptionClear"!==e.name&&!0!==e._posthogErrorHandler).length;e(r,{mechanism:{type:"onuncaughtexception",handled:!1}}),i||0!==a||(i=!0,t())},{_posthogErrorHandler:!0}))),r=this.onException.bind(this),global.process.on("unhandledRejection",e=>{r(e,{mechanism:{type:"onunhandledrejection",handled:!1}})})}}onException(e,t){R.captureException(this.client,e,t)}async onFatalError(){await this.client.shutdown(2e3)}isEnabled(){return!this.client.isDisabled&&this._exceptionAutocaptureEnabled}}function H(e){return e.replace(/^[A-Z]:/,"").replace(/\\/g,"/")}class D{constructor(e){this._maxSize=e,this._cache=new Map}get(e){let t=this._cache.get(e);if(void 0!==t)return this._cache.delete(e),this._cache.set(e,t),t}set(e,t){this._cache.set(e,t)}reduce(){for(;this._cache.size>=this._maxSize;){let e=this._cache.keys().next().value;e&&this._cache.delete(e)}}}let j=new D(25),M=new D(20);async function W(e){let t={};for(let a=e.length-1;a>=0;a--){var r,i;let s=e[a],o=s?.filename;!(!s||"string"!=typeof o||"number"!=typeof s.lineno||(r=o).startsWith("node:")||r.endsWith(".min.js")||r.endsWith(".min.cjs")||r.endsWith(".min.mjs")||r.startsWith("data:")||void 0!==(i=s).lineno&&i.lineno>1e4||void 0!==i.colno&&i.colno>1e3)&&(t[o]||(t[o]=[]),t[o].push(s.lineno))}let a=Object.keys(t);if(0==a.length)return e;let s=[];for(let e of a){if(M.get(e))continue;let r=t[e];if(!r)continue;r.sort((e,t)=>e-t);let i=function(e){if(!e.length)return[];let t=0,r=e[0];if("number"!=typeof r)return[];let i=L(r),a=[];for(;;){if(t===e.length-1){a.push(i);break}let r=e[t+1];if("number"!=typeof r)break;r<=i[1]?i[1]=r+7:(a.push(i),i=L(r)),t++}return a}(r);if(i.every(t=>(function(e,t){let r=j.get(e);if(void 0===r)return!1;for(let e=t[0];e<=t[1];e++)if(void 0===r[e])return!1;return!0})(e,t)))continue;let a=function(e,t,r){let i=e.get(t);return void 0===i?(e.set(t,r),r):i}(j,e,{});s.push(function(e,t,r){return new Promise(i=>{let a=(0,g.createReadStream)(e),s=(0,m.createInterface)({input:a});function o(){a.destroy(),i()}let n=0,l=0,c=t[0];if(void 0===c)return void o();let u=c[0],d=c[1];function h(){M.set(e,1),s.close(),s.removeAllListeners(),o()}a.on("error",h),s.on("error",h),s.on("close",o),s.on("line",e=>{if(!(++n<u)&&(r[n]=function(e,t){let r=e,i=r.length;if(i<=150)return r;t>i&&(t=i);let a=Math.max(t-60,0);a<5&&(a=0);let s=Math.min(a+140,i);return s>i-5&&(s=i),s===i&&(a=Math.max(s-140,0)),r=r.slice(a,s),a>0&&(r=`...${r}`),s<i&&(r+="..."),r}(e,0),n>=d)){if(l===t.length-1){s.close(),s.removeAllListeners();return}let e=t[++l];if(void 0===e){s.close(),s.removeAllListeners();return}u=e[0],d=e[1]}})})}(e,i,a))}return await Promise.all(s).catch(()=>{}),e&&e.length>0&&function(e,t){for(let r of e)if(r.filename&&void 0===r.context_line&&"number"==typeof r.lineno){let e=t.get(r.filename);if(void 0===e)continue;!function(e,t,r){if(void 0===t.lineno||void 0===r)return;t.pre_context=[];for(let i=J(e);i<e;i++){let e=r[i];if(void 0===e)return void K(t);t.pre_context.push(e)}if(void 0===r[e])return K(t);t.context_line=r[e];let i=function(e){return e+7}(e);t.post_context=[];for(let a=e+1;a<=i;a++){let e=r[a];if(void 0===e)break;t.post_context.push(e)}}(r.lineno,r,e)}}(e,j),j.reduce(),e}function K(e){delete e.pre_context,delete e.context_line,delete e.post_context}function L(e){return[J(e),e+7]}function J(e){return Math.max(1,e-7)}!function(e){e.AnonymousId="anonymous_id",e.DistinctId="distinct_id",e.Props="props",e.FeatureFlagDetails="feature_flag_details",e.FeatureFlags="feature_flags",e.FeatureFlagPayloads="feature_flag_payloads",e.BootstrapFeatureFlagDetails="bootstrap_feature_flag_details",e.BootstrapFeatureFlags="bootstrap_feature_flags",e.BootstrapFeatureFlagPayloads="bootstrap_feature_flag_payloads",e.OverrideFeatureFlags="override_feature_flags",e.Queue="queue",e.OptedOut="opted_out",e.SessionId="session_id",e.SessionLastTimestamp="session_timestamp",e.PersonProperties="person_properties",e.GroupProperties="group_properties",e.InstalledAppBuild="installed_app_build",e.InstalledAppVersion="installed_app_version",e.SessionReplay="session_replay",e.DecideEndpointWasHit="decide_endpoint_was_hit",e.SurveyLastSeenDate="survey_last_seen_date",e.SurveysSeen="surveys_seen",e.Surveys="surveys",e.RemoteConfig="remote_config"}(a||(a={})),function(e){e.Left="left",e.Right="right",e.Center="center"}(s||(s={})),function(e){e.Button="button",e.Tab="tab",e.Selector="selector"}(o||(o={})),function(e){e.Popover="popover",e.API="api",e.Widget="widget"}(n||(n={})),function(e){e.Html="html",e.Text="text"}(l||(l={})),function(e){e.Number="number",e.Emoji="emoji"}(c||(c={})),function(e){e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link"}(u||(u={})),function(e){e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question"}(d||(d={})),function(e){e.Regex="regex",e.NotRegex="not_regex",e.Exact="exact",e.IsNot="is_not",e.Icontains="icontains",e.NotIcontains="not_icontains"}(h||(h={})),function(e){e.Contains="contains",e.Exact="exact",e.Regex="regex"}(f||(f={}));let U=e=>{if("flags"in e){let t=N(e.flags),r=B(e.flags);return{...e,featureFlags:t,featureFlagPayloads:r}}{let t=e.featureFlags??{},r=Object.fromEntries(Object.entries(e.featureFlagPayloads||{}).map(([e,t])=>[e,V(t)])),i=Object.fromEntries(Object.entries(t).map(([e,t])=>[e,function(e,t,r){return{key:e,enabled:"string"==typeof t||t,variant:"string"==typeof t?t:void 0,reason:void 0,metadata:{id:void 0,version:void 0,payload:r?JSON.stringify(r):void 0,description:void 0}}}(e,t,r[e])]));return{...e,featureFlags:t,featureFlagPayloads:r,flags:i}}},N=e=>Object.fromEntries(Object.entries(e??{}).map(([e,t])=>[e,G(t)]).filter(([,e])=>void 0!==e)),B=e=>{let t=e??{};return Object.fromEntries(Object.keys(t).filter(e=>{let r=t[e];return r.enabled&&r.metadata&&void 0!==r.metadata.payload}).map(e=>{let r=t[e].metadata?.payload;return[e,r?V(r):void 0]}))},G=e=>void 0===e?void 0:e.variant??e.enabled,V=e=>{if("string"!=typeof e)return e;try{return JSON.parse(e)}catch{return e}},q=new Set(["61be3dd8","96f6df5f","8cfdba9b","bf027177","e59430a8","7fa5500b","569798e9","04809ff7","0ebc61a5","32de7f98","3beeb69a","12d34ad9","733853ec","0645bb64","5dcbee21","b1f95fa3","2189e408","82b460c2","3a8cc979","29ef8843","2cdbf767","38084b54","50f9f8de","41d0df91","5c236689","c11aedd3","ada46672","f4331ee1","42fed62a","c957462c","d62f705a","e0162666","01b3e5cf","441cef7f","bb9cafee","8f348eb0","b2553f3a","97469d7d","39f21a76","03706dcc","27d50569","307584a7","6433e92e","150c7fbb","49f57f22","3772f65b","01eb8256","3c9e9234","f853c7f7","c0ac4b67","cd609d40","10ca9b1a","8a87f11b","8e8e5216","1f6b63b3","db7943dd","79b7164c","07f78e33","2d21b6fd","952db5ee","a7d3b43f","1924dd9c","84e1b8f6","dff631b6","c5aa8a79","fa133a95","498a4508","24748755","98f3d658","21bbda67","7dbfed69","be3ec24c","fc80b8e2","75cc0998"]);async function z(e,t){let r=null;for(let i=0;i<t.retryCount+1;i++){i>0&&await new Promise(e=>setTimeout(e,t.retryDelay));try{return await e()}catch(e){if(r=e,!t.retryCheck(e))throw e}}throw r}function Y(){return new Date().getTime()}function Q(){return new Date().toISOString()}function X(e,t){let r=setTimeout(e,t);return r?.unref&&r?.unref(),r}function Z(e){return Promise.all(e.map(e=>(e??Promise.resolve()).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))}let ee=String.fromCharCode,et="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",er={},ei={compressToBase64:function(e){if(null==e)return"";let t=ei._compress(e,6,function(e){return et.charAt(e)});switch(t.length%4){default:case 0:return t;case 1:return t+"===";case 2:return t+"==";case 3:return t+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:ei._decompress(e.length,32,function(t){var r=e.charAt(t);if(!er[et]){er[et]={};for(let e=0;e<et.length;e++)er[et][et.charAt(e)]=e}return er[et][r]})},compress:function(e){return ei._compress(e,16,function(e){return ee(e)})},_compress:function(e,t,r){if(null==e)return"";let i={},a={},s=[],o,n,l="",c="",u="",d=2,h=3,f=2,p=0,y=0,g;for(g=0;g<e.length;g+=1)if(l=e.charAt(g),Object.prototype.hasOwnProperty.call(i,l)||(i[l]=h++,a[l]=!0),c=u+l,Object.prototype.hasOwnProperty.call(i,c))u=c;else{if(Object.prototype.hasOwnProperty.call(a,u)){if(256>u.charCodeAt(0)){for(o=0;o<f;o++)p<<=1,y==t-1?(y=0,s.push(r(p)),p=0):y++;for(o=0,n=u.charCodeAt(0);o<8;o++)p=p<<1|1&n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n>>=1}else{for(o=0,n=1;o<f;o++)p=p<<1|n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n=0;for(o=0,n=u.charCodeAt(0);o<16;o++)p=p<<1|1&n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n>>=1}0==--d&&(d=Math.pow(2,f),f++),delete a[u]}else for(o=0,n=i[u];o<f;o++)p=p<<1|1&n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n>>=1;0==--d&&(d=Math.pow(2,f),f++),i[c]=h++,u=String(l)}if(""!==u){if(Object.prototype.hasOwnProperty.call(a,u)){if(256>u.charCodeAt(0)){for(o=0;o<f;o++)p<<=1,y==t-1?(y=0,s.push(r(p)),p=0):y++;for(o=0,n=u.charCodeAt(0);o<8;o++)p=p<<1|1&n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n>>=1}else{for(o=0,n=1;o<f;o++)p=p<<1|n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n=0;for(o=0,n=u.charCodeAt(0);o<16;o++)p=p<<1|1&n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n>>=1}0==--d&&(d=Math.pow(2,f),f++),delete a[u]}else for(o=0,n=i[u];o<f;o++)p=p<<1|1&n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n>>=1;0==--d&&(d=Math.pow(2,f),f++)}for(o=0,n=2;o<f;o++)p=p<<1|1&n,y==t-1?(y=0,s.push(r(p)),p=0):y++,n>>=1;for(;;){if(p<<=1,y==t-1){s.push(r(p));break}y++}return s.join("")},decompress:function(e){return null==e?"":""==e?null:ei._decompress(e.length,32768,function(t){return e.charCodeAt(t)})},_decompress:function(e,t,r){let i=[],a=[],s={val:r(0),position:t,index:1},o=4,n=4,l=3,c="",u,d,h,f,p,y,g;for(u=0;u<3;u+=1)i[u]=u;for(h=0,p=4,y=1;y!=p;)f=s.val&s.position,s.position>>=1,0==s.position&&(s.position=t,s.val=r(s.index++)),h|=(f>0)*y,y<<=1;switch(h){case 0:for(h=0,p=256,y=1;y!=p;)f=s.val&s.position,s.position>>=1,0==s.position&&(s.position=t,s.val=r(s.index++)),h|=(f>0)*y,y<<=1;g=ee(h);break;case 1:for(h=0,p=65536,y=1;y!=p;)f=s.val&s.position,s.position>>=1,0==s.position&&(s.position=t,s.val=r(s.index++)),h|=(f>0)*y,y<<=1;g=ee(h);break;case 2:return""}for(i[3]=g,d=g,a.push(g);;){if(s.index>e)return"";for(h=0,p=Math.pow(2,l),y=1;y!=p;)f=s.val&s.position,s.position>>=1,0==s.position&&(s.position=t,s.val=r(s.index++)),h|=(f>0)*y,y<<=1;switch(g=h){case 0:for(h=0,p=256,y=1;y!=p;)f=s.val&s.position,s.position>>=1,0==s.position&&(s.position=t,s.val=r(s.index++)),h|=(f>0)*y,y<<=1;i[n++]=ee(h),g=n-1,o--;break;case 1:for(h=0,p=65536,y=1;y!=p;)f=s.val&s.position,s.position>>=1,0==s.position&&(s.position=t,s.val=r(s.index++)),h|=(f>0)*y,y<<=1;i[n++]=ee(h),g=n-1,o--;break;case 2:return a.join("")}if(0==o&&(o=Math.pow(2,l),l++),i[g])c=i[g];else{if(g!==n)return null;c=d+d.charAt(0)}a.push(c),i[n++]=d+c.charAt(0),o--,d=c,0==o&&(o=Math.pow(2,l),l++)}}};class ea{constructor(){this.events={},this.events={}}on(e,t){return this.events[e]||(this.events[e]=[]),this.events[e].push(t),()=>{this.events[e]=this.events[e].filter(e=>e!==t)}}emit(e,t){for(let r of this.events[e]||[])r(t);for(let r of this.events["*"]||[])r(e,t)}}class es extends Error{constructor(e,t){super("HTTP error while fetching PostHog: status="+e.status+", reqByteLength="+t),this.response=e,this.reqByteLength=t,this.name="PostHogFetchHttpError"}get status(){return this.response.status}get text(){return this.response.text()}get json(){return this.response.json()}}class eo extends Error{constructor(e){super("Network error while fetching PostHog",e instanceof Error?{cause:e}:{}),this.error=e,this.name="PostHogFetchNetworkError"}}async function en(e){if(e instanceof es){let t="";try{t=await e.text}catch{}console.error(`Error while flushing PostHog: message=${e.message}, response body=${t}`,e)}else console.error("Error while flushing PostHog",e);return Promise.resolve()}function el(e){return"object"==typeof e&&(e instanceof es||e instanceof eo)}function ec(e){return"object"==typeof e&&e instanceof es&&413===e.status}!function(e){e.FeatureFlags="feature_flags",e.Recordings="recordings"}(p||(p={}));class eu{constructor(e,t){var r;this.flushPromise=null,this.shutdownPromise=null,this.pendingPromises={},this._events=new ea,this._isInitialized=!1,function(e,t){if(!e||"string"!=typeof e||0===e.trim().length)throw Error(t)}(e,"You must pass your PostHog project's api key."),this.apiKey=e,this.host=(r=t?.host||"https://us.i.posthog.com",r?.replace(/\/+$/,"")),this.flushAt=t?.flushAt?Math.max(t?.flushAt,1):20,this.maxBatchSize=Math.max(this.flushAt,t?.maxBatchSize??100),this.maxQueueSize=Math.max(this.flushAt,t?.maxQueueSize??1e3),this.flushInterval=t?.flushInterval??1e4,this.captureMode=t?.captureMode||"json",this.preloadFeatureFlags=t?.preloadFeatureFlags??!0,this.defaultOptIn=t?.defaultOptIn??!0,this.disableSurveys=t?.disableSurveys??!1,this._retryOptions={retryCount:t?.fetchRetryCount??3,retryDelay:t?.fetchRetryDelay??3e3,retryCheck:el},this.requestTimeout=t?.requestTimeout??1e4,this.featureFlagsRequestTimeoutMs=t?.featureFlagsRequestTimeoutMs??3e3,this.remoteConfigRequestTimeoutMs=t?.remoteConfigRequestTimeoutMs??3e3,this.disableGeoip=t?.disableGeoip??!0,this.disabled=t?.disabled??!1,this.historicalMigration=t?.historicalMigration??!1,this._initPromise=Promise.resolve(),this._isInitialized=!0}logMsgIfDebug(e){this.isDebug&&e()}wrap(e){return this.disabled?void this.logMsgIfDebug(()=>console.warn("[PostHog] The client is disabled")):this._isInitialized?e():void this._initPromise.then(()=>e())}getCommonEventProperties(){return{$lib:this.getLibraryId(),$lib_version:this.getLibraryVersion()}}get optedOut(){return this.getPersistedProperty(a.OptedOut)??!this.defaultOptIn}async optIn(){this.wrap(()=>{this.setPersistedProperty(a.OptedOut,!1)})}async optOut(){this.wrap(()=>{this.setPersistedProperty(a.OptedOut,!0)})}on(e,t){return this._events.on(e,t)}debug(e=!0){if(this.removeDebugCallback?.(),e){let e=this.on("*",(e,t)=>console.log("PostHog Debug",e,t));this.removeDebugCallback=()=>{e(),this.removeDebugCallback=void 0}}}get isDebug(){return!!this.removeDebugCallback}get isDisabled(){return this.disabled}buildPayload(e){return{distinct_id:e.distinct_id,event:e.event,properties:{...e.properties||{},...this.getCommonEventProperties()}}}addPendingPromise(e){let t=_();return this.pendingPromises[t]=e,e.catch(()=>{}).finally(()=>{delete this.pendingPromises[t]}),e}identifyStateless(e,t,r){this.wrap(()=>{let i={...this.buildPayload({distinct_id:e,event:"$identify",properties:t})};this.enqueue("identify",i,r)})}async identifyStatelessImmediate(e,t,r){let i={...this.buildPayload({distinct_id:e,event:"$identify",properties:t})};await this.sendImmediate("identify",i,r)}captureStateless(e,t,r,i){this.wrap(()=>{let a=this.buildPayload({distinct_id:e,event:t,properties:r});this.enqueue("capture",a,i)})}async captureStatelessImmediate(e,t,r,i){let a=this.buildPayload({distinct_id:e,event:t,properties:r});await this.sendImmediate("capture",a,i)}aliasStateless(e,t,r,i){this.wrap(()=>{let a=this.buildPayload({event:"$create_alias",distinct_id:t,properties:{...r||{},distinct_id:t,alias:e}});this.enqueue("alias",a,i)})}async aliasStatelessImmediate(e,t,r,i){let a=this.buildPayload({event:"$create_alias",distinct_id:t,properties:{...r||{},distinct_id:t,alias:e}});await this.sendImmediate("alias",a,i)}groupIdentifyStateless(e,t,r,i,a,s){this.wrap(()=>{let o=this.buildPayload({distinct_id:a||`$${e}_${t}`,event:"$groupidentify",properties:{$group_type:e,$group_key:t,$group_set:r||{},...s||{}}});this.enqueue("capture",o,i)})}async getRemoteConfig(){await this._initPromise;let e=this.host;"https://us.i.posthog.com"===e?e="https://us-assets.i.posthog.com":"https://eu.i.posthog.com"===e&&(e="https://eu-assets.i.posthog.com");let t=`${e}/array/${this.apiKey}/config`,r={method:"GET",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"}};return this.fetchWithRetry(t,r,{retryCount:0},this.remoteConfigRequestTimeoutMs).then(e=>e.json()).catch(e=>{this.logMsgIfDebug(()=>console.error("Remote config could not be loaded",e)),this._events.emit("error",e)})}async getDecide(e,t={},r={},i={},a={}){await this._initPromise;let s=!function(e,t=0,r){let i=function(e){let t=0x811c9dc5;for(let r=0;r<e.length;r++)t^=e.charCodeAt(r),t+=(t<<1)+(t<<4)+(t<<7)+(t<<8)+(t<<24);return(t>>>0).toString(16).padStart(8,"0")}(e);return!r?.has(i)&&parseInt(i,16)/0xffffffff<t}(this.apiKey,1,q)?`${this.host}/decide/?v=4`:`${this.host}/flags/?v=2`,o={method:"POST",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"},body:JSON.stringify({token:this.apiKey,distinct_id:e,groups:t,person_properties:r,group_properties:i,...a})};return this.logMsgIfDebug(()=>console.log("PostHog Debug","Decide URL",s)),this.fetchWithRetry(s,o,{retryCount:0},this.featureFlagsRequestTimeoutMs).then(e=>e.json()).then(e=>U(e)).catch(e=>{this._events.emit("error",e)})}async getFeatureFlagStateless(e,t,r={},i={},a={},s){await this._initPromise;let o=await this.getFeatureFlagDetailStateless(e,t,r,i,a,s);if(void 0===o)return{response:void 0,requestId:void 0};let n=G(o.response);return void 0===n&&(n=!1),{response:n,requestId:o.requestId}}async getFeatureFlagDetailStateless(e,t,r={},i={},a={},s){await this._initPromise;let o=await this.getFeatureFlagDetailsStateless(t,r,i,a,s,[e]);if(void 0!==o)return{response:o.flags[e],requestId:o.requestId}}async getFeatureFlagPayloadStateless(e,t,r={},i={},a={},s){await this._initPromise;let o=await this.getFeatureFlagPayloadsStateless(t,r,i,a,s,[e]);if(!o)return;let n=o[e];return void 0===n?null:n}async getFeatureFlagPayloadsStateless(e,t={},r={},i={},a,s){return await this._initPromise,(await this.getFeatureFlagsAndPayloadsStateless(e,t,r,i,a,s)).payloads}async getFeatureFlagsStateless(e,t={},r={},i={},a,s){return await this._initPromise,await this.getFeatureFlagsAndPayloadsStateless(e,t,r,i,a,s)}async getFeatureFlagsAndPayloadsStateless(e,t={},r={},i={},a,s){await this._initPromise;let o=await this.getFeatureFlagDetailsStateless(e,t,r,i,a,s);return o?{flags:o.featureFlags,payloads:o.featureFlagPayloads,requestId:o.requestId}:{flags:void 0,payloads:void 0,requestId:void 0}}async getFeatureFlagDetailsStateless(e,t={},r={},i={},a,s){await this._initPromise;let o={};(a??this.disableGeoip)&&(o.geoip_disable=!0),s&&(o.flag_keys_to_evaluate=s);let n=await this.getDecide(e,t,r,i,o);if(void 0!==n)return(n.errorsWhileComputingFlags&&console.error("[FEATURE FLAGS] Error while computing feature flags, some flags may be missing or incorrect. Learn more at https://posthog.com/docs/feature-flags/best-practices"),n.quotaLimited?.includes(p.FeatureFlags))?(console.warn("[FEATURE FLAGS] Feature flags quota limit exceeded - feature flags unavailable. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts"),{flags:{},featureFlags:{},featureFlagPayloads:{},requestId:n?.requestId}):n}async getSurveysStateless(){if(await this._initPromise,!0===this.disableSurveys)return this.logMsgIfDebug(()=>console.log("PostHog Debug","Loading surveys is disabled.")),[];let e=`${this.host}/api/surveys/?token=${this.apiKey}`,t={method:"GET",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"}},r=await this.fetchWithRetry(e,t).then(e=>{if(200!==e.status||!e.json){let t=`Surveys API could not be loaded: ${e.status}`,r=Error(t);this.logMsgIfDebug(()=>console.error(r)),this._events.emit("error",Error(t));return}return e.json()}).catch(e=>{this.logMsgIfDebug(()=>console.error("Surveys API could not be loaded",e)),this._events.emit("error",e)}),i=r?.surveys;return i&&this.logMsgIfDebug(()=>console.log("PostHog Debug","Surveys fetched from API: ",JSON.stringify(i))),i??[]}get props(){return this._props||(this._props=this.getPersistedProperty(a.Props)),this._props||{}}set props(e){this._props=e}async register(e){this.wrap(()=>{this.props={...this.props,...e},this.setPersistedProperty(a.Props,this.props)})}async unregister(e){this.wrap(()=>{delete this.props[e],this.setPersistedProperty(a.Props,this.props)})}enqueue(e,t,r){this.wrap(()=>{if(this.optedOut)return void this._events.emit(e,"Library is disabled. Not sending event. To re-enable, call posthog.optIn()");let i=this.prepareMessage(e,t,r),s=this.getPersistedProperty(a.Queue)||[];s.length>=this.maxQueueSize&&(s.shift(),this.logMsgIfDebug(()=>console.info("Queue is full, the oldest event is dropped."))),s.push({message:i}),this.setPersistedProperty(a.Queue,s),this._events.emit(e,i),s.length>=this.flushAt&&this.flushBackground(),this.flushInterval&&!this._flushTimer&&(this._flushTimer=X(()=>this.flushBackground(),this.flushInterval))})}async sendImmediate(e,t,r){if(this.disabled)return void this.logMsgIfDebug(()=>console.warn("[PostHog] The client is disabled"));if(this._isInitialized||await this._initPromise,this.optedOut)return void this._events.emit(e,"Library is disabled. Not sending event. To re-enable, call posthog.optIn()");let i={api_key:this.apiKey,batch:[this.prepareMessage(e,t,r)],sent_at:Q()};this.historicalMigration&&(i.historical_migration=!0);let a=JSON.stringify(i),s="form"===this.captureMode?`${this.host}/e/?ip=1&_=${Y()}&v=${this.getLibraryVersion()}`:`${this.host}/batch/`,o="form"===this.captureMode?{method:"POST",mode:"no-cors",credentials:"omit",headers:{...this.getCustomHeaders(),"Content-Type":"application/x-www-form-urlencoded"},body:`data=${encodeURIComponent(ei.compressToBase64(a))}&compression=lz64`}:{method:"POST",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"},body:a};try{await this.fetchWithRetry(s,o)}catch(e){this._events.emit("error",e)}}prepareMessage(e,t,r){let i={...t,type:e,library:this.getLibraryId(),library_version:this.getLibraryVersion(),timestamp:r?.timestamp?r?.timestamp:Q(),uuid:r?.uuid?r.uuid:_()};return(r?.disableGeoip??this.disableGeoip)&&(i.properties||(i.properties={}),i.properties.$geoip_disable=!0),i.distinctId&&(i.distinct_id=i.distinctId,delete i.distinctId),i}clearFlushTimer(){this._flushTimer&&(clearTimeout(this._flushTimer),this._flushTimer=void 0)}flushBackground(){this.flush().catch(async e=>{await en(e)})}async flush(){let e=Z([this.flushPromise]).then(()=>this._flush());return this.flushPromise=e,this.addPendingPromise(e),Z([e]).then(()=>{this.flushPromise===e&&(this.flushPromise=null)}),e}getCustomHeaders(){let e=this.getCustomUserAgent(),t={};return e&&""!==e&&(t["User-Agent"]=e),t}async _flush(){this.clearFlushTimer(),await this._initPromise;let e=this.getPersistedProperty(a.Queue)||[];if(!e.length)return;let t=[],r=e.length;for(;e.length>0&&t.length<r;){let r=e.slice(0,this.maxBatchSize),i=r.map(e=>e.message),s=()=>{let t=(this.getPersistedProperty(a.Queue)||[]).slice(r.length);this.setPersistedProperty(a.Queue,t),e=t},o={api_key:this.apiKey,batch:i,sent_at:Q()};this.historicalMigration&&(o.historical_migration=!0);let n=JSON.stringify(o),l="form"===this.captureMode?`${this.host}/e/?ip=1&_=${Y()}&v=${this.getLibraryVersion()}`:`${this.host}/batch/`,c="form"===this.captureMode?{method:"POST",mode:"no-cors",credentials:"omit",headers:{...this.getCustomHeaders(),"Content-Type":"application/x-www-form-urlencoded"},body:`data=${encodeURIComponent(ei.compressToBase64(n))}&compression=lz64`}:{method:"POST",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"},body:n},u={retryCheck:e=>!ec(e)&&el(e)};try{await this.fetchWithRetry(l,c,u)}catch(e){if(ec(e)&&i.length>1){this.maxBatchSize=Math.max(1,Math.floor(i.length/2)),this.logMsgIfDebug(()=>console.warn(`Received 413 when sending batch of size ${i.length}, reducing batch size to ${this.maxBatchSize}`));continue}throw e instanceof eo||s(),this._events.emit("error",e),e}s(),t.push(...i)}this._events.emit("flush",t)}async fetchWithRetry(e,t,r,i){var a;(a=AbortSignal).timeout??(a.timeout=function(e){let t=new AbortController;return setTimeout(()=>t.abort(),e),t.signal});let s=t.body?t.body:"",o=-1;try{o=Buffer.byteLength(s,"utf8")}catch{o=new TextEncoder().encode(s).length}return await z(async()=>{let r=null;try{r=await this.fetch(e,{signal:AbortSignal.timeout(i??this.requestTimeout),...t})}catch(e){throw new eo(e)}if("no-cors"!==t.mode&&(r.status<200||r.status>=400))throw new es(r,o);return r},{...this._retryOptions,...r})}async _shutdown(e=3e4){await this._initPromise;let t=!1;this.clearFlushTimer();let r=async()=>{try{for(await Promise.all(Object.values(this.pendingPromises));;){let e=this.getPersistedProperty(a.Queue)||[];if(0===e.length||(await this.flush(),t))break}}catch(e){if(!el(e))throw e;await en(e)}};return Promise.race([new Promise((r,i)=>{X(()=>{this.logMsgIfDebug(()=>console.error("Timed out while shutting down PostHog")),t=!0,i("Timeout while shutting down PostHog. Some events may not have been sent.")},e)}),r()])}async shutdown(e=3e4){return this.shutdownPromise?this.logMsgIfDebug(()=>console.warn("shutdown() called while already shutting down. shutdown() is meant to be called once before process exit - use flush() for per-request cleanup")):this.shutdownPromise=this._shutdown(e).finally(()=>{this.shutdownPromise=null}),this.shutdownPromise}}let ed="undefined"!=typeof fetch?fetch:void 0!==globalThis.fetch?globalThis.fetch:void 0;if(!ed){let e=require("axios");ed=async(t,r)=>{let i=await e.request({url:t,headers:r.headers,method:r.method.toLowerCase(),data:r.body,signal:r.signal,validateStatus:()=>!0});return{status:i.status,text:async()=>i.data,json:async()=>i.data}}}var eh=ed;class ef{constructor(e){this.factory=e}async getValue(){return void 0!==this.value?this.value:(void 0===this.initializationPromise&&(this.initializationPromise=(async()=>{try{let e=await this.factory();return this.value=e,e}finally{this.initializationPromise=void 0}})()),this.initializationPromise)}isInitialized(){return void 0!==this.value}async waitForInitialization(){this.isInitialized()||await this.getValue()}}let ep=new ef(async()=>{try{return await Promise.resolve().then(r.t.bind(r,55511,19))}catch{return}});async function ey(){return await ep.getValue()}let eg=new ef(async()=>{if(void 0!==globalThis.crypto?.subtle)return globalThis.crypto.subtle;try{let e=await ep.getValue();if(e?.webcrypto?.subtle)return e.webcrypto.subtle}catch{}});async function em(){return await eg.getValue()}async function ew(e){let t=await ey();if(t)return t.createHash("sha1").update(e).digest("hex");let r=await em();if(r)return Array.from(new Uint8Array(await r.digest("SHA-1",new TextEncoder().encode(e)))).map(e=>e.toString(16).padStart(2,"0")).join("");throw Error("No crypto implementation available. Tried Node Crypto API and Web SubtleCrypto API")}let eb=["is_not"];class ev extends Error{constructor(e){super(),Error.captureStackTrace(this,this.constructor),this.name="ClientError",this.message=e,Object.setPrototypeOf(this,ev.prototype)}}class eA extends Error{constructor(e){super(e),this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor),Object.setPrototypeOf(this,eA.prototype)}}class eE{constructor({pollingInterval:e,personalApiKey:t,projectApiKey:r,timeout:i,host:a,customHeaders:s,...o}){this.debugMode=!1,this.shouldBeginExponentialBackoff=!1,this.backOffCount=0,this.pollingInterval=e,this.personalApiKey=t,this.featureFlags=[],this.featureFlagsByKey={},this.groupTypeMapping={},this.cohorts={},this.loadedSuccessfullyOnce=!1,this.timeout=i,this.projectApiKey=r,this.host=a,this.poller=void 0,this.fetch=o.fetch||eh,this.onError=o.onError,this.customHeaders=s,this.onLoad=o.onLoad,this.loadFeatureFlags()}debug(e=!0){this.debugMode=e}logMsgIfDebug(e){this.debugMode&&e()}async getFeatureFlag(e,t,r={},i={},a={}){let s,o;if(await this.loadFeatureFlags(),!this.loadedSuccessfullyOnce)return s;for(let t of this.featureFlags)if(e===t.key){o=t;break}if(void 0!==o)try{s=await this.computeFlagLocally(o,t,r,i,a),this.logMsgIfDebug(()=>console.debug(`Successfully computed flag locally: ${e} -> ${s}`))}catch(t){t instanceof eA?this.logMsgIfDebug(()=>console.debug(`InconclusiveMatchError when computing flag locally: ${e}: ${t}`)):t instanceof Error&&this.onError?.(Error(`Error computing flag locally: ${e}: ${t}`))}return s}async computeFeatureFlagPayloadLocally(e,t){let r;if(await this.loadFeatureFlags(),this.loadedSuccessfullyOnce){if("boolean"==typeof t?r=this.featureFlagsByKey?.[e]?.filters?.payloads?.[t.toString()]:"string"==typeof t&&(r=this.featureFlagsByKey?.[e]?.filters?.payloads?.[t]),null==r)return null;try{return JSON.parse(r)}catch{return r}}}async getAllFlagsAndPayloads(e,t={},r={},i={}){await this.loadFeatureFlags();let a={},s={},o=0==this.featureFlags.length;return await Promise.all(this.featureFlags.map(async n=>{try{let o=await this.computeFlagLocally(n,e,t,r,i);a[n.key]=o;let l=await this.computeFeatureFlagPayloadLocally(n.key,o);l&&(s[n.key]=l)}catch(e){e instanceof eA||e instanceof Error&&this.onError?.(Error(`Error computing flag locally: ${n.key}: ${e}`)),o=!0}})),{response:a,payloads:s,fallbackToDecide:o}}async computeFlagLocally(e,t,r={},i={},a={}){if(e.ensure_experience_continuity)throw new eA("Flag has experience continuity enabled");if(!e.active)return!1;let s=(e.filters||{}).aggregation_group_type_index;if(void 0==s)return await this.matchFeatureFlagProperties(e,t,i);{let t=this.groupTypeMapping[String(s)];if(!t)throw this.logMsgIfDebug(()=>console.warn(`[FEATURE FLAGS] Unknown group type index ${s} for feature flag ${e.key}`)),new eA("Flag has unknown group type index");if(!(t in r))return this.logMsgIfDebug(()=>console.warn(`[FEATURE FLAGS] Can't compute group feature flag: ${e.key} without group names passed in`)),!1;let i=a[t];return await this.matchFeatureFlagProperties(e,r[t],i)}}async matchFeatureFlagProperties(e,t,r){let i,a=e.filters||{},s=a.groups||[],o=!1;for(let n of[...s].sort((e,t)=>{let r=!!e.variant,i=!!t.variant;return r&&i?0:r?-1:1*!!i}))try{if(await this.isConditionMatch(e,t,n,r)){let r=n.variant,s=a.multivariate?.variants||[];i=r&&s.some(e=>e.key===r)?r:await this.getMatchingVariant(e,t)||!0;break}}catch(e){if(e instanceof eA)o=!0;else throw e}if(void 0!==i)return i;if(o)throw new eA("Can't determine if feature flag is enabled or not with given properties");return!1}async isConditionMatch(e,t,r,i){let a=r.rollout_percentage,s=e=>{this.logMsgIfDebug(()=>console.warn(e))};if((r.properties||[]).length>0){for(let e of r.properties){let t=e.type,r=!1;if(!("cohort"===t?function e(t,r,i,a=!1){let s=String(t.value);if(!(s in i))throw new eA("can't match cohort without a given cohort property value");return function t(r,i,a,s=!1){if(!r)return!0;let o=r.type,n=r.values;if(!n||0===n.length)return!0;let l=!1;if("values"in n[0]){for(let e of n)try{let r=t(e,i,a,s);if("AND"===o){if(!r)return!1}else if(r)return!0}catch(t){if(t instanceof eA)s&&console.debug(`Failed to compute property ${e} locally: ${t}`),l=!0;else throw t}if(l)throw new eA("Can't match cohort without a given cohort property value");return"AND"===o}for(let t of n)try{let r;r="cohort"===t.type?e(t,i,a,s):e_(t,i);let n=t.negation||!1;if("AND"===o){if(!r&&!n||r&&n)return!1}else if(r&&!n||!r&&n)return!0}catch(e){if(e instanceof eA)s&&console.debug(`Failed to compute property ${t} locally: ${e}`),l=!0;else throw e}if(l)throw new eA("can't match cohort without a given cohort property value");return"AND"===o}(i[s],r,i,a)}(e,i,this.cohorts,this.debugMode):e_(e,i,s)))return!1}if(void 0==a)return!0}return!(void 0!=a&&await eS(e.key,t)>a/100)}async getMatchingVariant(e,t){let r=await eS(e.key,t,"variant"),i=this.variantLookupTable(e).find(e=>r>=e.valueMin&&r<e.valueMax);if(i)return i.key}variantLookupTable(e){let t=[],r=0,i=0,a=e.filters||{};return(a.multivariate?.variants||[]).forEach(e=>{i=r+e.rollout_percentage/100,t.push({valueMin:r,valueMax:i,key:e.key}),r=i}),t}async loadFeatureFlags(e=!1){(!this.loadedSuccessfullyOnce||e)&&await this._loadFeatureFlags()}isLocalEvaluationReady(){return(this.loadedSuccessfullyOnce??!1)&&(this.featureFlags?.length??0)>0}getPollingInterval(){return this.shouldBeginExponentialBackoff?Math.min(6e4,this.pollingInterval*2**this.backOffCount):this.pollingInterval}async _loadFeatureFlags(){this.poller&&(clearTimeout(this.poller),this.poller=void 0),this.poller=setTimeout(()=>this._loadFeatureFlags(),this.getPollingInterval());try{let e=await this._requestFeatureFlagDefinitions();if(!e)return;switch(e.status){case 401:throw this.shouldBeginExponentialBackoff=!0,this.backOffCount+=1,new ev(`Your project key or personal API key is invalid. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);case 402:console.warn("[FEATURE FLAGS] Feature flags quota limit exceeded - unsetting all local flags. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts"),this.featureFlags=[],this.featureFlagsByKey={},this.groupTypeMapping={},this.cohorts={};return;case 403:throw this.shouldBeginExponentialBackoff=!0,this.backOffCount+=1,new ev(`Your personal API key does not have permission to fetch feature flag definitions for local evaluation. Setting next polling interval to ${this.getPollingInterval()}ms. Are you sure you're using the correct personal and Project API key pair? More information: https://posthog.com/docs/api/overview`);case 429:throw this.shouldBeginExponentialBackoff=!0,this.backOffCount+=1,new ev(`You are being rate limited. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);case 200:{let t=await e.json()??{};if(!("flags"in t))return void this.onError?.(Error(`Invalid response when getting feature flags: ${JSON.stringify(t)}`));this.featureFlags=t.flags??[],this.featureFlagsByKey=this.featureFlags.reduce((e,t)=>(e[t.key]=t,e),{}),this.groupTypeMapping=t.group_type_mapping||{},this.cohorts=t.cohorts||{},this.loadedSuccessfullyOnce=!0,this.shouldBeginExponentialBackoff=!1,this.backOffCount=0,this.onLoad?.(this.featureFlags.length);break}default:return}}catch(e){e instanceof ev&&this.onError?.(e)}}getPersonalApiKeyRequestOptions(e="GET"){return{method:e,headers:{...this.customHeaders,"Content-Type":"application/json",Authorization:`Bearer ${this.personalApiKey}`}}}async _requestFeatureFlagDefinitions(){let e=`${this.host}/api/feature_flag/local_evaluation?token=${this.projectApiKey}&send_cohorts`,t=this.getPersonalApiKeyRequestOptions(),r=null;if(this.timeout&&"number"==typeof this.timeout){let e=new AbortController;r=X(()=>{e.abort()},this.timeout),t.signal=e.signal}try{return await this.fetch(e,t)}finally{clearTimeout(r)}}stopPoller(){clearTimeout(this.poller)}_requestRemoteConfigPayload(e){let t=`${this.host}/api/projects/@current/feature_flags/${e}/remote_config/`,r=this.getPersonalApiKeyRequestOptions(),i=null;if(this.timeout&&"number"==typeof this.timeout){let e=new AbortController;i=X(()=>{e.abort()},this.timeout),r.signal=e.signal}try{return this.fetch(t,r)}finally{clearTimeout(i)}}}async function eS(e,t,r=""){return parseInt((await ew(`${e}.${t}${r}`)).slice(0,15),16)/0x1000000000000000}function e_(e,t,r){let i=e.key,a=e.value,s=e.operator||"exact";if(i in t){if("is_not_set"===s)throw new eA("Operator is_not_set is not supported")}else throw new eA(`Property ${i} not found in propertyValues`);let o=t[i];if(null==o&&!eb.includes(s))return r&&r(`Property ${i} cannot have a value of null/undefined with the ${s} operator`),!1;function n(e,t){return Array.isArray(e)?e.map(e=>String(e).toLowerCase()).includes(String(t).toLowerCase()):String(e).toLowerCase()===String(t).toLowerCase()}function l(e,t,r){if("gt"===r)return e>t;if("gte"===r)return e>=t;if("lt"===r)return e<t;if("lte"===r)return e<=t;throw Error(`Invalid operator: ${r}`)}switch(s){case"exact":return n(a,o);case"is_not":return!n(a,o);case"is_set":return i in t;case"icontains":return String(o).toLowerCase().includes(String(a).toLowerCase());case"not_icontains":return!String(o).toLowerCase().includes(String(a).toLowerCase());case"regex":return eP(String(a))&&null!==String(o).match(String(a));case"not_regex":return eP(String(a))&&null===String(o).match(String(a));case"gt":case"gte":case"lt":case"lte":{let e="number"==typeof a?a:null;if("string"==typeof a)try{e=parseFloat(a)}catch(e){}if(null==e||null==o)return l(String(o),String(a),s);if("string"==typeof o)return l(o,String(a),s);return l(o,e,s)}case"is_date_after":case"is_date_before":{let e=function(e){let t=e.match(/^-?(?<number>[0-9]+)(?<interval>[a-z])$/),r=new Date(new Date().toISOString());if(!t)return null;{if(!t.groups)return null;let e=parseInt(t.groups.number);if(e>=1e4)return null;let i=t.groups.interval;if("h"==i)r.setUTCHours(r.getUTCHours()-e);else if("d"==i)r.setUTCDate(r.getUTCDate()-e);else if("w"==i)r.setUTCDate(r.getUTCDate()-7*e);else if("m"==i)r.setUTCMonth(r.getUTCMonth()-e);else{if("y"!=i)return null;r.setUTCFullYear(r.getUTCFullYear()-e)}return r}}(String(a));if(null==e&&(e=ek(a)),null==e)throw new eA(`Invalid date: ${a}`);let t=ek(o);if(["is_date_before"].includes(s))return t<e;return t>e}default:throw new eA(`Unknown operator: ${s}`)}}function eP(e){try{return new RegExp(e),!0}catch(e){return!1}}function ek(e){if(e instanceof Date)return e;if("string"==typeof e||"number"==typeof e){let t=new Date(e);if(!isNaN(t.valueOf()))return t;throw new eA(`${e} is in an invalid date format`)}throw new eA(`The date provided ${e} must be a string, number, or date object`)}class eC{constructor(){this._memoryStorage={}}getProperty(e){return this._memoryStorage[e]}setProperty(e,t){this._memoryStorage[e]=null!==t?t:void 0}}class eT extends eu{constructor(e,t={}){if(super(e,t),this._memoryStorage=new eC,this.options=t,this.options.featureFlagsPollingInterval="number"==typeof t.featureFlagsPollingInterval?Math.max(t.featureFlagsPollingInterval,100):3e4,t.personalApiKey){if(t.personalApiKey.includes("phc_"))throw Error('Your Personal API key is invalid. These keys are prefixed with "phx_" and can be created in PostHog project settings.');this.featureFlagsPoller=new eE({pollingInterval:this.options.featureFlagsPollingInterval,personalApiKey:t.personalApiKey,projectApiKey:e,timeout:t.requestTimeout??1e4,host:this.host,fetch:t.fetch,onError:e=>{this._events.emit("error",e)},onLoad:e=>{this._events.emit("localEvaluationFlagsLoaded",e)},customHeaders:this.getCustomHeaders()})}this.errorTracking=new R(this,t),this.distinctIdHasSentFlagCalls={},this.maxCacheSize=t.maxCacheSize||5e4}getPersistedProperty(e){return this._memoryStorage.getProperty(e)}setPersistedProperty(e,t){return this._memoryStorage.setProperty(e,t)}fetch(e,t){return this.options.fetch?this.options.fetch(e,t):eh(e,t)}getLibraryVersion(){return"4.17.2"}getCustomUserAgent(){return`${this.getLibraryId()}/${this.getLibraryVersion()}`}enable(){return super.optIn()}disable(){return super.optOut()}debug(e=!0){super.debug(e),this.featureFlagsPoller?.debug(e)}capture(e){"string"==typeof e&&this.logMsgIfDebug(()=>console.warn("Called capture() with a string as the first argument when an object was expected."));let{distinctId:t,event:r,properties:i,groups:a,sendFeatureFlags:s,timestamp:o,disableGeoip:n,uuid:l}=e,c=e=>{super.captureStateless(t,r,e,{timestamp:o,disableGeoip:n,uuid:l})},u=async(e,t,r)=>(await super.getFeatureFlagsStateless(e,t,void 0,void 0,r)).flags,d=Promise.resolve().then(async()=>{if(s)return await u(t,a,n);if("$feature_flag_called"===r)return{};if((this.featureFlagsPoller?.featureFlags?.length||0)>0){let e={};for(let[t,r]of Object.entries(a||{}))e[t]=String(r);return await this.getAllFlags(t,{groups:e,disableGeoip:n,onlyEvaluateLocally:!0})}return{}}).then(e=>{let t={};if(e)for(let[r,i]of Object.entries(e))t[`$feature/${r}`]=i;let r=Object.keys(e||{}).filter(t=>e?.[t]!==!1).sort();return r.length>0&&(t.$active_feature_flags=r),t}).catch(()=>({})).then(e=>{c({...e,...i,$groups:a})});this.addPendingPromise(d)}async captureImmediate(e){"string"==typeof e&&this.logMsgIfDebug(()=>console.warn("Called capture() with a string as the first argument when an object was expected."));let{distinctId:t,event:r,properties:i,groups:a,sendFeatureFlags:s,timestamp:o,disableGeoip:n,uuid:l}=e,c=e=>super.captureStatelessImmediate(t,r,e,{timestamp:o,disableGeoip:n,uuid:l}),u=async(e,t,r)=>(await super.getFeatureFlagsStateless(e,t,void 0,void 0,r)).flags,d=Promise.resolve().then(async()=>{if(s)return await u(t,a,n);if("$feature_flag_called"===r)return{};if((this.featureFlagsPoller?.featureFlags?.length||0)>0){let e={};for(let[t,r]of Object.entries(a||{}))e[t]=String(r);return await this.getAllFlags(t,{groups:e,disableGeoip:n,onlyEvaluateLocally:!0})}return{}}).then(e=>{let t={};if(e)for(let[r,i]of Object.entries(e))t[`$feature/${r}`]=i;let r=Object.keys(e||{}).filter(t=>e?.[t]!==!1).sort();return r.length>0&&(t.$active_feature_flags=r),t}).catch(()=>({})).then(e=>{c({...e,...i,$groups:a})});await d}identify({distinctId:e,properties:t,disableGeoip:r}){let i=t?.$set_once;delete t?.$set_once;let a=t?.$set||t;super.identifyStateless(e,{$set:a,$set_once:i},{disableGeoip:r})}async identifyImmediate({distinctId:e,properties:t,disableGeoip:r}){let i=t?.$set_once;delete t?.$set_once;let a=t?.$set||t;await super.identifyStatelessImmediate(e,{$set:a,$set_once:i},{disableGeoip:r})}alias(e){super.aliasStateless(e.alias,e.distinctId,void 0,{disableGeoip:e.disableGeoip})}async aliasImmediate(e){await super.aliasStatelessImmediate(e.alias,e.distinctId,void 0,{disableGeoip:e.disableGeoip})}isLocalEvaluationReady(){return this.featureFlagsPoller?.isLocalEvaluationReady()??!1}async waitForLocalEvaluationReady(e=3e4){return!!this.isLocalEvaluationReady()||void 0!==this.featureFlagsPoller&&new Promise(t=>{let r=setTimeout(()=>{i(),t(!1)},e),i=this._events.on("localEvaluationFlagsLoaded",e=>{clearTimeout(r),i(),t(e>0)})})}async getFeatureFlag(e,t,r){let i,a,{groups:s,disableGeoip:o}=r||{},{onlyEvaluateLocally:n,sendFeatureFlagEvents:l,personProperties:c,groupProperties:u}=r||{},d=this.addLocalPersonAndGroupProperties(t,s,c,u);c=d.allPersonProperties,u=d.allGroupProperties,void 0==n&&(n=!1),void 0==l&&(l=!0);let h=await this.featureFlagsPoller?.getFeatureFlag(e,t,s,c,u),f=void 0!==h;if(!f&&!n){let r=await super.getFeatureFlagDetailStateless(e,t,s,c,u,o);if(void 0===r)return;h=G(a=r.response),i=r?.requestId}let p=`${e}_${h}`;return!l||t in this.distinctIdHasSentFlagCalls&&this.distinctIdHasSentFlagCalls[t].includes(p)||(Object.keys(this.distinctIdHasSentFlagCalls).length>=this.maxCacheSize&&(this.distinctIdHasSentFlagCalls={}),Array.isArray(this.distinctIdHasSentFlagCalls[t])?this.distinctIdHasSentFlagCalls[t].push(p):this.distinctIdHasSentFlagCalls[t]=[p],this.capture({distinctId:t,event:"$feature_flag_called",properties:{$feature_flag:e,$feature_flag_response:h,$feature_flag_id:a?.metadata?.id,$feature_flag_version:a?.metadata?.version,$feature_flag_reason:a?.reason?.description??a?.reason?.code,locally_evaluated:f,[`$feature/${e}`]:h,$feature_flag_request_id:i},groups:s,disableGeoip:o})),h}async getFeatureFlagPayload(e,t,r,i){let a,{groups:s,disableGeoip:o}=i||{},{onlyEvaluateLocally:n,sendFeatureFlagEvents:l,personProperties:c,groupProperties:u}=i||{},d=this.addLocalPersonAndGroupProperties(t,s,c,u);return c=d.allPersonProperties,u=d.allGroupProperties,void 0!==this.featureFlagsPoller&&(r||(r=await this.getFeatureFlag(e,t,{...i,onlyEvaluateLocally:!0,sendFeatureFlagEvents:!1})),r&&(a=await this.featureFlagsPoller?.computeFeatureFlagPayloadLocally(e,r))),void 0==n&&(n=!1),void 0==l&&(l=!0),void 0==n&&(n=!1),void 0!==a||n||(a=await super.getFeatureFlagPayloadStateless(e,t,s,c,u,o)),a}async getRemoteConfigPayload(e){return(await this.featureFlagsPoller?._requestRemoteConfigPayload(e))?.json()}async isFeatureEnabled(e,t,r){let i=await this.getFeatureFlag(e,t,r);if(void 0!==i)return!!i}async getAllFlags(e,t){return(await this.getAllFlagsAndPayloads(e,t)).featureFlags||{}}async getAllFlagsAndPayloads(e,t){let{groups:r,disableGeoip:i}=t||{},{onlyEvaluateLocally:a,personProperties:s,groupProperties:o}=t||{},n=this.addLocalPersonAndGroupProperties(e,r,s,o);s=n.allPersonProperties,o=n.allGroupProperties,void 0==a&&(a=!1);let l=await this.featureFlagsPoller?.getAllFlagsAndPayloads(e,r,s,o),c={},u={},d=!0;if(l&&(c=l.response,u=l.payloads,d=l.fallbackToDecide),d&&!a){let t=await super.getFeatureFlagsAndPayloadsStateless(e,r,s,o,i);c={...c,...t.flags||{}},u={...u,...t.payloads||{}}}return{featureFlags:c,featureFlagPayloads:u}}groupIdentify({groupType:e,groupKey:t,properties:r,distinctId:i,disableGeoip:a}){super.groupIdentifyStateless(e,t,r,{disableGeoip:a},i)}async reloadFeatureFlags(){await this.featureFlagsPoller?.loadFeatureFlags(!0)}async _shutdown(e){return this.featureFlagsPoller?.stopPoller(),super._shutdown(e)}addLocalPersonAndGroupProperties(e,t,r,i){let a={distinct_id:e,...r||{}},s={};if(t)for(let e of Object.keys(t))s[e]={$group_key:t[e],...i?.[e]||{}};return{allPersonProperties:a,allGroupProperties:s}}captureException(e,t,r){let i=Error("PostHog syntheticException");R.captureException(this,e,{syntheticException:i},t,r)}}let eI=/\(error: (.*)\)/;function ex(e){return parseInt(e||"",10)||void 0}R.stackParser=function(e){let t=[[90,function(e){let t=/^\s*[-]{4,}$/,r=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return i=>{let a=i.match(r);if(a){let t,r,i,s,o;if(a[1]){let e=(i=a[1]).lastIndexOf(".");if("."===i[e-1]&&e--,e>0){t=i.slice(0,e),r=i.slice(e+1);let a=t.indexOf(".Module");a>0&&(i=i.slice(a+1),t=t.slice(0,a))}s=void 0}r&&(s=t,o=r),"<anonymous>"===r&&(o=void 0,i=void 0),void 0===i&&(o=o||"?",i=s?`${s}.${o}`:o);let n=a[2]?.startsWith("file://")?a[2].slice(7):a[2],l="native"===a[5];return n?.match(/\/[A-Z]:/)&&(n=n.slice(1)),n||!a[5]||l||(n=a[5]),{filename:n?decodeURI(n):void 0,module:e?e(n):void 0,function:i,lineno:ex(a[3]),colno:ex(a[4]),in_app:function(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&void 0!==e&&!e.includes("node_modules/")}(n||"",l),platform:"node:javascript"}}if(i.match(t))return{filename:i,platform:"node:javascript"}}}(e)]].sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,r=0)=>{let i=[],a=e.split("\n");for(let e=r;e<a.length;e++){let r=a[e];if(r.length>1024)continue;let s=eI.test(r)?r.replace(eI,"$1"):r;if(!s.match(/\S*Error: /)){for(let e of t){let t=e(s);if(t){i.push(t);break}}if(i.length>=50)break}}var s=i;if(!s.length)return[];let o=Array.from(s);return o.reverse(),o.slice(0,50).map(e=>{var t;return{...e,filename:e.filename||((t=o)[t.length-1]||{}).filename,function:e.function||"?"}})}}(function(e=process.argv[1]?(0,y.dirname)(process.argv[1]):process.cwd(),t="\\"===y.sep){let r=t?H(e):e;return e=>{if(!e)return;let i=t?H(e):e,{dir:a,base:s,ext:o}=y.posix.parse(i);(".js"===o||".mjs"===o||".cjs"===o)&&(s=s.slice(0,-1*o.length));let n=decodeURIComponent(s);a||(a=".");let l=a.lastIndexOf("/node_modules");if(l>-1)return`${a.slice(l+14).replace(/\//g,".")}:${n}`;if(a.startsWith(r)){let e=a.slice(r.length+1).replace(/\//g,".");return e?`${e}:${n}`:n}return n}}()),R.frameModifiers=[W];class eF extends eT{getLibraryId(){return"posthog-node"}}},61824:(e,t,r)=>{r.d(t,{A:()=>i});let i=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0}},63669:(e,t,r)=>{function i(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}r.d(t,{A:()=>a,t:()=>s});let a=(e,...t)=>i("Key must be ",e,...t);function s(e,t,...r){return i(`Key for the ${e} algorithm must be `,t,...r)}},67407:(e,t,r)=>{r.d(t,{A:()=>i});function i(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},69472:(e,t,r)=>{r.d(t,{A:()=>s,g:()=>o});var i=r(89605),a=r(22556);let s=e=>(0,a.A)(e)||(0,i.R)(e),o=["KeyObject"];(globalThis.CryptoKey||i.A?.CryptoKey)&&o.push("CryptoKey")},70556:(e,t,r)=>{r.d(t,{A:()=>d,I:()=>h});var i=r(63669),a=r(69472),s=r(6798);let o=e=>e?.[Symbol.toStringTag],n=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},l=(e,t,r,l)=>{if(!(t instanceof Uint8Array)){if(l&&s.ll(t)){if(s.t9(t)&&n(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!(0,a.A)(t))throw TypeError((0,i.t)(e,t,...a.g,"Uint8Array",l?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${o(t)} instances for symmetric algorithms must be of type "secret"`)}},c=(e,t,r,l)=>{if(l&&s.ll(t))switch(r){case"sign":if(s.W2(t)&&n(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(s.M3(t)&&n(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!(0,a.A)(t))throw TypeError((0,i.t)(e,t,...a.g,l?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${o(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${o(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${o(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${o(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${o(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function u(e,t,r,i){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?l(t,r,i,e):c(t,r,i,e)}let d=u.bind(void 0,!1),h=u.bind(void 0,!0)},76285:(e,t,r)=>{r.d(t,{Og:()=>l});var i=r(13690),a=r(77598);let s=e=>e.d?(0,a.createPrivateKey)({format:"jwk",key:e}):(0,a.createPublicKey)({format:"jwk",key:e});var o=r(14295),n=r(67407);async function l(e,t){if(!(0,n.A)(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return(0,i.D4)(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new o.T0('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return s({...e,alg:t});default:throw new o.T0('Unsupported "kty" (Key Type) Parameter value')}}},89605:(e,t,r)=>{r.d(t,{A:()=>s,R:()=>o});var i=r(77598),a=r(57975);let s=i.webcrypto,o=e=>a.types.isCryptoKey(e)}};