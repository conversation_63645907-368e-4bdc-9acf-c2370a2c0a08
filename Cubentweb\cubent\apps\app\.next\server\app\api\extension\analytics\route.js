(()=>{var e={};e.id=2428,e.ids=[2428],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>N});var s=r(8741),n=r(62923),a=r(54726),i=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),d=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,a;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((a=e,`[clerk debug end: ${a}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},l=(e,t)=>(...r)=>{let s=("string"==typeof e?d(e,c):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),g=r(37081),h=r(27322),x=r(6264);function m(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return h.r0.stringify(r,{pad:!1})}async function A(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,h.hJ)(r.algorithm);if(!n)return{errors:[new x.xy(`Unsupported algorithm ${r.algorithm}`)]};let a=await (0,h.Fh)(t,n,"sign"),i=r.header||{typ:"JWT"};i.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=m(i),u=m(e),c=`${o}.${u}`;try{let e=await h.fA.crypto.subtle.sign(n,a,s.encode(c));return{data:`${c}.${h.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new x.xy(e?.message)]}}}(0,g.C)(h.J0);var f=(0,g.R)(h.iU);(0,g.C)(A),(0,g.C)(h.nk);var y=r(97495),w=r(60606);function v(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,i,o;let u,c=(0,y.NE)(e,"AuthStatus"),d=(0,y.NE)(e,"AuthToken"),l=(0,y.NE)(e,"AuthMessage"),p=(0,y.NE)(e,"AuthReason"),g=(0,y.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:c,authMessage:l,authReason:p});let h=(0,y._b)(e,s.AA.Headers.ClerkRequestData),x=(0,w.Kk)(h),m={secretKey:(null==r?void 0:r.secretKey)||x.secretKey||a.rB,publishableKey:x.publishableKey||a.At,apiUrl:a.H$,apiVersion:a.mG,authStatus:c,authMessage:l,authReason:p,treatPendingAsSignedOut:t};if(null==(i=r.logger)||i.debug("auth options",m),c&&c===s.TD.SignedIn){(0,w._l)(d,m.secretKey,g);let e=f(d);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(m,e.raw.text,e.payload)}else u=(0,s.wI)(m);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(m,u.sessionStatus)),u}var b=r(68478);let U=({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>async(n,a)=>{if((0,i.zz)((0,y._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,y.Zd)(n)){p.M&&(0,w.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,w.$K)(n,t)}return v(n,{...a,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>(r,n)=>((0,i.zz)((0,y._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,w.$K)(r,t),v(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,b.AG)()});let k={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},q=e=>{var t,r;return!!e.headers.get(k.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(k.Headers.NextAction))},S=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||M(e)||D(e)},M=e=>!!e.headers.get(k.Headers.NextUrl)&&!q(e)||T(),T=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},D=e=>!!e.headers.get(k.Headers.NextjsData);var j=r(23056);let N=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,j.TG)(),i=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await U({debugLoggerName:"auth()",noAuthStatusMessage:(0,b.sd)("auth",await i())})(t,{treatPendingAsSignedOut:e}),u=(0,y.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},i=(0,s.tl)(t),c=i.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||i.cookies.get(s.AA.Cookies.DevBrowser),d=(0,y._b)(t,s.AA.Headers.ClerkRequestData),l=(0,w.Kk)(d);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:c,baseUrl:i.clerkUrl.toString(),publishableKey:l.publishableKey||a.At,signInUrl:l.signInUrl||a.qW,signUpUrl:l.signUpUrl||a.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};N.protect=async(...e)=>{r(1447);let t=await (0,j.TG)(),s=await N();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:a}=e;return async(...e)=>{var i,o,u,c,d,l;let p=(null==(i=e[0])?void 0:i.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],g=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),h=(null==(d=e[0])?void 0:d.unauthorizedUrl)||(null==(l=e[1])?void 0:l.unauthorizedUrl),x=()=>h?s(h):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:x():r.has(p)?r:x():r:g?s(g):S(a)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>g,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>d});var n=r(26142),a=r(94327),i=r(34862),o=r(37838),u=r(18815),c=r(26239);async function d(e){try{var t,r,s,n,a,i,d;let g,{userId:h}=await (0,o.j)();if(!h)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:x}=new URL(e.url),m=x.get("period")||"30d",A=x.get("groupBy")||"day",f=await u.database.user.findUnique({where:{clerkId:h}});if(!f)return c.NextResponse.json({error:"User not found"},{status:404});let y=new Date;switch(m){case"1d":g=new Date(y.getTime()-864e5);break;case"7d":g=new Date(y.getTime()-6048e5);break;case"30d":default:g=new Date(y.getTime()-2592e6);break;case"90d":g=new Date(y.getTime()-7776e6);break;case"1y":g=new Date(y.getTime()-31536e6)}let w=await u.database.usageMetrics.findMany({where:{userId:f.id,date:{gte:g}},orderBy:{date:"asc"}}),v=await u.database.extensionSession.findMany({where:{userId:f.id,createdAt:{gte:g}},orderBy:{createdAt:"asc"}}),b=w.reduce((e,t)=>({tokensUsed:e.tokensUsed+t.tokensUsed,requestsMade:e.requestsMade+t.requestsMade,costAccrued:e.costAccrued+t.costAccrued}),{tokensUsed:0,requestsMade:0,costAccrued:0}),U="day"===A?l(w,g,y):"week"===A?(t=w,r=g,s=y,l(t,r,s)):(n=w,a=g,i=y,l(n,a,i)),k={totalSessions:v.length,activeSessions:v.filter(e=>e.isActive).length,averageSessionDuration:(d=v,0===d.length?0:Math.round(d.reduce((e,t)=>t.lastActiveAt&&t.createdAt?e+(t.lastActiveAt.getTime()-t.createdAt.getTime()):e,0)/d.length/6e4)),mostUsedVersion:function(e){let t={};return e.forEach(e=>{e.extensionVersion&&(t[e.extensionVersion]=(t[e.extensionVersion]||0)+1)}),Object.entries(t).sort(([,e],[,t])=>t-e)[0]?.[0]||"Unknown"}(v),platformDistribution:function(e){let t={};return e.forEach(e=>{e.platform&&(t[e.platform]=(t[e.platform]||0)+1)}),t}(v)},q=new Date(g.getTime()-(y.getTime()-g.getTime())),S=(await u.database.usageMetrics.findMany({where:{userId:f.id,date:{gte:q,lt:g}}})).reduce((e,t)=>({tokensUsed:e.tokensUsed+t.tokensUsed,requestsMade:e.requestsMade+t.requestsMade,costAccrued:e.costAccrued+t.costAccrued}),{tokensUsed:0,requestsMade:0,costAccrued:0}),M={tokensUsed:p(b.tokensUsed,S.tokensUsed),requestsMade:p(b.requestsMade,S.requestsMade),costAccrued:p(b.costAccrued,S.costAccrued)},T=w.sort((e,t)=>t.tokensUsed-e.tokensUsed).slice(0,5).map(e=>({date:e.date,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,costAccrued:e.costAccrued})),D={period:m,dateRange:{start:g,end:y},totals:b,trends:M,timeSeries:U,sessions:k,topUsageDays:T,insights:function(e,t,r){let s=[];return r.tokensUsed>20?s.push({type:"positive",message:`Token usage increased by ${r.tokensUsed}% compared to the previous period`}):r.tokensUsed<-20&&s.push({type:"neutral",message:`Token usage decreased by ${Math.abs(r.tokensUsed)}% compared to the previous period`}),t.averageSessionDuration>60&&s.push({type:"positive",message:`Long average session duration (${t.averageSessionDuration} minutes) indicates high engagement`}),e.costAccrued>10&&s.push({type:"warning",message:`High cost accrued ($${e.costAccrued.toFixed(2)}). Consider optimizing usage or upgrading plan`}),s}(b,k,M)};return c.NextResponse.json(D)}catch(e){return console.error("Analytics fetch error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}function l(e,t,r){let s={},n=new Date(t);for(;n<=r;){let e=n.toISOString().split("T")[0];s[e]={date:e,tokensUsed:0,requestsMade:0,costAccrued:0},n.setDate(n.getDate()+1)}return e.forEach(e=>{let t=e.date.toISOString().split("T")[0];s[t]&&(s[t].tokensUsed+=e.tokensUsed,s[t].requestsMade+=e.requestsMade,s[t].costAccrued+=e.costAccrued)}),Object.values(s)}function p(e,t){return 0===t?100*(e>0):Math.round((e-t)/t*100)}let g=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/extension/analytics/route",pathname:"/api/extension/analytics",filename:"route",bundlePath:"app/api/extension/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\analytics\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:m}=g;function A(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(63102));module.exports=s})();