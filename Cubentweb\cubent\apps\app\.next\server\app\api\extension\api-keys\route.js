(()=>{var e={};e.id=6465,e.ids=[6465],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>K});var s=r(8741),n=r(62923),a=r(54726),i=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},d=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),c=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,a;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((a=e,`[clerk debug end: ${a}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},l=(e,t)=>(...r)=>{let s=("string"==typeof e?c(e,d):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),x=r(37081),g=r(27322),h=r(6264);function y(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return g.r0.stringify(r,{pad:!1})}async function A(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,g.hJ)(r.algorithm);if(!n)return{errors:[new h.xy(`Unsupported algorithm ${r.algorithm}`)]};let a=await (0,g.Fh)(t,n,"sign"),i=r.header||{typ:"JWT"};i.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=y(i),u=y(e),d=`${o}.${u}`;try{let e=await g.fA.crypto.subtle.sign(n,a,s.encode(d));return{data:`${d}.${g.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new h.xy(e?.message)]}}}(0,x.C)(g.J0);var m=(0,x.R)(g.iU);(0,x.C)(A),(0,x.C)(g.nk);var w=r(97495),f=r(60606);function b(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,i,o;let u,d=(0,w.NE)(e,"AuthStatus"),c=(0,w.NE)(e,"AuthToken"),l=(0,w.NE)(e,"AuthMessage"),p=(0,w.NE)(e,"AuthReason"),x=(0,w.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:d,authMessage:l,authReason:p});let g=(0,w._b)(e,s.AA.Headers.ClerkRequestData),h=(0,f.Kk)(g),y={secretKey:(null==r?void 0:r.secretKey)||h.secretKey||a.rB,publishableKey:h.publishableKey||a.At,apiUrl:a.H$,apiVersion:a.mG,authStatus:d,authMessage:l,authReason:p,treatPendingAsSignedOut:t};if(null==(i=r.logger)||i.debug("auth options",y),d&&d===s.TD.SignedIn){(0,f._l)(c,y.secretKey,x);let e=m(c);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(y,e.raw.text,e.payload)}else u=(0,s.wI)(y);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(y,u.sessionStatus)),u}var v=r(68478);let j=({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>async(n,a)=>{if((0,i.zz)((0,w._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,w.Zd)(n)){p.M&&(0,f.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,f.$K)(n,t)}return b(n,{...a,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>(r,n)=>((0,i.zz)((0,w._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,f.$K)(r,t),b(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,v.AG)()});let k={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},U=e=>{var t,r;return!!e.headers.get(k.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(k.Headers.NextAction))},N=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||I(e)||S(e)},I=e=>!!e.headers.get(k.Headers.NextUrl)&&!U(e)||q(),q=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},S=e=>!!e.headers.get(k.Headers.NextjsData);var R=r(23056);let K=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,R.TG)(),i=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await j({debugLoggerName:"auth()",noAuthStatusMessage:(0,v.sd)("auth",await i())})(t,{treatPendingAsSignedOut:e}),u=(0,w.NE)(t,"ClerkUrl"),d=(...e)=>{let{returnBackUrl:r}=e[0]||{},i=(0,s.tl)(t),d=i.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||i.cookies.get(s.AA.Cookies.DevBrowser),c=(0,w._b)(t,s.AA.Headers.ClerkRequestData),l=(0,f.Kk)(c);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:d,baseUrl:i.clerkUrl.toString(),publishableKey:l.publishableKey||a.At,signInUrl:l.signInUrl||a.qW,signUpUrl:l.signUpUrl||a.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=d(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=d(e);return t.redirectToSignUp({returnBackUrl:r})}})};K.protect=async(...e)=>{r(1447);let t=await (0,R.TG)(),s=await K();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:a}=e;return async(...e)=>{var i,o,u,d,c,l;let p=(null==(i=e[0])?void 0:i.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],x=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(d=e[1])?void 0:d.unauthenticatedUrl),g=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(l=e[1])?void 0:l.unauthorizedUrl),h=()=>g?s(g):n();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:h():r.has(p)?r:h():r:x?s(x):N(a)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45002:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>A,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>g,PATCH:()=>h,POST:()=>x});var n=r(26142),a=r(94327),i=r(34862),o=r(37838),u=r(18815),d=r(26239),c=r(25),l=r(55511);let p=c.z.object({name:c.z.string().min(1).max(100),description:c.z.string().max(500).optional(),expiresAt:c.z.string().datetime().optional(),permissions:c.z.array(c.z.string()).default(["read","write"])});async function x(e){try{let{userId:t}=await (0,o.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=p.safeParse(r);if(!s.success)return d.NextResponse.json({error:"Invalid API key data",details:s.error.errors},{status:400});let{name:n,description:a,expiresAt:i,permissions:c}=s.data,x=await u.database.user.findUnique({where:{clerkId:t}});if(!x)return d.NextResponse.json({error:"User not found"},{status:404});if(await u.database.apiKey.count({where:{userId:x.id,isActive:!0}})>=10)return d.NextResponse.json({error:"Maximum number of API keys reached (10)"},{status:400});let g=`cubent_${(0,l.randomBytes)(32).toString("hex")}`,h=await y(g),A=await u.database.apiKey.create({data:{userId:x.id,name:n,description:a,keyHash:h,permissions:c,expiresAt:i?new Date(i):null,isActive:!0,lastUsedAt:null,usageCount:0}});return d.NextResponse.json({success:!0,apiKey:{id:A.id,name:A.name,description:A.description,key:g,permissions:A.permissions,expiresAt:A.expiresAt,createdAt:A.createdAt},warning:"This API key will only be shown once. Please save it securely."})}catch(e){return console.error("API key creation error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(){try{let{userId:e}=await (0,o.j)();if(!e)return d.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return d.NextResponse.json({error:"User not found"},{status:404});let r=await u.database.apiKey.findMany({where:{userId:t.id},orderBy:{createdAt:"desc"},select:{id:!0,name:!0,description:!0,permissions:!0,isActive:!0,expiresAt:!0,lastUsedAt:!0,usageCount:!0,createdAt:!0}}),s=r.filter(e=>e.isActive),n=r.filter(e=>e.expiresAt&&e.expiresAt<new Date);return d.NextResponse.json({apiKeys:r.map(e=>({...e,keyPreview:"cubent_****...****",isExpired:!!e.expiresAt&&e.expiresAt<new Date})),summary:{totalKeys:r.length,activeKeys:s.length,expiredKeys:n.length,remainingSlots:Math.max(0,10-s.length)}})}catch(e){return console.error("API keys fetch error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let{userId:t}=await (0,o.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{keyId:r,action:s,name:n,description:a,isActive:i}=await e.json();if(!r||!s)return d.NextResponse.json({error:"Key ID and action required"},{status:400});let c=await u.database.user.findUnique({where:{clerkId:t}});if(!c)return d.NextResponse.json({error:"User not found"},{status:404});switch(s){case"update":let p={};n&&(p.name=n),void 0!==a&&(p.description=a),"boolean"==typeof i&&(p.isActive=i);let x=await u.database.apiKey.update({where:{id:r,userId:c.id},data:p,select:{id:!0,name:!0,description:!0,permissions:!0,isActive:!0,expiresAt:!0,lastUsedAt:!0,usageCount:!0,createdAt:!0}});return d.NextResponse.json({success:!0,apiKey:x,message:"API key updated successfully"});case"delete":return await u.database.apiKey.delete({where:{id:r,userId:c.id}}),d.NextResponse.json({success:!0,message:"API key deleted successfully"});case"regenerate":await u.database.apiKey.update({where:{id:r,userId:c.id},data:{isActive:!1}});let g=await u.database.apiKey.findUnique({where:{id:r},select:{name:!0,description:!0,permissions:!0,expiresAt:!0}});if(!g)return d.NextResponse.json({error:"API key not found"},{status:404});let h=`cubent_${(0,l.randomBytes)(32).toString("hex")}`,A=await y(h),m=await u.database.apiKey.create({data:{userId:c.id,name:g.name,description:g.description,keyHash:A,permissions:g.permissions,expiresAt:g.expiresAt,isActive:!0,lastUsedAt:null,usageCount:0}});return d.NextResponse.json({success:!0,apiKey:{id:m.id,name:m.name,description:m.description,key:h,permissions:m.permissions,expiresAt:m.expiresAt,createdAt:m.createdAt},warning:"This new API key will only be shown once. Please save it securely.",message:"API key regenerated successfully"});default:return d.NextResponse.json({error:"Invalid action. Use: update, delete, or regenerate"},{status:400})}}catch(e){return console.error("API key management error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("")}let A=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/extension/api-keys/route",pathname:"/api/extension/api-keys",filename:"route",bundlePath:"app/api/extension/api-keys/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\api-keys\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:f}=A;function b(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(45002));module.exports=s})();