(()=>{var e={};e.id=372,e.ids=[372],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12390:(e,r,t)=>{Promise.resolve().then(t.bind(t,86332)),Promise.resolve().then(t.bind(t,22683)),Promise.resolve().then(t.bind(t,85733)),Promise.resolve().then(t.bind(t,12350))},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20638:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,21034,23)),Promise.resolve().then(t.bind(t,93665)),Promise.resolve().then(t.bind(t,61847)),Promise.resolve().then(t.bind(t,68741))},21820:e=>{"use strict";e.exports=require("os")},24767:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(23233);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),o=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},a=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),u=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:i,className:n="",children:o,iconNode:d,...c},p)=>(0,s.createElement)("svg",{ref:p,...l,width:r,height:r,stroke:e,strokeWidth:i?24*Number(t)/Number(r):t,className:a("lucide",n),...!o&&!u(c)&&{"aria-hidden":"true"},...c},[...d.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(o)?o:[o]])),c=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...n},u)=>(0,s.createElement)(d,{ref:u,iconNode:r,className:a(`lucide-${i(o(e))}`,`lucide-${e}`,t),...n}));return t.displayName=o(e),t}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30409:(e,r,t)=>{"use strict";t.d(r,{DX:()=>o});var s=t(23233);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var n=t(94752),o=function(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var o;let e,a,u=(o=t,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),l=function(e,r){let t={...r};for(let s in r){let i=e[s],n=r[s];/^on[A-Z]/.test(s)?i&&n?t[s]=(...e)=>{let r=n(...e);return i(...e),r}:i&&(t[s]=i):"style"===s?t[s]={...i,...n}:"className"===s&&(t[s]=[i,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(l.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=i(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():i(e[r],null)}}}}(r,u):u),s.cloneElement(t,l)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:i,...o}=e,a=s.Children.toArray(i),l=a.find(u);if(l){let e=l.props.children,i=a.map(r=>r!==l?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...o,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,n.jsx)(r,{...o,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}("Slot"),a=Symbol("radix.slottable");function u(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},31421:e=>{"use strict";e.exports=require("node:child_process")},32279:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>l,routeModule:()=>c,tree:()=>u});var s=t(57864),i=t(94327),n=t(70814),o=t(17984),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);t.d(r,a);let u={children:["",{children:["(authenticated)",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,65081)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\search\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(authenticated)/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59988:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>s.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>s.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>i.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>s.at});var s=t(54841),i=t(44089)},61847:(e,r,t)=>{"use strict";t.d(r,{Separator:()=>s});let s=(0,t(6340).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\separator.tsx","Separator")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63913:(e,r,t)=>{"use strict";t.d(r,{Y:()=>f});var s=t(94752),i=t(23233),n=t(30409);let o=(0,t(24767).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var a=t(58559);function u({...e}){return(0,s.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function l({className:e,...r}){return(0,s.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,a.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...r})}function d({className:e,...r}){return(0,s.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,a.cn)("inline-flex items-center gap-1.5",e),...r})}function c({asChild:e,className:r,...t}){let i=e?n.DX:"a";return(0,s.jsx)(i,{"data-slot":"breadcrumb-link",className:(0,a.cn)("hover:text-foreground transition-colors",r),...t})}function p({className:e,...r}){return(0,s.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,a.cn)("text-foreground font-normal",e),...r})}function m({children:e,className:r,...t}){return(0,s.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,a.cn)("[&>svg]:size-3.5",r),...t,children:e??(0,s.jsx)(o,{})})}var x=t(61847),h=t(68741);let f=({pages:e,page:r,children:t})=>(0,s.jsxs)("header",{className:"flex h-16 shrink-0 items-center justify-between gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,s.jsx)(h.SidebarTrigger,{className:"-ml-1"}),(0,s.jsx)(x.Separator,{orientation:"vertical",className:"mr-2 h-4"}),(0,s.jsx)(u,{children:(0,s.jsxs)(l,{children:[e.map((e,r)=>(0,s.jsxs)(i.Fragment,{children:[r>0&&(0,s.jsx)(m,{className:"hidden md:block"}),(0,s.jsx)(d,{className:"hidden md:block",children:(0,s.jsx)(c,{href:"#",children:e})})]},e)),(0,s.jsx)(m,{className:"hidden md:block"}),(0,s.jsx)(d,{children:(0,s.jsx)(p,{children:r})})]})})]}),t]})},65081:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,generateMetadata:()=>u});var s=t(94752),i=t(37838),n=t(18815),o=t(62923),a=t(63913);let u=async({searchParams:e})=>{let{q:r}=await e;return{title:`${r} - Search results`,description:`Search results for ${r}`}},l=async({searchParams:e})=>{let{q:r}=await e,t=await n.database.page.findMany({where:{name:{contains:r}}}),{orgId:u}=await (0,i.j)();return u||(0,o.notFound)(),r||(0,o.redirect)("/"),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.Y,{pages:["Building Your Application"],page:"Search"}),(0,s.jsxs)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:[(0,s.jsx)("div",{className:"grid auto-rows-min gap-4 md:grid-cols-3",children:t.map(e=>(0,s.jsx)("div",{className:"aspect-video rounded-xl bg-muted/50",children:e.name},e.id))}),(0,s.jsx)("div",{className:"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min"})]})]})}},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,7873,3887,5480,1359,3319,2644,277,1988,5432,4841,8482,864,7209,6648],()=>t(32279));module.exports=s})();