(()=>{var e={};e.id=3666,e.ids=[3666],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>N});var s=r(8741),a=r(62923),n=r(54726),o=r(87553),i=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},d=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),c=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var a,n;for(let s of(console.log((a=e,`[clerk debug start: ${a}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),a=r.encode(e).slice(0,4096);return s.decode(a).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((n=e,`[clerk debug end: ${n}] (@clerk/nextjs=6.20.0,next=${i.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},l=(e,t)=>(...r)=>{let s=("string"==typeof e?c(e,d):e)(),a=t(s);try{let e=a(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var p=r(74365),g=r(37081),h=r(27322),x=r(6264);function f(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return h.r0.stringify(r,{pad:!1})}async function m(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,a=(0,h.hJ)(r.algorithm);if(!a)return{errors:[new x.xy(`Unsupported algorithm ${r.algorithm}`)]};let n=await (0,h.Fh)(t,a,"sign"),o=r.header||{typ:"JWT"};o.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let i=f(o),u=f(e),d=`${i}.${u}`;try{let e=await h.fA.crypto.subtle.sign(a,n,s.encode(d));return{data:`${d}.${h.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new x.xy(e?.message)]}}}(0,g.C)(h.J0);var w=(0,g.R)(h.iU);(0,g.C)(m),(0,g.C)(h.nk);var y=r(97495),A=r(60606);function v(e,{treatPendingAsSignedOut:t=!0,...r}={}){var a,o,i;let u,d=(0,y.NE)(e,"AuthStatus"),c=(0,y.NE)(e,"AuthToken"),l=(0,y.NE)(e,"AuthMessage"),p=(0,y.NE)(e,"AuthReason"),g=(0,y.NE)(e,"AuthSignature");null==(a=r.logger)||a.debug("headers",{authStatus:d,authMessage:l,authReason:p});let h=(0,y._b)(e,s.AA.Headers.ClerkRequestData),x=(0,A.Kk)(h),f={secretKey:(null==r?void 0:r.secretKey)||x.secretKey||n.rB,publishableKey:x.publishableKey||n.At,apiUrl:n.H$,apiVersion:n.mG,authStatus:d,authMessage:l,authReason:p,treatPendingAsSignedOut:t};if(null==(o=r.logger)||o.debug("auth options",f),d&&d===s.TD.SignedIn){(0,A._l)(c,f.secretKey,g);let e=w(c);null==(i=r.logger)||i.debug("jwt",e.raw),u=(0,s.Z5)(f,e.raw.text,e.payload)}else u=(0,s.wI)(f);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(f,u.sessionStatus)),u}var b=r(68478);let S=({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>async(a,n)=>{if((0,o.zz)((0,y._b)(a,s.AA.Headers.EnableDebug))&&e.enable(),!(0,y.Zd)(a)){p.M&&(0,A.$K)(a,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,A.$K)(a,t)}return v(a,{...n,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>l(e,e=>(r,a)=>((0,o.zz)((0,y._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,A.$K)(r,t),v(r,{...a,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,b.AG)()});let U={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},q=e=>{var t,r;return!!e.headers.get(U.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(U.Headers.NextAction))},k=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||j(e)||T(e)},j=e=>!!e.headers.get(U.Headers.NextUrl)&&!q(e)||M(),M=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},T=e=>!!e.headers.get(U.Headers.NextjsData);var D=r(23056);let N=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,D.TG)(),o=async()=>{if(p.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},i=await S({debugLoggerName:"auth()",noAuthStatusMessage:(0,b.sd)("auth",await o())})(t,{treatPendingAsSignedOut:e}),u=(0,y.NE)(t,"ClerkUrl"),d=(...e)=>{let{returnBackUrl:r}=e[0]||{},o=(0,s.tl)(t),d=o.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||o.cookies.get(s.AA.Cookies.DevBrowser),c=(0,y._b)(t,s.AA.Headers.ClerkRequestData),l=(0,A.Kk)(c);return[(0,s.vH)({redirectAdapter:a.redirect,devBrowserToken:d,baseUrl:o.clerkUrl.toString(),publishableKey:l.publishableKey||n.At,signInUrl:l.signInUrl||n.qW,signUpUrl:l.signUpUrl||n.sE,sessionStatus:i.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(i,{redirectToSignIn:(e={})=>{let[t,r]=d(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=d(e);return t.redirectToSignUp({returnBackUrl:r})}})};N.protect=async(...e)=>{r(1447);let t=await (0,D.TG)(),s=await N();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:a,request:n}=e;return async(...e)=>{var o,i,u,d,c,l;let p=(null==(o=e[0])?void 0:o.unauthenticatedUrl)||(null==(i=e[0])?void 0:i.unauthorizedUrl)?void 0:e[0],g=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(d=e[1])?void 0:d.unauthenticatedUrl),h=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(l=e[1])?void 0:l.unauthorizedUrl),x=()=>h?s(h):a();return"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:x():r.has(p)?r:x():r:g?s(g):k(n)?t():a()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:a.notFound,redirect:a.redirect})(...e)}},44679:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>l});var a=r(26142),n=r(94327),o=r(34862),i=r(37838),u=r(18815),d=r(26239);async function c(e){try{let r,{userId:s}=await (0,i.j)();if(!s)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),n=a.get("format")||"json",o=a.get("period")||"30d",c="true"===a.get("includeMetadata"),l=await u.database.user.findUnique({where:{clerkId:s}});if(!l)return d.NextResponse.json({error:"User not found"},{status:404});let p=new Date;switch(o){case"7d":r=new Date(p.getTime()-6048e5);break;case"30d":default:r=new Date(p.getTime()-2592e6);break;case"90d":r=new Date(p.getTime()-7776e6);break;case"1y":r=new Date(p.getTime()-31536e6);break;case"all":r=new Date(0)}let g=await u.database.usageMetrics.findMany({where:{userId:l.id,date:{gte:r}},orderBy:{date:"asc"}}),h=[];c&&(h=await u.database.extensionSession.findMany({where:{userId:l.id,createdAt:{gte:r}},orderBy:{createdAt:"asc"}}));let x={exportInfo:{userId:s,exportDate:p.toISOString(),period:o,dateRange:{start:r.toISOString(),end:p.toISOString()},totalRecords:g.length,format:n},usageMetrics:g.map(e=>({date:e.date.toISOString(),tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,costAccrued:e.costAccrued})),summary:{totalTokensUsed:g.reduce((e,t)=>e+t.tokensUsed,0),totalRequestsMade:g.reduce((e,t)=>e+t.requestsMade,0),totalCostAccrued:g.reduce((e,t)=>e+t.costAccrued,0),averageDailyTokens:g.length>0?Math.round(g.reduce((e,t)=>e+t.tokensUsed,0)/g.length):0,averageDailyRequests:g.length>0?Math.round(g.reduce((e,t)=>e+t.requestsMade,0)/g.length):0},...c&&{extensionSessions:h.map(e=>({sessionId:e.sessionId,extensionVersion:e.extensionVersion,vscodeVersion:e.vscodeVersion,platform:e.platform,isActive:e.isActive,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,createdAt:e.createdAt.toISOString(),lastActiveAt:e.lastActiveAt?.toISOString(),metadata:e.metadata}))}};if("csv"===n){var t;let e=(t=x.usageMetrics,0===t.length?"Date,Tokens Used,Requests Made,Cost Accrued,Created At\n":"Date,Tokens Used,Requests Made,Cost Accrued,Created At\n"+t.map(e=>`${e.date},${e.tokensUsed},${e.requestsMade},${e.costAccrued},${e.createdAt}`).join("\n"));return new d.NextResponse(e,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="cubent-usage-${o}-${p.toISOString().split("T")[0]}.csv"`}})}if("xlsx"===n)return d.NextResponse.json({...x,note:"Excel format not yet implemented. Use CSV format for spreadsheet compatibility."});return d.NextResponse.json(x,{headers:{"Content-Disposition":`attachment; filename="cubent-usage-${o}-${p.toISOString().split("T")[0]}.json"`}})}catch(e){return console.error("Export error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let{userId:e}=await (0,i.j)();if(!e)return d.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return d.NextResponse.json({error:"User not found"},{status:404});let r=await u.database.usageMetrics.count({where:{userId:t.id}}),s=await u.database.extensionSession.count({where:{userId:t.id}}),a=await u.database.usageMetrics.findFirst({where:{userId:t.id},orderBy:{date:"asc"},select:{date:!0}}),n=await u.database.usageMetrics.findFirst({where:{userId:t.id},orderBy:{date:"desc"},select:{date:!0}});return d.NextResponse.json({availableData:{totalUsageRecords:r,totalSessionRecords:s,dateRange:{oldest:a?.date||null,newest:n?.date||null}},exportOptions:{formats:["json","csv"],periods:["7d","30d","90d","1y","all"],includeMetadata:!0},estimatedSizes:{json:`${Math.round(.2*r)}KB`,csv:`${Math.round(.1*r)}KB`}})}catch(e){return console.error("Export info error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/export/route",pathname:"/api/extension/export",filename:"route",bundlePath:"app/api/extension/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\export\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:x}=p;function f(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(44679));module.exports=s})();