(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{14:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DiagConsoleLogger:()=>U,DiagLogLevel:()=>n,INVALID_SPANID:()=>ed,INVALID_SPAN_CONTEXT:()=>ep,INVALID_TRACEID:()=>eh,ProxyTracer:()=>eI,ProxyTracerProvider:()=>eA,ROOT_CONTEXT:()=>N,SamplingDecision:()=>a,SpanKind:()=>o,SpanStatusCode:()=>l,TraceFlags:()=>s,ValueType:()=>i,baggageEntryMetadataFromString:()=>P,context:()=>e$,createContextKey:()=>I,createNoopMeter:()=>ee,createTraceState:()=>eq,default:()=>e2,defaultTextMapGetter:()=>et,defaultTextMapSetter:()=>er,diag:()=>eB,isSpanContextValid:()=>eT,isValidSpanId:()=>ex,isValidTraceId:()=>eS,metrics:()=>eH,propagation:()=>eQ,trace:()=>e1});var n,i,s,a,o,l,c="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof r.g?r.g:{},u="1.9.0",d=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,h=function(e){var t=new Set([e]),r=new Set,n=e.match(d);if(!n)return function(){return!1};var i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function s(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;var n=e.match(d);if(!n)return s(e);var a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease||i.major!==a.major)return s(e);if(0===i.major)return i.minor===a.minor&&i.patch<=a.patch?(t.add(e),!0):s(e);return i.minor<=a.minor?(t.add(e),!0):s(e)}}(u),p=Symbol.for("opentelemetry.js.api."+u.split(".")[0]);function f(e,t,r,n){void 0===n&&(n=!1);var i,s=c[p]=null!=(i=c[p])?i:{version:u};if(!n&&s[e]){var a=Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(a.stack||a.message),!1}if(s.version!==u){var a=Error("@opentelemetry/api: Registration of version v"+s.version+" for "+e+" does not match previously registered API v"+u);return r.error(a.stack||a.message),!1}return s[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+u+"."),!0}function m(e){var t,r,n=null==(t=c[p])?void 0:t.version;if(n&&h(n))return null==(r=c[p])?void 0:r[e]}function g(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+u+".");var r=c[p];r&&delete r[e]}var y=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},_=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},v=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("verbose",this._namespace,e)},e}();function b(e,t,r){var n=m("diag");if(n)return r.unshift(t),n[e].apply(n,_([],y(r),!1))}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(n||(n={}));var w=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},E=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},k=function(){function e(){function e(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=m("diag");if(n)return n[e].apply(n,E([],w(t),!1))}}var t=this;t.setLogger=function(e,r){if(void 0===r&&(r={logLevel:n.INFO}),e===t){var i,s,a,o=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(i=o.stack)?i:o.message),!1}"number"==typeof r&&(r={logLevel:r});var l=m("diag"),c=function(e,t){function r(r,n){var i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.NONE?e=n.NONE:e>n.ALL&&(e=n.ALL),t=t||{},{error:r("error",n.ERROR),warn:r("warn",n.WARN),info:r("info",n.INFO),debug:r("debug",n.DEBUG),verbose:r("verbose",n.VERBOSE)}}(null!=(s=r.logLevel)?s:n.INFO,e);if(l&&!r.suppressOverrideMessage){var u=null!=(a=Error().stack)?a:"<failed to generate stacktrace>";l.warn("Current logger will be overwritten from "+u),c.warn("Current logger will overwrite one already registered from "+u)}return f("diag",c,t,!0)},t.disable=function(){g("diag",t)},t.createComponentLogger=function(e){return new v(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}(),S=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},x=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},T=function(){function e(e){this._entries=e?new Map(e):new Map}return e.prototype.getEntry=function(e){var t=this._entries.get(e);if(t)return Object.assign({},t)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(e){var t=S(e,2);return[t[0],t[1]]})},e.prototype.setEntry=function(t,r){var n=new e(this._entries);return n._entries.set(t,r),n},e.prototype.removeEntry=function(t){var r=new e(this._entries);return r._entries.delete(t),r},e.prototype.removeEntries=function(){for(var t,r,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=new e(this._entries);try{for(var a=x(n),o=a.next();!o.done;o=a.next()){var l=o.value;s._entries.delete(l)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return s},e.prototype.clear=function(){return new e},e}(),C=Symbol("BaggageEntryMetadata"),R=k.instance();function O(e){return void 0===e&&(e={}),new T(new Map(Object.entries(e)))}function P(e){return"string"!=typeof e&&(R.error("Cannot create baggage metadata from unknown type: "+typeof e),e=""),{__TYPE__:C,toString:function(){return e}}}function I(e){return Symbol.for(e)}var N=new function e(t){var r=this;r._currentContext=t?new Map(t):new Map,r.getValue=function(e){return r._currentContext.get(e)},r.setValue=function(t,n){var i=new e(r._currentContext);return i._currentContext.set(t,n),i},r.deleteValue=function(t){var n=new e(r._currentContext);return n._currentContext.delete(t),n}},A=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}],U=function(){for(var e=0;e<A.length;e++)this[A[e].n]=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(console){var n=console[e];if("function"!=typeof n&&(n=console.log),"function"==typeof n)return n.apply(console,t)}}}(A[e].c)},L=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),j=function(){function e(){}return e.prototype.createGauge=function(e,t){return V},e.prototype.createHistogram=function(e,t){return Z},e.prototype.createCounter=function(e,t){return J},e.prototype.createUpDownCounter=function(e,t){return X},e.prototype.createObservableGauge=function(e,t){return Y},e.prototype.createObservableCounter=function(e,t){return G},e.prototype.createObservableUpDownCounter=function(e,t){return Q},e.prototype.addBatchObservableCallback=function(e,t){},e.prototype.removeBatchObservableCallback=function(e){},e}(),D=function(){},M=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t.prototype.add=function(e,t){},t}(D),q=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t.prototype.add=function(e,t){},t}(D),$=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t.prototype.record=function(e,t){},t}(D),B=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t.prototype.record=function(e,t){},t}(D),z=function(){function e(){}return e.prototype.addCallback=function(e){},e.prototype.removeCallback=function(e){},e}(),K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t}(z),H=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t}(z),F=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return L(t,e),t}(z),W=new j,J=new M,V=new $,Z=new B,X=new q,G=new K,Y=new H,Q=new F;function ee(){return W}!function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(i||(i={}));var et={get:function(e,t){if(null!=e)return e[t]},keys:function(e){return null==e?[]:Object.keys(e)}},er={set:function(e,t,r){null!=e&&(e[t]=r)}},en=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},ei=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},es=function(){function e(){}return e.prototype.active=function(){return N},e.prototype.with=function(e,t,r){for(var n=[],i=3;i<arguments.length;i++)n[i-3]=arguments[i];return t.call.apply(t,ei([r],en(n),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),ea=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},eo=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},el="context",ec=new es,eu=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return f(el,e,k.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,r){for(var n,i=[],s=3;s<arguments.length;s++)i[s-3]=arguments[s];return(n=this._getContextManager()).with.apply(n,eo([e,t,r],ea(i),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return m(el)||ec},e.prototype.disable=function(){this._getContextManager().disable(),g(el,k.instance())},e}();!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(s||(s={}));var ed="0000000000000000",eh="00000000000000000000000000000000",ep={traceId:eh,spanId:ed,traceFlags:s.NONE},ef=function(){function e(e){void 0===e&&(e=ep),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),em=I("OpenTelemetry Context Key SPAN");function eg(e){return e.getValue(em)||void 0}function ey(){return eg(eu.getInstance().active())}function e_(e,t){return e.setValue(em,t)}function ev(e){return e.deleteValue(em)}function eb(e,t){return e_(e,new ef(t))}function ew(e){var t;return null==(t=eg(e))?void 0:t.spanContext()}var eE=/^([0-9a-f]{32})$/i,ek=/^[0-9a-f]{16}$/i;function eS(e){return eE.test(e)&&e!==eh}function ex(e){return ek.test(e)&&e!==ed}function eT(e){return eS(e.traceId)&&ex(e.spanId)}function eC(e){return new ef(e)}var eR=eu.getInstance(),eO=function(){function e(){}return e.prototype.startSpan=function(e,t,r){if(void 0===r&&(r=eR.active()),null==t?void 0:t.root)return new ef;var n,i=r&&ew(r);return"object"==typeof(n=i)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&eT(i)?new ef(i):new ef},e.prototype.startActiveSpan=function(e,t,r,n){if(!(arguments.length<2)){2==arguments.length?a=t:3==arguments.length?(i=t,a=r):(i=t,s=r,a=n);var i,s,a,o=null!=s?s:eR.active(),l=this.startSpan(e,i,o),c=e_(o,l);return eR.with(c,a,void 0,l)}},e}(),eP=new eO,eI=function(){function e(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}return e.prototype.startSpan=function(e,t,r){return this._getTracer().startSpan(e,t,r)},e.prototype.startActiveSpan=function(e,t,r,n){var i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):eP},e}(),eN=new(function(){function e(){}return e.prototype.getTracer=function(e,t,r){return new eO},e}()),eA=function(){function e(){}return e.prototype.getTracer=function(e,t,r){var n;return null!=(n=this.getDelegateTracer(e,t,r))?n:new eI(this,e,t,r)},e.prototype.getDelegate=function(){var e;return null!=(e=this._delegate)?e:eN},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)},e}();!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(a||(a={})),function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(o||(o={})),function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(l||(l={}));var eU="[_0-9a-z-*/]",eL=RegExp("^(?:[a-z]"+eU+"{0,255}|"+("[a-z0-9]"+eU+"{0,240}@[a-z]")+eU+"{0,13})$"),ej=/^[ -~]{0,255}[!-~]$/,eD=/,|=/,eM=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce(function(t,r){return t.push(r+"="+e.get(r)),t},[]).join(",")},e.prototype._parse=function(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce(function(e,t){var r=t.trim(),n=r.indexOf("=");if(-1!==n){var i=r.slice(0,n),s=r.slice(n+1,t.length);eL.test(i)&&ej.test(s)&&!eD.test(s)&&e.set(i,s)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}();function eq(e){return new eM(e)}var e$=eu.getInstance(),eB=k.instance(),ez=new(function(){function e(){}return e.prototype.getMeter=function(e,t,r){return W},e}()),eK="metrics",eH=(function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalMeterProvider=function(e){return f(eK,e,k.instance())},e.prototype.getMeterProvider=function(){return m(eK)||ez},e.prototype.getMeter=function(e,t,r){return this.getMeterProvider().getMeter(e,t,r)},e.prototype.disable=function(){g(eK,k.instance())},e})().getInstance(),eF=function(){function e(){}return e.prototype.inject=function(e,t){},e.prototype.extract=function(e,t){return e},e.prototype.fields=function(){return[]},e}(),eW=I("OpenTelemetry Baggage Key");function eJ(e){return e.getValue(eW)||void 0}function eV(){return eJ(eu.getInstance().active())}function eZ(e,t){return e.setValue(eW,t)}function eX(e){return e.deleteValue(eW)}var eG="propagation",eY=new eF,eQ=(function(){function e(){this.createBaggage=O,this.getBaggage=eJ,this.getActiveBaggage=eV,this.setBaggage=eZ,this.deleteBaggage=eX}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(e){return f(eG,e,k.instance())},e.prototype.inject=function(e,t,r){return void 0===r&&(r=er),this._getGlobalPropagator().inject(e,t,r)},e.prototype.extract=function(e,t,r){return void 0===r&&(r=et),this._getGlobalPropagator().extract(e,t,r)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){g(eG,k.instance())},e.prototype._getGlobalPropagator=function(){return m(eG)||eY},e})().getInstance(),e0="trace",e1=(function(){function e(){this._proxyTracerProvider=new eA,this.wrapSpanContext=eC,this.isSpanContextValid=eT,this.deleteSpan=ev,this.getSpan=eg,this.getActiveSpan=ey,this.getSpanContext=ew,this.setSpan=e_,this.setSpanContext=eb}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=f(e0,this._proxyTracerProvider,k.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return m(e0)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){g(e0,k.instance()),this._proxyTracerProvider=new eA},e})().getInstance();let e2={context:e$,diag:eB,metrics:eH,propagation:eQ,trace:e1}},21:(e,t,r)=>{"use strict";r.d(t,{xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}},51:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({})},56:(e,t,r)=>{"use strict";r.d(t,{headers:()=>v}),r(200),r(75);var n=r(72),i=r(86),s=r(679),a=r(476),o=r(727),l=r(449);let c={current:null},u="function"==typeof l.cache?l.cache:e=>e,d=console.warn;function h(e){return function(...t){d(e(...t))}}u(e=>{try{d(c.current)}finally{c.current=null}});var p=r(666);let f=new WeakMap,m=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})});function g(){return this.getAll().map(e=>[e.name,e]).values()}function y(e){for(let e of this.getAll())this.delete(e.name);return e}var _=r(439);function v(){let e=n.J.getStore(),t=i.FP.getStore();if(e){if(t&&"after"===t.phase&&!(0,p.iC)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return w(_.o.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=b.get(l);if(n)return n;let i=(0,o.W)(l.renderSignal,"`headers()`");return b.set(l,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${E(arguments[0])}, ...)\``,t=S(r,e);(0,s.t3)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${E(arguments[0])})\``,t=S(r,e);(0,s.t3)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${E(arguments[0])})\``,t=S(r,e);(0,s.t3)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${E(arguments[0])})\``,t=S(r,e);(0,s.t3)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${E(arguments[0])}, ...)\``,t=S(r,e);(0,s.t3)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=S(r,e);(0,s.t3)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=S(r,e);(0,s.t3)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=S(r,e);(0,s.t3)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=S(r,e);(0,s.t3)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=S(r,e);(0,s.t3)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=S(r,e);(0,s.t3)(r,e,t,l)}}}),i}else"prerender-ppr"===t.type?(0,s.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,s.xI)("headers",e,t);(0,s.Pk)(e,t)}return w((0,i.XN)("headers").headers)}let b=new WeakMap;function w(e){let t=b.get(e);if(t)return t;let r=Promise.resolve(e);return b.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function E(e){return"string"==typeof e?`'${e}'`:"..."}let k=h(S);function S(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function x(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return T(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return T(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return R(null);default:return t}}function T(e,t){let r,n=C.get(x);return n||(r=R(e),C.set(e,r),r)}r(830);let C=new WeakMap;function R(e){let t=new O(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class O{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){I("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){I("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let P=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function I(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},72:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(21).xl)()},75:(e,t,r)=>{"use strict";r.d(t,{Ud:()=>n.stringifyCookie,VO:()=>n.ResponseCookies,tm:()=>n.RequestCookies});var n=r(782)},78:(e,t,r)=>{var n;(()=>{var i={226:function(i,s){!function(a,o){"use strict";var l="function",c="undefined",u="object",d="string",h="major",p="model",f="name",m="type",g="vendor",y="version",_="architecture",v="console",b="mobile",w="tablet",E="smarttv",k="wearable",S="embedded",x="Amazon",T="Apple",C="ASUS",R="BlackBerry",O="Browser",P="Chrome",I="Firefox",N="Google",A="Huawei",U="Microsoft",L="Motorola",j="Opera",D="Samsung",M="Sharp",q="Sony",$="Xiaomi",B="Zebra",z="Facebook",K="Chromium OS",H="Mac OS",F=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},W=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},J=function(e,t){return typeof e===d&&-1!==V(t).indexOf(V(e))},V=function(e){return e.toLowerCase()},Z=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,s,a,c,d=0;d<t.length&&!a;){var h=t[d],p=t[d+1];for(r=n=0;r<h.length&&!a&&h[r];)if(a=h[r++].exec(e))for(i=0;i<p.length;i++)c=a[++n],typeof(s=p[i])===u&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,c):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=c?c.replace(s[1],s[2]):void 0:this[s[0]]=c?s[1].call(this,c,s[2]):void 0:4===s.length&&(this[s[0]]=c?s[3].call(this,c.replace(s[1],s[2])):o):this[s]=c||o;d+=2}},G=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(J(t[r][n],e))return"?"===r?o:r}else if(J(t[r],e))return"?"===r?o:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,y],[/opios[\/ ]+([\w\.]+)/i],[y,[f,j+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[f,j]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[f,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+O],y],[/\bfocus\/([\w\.]+)/i],[y,[f,I+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[f,j+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[f,j+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[f,"MIUI "+O]],[/fxios\/([-\w\.]+)/i],[y,[f,I]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+O]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+O],y],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,z],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[f,P+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,P+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[f,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[y,G,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[f,I+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,y],[/(cobalt)\/([\w\.]+)/i],[f,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[_,"amd64"]],[/(ia32(?=;))/i],[[_,V]],[/((?:i[346]|x)86)[;\)]/i],[[_,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[_,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[_,"armhf"]],[/windows (ce|mobile); ppc;/i],[[_,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[_,/ower/,"",V]],[/(sun4\w)[;\)]/i],[[_,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[_,V]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[g,D],[m,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[g,D],[m,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[g,T],[m,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[g,T],[m,w]],[/(macintosh);/i],[p,[g,T]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[g,M],[m,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[g,A],[m,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[g,A],[m,b]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[g,$],[m,b]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[g,$],[m,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[g,"OPPO"],[m,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[g,"Vivo"],[m,b]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[g,"Realme"],[m,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[g,L],[m,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[g,L],[m,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[g,"LG"],[m,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[g,"LG"],[m,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[g,"Lenovo"],[m,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[g,"Nokia"],[m,b]],[/(pixel c)\b/i],[p,[g,N],[m,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[g,N],[m,b]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[g,q],[m,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[g,q],[m,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[g,"OnePlus"],[m,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[g,x],[m,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[g,x],[m,b]],[/(playbook);[-\w\),; ]+(rim)/i],[p,g,[m,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[g,R],[m,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[g,C],[m,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[g,C],[m,b]],[/(nexus 9)/i],[p,[g,"HTC"],[m,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[g,[p,/_/g," "],[m,b]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[g,"Acer"],[m,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[g,"Meizu"],[m,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[g,p,[m,b]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[g,p,[m,w]],[/(surface duo)/i],[p,[g,U],[m,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[g,"Fairphone"],[m,b]],[/(u304aa)/i],[p,[g,"AT&T"],[m,b]],[/\bsie-(\w*)/i],[p,[g,"Siemens"],[m,b]],[/\b(rct\w+) b/i],[p,[g,"RCA"],[m,w]],[/\b(venue[\d ]{2,7}) b/i],[p,[g,"Dell"],[m,w]],[/\b(q(?:mv|ta)\w+) b/i],[p,[g,"Verizon"],[m,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[g,"Barnes & Noble"],[m,w]],[/\b(tm\d{3}\w+) b/i],[p,[g,"NuVision"],[m,w]],[/\b(k88) b/i],[p,[g,"ZTE"],[m,w]],[/\b(nx\d{3}j) b/i],[p,[g,"ZTE"],[m,b]],[/\b(gen\d{3}) b.+49h/i],[p,[g,"Swiss"],[m,b]],[/\b(zur\d{3}) b/i],[p,[g,"Swiss"],[m,w]],[/\b((zeki)?tb.*\b) b/i],[p,[g,"Zeki"],[m,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[g,"Dragon Touch"],p,[m,w]],[/\b(ns-?\w{0,9}) b/i],[p,[g,"Insignia"],[m,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[g,"NextBook"],[m,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[g,"Voice"],p,[m,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[g,"LvTel"],p,[m,b]],[/\b(ph-1) /i],[p,[g,"Essential"],[m,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[g,"Envizen"],[m,w]],[/\b(trio[-\w\. ]+) b/i],[p,[g,"MachSpeed"],[m,w]],[/\btu_(1491) b/i],[p,[g,"Rotor"],[m,w]],[/(shield[\w ]+) b/i],[p,[g,"Nvidia"],[m,w]],[/(sprint) (\w+)/i],[g,p,[m,b]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[g,U],[m,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[g,B],[m,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[g,B],[m,b]],[/smart-tv.+(samsung)/i],[g,[m,E]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[g,D],[m,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[g,"LG"],[m,E]],[/(apple) ?tv/i],[g,[p,T+" TV"],[m,E]],[/crkey/i],[[p,P+"cast"],[g,N],[m,E]],[/droid.+aft(\w)( bui|\))/i],[p,[g,x],[m,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[g,M],[m,E]],[/(bravia[\w ]+)( bui|\))/i],[p,[g,q],[m,E]],[/(mitv-\w{5}) bui/i],[p,[g,$],[m,E]],[/Hbbtv.*(technisat) (.*);/i],[g,p,[m,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[g,Z],[p,Z],[m,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[g,p,[m,v]],[/droid.+; (shield) bui/i],[p,[g,"Nvidia"],[m,v]],[/(playstation [345portablevi]+)/i],[p,[g,q],[m,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[g,U],[m,v]],[/((pebble))app/i],[g,p,[m,k]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[g,T],[m,k]],[/droid.+; (glass) \d/i],[p,[g,N],[m,k]],[/droid.+; (wt63?0{2,3})\)/i],[p,[g,B],[m,k]],[/(quest( 2| pro)?)/i],[p,[g,z],[m,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[g,[m,S]],[/(aeobc)\b/i],[p,[g,x],[m,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[m,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[m,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,b]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[g,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[y,G,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[y,G,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,H],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,y],[/\(bb(10);/i],[y,[f,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[f,I+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[f,P+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,K],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,y],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,y]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:o,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:o,s=t?F(Q,t):Q,v=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=o,t[y]=o,X.call(t,n,s.browser),t[h]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:o,v&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[_]=o,X.call(e,n,s.cpu),e},this.getDevice=function(){var e={};return e[g]=o,e[p]=o,e[m]=o,X.call(e,n,s.device),v&&!e[m]&&i&&i.mobile&&(e[m]=b),v&&"Macintosh"==e[p]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[m]=w),e},this.getEngine=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,s.engine),e},this.getOS=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,s.os),v&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,K).replace(/macos/i,H)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?Z(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=W([f,y,h]),ee.CPU=W([_]),ee.DEVICE=W([p,g,m,v,b,E,w,k,S]),ee.ENGINE=ee.OS=W([f,y]),typeof s!==c?(i.exports&&(s=i.exports=ee),s.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete s[e]}return r.exports}a.ab="//",e.exports=a(226)})()},86:(e,t,r)=>{"use strict";r.d(t,{XN:()=>i,FP:()=>n});let n=(0,r(21).xl)();function i(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},122:(e,t,r)=>{"use strict";r.d(t,{cg:()=>o,xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}function o(e){return s?s.bind(e):i.bind(e)}},149:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(122).xl)()},200:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>l,K8:()=>u,hm:()=>d});var n=r(75),i=r(906),s=r(72),a=r(86);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new o}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return i.l.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");class u{static wrap(e,t){let r=new n.VO(new Headers);for(let t of e.getAll())r.set(t);let a=[],o=new Set,l=()=>{let e=s.J.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of a){let r=new n.VO(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case c:return a;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{l()}};default:return i.l.get(e,t,r)}}});return u}}function d(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return h("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return h("cookies().set"),e.set(...r),t};default:return i.l.get(e,r,n)}}});return t}function h(e){if("action"!==(0,a.XN)(e).phase)throw new o}},270:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>l});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function i(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var s=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,l=t.transform,c=t.delimiter,u=o(o(e,void 0===r?s:r,"$1\0$2"),void 0===n?a:n,"\0"),d=0,h=u.length;"\0"===u.charAt(d);)d++;for(;"\0"===u.charAt(h-1);)h--;return u.slice(d,h).split("\0").map(void 0===l?i:l).join(void 0===c?" ":c)}(e,n({delimiter:"."},r))}},302:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return s}});let i=r(819),s={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:i,headers:s,body:a,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(s),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?n.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}}}async function o(e,t){let r=(0,i.getTestReqInfo)(t,s);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await a(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:h}=d;switch(h){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:p,headers:f,body:m}=d.response;return new Response(m?n.from(m,"base64"):null,{status:p,headers:new Headers(f)})}function l(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},356:e=>{"use strict";e.exports=require("node:buffer")},372:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},s=t.split(n),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var s=n||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var c=s.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},377:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,s=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator,m=Object.prototype.hasOwnProperty,g=Object.assign;function y(e,t,r,n,i,a){return{$$typeof:s,type:e,key:t,ref:void 0!==(r=a.ref)?r:null,props:a}}function _(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var v=/\/+/g;function b(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function w(){}function E(e,t,r){if(null==e)return e;var o=[],l=0;return!function e(t,r,o,l,c){var u,d,h,m=typeof t;("undefined"===m||"boolean"===m)&&(t=null);var g=!1;if(null===t)g=!0;else switch(m){case"bigint":case"string":case"number":g=!0;break;case"object":switch(t.$$typeof){case s:case a:g=!0;break;case p:return e((g=t._init)(t._payload),r,o,l,c)}}if(g)return c=c(t),g=""===l?"."+b(t,0):l,i(c)?(o="",null!=g&&(o=g.replace(v,"$&/")+"/"),e(c,r,o,"",function(e){return e})):null!=c&&(_(c)&&(u=c,d=o+(null==c.key||t&&t.key===c.key?"":(""+c.key).replace(v,"$&/")+"/")+g,c=y(u.type,d,void 0,void 0,void 0,u.props)),r.push(c)),1;g=0;var E=""===l?".":l+":";if(i(t))for(var k=0;k<t.length;k++)m=E+b(l=t[k],k),g+=e(l,r,o,m,c);else if("function"==typeof(k=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=f&&h[f]||h["@@iterator"])?h:null))for(t=k.call(t),k=0;!(l=t.next()).done;)m=E+b(l=l.value,k++),g+=e(l,r,o,m,c);else if("object"===m){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(w,w):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,o,l,c);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return g}(e,o,"","",function(e){return t.call(r,e,l++)}),o}function k(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function S(){return new WeakMap}function x(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:E,forEach:function(e,t,r){E(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return E(e,function(){t++}),t},toArray:function(e){return E(e,function(e){return e})||[]},only:function(e){if(!_(e))throw Error(n(143));return e}},t.Fragment=o,t.Profiler=c,t.StrictMode=l,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(S);void 0===(t=n.get(e))&&(t=x(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var s=arguments[n];if("function"==typeof s||"object"==typeof s&&null!==s){var a=t.o;null===a&&(t.o=a=new WeakMap),void 0===(t=a.get(s))&&(t=x(),a.set(s,t))}else null===(a=t.p)&&(t.p=a=new Map),void 0===(t=a.get(s))&&(t=x(),a.set(s,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var o=e.apply(null,arguments);return(n=t).s=1,n.v=o}catch(e){throw(o=t).s=2,o.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=g({},e.props),s=e.key,a=void 0;if(null!=t)for(o in void 0!==t.ref&&(a=void 0),void 0!==t.key&&(s=""+t.key),t)m.call(t,o)&&"key"!==o&&"__self"!==o&&"__source"!==o&&("ref"!==o||void 0!==t.ref)&&(i[o]=t[o]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var l=Array(o),c=0;c<o;c++)l[c]=arguments[c+2];i.children=l}return y(e.type,s,void 0,void 0,a,i)},t.createElement=function(e,t,r){var n,i={},s=null;if(null!=t)for(n in void 0!==t.key&&(s=""+t.key),t)m.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var a=arguments.length-2;if(1===a)i.children=r;else if(1<a){for(var o=Array(a),l=0;l<a;l++)o[l]=arguments[l+2];i.children=o}if(e&&e.defaultProps)for(n in a=e.defaultProps)void 0===i[n]&&(i[n]=a[n]);return y(e,s,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:k}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},410:(e,t,r)=>{"use strict";let n=r(866),{snakeCase:i}=r(270),s={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==s))throw Error("obj must be array of plain objects")}else if(e.constructor!==s)throw Error("obj must be an plain object");return n(e,function(e,r){var n,s,a,o,l;return[(n=t.exclude,s=e,n.some(function(e){return"string"==typeof e?e===s:e.test(s)}))?e:i(e,t.parsingOptions),r,(a=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(a,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},439:(e,t,r)=>{"use strict";r.d(t,{o:()=>s});var n=r(906);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.l.get(t,r,i);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return n.l.get(t,a,i)},set(t,r,i,s){if("symbol"==typeof r)return n.l.set(t,r,i,s);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return n.l.set(t,o??r,i,s)},has(t,r){if("symbol"==typeof r)return n.l.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&n.l.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.l.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||n.l.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.l.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},449:(e,t,r)=>{"use strict";e.exports=r(377)},476:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},486:(e,t,r)=>{"use strict";let n,i;r.r(t),r.d(t,{default:()=>l$});var s,a,o,l,c,u,d={};async function h(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(d),r.d(d,{config:()=>lj,default:()=>lL});let p=null;async function f(){if("phase-production-build"===process.env.NEXT_PHASE)return;p||(p=h());let e=await p;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function m(...e){let t=await h();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let g=null;function y(){return g||(g=f()),g}function _(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(_(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(_(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(_(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),y();class v extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class b extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class w extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let E="_N_T_",k={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function S(e){var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function x(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...S(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function T(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...k,GROUP:{builtinReact:[k.reactServerComponents,k.actionBrowser],serverOnly:[k.reactServerComponents,k.actionBrowser,k.instrument,k.middleware],neutralTarget:[k.apiNode,k.apiEdge],clientOnly:[k.serverSideRendering,k.appPagesBrowser],bundled:[k.reactServerComponents,k.actionBrowser,k.serverSideRendering,k.appPagesBrowser,k.shared,k.instrument,k.middleware],appPages:[k.reactServerComponents,k.serverSideRendering,k.appPagesBrowser,k.actionBrowser]}});let C=Symbol("response"),R=Symbol("passThrough"),O=Symbol("waitUntil");class P{constructor(e,t){this[R]=!1,this[O]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[C]||(this[C]=Promise.resolve(e))}passThroughOnException(){this[R]=!0}waitUntil(e){if("external"===this[O].kind)return(0,this[O].function)(e);this[O].promises.push(e)}}class I extends P{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function N(e){return e.replace(/\/$/,"")||"/"}function A(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function U(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=A(e);return""+t+r+n+i}function L(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=A(e);return""+r+t+n+i}function j(e,t){if("string"!=typeof e)return!1;let{pathname:r}=A(e);return r===t||r.startsWith(t+"/")}let D=new WeakMap;function M(e,t){let r;if(!t)return{pathname:e};let n=D.get(t);n||(n=t.map(e=>e.toLowerCase()),D.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let s=i[1].toLowerCase(),a=n.indexOf(s);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let q=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function $(e,t){return new URL(String(e).replace(q,"localhost"),t&&String(t).replace(q,"localhost"))}let B=Symbol("NextURLInternal");class z{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[B]={url:$(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let s=function(e,t){var r,n;let{basePath:i,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&j(o.pathname,i)&&(o.pathname=function(e,t){if(!j(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,i),o.basePath=i);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):M(o.pathname,s.locales);o.locale=e.detectedLocale,o.pathname=null!=(n=e.pathname)?n:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):M(l,s.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[B].url.pathname,{nextConfig:this[B].options.nextConfig,parseData:!0,i18nProvider:this[B].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[B].url,this[B].options.headers);this[B].domainLocale=this[B].options.i18nProvider?this[B].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(i=s.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[B].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[B].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[B].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[B].url.pathname=s.pathname,this[B].defaultLocale=o,this[B].basePath=s.basePath??"",this[B].buildId=s.buildId,this[B].locale=s.locale??o,this[B].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(j(i,"/api")||j(i,"/"+t.toLowerCase()))?e:U(e,"/"+t)}((e={basePath:this[B].basePath,buildId:this[B].buildId,defaultLocale:this[B].options.forceLocale?void 0:this[B].defaultLocale,locale:this[B].locale,pathname:this[B].url.pathname,trailingSlash:this[B].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=N(t)),e.buildId&&(t=L(U(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=U(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:L(t,"/"):N(t)}formatSearch(){return this[B].url.search}get buildId(){return this[B].buildId}set buildId(e){this[B].buildId=e}get locale(){return this[B].locale??""}set locale(e){var t,r;if(!this[B].locale||!(null==(r=this[B].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[B].locale=e}get defaultLocale(){return this[B].defaultLocale}get domainLocale(){return this[B].domainLocale}get searchParams(){return this[B].url.searchParams}get host(){return this[B].url.host}set host(e){this[B].url.host=e}get hostname(){return this[B].url.hostname}set hostname(e){this[B].url.hostname=e}get port(){return this[B].url.port}set port(e){this[B].url.port=e}get protocol(){return this[B].url.protocol}set protocol(e){this[B].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[B].url=$(e),this.analyze()}get origin(){return this[B].url.origin}get pathname(){return this[B].url.pathname}set pathname(e){this[B].url.pathname=e}get hash(){return this[B].url.hash}set hash(e){this[B].url.hash=e}get search(){return this[B].url.search}set search(e){this[B].url.search=e}get password(){return this[B].url.password}set password(e){this[B].url.password=e}get username(){return this[B].url.username}set username(e){this[B].url.username=e}get basePath(){return this[B].basePath}set basePath(e){this[B].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new z(String(this),this[B].options)}}var K=r(75);let H=Symbol("internal request");class F extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);T(r),e instanceof Request?super(e,t):super(r,t);let n=new z(r,{headers:x(this.headers),nextConfig:t.nextConfig});this[H]={cookies:new K.tm(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[H].cookies}get nextUrl(){return this[H].nextUrl}get page(){throw new b}get ua(){throw new w}get url(){return this[H].url}}var W=r(906);let J=Symbol("internal response"),V=new Set([301,302,303,307,308]);function Z(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class X extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new K.VO(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let s=Reflect.apply(e[n],e,i),a=new Headers(r);return s instanceof K.VO&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,K.Ud)(e)).join(",")),Z(t,a),s};default:return W.l.get(e,n,i)}}});this[J]={cookies:n,url:t.url?new z(t.url,{headers:x(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[J].cookies}static json(e,t){let r=Response.json(e,t);return new X(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!V.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",T(e)),new X(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",T(e)),Z(t,r),new X(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),Z(e,t),new X(null,{...e,headers:t})}}function G(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let Y="Next-Router-Prefetch",Q=["RSC","Next-Router-State-Tree",Y,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],ee="_rsc";var et=r(439),er=r(200),en=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(en||{}),ei=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ei||{}),es=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(es||{}),ea=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ea||{}),eo=function(e){return e.startServer="startServer.startServer",e}(eo||{}),el=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(el||{}),ec=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(ec||{}),eu=function(e){return e.executeRoute="Router.executeRoute",e}(eu||{}),ed=function(e){return e.runHandler="Node.runHandler",e}(ed||{}),eh=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(eh||{}),ep=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(ep||{}),ef=function(e){return e.execute="Middleware.execute",e}(ef||{});let em=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eg=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function ey(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:e_,propagation:ev,trace:eb,SpanStatusCode:ew,SpanKind:eE,ROOT_CONTEXT:ek}=n=r(14);class eS extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let ex=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eS})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:ew.ERROR,message:null==t?void 0:t.message})),e.end()},eT=new Map,eC=n.createContextKey("next.rootSpanId"),eR=0,eO=()=>eR++,eP={set(e,t,r){e.push({key:t,value:r})}};class eI{getTracerInstance(){return eb.getTracer("next.js","0.0.1")}getContext(){return e_}getTracePropagationData(){let e=e_.active(),t=[];return ev.inject(e,t,eP),t}getActiveScopeSpan(){return eb.getSpan(null==e_?void 0:e_.active())}withPropagatedContext(e,t,r){let n=e_.active();if(eb.getSpanContext(n))return t();let i=ev.extract(n,e,r);return e_.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:s,options:a}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},o=a.spanName??r;if(!em.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return s();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=eb.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==e_?void 0:e_.active())??ek,c=!0);let u=eO();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},e_.with(l.setValue(eC,u),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{eT.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eg.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&eT.set(u,new Map(Object.entries(a.attributes??{})));try{if(s.length>1)return s(e,t=>ex(e,t));let t=s(e);if(ey(t))return t.then(t=>(e.end(),t)).catch(t=>{throw ex(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw ex(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return em.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let s=arguments.length-1,a=arguments[s];if("function"!=typeof a)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(e_.active(),a);return t.trace(r,e,(e,t)=>(arguments[s]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eb.setSpan(e_.active(),e):void 0}getRootSpanAttributes(){let e=e_.active().getValue(eC);return eT.get(e)}setRootSpanAttribute(e,t){let r=e_.active().getValue(eC),n=eT.get(r);n&&n.set(e,t)}}let eN=(()=>{let e=new eI;return()=>e})(),eA="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eA);class eU{constructor(e,t,r,n){var i;let s=e&&function(e,t){let r=et.o.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(i=r.get(eA))?void 0:i.value;this._isEnabled=!!(!s&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eA,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eA,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eL(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of S(r))n.append("set-cookie",e);for(let e of new K.VO(n).getAll())t.set(e)}}var ej=r(86),eD=r(600),eM=r.n(eD);class eq extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}var e$=r(72);class eB{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new eB(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let ez=Symbol.for("@next/cache-handlers-map"),eK=Symbol.for("@next/cache-handlers-set"),eH=globalThis;function eF(){if(eH[ez])return eH[ez].entries()}async function eW(e,t){if(!e)return t();let r=eJ(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eJ(e));await eZ(e,t)}}function eJ(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function eV(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eH[eK])return eH[eK].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function eZ(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([eV(r,e.incrementalCache),...Object.values(n),...i])}var eX=r(122),eG=r(149);class eY{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eM()),this.callbackQueue.pause()}after(e){if(ey(e))this.waitUntil||eQ(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){this.waitUntil||eQ();let t=ej.FP.getStore();t&&this.workUnitStores.add(t);let r=eG.Z.getStore(),n=r?r.rootTaskSpawnPhase:null==t?void 0:t.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let i=(0,eX.cg)(async()=>{try{await eG.Z.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}});this.callbackQueue.add(i)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=e$.J.getStore();if(!e)throw Object.defineProperty(new eq("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return eW(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eq("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function eQ(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function e0(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}class e1{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function e2(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let e4=Symbol.for("@next/request-context"),e3=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function e5(e,t,r){let n=[],i=r&&r.size>0;for(let t of e3(e))t=`${E}${t}`,n.push(t);if(t.pathname&&!i){let e=`${E}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=eF();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,e0(async()=>i.getExpiration(...e)));return t}(n)}}class e6 extends F{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let e9={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},e8=(e,t)=>eN().withPropagatedContext(e.headers,t,e9),e7=!1;async function te(e){var t;let n,i;if(!e7&&(e7=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(683);e(),e8=t(e8)}await y();let s=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new z(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=l.has("x-nextjs-data"),u="1"===l.get("RSC");c&&"/index"===a.pathname&&(a.pathname="/");let d=new Map;if(!s)for(let e of Q){let t=e.toLowerCase(),r=l.get(t);null!==r&&(d.set(t,r),l.delete(t))}let h=new e6({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(ee),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(h,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:e2()})}));let p=e.request.waitUntil??(null==(t=function(){let e=globalThis[e4];return null==e?void 0:e.get()}())?void 0:t.waitUntil),f=new I({request:h,page:e.page,context:p?{waitUntil:p}:void 0});if((n=await e8(h,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=f.waitUntil.bind(f),r=new e1;return eN().trace(ef.execute,{spanName:`middleware ${h.method} ${h.nextUrl.pathname}`,attributes:{"http.target":h.nextUrl.pathname,"http.method":h.method}},async()=>{try{var n,s,a,l,c,u;let d=e2(),p=await e5("/",h.nextUrl,null),m=(c=h.nextUrl,u=e=>{i=e},function(e,t,r,n,i,s,a,o,l,c,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let h={};return{type:"request",phase:e,implicitTags:s,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return h.headers||(h.headers=function(e){let t=et.o.from(e);for(let e of Q)t.delete(e.toLowerCase());return et.o.seal(t)}(t.headers)),h.headers},get cookies(){if(!h.cookies){let e=new K.tm(et.o.from(t.headers));eL(t,e),h.cookies=er.Ck.seal(e)}return h.cookies},set cookies(value){h.cookies=value},get mutableCookies(){if(!h.mutableCookies){let e=function(e,t){let r=new K.tm(et.o.from(e));return er.K8.wrap(r,t)}(t.headers,a||(r?d:void 0));eL(t,e),h.mutableCookies=e}return h.mutableCookies},get userspaceMutableCookies(){return h.userspaceMutableCookies||(h.userspaceMutableCookies=(0,er.hm)(this.mutableCookies)),h.userspaceMutableCookies},get draftMode(){return h.draftMode||(h.draftMode=new eU(l,t,this.cookies,this.mutableCookies)),h.draftMode},renderResumeDataCache:o??null,isHmrRefresh:c,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",h,void 0,c,{},p,u,void 0,d,!1,void 0)),g=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:s,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:s,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new eY({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=eF();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,e0(async()=>n.refreshTags()));return e}()};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(s=e.request.nextConfig)||null==(n=s.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:h.headers.has(Y),buildId:o??"",previouslyRevalidatedTags:[]});return await e$.J.run(g,()=>ej.FP.run(m,e.handler,h,f))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(h,f)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let m=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&m&&(u||!s)){let t=new z(m,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});s||t.host!==h.nextUrl.host||(t.buildId=o||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=G(t.toString(),a.toString());!s&&c&&n.headers.set("x-nextjs-rewrite",r),u&&i&&(a.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let g=null==n?void 0:n.headers.get("Location");if(n&&g&&!s){let t=new z(g,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===a.host&&(t.buildId=o||t.buildId,n.headers.set("Location",t.toString())),c&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",G(t.toString(),a.toString()).url))}let _=n||X.next(),v=_.headers.get("x-middleware-override-headers"),b=[];if(v){for(let[e,t]of d)_.headers.set(`x-middleware-request-${e}`,t),b.push(e);b.length>0&&_.headers.set("x-middleware-override-headers",v+","+b.join(","))}return{response:_,waitUntil:("internal"===f[O].kind?Promise.all(f[O].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:h.fetchMetrics}}var tt=Object.defineProperty,tr=Object.getOwnPropertyDescriptor,tn=Object.getOwnPropertyNames,ti=Object.prototype.hasOwnProperty,ts=e=>{throw TypeError(e)},ta=(e,t,r)=>t.has(e)||ts("Cannot "+r),to=(e,t,r)=>(ta(e,t,"read from private field"),r?r.call(e):t.get(e)),tl=(e,t,r)=>t.has(e)?ts("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),tc=(e,t,r,n)=>(ta(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),tu=(e,t,r)=>(ta(e,t,"access private method"),r),td={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},th=async e=>new Promise(t=>setTimeout(t,e)),tp=(e,t)=>t?e*(1+Math.random()):e,tf=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=tp(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await th(r()),t++}},tm=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:s,factor:a,retryImmediately:o,jitter:l}={...td,...t},c=tf({initialDelay:i,maxDelayBetweenRetries:s,factor:a,jitter:l});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;o&&1===r?await th(tp(100,l)):await c()}},tg=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,ty=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,t_=[".lcl.dev",".lclstage.dev",".lclclerk.com"],tv=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],tb=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],tw=[".accountsstage.dev"],tE="https://api.clerk.com",tk="pk_live_";function tS(e,t={}){if(!(e=e||"")||!tx(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!tx(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(tk)?"production":"development",n=tg(e.split("_")[2]);return n=n.slice(0,-1),t.proxyUrl?n=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(n=`clerk.${t.domain}`),{instanceType:r,frontendApi:n}}function tx(e=""){try{let t=e.startsWith(tk)||e.startsWith("pk_test_"),r=tg(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function tT(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function tC(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return ty(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var tR=(e,t)=>`${e}_${t}`,tO=()=>!1,tP=()=>{try{return!0}catch{}return!1},tI=new Set,tN=(e,t,r)=>{let n=tO()||tP(),i=r??e;tI.has(i)||n||(tI.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};function tA(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var tU=class e extends Error{constructor(t,{data:r,status:n,clerkTraceId:i,retryAfter:s}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=n,this.message=t,this.clerkTraceId=i,this.retryAfter=s,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(tA):[]}(r)}},tL=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function tj({packageName:e,customMessages:t}){let r=e,n={...tL,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}var tD=tj({packageName:"@clerk/backend"}),{isDevOrStagingUrl:tM}=function(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=tv.some(e=>r.endsWith(e)),e.set(r,n)),n}}}(),tq={InvalidSecretKey:"clerk_key_invalid"},t$={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},tB={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable."},tz=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}};let tK=crypto;var tH=fetch.bind(globalThis),tF={crypto:tK,get fetch(){return tH},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},tW={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),s=0,a=0,o=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|n,(s+=t.bits)>=8&&(s-=8,i[o++]=255&a>>s)}if(s>=t.bits||255&a<<8-s)throw SyntaxError("Unexpected end of data");return i})(e,tJ,t)},tJ={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},tV={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},tZ="RSASSA-PKCS1-v1_5",tX={RS256:tZ,RS384:tZ,RS512:tZ},tG=Object.keys(tV),tY=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),tQ=(e,t)=>{let r=[t].flat().filter(e=>!!e),n=[e].flat().filter(e=>!!e);if(r.length>0&&n.length>0){if("string"==typeof e){if(!r.includes(e))throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(tY(e)&&!e.some(e=>r.includes(e)))throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},t0=e=>{if(void 0!==e&&"JWT"!==e)throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},t1=e=>{if(!tG.includes(e))throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${tG}.`})},t2=e=>{if("string"!=typeof e)throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},t4=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new tz({reason:t$.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},t3=(e,t)=>{if("number"!=typeof e)throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()<=r.getTime()-t)throw new tz({reason:t$.TokenExpired,message:`JWT is expired. Expiry date: ${n.toUTCString()}, Current date: ${r.toUTCString()}.`})},t5=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tz({reason:t$.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})},t6=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tz({action:tB.EnsureClerkJWT,reason:t$.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tz({reason:t$.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})};async function t9(e,t){let{header:r,signature:n,raw:i}=e,s=new TextEncoder().encode([i.header,i.payload].join(".")),a=function(e){let t=tV[e],r=tX[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${tG.join(",")}.`);return{hash:{name:tV[e]},name:tX[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return tF.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=tg(e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,"")),r=new Uint8Array(new ArrayBuffer(t.length));for(let e=0,n=t.length;e<n;e++)r[e]=t.charCodeAt(e);return r}(e),i="sign"===r?"pkcs8":"spki";return tF.crypto.subtle.importKey(i,n,t,!1,[r])}(t,a,"verify");return{data:await tF.crypto.subtle.verify(a.name,e,n,s)}}catch(e){return{errors:[new tz({reason:t$.TokenInvalidSignature,message:e?.message})]}}}function t8(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new tz({reason:t$.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,n,i]=t,s=new TextDecoder,a=JSON.parse(s.decode(tW.parse(r,{loose:!0}))),o=JSON.parse(s.decode(tW.parse(n,{loose:!0})));return{data:{header:a,payload:o,signature:tW.parse(i,{loose:!0}),raw:{header:r,payload:n,signature:i,text:e}}}}async function t7(e,t){let{audience:r,authorizedParties:n,clockSkewInMs:i,key:s}=t,a=i||5e3,{data:o,errors:l}=t8(e);if(l)return{errors:l};let{header:c,payload:u}=o;try{let{typ:e,alg:t}=c;t0(e),t1(t);let{azp:i,sub:s,aud:o,iat:l,exp:d,nbf:h}=u;t2(s),tQ([o],[r]),t4(i,n),t3(d,a),t5(h,a),t6(l,a)}catch(e){return{errors:[e]}}let{data:d,errors:h}=await t9(o,s);return h?{errors:[new tz({action:tB.EnsureClerkJWT,reason:t$.TokenVerificationFailed,message:`Error verifying JWT signature. ${h[0]}`})]}:d?{data:u}:{errors:[new tz({reason:t$.TokenInvalidSignature,message:"JWT signature is invalid."})]}}var re=r(410),rt={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},rr=new Set(["first_factor","second_factor","multi_factor"]),rn=new Set(["strict_mfa","strict","moderate","lax"]),ri=e=>"number"==typeof e&&e>0,rs=e=>rr.has(e),ra=e=>rn.has(e),ro=e=>e.startsWith("org:")?e:`org:${e}`,rl=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(ro(e.permission)):e.role?n===ro(e.role):null:null},rc=(e,t)=>{let{org:r,user:n}=rd(e),[i,s]=t.split(":"),a=s||i;return"org"===i?r.includes(a):"user"===i?n.includes(a):[...r,...n].includes(a)},ru=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?rc(r,e.feature):e.plan&&n?rc(n,e.plan):null},rd=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},rh=e=>{if(!e)return!1;let t="string"==typeof e&&ra(e),r="object"==typeof e&&rs(e.level)&&ri(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?rt[e]:e).bind(null,e)},rp=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=rh(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[s,a]=t,o=-1!==s?i>s:null,l=-1!==a?i>a:null;switch(n){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},rf=e=>t=>{if(!e.userId)return!1;let r=ru(t,e),n=rl(t,e),i=rp(t,e);return[r||n,i].some(e=>null===e)?[r||n,i].some(e=>!0===e):[r||n,i].every(e=>!0===e)},rm=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),n=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:n}},rg=e=>{let t,r,n,i,s=e.fva??null,a=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,n=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:s}=rd(e.fea),{permissions:a,featurePermissionMap:o}=rm({per:e.o?.per,fpm:e.o?.fpm});i=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let n=[];for(let i=0;i<e.length;i++){let s=e[i];if(i>=r.length)continue;let a=r[i];if(a)for(let e=0;e<a.length;e++)1===a[e]&&n.push(`org:${s}:${t[e]}`)}return n}({features:s,featurePermissionMap:o,permissions:a})}}else t=e.org_id,r=e.org_role,n=e.org_slug,i=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:a,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:n,orgPermissions:i,factorVerificationAge:s}},ry=r(843);function r_(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function rv(e){return e&&e.sensitive?"":"i"}var rb="https://api.clerk.com",rw="@clerk/backend@1.33.0",rE="2025-04-10",rk={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},rS={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:rk.DevBrowser,Handshake:rk.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:rk.HandshakeNonce},rx={Cookies:rk,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:rS},rT=RegExp("(?<!:)/{1,}","g");function rC(...e){return e.filter(e=>e).join("/").replace(rT,"/")}var rR=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},rO="/actor_tokens",rP=class extends rR{async create(e){return this.request({method:"POST",path:rO,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:rC(rO,e,"revoke")})}},rI="/accountless_applications",rN=class extends rR{async createAccountlessApplication(){return this.request({method:"POST",path:rI})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:rC(rI,"complete")})}},rA="/allowlist_identifiers",rU=class extends rR{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:rA,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:rA,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:rC(rA,e)})}},rL=class extends rR{async changeDomain(e){return this.request({method:"POST",path:rC("/beta_features","change_domain"),bodyParams:e})}},rj="/blocklist_identifiers",rD=class extends rR{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:rj,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:rj,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:rC(rj,e)})}},rM="/clients",rq=class extends rR{async getClientList(e={}){return this.request({method:"GET",path:rM,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:rC(rM,e)})}verifyClient(e){return this.request({method:"POST",path:rC(rM,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:rC(rM,"handshake_payload"),queryParams:e})}},r$="/domains",rB=class extends rR{async list(){return this.request({method:"GET",path:r$})}async add(e){return this.request({method:"POST",path:r$,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rC(r$,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:rC(r$,e)})}},rz="/email_addresses",rK=class extends rR{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:rC(rz,e)})}async createEmailAddress(e){return this.request({method:"POST",path:rz,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rC(rz,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:rC(rz,e)})}},rH="/instance",rF=class extends rR{async get(){return this.request({method:"GET",path:rH})}async update(e){return this.request({method:"PATCH",path:rH,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:rC(rH,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:rC(rH,"organization_settings"),bodyParams:e})}},rW="/invitations",rJ=class extends rR{async getInvitationList(e={}){return this.request({method:"GET",path:rW,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:rW,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:rC(rW,e,"revoke")})}},rV=class extends rR{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},rZ="/jwt_templates",rX=class extends rR{async list(e={}){return this.request({method:"GET",path:rZ,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:rC(rZ,e)})}async create(e){return this.request({method:"POST",path:rZ,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rC(rZ,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:rC(rZ,e)})}},rG="/organizations",rY=class extends rR{async getOrganizationList(e){return this.request({method:"GET",path:rG,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:rG,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:rC(rG,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:rC(rG,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new tF.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:rC(rG,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:rC(rG,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:rC(rG,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:rC(rG,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rC(rG,t,"memberships"),queryParams:r})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rC(rG,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...n}=e;return this.requireId(t),this.request({method:"PATCH",path:rC(rG,t,"memberships",r),bodyParams:n})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...n}=e;return this.request({method:"PATCH",path:rC(rG,t,"memberships",r,"metadata"),bodyParams:n})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:rC(rG,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rC(rG,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rC(rG,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:rC(rG,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:rC(rG,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...n}=e;return this.requireId(t),this.request({method:"POST",path:rC(rG,t,"invitations",r,"revoke"),bodyParams:n})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rC(rG,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rC(rG,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:rC(rG,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:rC(rG,t,"domains",r)})}},rQ="/oauth_applications",r0=class extends rR{async list(e={}){return this.request({method:"GET",path:rQ,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:rC(rQ,e)})}async create(e){return this.request({method:"POST",path:rQ,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rC(rQ,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:rC(rQ,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:rC(rQ,e,"rotate_secret")})}},r1="/phone_numbers",r2=class extends rR{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:rC(r1,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:r1,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rC(r1,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:rC(r1,e)})}},r4=class extends rR{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},r3="/redirect_urls",r5=class extends rR{async getRedirectUrlList(){return this.request({method:"GET",path:r3,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:rC(r3,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:r3,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:rC(r3,e)})}},r6="/saml_connections",r9=class extends rR{async getSamlConnectionList(e={}){return this.request({method:"GET",path:r6,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:r6,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:rC(r6,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rC(r6,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:rC(r6,e)})}},r8="/sessions",r7=class extends rR{async getSessionList(e={}){return this.request({method:"GET",path:r8,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:rC(r8,e)})}async createSession(e){return this.request({method:"POST",path:r8,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:rC(r8,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:rC(r8,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:rC(r8,e,"tokens",t||"")})}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...n}=t;return this.request({method:"POST",path:rC(r8,e,"refresh"),bodyParams:n,queryParams:{suffixed_cookies:r}})}},ne="/sign_in_tokens",nt=class extends rR{async createSignInToken(e){return this.request({method:"POST",path:ne,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:rC(ne,e,"revoke")})}},nr="/sign_ups",nn=class extends rR{async get(e){return this.requireId(e),this.request({method:"GET",path:rC(nr,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:rC(nr,t),bodyParams:r})}},ni=class extends rR{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},ns="/users",na=class extends rR{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...i}=e,[s,a]=await Promise.all([this.request({method:"GET",path:ns,queryParams:e}),this.getCount(i)]);return{data:s,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:rC(ns,e)})}async createUser(e){return this.request({method:"POST",path:ns,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rC(ns,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new tF.FormData;return r.append("file",t?.file),this.request({method:"POST",path:rC(ns,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:rC(ns,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:rC(ns,e)})}async getCount(e={}){return this.request({method:"GET",path:rC(ns,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),n=r?t:`oauth_${t}`;return r&&tN("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:rC(ns,e,"oauth_access_tokens",n),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:rC(ns,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:rC(ns,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rC(ns,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:rC(ns,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:rC(ns,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:rC(ns,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:rC(ns,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:rC(ns,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:rC(ns,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:rC(ns,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:rC(ns,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:rC(ns,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:rC(ns,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:rC(ns,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:rC(ns,e,"totp")})}},no="/waitlist_entries",nl=class extends rR{async list(e={}){return this.request({method:"GET",path:no,queryParams:e})}async create(e){return this.request({method:"POST",path:no,bodyParams:e})}},nc="/webhooks",nu=class extends rR{async createSvixApp(){return this.request({method:"POST",path:rC(nc,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:rC(nc,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:rC(nc,"svix")})}};function nd(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var nh=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.status=t,this.userId=r,this.actor=n,this.token=i,this.url=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},np=class e{constructor(e,t,r,n){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=n}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},nf=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=i,this.instanceId=s,this.invitationId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},nm=class e{constructor(e,t,r,n,i,s){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=i,this.instanceId=s}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},ng=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=n,this.country=i,this.browserVersion=s,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},ny=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d=null){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=s,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=c,this.latestActivity=u,this.actor=d}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&ng.fromJSON(t.latest_activity),t.actor)}},n_=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>ny.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},nv=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},nb=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},nw=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},nE=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=n,this.developmentOrigin=i,this.cnameTargets=s,this.accountsPortalUrl=a,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>nv.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},nk=class e{constructor(e,t,r,n,i,s,a,o,l,c,u){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=s,this.bodyPlain=a,this.status=o,this.slug=l,this.data=c,this.deliveredByClerk=u}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},nS=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},nx=class e{constructor(e,t,r=null,n=null,i=null,s=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=s,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},nT=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&nx.fromJSON(t.verification),t.linked_to.map(e=>nS.fromJSON(e)))}},nC=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=s,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=c,this.phoneNumber=u,this.publicMetadata=d,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&nx.fromJSON(t.verification))}},nR=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},nO=class e{constructor(e,t,r,n,i){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=n,this.ignoreDotsForGmailAddresses=i}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},nP=class e{constructor(e,t,r,n,i){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=n,this.enhancedEmailDeliverability=i}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},nI=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=s,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},nN={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},nA=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=n,this.allowedClockSkew=i,this.customSigningKey=s,this.signingAlgorithm=a,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},nU=class e{constructor(e,t,r,n={},i,s,a,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=i,this.scopes=s,this.tokenSecret=a,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},nL=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=n,this.isPublic=i,this.scopes=s,this.redirectUris=a,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=c,this.discoveryUrl=u,this.tokenIntrospectionUrl=d,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},nj=class e{constructor(e,t,r,n,i,s,a,o={},l={},c,u,d,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=i,this.createdAt=s,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=c,this.adminDeleteEnabled=u,this.membersCount=d,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},nD=class e{constructor(e,t,r,n,i,s,a,o,l,c,u={},d={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=n,this.organizationId=i,this.createdAt=s,this.updatedAt=a,this.expiresAt=o,this.url=l,this.status=c,this.publicMetadata=u,this.privateMetadata=d,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},nM=class e{constructor(e,t,r,n={},i={},s,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=i,this.createdAt=s,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,nj.fromJSON(t.organization),nq.fromJSON(t.public_user_data));return r._raw=t,r}},nq=class e{constructor(e,t,r,n,i,s){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=i,this.userId=s}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},n$=class e{constructor(e,t,r,n,i,s,a,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=n,this.creatorRole=i,this.adminDeleteEnabled=s,this.domainsEnabled=a,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},nB=class e{constructor(e,t,r,n,i,s){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&nx.fromJSON(t.verification),t.linked_to.map(e=>nS.fromJSON(e)))}},nz=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=n,this.successful=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},nK=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},nH=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},nF=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},nW=class e{constructor(e,t,r,n){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=n}static fromJSON(t){return new e(t.email_address&&nF.fromJSON(t.email_address),t.phone_number&&nF.fromJSON(t.phone_number),t.web3_wallet&&nF.fromJSON(t.web3_wallet),t.external_account)}},nJ=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,m,g,y,_,v,b,w){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=n,this.missingFields=i,this.unverifiedFields=s,this.verifications=a,this.username=o,this.emailAddress=l,this.phoneNumber=c,this.web3Wallet=u,this.passwordEnabled=d,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=m,this.createdSessionId=g,this.createdUserId=y,this.abandonAt=_,this.legalAcceptedAt=v,this.publicMetadata=b,this.unsafeMetadata=w}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?nW.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},nV=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=s,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},nZ=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},nX=class e{constructor(e,t,r,n,i,s,a,o,l,c){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=i,this.syncUserAttributes=s,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},nG=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=i,this.firstName=s,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&nx.fromJSON(t.verification),t.saml_connection&&nX.fromJSON(t.saml_connection))}},nY=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&nx.fromJSON(t.verification))}},nQ=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,m,g,y,_,v={},b={},w={},E=[],k=[],S=[],x=[],T=[],C,R,O=null,P,I){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=s,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=c,this.hasImage=u,this.primaryEmailAddressId=d,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=m,this.username=g,this.firstName=y,this.lastName=_,this.publicMetadata=v,this.privateMetadata=b,this.unsafeMetadata=w,this.emailAddresses=E,this.phoneNumbers=k,this.web3Wallets=S,this.externalAccounts=x,this.samlAccounts=T,this.lastActiveAt=C,this.createOrganizationEnabled=R,this.createOrganizationsLimit=O,this.deleteSelfEnabled=P,this.legalAcceptedAt=I,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>nT.fromJSON(e)),(t.phone_numbers||[]).map(e=>nB.fromJSON(e)),(t.web3_wallets||[]).map(e=>nY.fromJSON(e)),(t.external_accounts||[]).map(e=>nC.fromJSON(e)),(t.saml_accounts||[]).map(e=>nG.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},n0=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.emailAddress=t,this.status=r,this.invitation=n,this.createdAt=i,this.updatedAt=s,this.isLocked=a}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&nI.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function n1(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return nw.fromJSON(e);switch(e.object){case nN.AccountlessApplication:return np.fromJSON(e);case nN.ActorToken:return nh.fromJSON(e);case nN.AllowlistIdentifier:return nf.fromJSON(e);case nN.BlocklistIdentifier:return nm.fromJSON(e);case nN.Client:return n_.fromJSON(e);case nN.Cookies:return nb.fromJSON(e);case nN.Domain:return nE.fromJSON(e);case nN.EmailAddress:return nT.fromJSON(e);case nN.Email:return nk.fromJSON(e);case nN.Instance:return nR.fromJSON(e);case nN.InstanceRestrictions:return nO.fromJSON(e);case nN.InstanceSettings:return nP.fromJSON(e);case nN.Invitation:return nI.fromJSON(e);case nN.JwtTemplate:return nA.fromJSON(e);case nN.OauthAccessToken:return nU.fromJSON(e);case nN.OAuthApplication:return nL.fromJSON(e);case nN.Organization:return nj.fromJSON(e);case nN.OrganizationInvitation:return nD.fromJSON(e);case nN.OrganizationMembership:return nM.fromJSON(e);case nN.OrganizationSettings:return n$.fromJSON(e);case nN.PhoneNumber:return nB.fromJSON(e);case nN.ProxyCheck:return nz.fromJSON(e);case nN.RedirectUrl:return nK.fromJSON(e);case nN.SignInToken:return nH.fromJSON(e);case nN.SignUpAttempt:return nJ.fromJSON(e);case nN.Session:return ny.fromJSON(e);case nN.SmsMessage:return nV.fromJSON(e);case nN.Token:return nZ.fromJSON(e);case nN.TotalCount:return e.total_count;case nN.User:return nQ.fromJSON(e);case nN.WaitlistEntry:return n0.fromJSON(e);default:return e}}function n2(e){var t;return t=async t=>{let r,{secretKey:n,requireSecretKey:i=!0,apiUrl:s=rb,apiVersion:a="v1",userAgent:o=rw}=e,{path:l,method:c,queryParams:u,headerParams:d,bodyParams:h,formData:p}=t;i&&nd(n);let f=new URL(rC(s,a,l));if(u)for(let[e,t]of Object.entries(re({...u})))t&&[t].flat().forEach(t=>f.searchParams.append(e,t));let m={"Clerk-API-Version":rE,"User-Agent":o,...d};n&&(m.Authorization=`Bearer ${n}`);try{var g;p?r=await tF.fetch(f.href,{method:c,headers:m,body:p}):(m["Content-Type"]="application/json",r=await tF.fetch(f.href,{method:c,headers:m,...(()=>{if(!("GET"!==c&&h&&Object.keys(h).length>0))return null;let e=e=>re(e,{deep:!1});return{body:JSON.stringify(Array.isArray(h)?h.map(e):e(h))}})()}));let e=r?.headers&&r.headers?.get(rx.Headers.ContentType)===rx.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:n5(t),status:r?.status,statusText:r?.statusText,clerkTraceId:n4(t,r?.headers),retryAfter:n3(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>n1(e))}:(g=t)&&"object"==typeof g&&"data"in g&&Array.isArray(g.data)&&void 0!==g.data?{data:t.data.map(e=>n1(e)),totalCount:t.total_count}:{data:n1(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:n4(e,r?.headers)};return{data:null,errors:n5(e),status:r?.status,statusText:r?.statusText,clerkTraceId:n4(e,r?.headers),retryAfter:n3(r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:i,status:s,statusText:a,clerkTraceId:o,retryAfter:l}=await t(...e);if(n){let e=new tU(a||"",{data:[],status:s,clerkTraceId:o,retryAfter:l});throw e.errors=n,e}return void 0!==i?{data:r,totalCount:i}:r}}function n4(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function n3(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function n5(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(tA):[]}return[]}function n6(e){let t=n2(e);return{__experimental_accountlessApplications:new rN(n2({...e,requireSecretKey:!1})),actorTokens:new rP(t),allowlistIdentifiers:new rU(t),betaFeatures:new rL(t),blocklistIdentifiers:new rD(t),clients:new rq(t),domains:new rB(t),emailAddresses:new rK(t),instance:new rF(t),invitations:new rJ(t),jwks:new rV(t),jwtTemplates:new rX(t),oauthApplications:new r0(t),organizations:new rY(t),phoneNumbers:new r2(t),proxyChecks:new r4(t),redirectUrls:new r5(t),samlConnections:new r9(t),sessions:new r7(t),signInTokens:new nt(t),signUps:new nn(t),testingTokens:new ni(t),users:new na(t),waitlistEntries:new nl(t),webhooks:new nu(t)}}var n9=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function n8(e,t){return{sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:n9(e)}}var n7=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template?t(n,e.template):r:null},ie={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},it={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function ir(e,t,r=new Headers,n){let i=function(e,t,r){let{actor:n,sessionId:i,sessionStatus:s,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d}=rg(r),h=n6(e),p=n7({sessionId:i,sessionToken:t,fetcher:async(...e)=>(await h.sessions.getToken(...e)).jwt});return{actor:n,sessionClaims:r,sessionId:i,sessionStatus:s,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d,getToken:p,has:rf({orgId:o,orgRole:l,orgPermissions:u,userId:a,factorVerificationAge:d,features:r.fea||"",plans:r.pla||""}),debug:n9({...e,sessionToken:t})}}(e,n,t);return{status:ie.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:({treatPendingAsSignedOut:e=!0}={})=>e&&"pending"===i.sessionStatus?n8(void 0,i.sessionStatus):i,headers:r,token:n}}function ii(e,t,r="",n=new Headers){return is({status:ie.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>n8({...e,status:ie.SignedOut,reason:t,message:r}),token:null})}var is=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(rx.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(rx.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(rx.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},ia=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},io=(...e)=>new ia(...e),il=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(rx.Headers.ForwardedProto),n=e.headers.get(rx.Headers.ForwardedHost),i=e.headers.get(rx.Headers.Host),s=t.protocol,a=this.getFirstValueFromHeader(n)??i,o=this.getFirstValueFromHeader(r)??s?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?io(t):io(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,ry.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},ic=(...e)=>e[0]instanceof il?e[0]:new il(...e),iu={},id=0;function ih(e,t=!0){iu[e.kid]=e,id=t?Date.now():-1}var ip="local";function im(e){if(!iu[ip]){if(!e)throw new tz({action:tB.SetClerkJWTKey,message:"Missing local JWK.",reason:t$.LocalJWKMissing});ih({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return iu[ip]}async function ig({secretKey:e,apiUrl:t=rb,apiVersion:r="v1",kid:n,skipJwksCache:i}){if(i||function(){if(-1===id)return!1;let e=Date.now()-id>=3e5;return e&&(iu={}),e}()||!iu[n]){if(!e)throw new tz({action:tB.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:t$.RemoteJWKFailedToLoad});let{keys:n}=await tm(()=>iy(t,e,r));if(!n||!n.length)throw new tz({action:tB.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:t$.RemoteJWKFailedToLoad});n.forEach(e=>ih(e))}let s=iu[n];if(!s){let e=Object.values(iu).map(e=>e.kid).sort().join(", ");throw new tz({action:`Go to your Dashboard and validate your secret and public keys are correct. ${tB.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${n}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:t$.JWKKidMismatch})}return s}async function iy(e,t,r){if(!t)throw new tz({action:tB.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:t$.RemoteJWKFailedToLoad});let n=new URL(e);n.pathname=rC(n.pathname,r,"/jwks");let i=await tF.fetch(n.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":rE,"Content-Type":"application/json","User-Agent":rw}});if(!i.ok){let e=await i.json(),t=i_(e?.errors,tq.InvalidSecretKey);if(t){let e=t$.InvalidSecretKey;throw new tz({action:tB.ContactSupport,message:t.message,reason:e})}throw new tz({action:tB.ContactSupport,message:`Error loading Clerk JWKS from ${n.href} with code=${i.status}`,reason:t$.RemoteJWKFailedToLoad})}return i.json()}var i_=(e,t)=>e?e.find(e=>e.code===t):null;async function iv(e,t){let{data:r,errors:n}=t8(e);if(n)return{errors:n};let{header:i}=r,{kid:s}=i;try{let r;if(t.jwtKey)r=im(t.jwtKey);else{if(!t.secretKey)return{errors:[new tz({action:tB.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:t$.JWKFailedToResolve})]};r=await ig({...t,kid:s})}return await t7(e,{...t,key:r})}catch(e){return{errors:[e]}}}var ib=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(rx.Cookies.ClientUat),t=this.getCookie(rx.Cookies.ClientUat),r=this.getSuffixedCookie(rx.Cookies.Session)||"",n=this.getCookie(rx.Cookies.Session)||"";if(n&&!this.tokenHasIssuer(n))return!1;if(n&&!this.tokenBelongsToInstance(n))return!0;if(!e&&!r)return!1;let{data:i}=t8(n),s=i?.payload.iat||0,{data:a}=t8(r),o=a?.payload.iat||0;if("0"!==e&&"0"!==t&&s>o||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(a);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}initPublishableKeyValues(e){tS(e.publishableKey,{fatal:!0}),this.publishableKey=e.publishableKey;let t=tS(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=t.instanceType,this.frontendApi=t.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.parseAuthorizationHeader(this.getHeader(rx.Headers.Authorization)),this.origin=this.getHeader(rx.Headers.Origin),this.host=this.getHeader(rx.Headers.Host),this.forwardedHost=this.getHeader(rx.Headers.ForwardedHost),this.forwardedProto=this.getHeader(rx.Headers.CloudFrontForwardedProto)||this.getHeader(rx.Headers.ForwardedProto),this.referrer=this.getHeader(rx.Headers.Referrer),this.userAgent=this.getHeader(rx.Headers.UserAgent),this.secFetchDest=this.getHeader(rx.Headers.SecFetchDest),this.accept=this.getHeader(rx.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(rx.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(rx.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(rx.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(rx.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(rx.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(rx.QueryParameters.Handshake)||this.getCookie(rx.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(rx.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(rx.QueryParameters.HandshakeNonce)||this.getCookie(rx.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie(tR(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=t8(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=t8(e);if(r)return!1;let n=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===n}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},iw=async(e,t)=>new ib(t.publishableKey?await tC(t.publishableKey,tF.crypto.subtle):"",e,t),iE=e=>e.split(";")[0]?.split("=")[0],ik=e=>e.split(";")[0]?.split("=")[1];async function iS(e,{key:t}){let{data:r,errors:n}=t8(e);if(n)throw n[0];let{header:i,payload:s}=r,{typ:a,alg:o}=i;t0(a),t1(o);let{data:l,errors:c}=await t9(r,t);if(c)throw new tz({reason:t$.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!l)throw new tz({reason:t$.TokenInvalidSignature,message:"Handshake signature is invalid."});return s}async function ix(e,t){let r,{secretKey:n,apiUrl:i,apiVersion:s,jwksCacheTtlInMs:a,jwtKey:o,skipJwksCache:l}=t,{data:c,errors:u}=t8(e);if(u)throw u[0];let{kid:d}=c.header;if(o)r=im(o);else if(n)r=await ig({secretKey:n,apiUrl:i,apiVersion:s,kid:d,jwksCacheTtlInMs:a,skipJwksCache:l});else throw new tz({action:tB.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:t$.JWKFailedToResolve});return await iS(e,{key:r})}var iT=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.replace(/http(s)?:\/\//,""),n=new URL(`https://${r}/v1/client/handshake`);n.searchParams.append("redirect_url",t?.href||""),n.searchParams.append("__clerk_api_version",rE),n.searchParams.append(rx.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),n.searchParams.append(rx.QueryParameters.HandshakeReason,e),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&n.searchParams.append(rx.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let i=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return i&&this.getOrganizationSyncQueryParams(i).forEach((e,t)=>{n.searchParams.append(t,e)}),new Headers({[rx.Headers.Location]:n.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await ix(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),iE(t).startsWith(rx.Cookies.Session)&&(r=ik(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(rx.QueryParameters.Handshake),t.searchParams.delete(rx.QueryParameters.HandshakeHelp),e.append(rx.Headers.Location,t.toString()),e.set(rx.Headers.CacheControl,"no-store")}if(""===r)return ii(this.authenticateContext,it.SessionTokenMissing,"",e);let{data:n,errors:[i]=[]}=await iv(r,this.authenticateContext);if(n)return ir(this.authenticateContext,n,e,r);if("development"===this.authenticateContext.instanceType&&(i?.reason===t$.TokenExpired||i?.reason===t$.TokenNotActiveYet||i?.reason===t$.TokenIatInTheFuture)){let t=new tz({action:i.action,message:i.message,reason:i.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:n,errors:[s]=[]}=await iv(r,{...this.authenticateContext,clockSkewInMs:864e5});if(n)return ir(this.authenticateContext,n,e,r);throw Error(s?.message||"Clerk: Handshake retry failed.")}throw Error(i?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===t$.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=rx.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(rx.QueryParameters.DevBrowser),t.searchParams.delete(rx.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},iC=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,n,i,s,a,o,l;return r=void 0,n=[],i=function e(t,r,n){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,n).source}),new RegExp("(?:".concat(i.join("|"),")"),rv(n))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,c=r.delimiter,u=r.endsWith,d="[".concat(r_(void 0===u?"":u),"]|$"),h="[".concat(r_(void 0===c?"/#?":c),"]"),p=void 0===s||s?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=r_(l(m));else{var g=r_(l(m.prefix)),y=r_(l(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var _="*"===m.modifier?"?":"";p+="(?:".concat(g,"((?:").concat(m.pattern,")(?:").concat(y).concat(g,"(?:").concat(m.pattern,"))*)").concat(y,")").concat(_)}else p+="(?:".concat(g,"(").concat(m.pattern,")").concat(y,")").concat(m.modifier);else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));p+="(".concat(m.pattern,")").concat(m.modifier)}else p+="(?:".concat(g).concat(y,")").concat(m.modifier)}}if(void 0===a||a)i||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(d,")"):"$";else{var v=e[e.length-1],b="string"==typeof v?h.indexOf(v[v.length-1])>-1:void 0===v;i||(p+="(?:".concat(h,"(?=").concat(d,"))?")),b||(p+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(p,rv(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[s++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=s;continue}if("("===n){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '.concat(s));for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at ".concat(s));l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s=t.delimiter,a=void 0===s?"/#?":s,o=[],l=0,c=0,u="",d=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[c],i=n.type,s=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(s,", expected ").concat(e))},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(r_(a),"]+?"):"(?:(?!".concat(r_(r),")[^").concat(r_(a),"])+?")};c<r.length;){var g=d("CHAR"),y=d("NAME"),_=d("PATTERN");if(y||_){var v=g||"";-1===i.indexOf(v)&&(u+=v,v=""),u&&(o.push(u),u=""),o.push({name:y||l++,prefix:v,suffix:"",pattern:_||m(v),modifier:d("MODIFIER")||""});continue}var b=g||d("ESCAPED_CHAR");if(b){u+=b;continue}if(u&&(o.push(u),u=""),d("OPEN")){var v=p(),w=d("NAME")||"",E=d("PATTERN")||"",k=p();h("CLOSE"),o.push({name:w||(E?l++:""),pattern:w&&!E?m(v):E,prefix:v,suffix:k,modifier:d("MODIFIER")||""});continue}h("END")}return o}(t,n),r,n)}(e,n,r),s=n,a=r,void 0===a&&(a={}),o=a.decode,l=void 0===o?function(e){return e}:o,function(e){var t=i.exec(e);if(!t)return!1;for(var r=t[0],n=t.index,a=Object.create(null),o=1;o<t.length;o++)!function(e){if(void 0!==t[e]){var r=s[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):a[r.name]=l(t[e],r)}}(o);return{path:r,index:n,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},iR={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};async function iO(e,t){let r=await iw(ic(e),t);if(nd(r.secretKey),r.isSatellite){var n=r.signInUrl,i=r.secretKey;if(!n&&tT(i))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let s=new iC(t.organizationSyncOptions),a=new iT(r,{organizationSyncOptions:t.organizationSyncOptions},s);async function o(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:iR.MissingApiClient}}};let{sessionToken:n,refreshTokenInCookie:i}=r;if(!n)return{data:null,error:{message:"Session token must be provided.",cause:{reason:iR.MissingSessionToken}}};if(!i)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:iR.MissingRefreshToken}}};let{data:s,errors:a}=t8(n);if(!s||a)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:iR.ExpiredSessionTokenDecodeFailed,errors:a}}};if(!s?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:iR.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(s.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:n||"",refresh_token:i||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:iR.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:iR.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function l(e){let{data:t,error:r}=await o(e);if(!t||0===t.length)return{data:null,error:r};let n=new Headers,i="";t.forEach(e=>{n.append("Set-Cookie",e),iE(e).startsWith(rx.Cookies.Session)&&(i=ik(e))});let{data:s,errors:a}=await iv(i,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:iR.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:s,sessionToken:i,headers:n},error:null}}function c(e,t,r,n){if(!a.isRequestEligibleForHandshake())return ii(e,t,r);let i=n??a.buildRedirectToHandshake(t);return(i.get(rx.Headers.Location)&&i.set(rx.Headers.CacheControl,"no-store"),a.checkAndTrackRedirectLoop(i))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),ii(e,t,r)):function(e,t,r="",n){return is({status:ie.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>null,token:null})}(e,t,r,i)}async function u(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:n}=await iv(e,r);if(n)throw n[0];return ir(r,t,void 0,e)}catch(e){return h(e,"header")}}async function d(){let e=r.clientUat,t=!!r.sessionTokenInCookie,n=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await a.resolveHandshake()}catch(e){e instanceof tz&&"development"===r.instanceType?a.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(rx.QueryParameters.DevBrowser))return c(r,it.DevBrowserSync,"");let i=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&i)return c(r,it.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&i&&!r.clerkUrl.searchParams.has(rx.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(rx.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[rx.Headers.Location]:e.toString()});return c(r,it.SatelliteCookieNeedsSyncing,"",t)}let o=new URL(r.clerkUrl).searchParams.get(rx.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&o){let e=new URL(o);r.devBrowserToken&&e.searchParams.append(rx.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(rx.QueryParameters.ClerkSynced,"true");let t=new Headers({[rx.Headers.Location]:e.toString()});return c(r,it.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!n)return c(r,it.DevBrowserMissing,"");if(!e&&!t)return ii(r,it.SessionTokenAndUATMissing,"");if(!e&&t)return c(r,it.SessionTokenWithoutClientUAT,"");if(e&&!t)return c(r,it.ClientUATWithoutSessionToken,"");let{data:l,errors:u}=t8(r.sessionTokenInCookie);if(u)return h(u[0],"cookie");if(l.payload.iat<r.clientUat)return c(r,it.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await iv(r.sessionTokenInCookie,r);if(t)throw t[0];let n=ir(r,e,void 0,r.sessionTokenInCookie),i=n.toAuth();if(i.userId){let e=function(e,t){let r=s.findTarget(e.clerkUrl);if(!r)return null;let n=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(n=!0),r.organizationId&&r.organizationId!==t.orgId&&(n=!0)),"personalAccount"===r.type&&t.orgId&&(n=!0),!n)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let i=c(e,it.ActiveOrganizationMismatch,"");return"handshake"!==i.status?null:i}(r,i);if(e)return e}return n}catch(e){return h(e,"cookie")}}async function h(t,n){let i;if(!(t instanceof tz))return ii(r,it.UnexpectedError);if(t.reason===t$.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await l(r);if(e)return ir(r,e.jwtPayload,e.headers,e.sessionToken);i=t?.cause?.reason?t.cause.reason:iR.UnexpectedSDKError}else i="GET"!==e.method?iR.NonEligibleNonGet:r.refreshTokenInCookie?null:iR.NonEligibleNoCookie;return(t.tokenCarrier=n,[t$.TokenExpired,t$.TokenNotActiveYet,t$.TokenIatInTheFuture].includes(t.reason))?c(r,iI({tokenError:t.reason,refreshError:i}),t.getFullMessage()):ii(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?u():d()}var iP=e=>{let{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:s,isSatellite:a,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:s,isSatellite:a,domain:o}},iI=({tokenError:e,refreshError:t})=>{switch(e){case t$.TokenExpired:return`${it.SessionTokenExpired}-refresh-${t}`;case t$.TokenNotActiveYet:return it.SessionTokenNBF;case t$.TokenIatInTheFuture:return it.SessionTokenIatInTheFuture;default:return it.UnexpectedError}};function iN(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var iA={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""},iU=(e,t,r,n)=>{if(""===e)return iL(t.toString(),r?.toString());let i=new URL(e),s=r?new URL(r,i):void 0,a=new URL(t,i);return s&&a.searchParams.set("redirect_url",s.toString()),n&&i.hostname!==a.hostname&&a.searchParams.set(rx.QueryParameters.DevBrowser,n),a.toString()},iL=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},ij=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:n,signUpUrl:i,baseUrl:s,sessionStatus:a}=e,o=tS(t),l=o?.frontendApi,c=o?.instanceType==="development",u=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(l),d="pending"===a,h=(t,{returnBackUrl:n})=>r(iU(s,`${t}/tasks`,n,c?e.devBrowserToken:null));return{redirectToSignUp:({returnBackUrl:t}={})=>{i||u||tD.throwMissingPublishableKeyError();let a=`${u}/sign-up`,o=i||function(e){if(!e)return;let t=new URL(e,s);return t.pathname=`${t.pathname}/create`,t.toString()}(n)||a;return d?h(o,{returnBackUrl:t}):r(iU(s,o,t,c?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{n||u||tD.throwMissingPublishableKeyError();let i=`${u}/sign-in`,a=n||i;return d?h(a,{returnBackUrl:t}):r(iU(s,a,t,c?e.devBrowserToken:null))}}};r(51),r(917),r(902).s;var iD=r(737);let iM=""+iD.s8+";404";iD.s8,iD.s8,r(878).X,r(78),"undefined"==typeof URLPattern||URLPattern,r(679),r(476),r(727),r(666),new WeakMap;let iq={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},i$=e=>e.headers.get(iq.Headers.NextRedirect),iB=(e,t,r)=>(e.headers.set(t,r),e);var iz="__clerk_db_jwt",iK=e=>{let t=new URL(e);return t.searchParams.delete(iz),t},iH=e=>{let t=new URL(e);return t.searchParams.delete("__dev_session"),t.hash=decodeURI(t.hash).replace(/__clerk_db_jwt\[(.*)\]/,""),t.href.endsWith("#")&&(t.hash=""),t};let iF=(e,t,r)=>{let n=t.headers.get("location");if("true"===t.headers.get(rx.Headers.ClerkRedirectTo)&&n&&tT(r.secretKey)&&e.clerkUrl.isCrossOrigin(n)){let r=e.cookies.get(iz)||"",i=function(e,t){let r=new URL(e),n=r.searchParams.get(iz);r.searchParams.delete(iz);let i=n||t;return i&&r.searchParams.set(iz,i),r}(new URL(n),r);return X.redirect(i.href,t)}return t},iW={rE:"15.3.2"},iJ=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},iV=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?iJ(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,iJ(t)])),null,2)).join(", "),iZ=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){var i,s;for(let n of(console.log((i=e,`[clerk debug start: ${i}]`)),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return n.decode(i).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((s=e,`[clerk debug end: ${s}] (@clerk/nextjs=6.20.0,next=${iW.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},iX=(e,t)=>(...r)=>{let n=("string"==typeof e?iZ(e,iV):e)(),i=t(n);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};function iG(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}var iY=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function iQ(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}iY(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),iY(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""}),process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let i0=process.env.CLERK_API_VERSION||"v1",i1=process.env.CLERK_SECRET_KEY||"",i2="pk_live_Y2xlcmsuY3ViZW50LmRldiQ",i4=process.env.CLERK_ENCRYPTION_KEY||"",i3=process.env.CLERK_API_URL||(e=>{let t=tS(e)?.frontendApi;return t?.startsWith("clerk.")&&t_.some(e=>t?.endsWith(e))?tE:tb.some(e=>t?.endsWith(e))?"https://api.lclclerk.com":tw.some(e=>t?.endsWith(e))?"https://api.clerkstage.dev":tE})(i2),i5="cubent.dev",i6=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",i9=iQ(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,i8="/sign-in",i7=iQ(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),se=iQ(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),st=iQ(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1,sr=!(iW.rE.startsWith("13.")||iW.rE.startsWith("14.0"))&&!1,sn=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),n=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||n||i};async function si(){try{let{headers:e}=await Promise.resolve().then(r.bind(r,56)),t=await e();return new F("https://placeholder.com",{headers:t})}catch(e){if(e&&sn(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}var ss=class{constructor(){tl(this,s_),tl(this,sg,"clerk_telemetry_throttler"),tl(this,sy,864e5)}isEventThrottled(e){if(!to(this,s_,sw))return!1;let t=Date.now(),r=tu(this,s_,sv).call(this,e),n=to(this,s_,sb)?.[r];if(!n){let e={...to(this,s_,sb),[r]:t};localStorage.setItem(to(this,sg),JSON.stringify(e))}if(n&&t-n>to(this,sy)){let e=to(this,s_,sb);delete e[r],localStorage.setItem(to(this,sg),JSON.stringify(e))}return!!n}};sg=new WeakMap,sy=new WeakMap,s_=new WeakSet,sv=function(e){let{sk:t,pk:r,payload:n,...i}=e,s={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>s[e]))},sb=function(){let e=localStorage.getItem(to(this,sg));return e?JSON.parse(e):{}},sw=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem(to(this,sg)),!1}};var sa={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},so=class{constructor(e){tl(this,sC),tl(this,sE),tl(this,sk),tl(this,sS,{}),tl(this,sx,[]),tl(this,sT),tc(this,sE,{maxBufferSize:e.maxBufferSize??sa.maxBufferSize,samplingRate:e.samplingRate??sa.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:sa.endpoint}),e.clerkVersion||"undefined"!=typeof window?to(this,sS).clerkVersion=e.clerkVersion??"":to(this,sS).clerkVersion="",to(this,sS).sdk=e.sdk,to(this,sS).sdkVersion=e.sdkVersion,to(this,sS).publishableKey=e.publishableKey??"";let t=tS(e.publishableKey);t&&(to(this,sS).instanceType=t.instanceType),e.secretKey&&(to(this,sS).secretKey=e.secretKey.substring(0,16)),tc(this,sk,new ss)}get isEnabled(){return!("development"!==to(this,sS).instanceType||to(this,sE).disabled||"undefined"!=typeof process&&iQ(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return to(this,sE).debug||"undefined"!=typeof process&&iQ(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=tu(this,sC,sU).call(this,e.event,e.payload);tu(this,sC,sN).call(this,t.event,t),tu(this,sC,sR).call(this,t,e.eventSamplingRate)&&(to(this,sx).push(t),tu(this,sC,sP).call(this))}};sE=new WeakMap,sk=new WeakMap,sS=new WeakMap,sx=new WeakMap,sT=new WeakMap,sC=new WeakSet,sR=function(e,t){return this.isEnabled&&!this.isDebug&&tu(this,sC,sO).call(this,e,t)},sO=function(e,t){let r=Math.random();return!!(r<=to(this,sE).samplingRate&&(void 0===t||r<=t))&&!to(this,sk).isEventThrottled(e)},sP=function(){if("undefined"==typeof window)return void tu(this,sC,sI).call(this);if(to(this,sx).length>=to(this,sE).maxBufferSize){to(this,sT)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)(to(this,sT)),tu(this,sC,sI).call(this);return}to(this,sT)||("requestIdleCallback"in window?tc(this,sT,requestIdleCallback(()=>{tu(this,sC,sI).call(this)})):tc(this,sT,setTimeout(()=>{tu(this,sC,sI).call(this)},0)))},sI=function(){fetch(new URL("/v1/event",to(this,sE).endpoint),{method:"POST",body:JSON.stringify({events:to(this,sx)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{tc(this,sx,[])}).catch(()=>void 0)},sN=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},sA=function(){let e={name:to(this,sS).sdk,version:to(this,sS).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},sU=function(e,t){let r=tu(this,sC,sA).call(this);return{event:e,cv:to(this,sS).clerkVersion??"",it:to(this,sS).instanceType??"",sdk:r.name,sdkv:r.version,...to(this,sS).publishableKey?{pk:to(this,sS).publishableKey}:{},...to(this,sS).secretKey?{sk:to(this,sS).secretKey}:{},payload:t}};let sl={secretKey:i1,publishableKey:i2,apiUrl:i3,apiVersion:i0,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i6,domain:i5,isSatellite:i9,sdkMetadata:{name:"@clerk/nextjs",version:"6.20.0",environment:"production"},telemetry:{disabled:i7,debug:se}},sc=e=>(function(e){let t={...e},r=n6(t),n=function(e){let t=iN(iA,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:i,apiVersion:s}=t,a=iN(t,n);return iO(e,{...n,...a,apiUrl:i,apiVersion:s,apiClient:r})},debugRequestState:iP}}({options:t,apiClient:r}),i=new so({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...n,telemetry:i}})({...sl,...e});var su=r(521);let sd=new Map,sh=new su.AsyncLocalStorage;var sp=new Set,sf={warnOnce:e=>{sp.has(e)||(sp.add(e),console.warn(e))}};function sm(e){return/^http(s)?:\/\//.test(e||"")}var sg,sy,s_,sv,sb,sw,sE,sk,sS,sx,sT,sC,sR,sO,sP,sI,sN,sA,sU,sL,sj,sD,sM,sq,s$,sB,sz=Object.defineProperty,sK=(e,t,r)=>t in e?sz(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,sH=(null==(sL="undefined"!=typeof globalThis?globalThis:void 0)?void 0:sL.crypto)||(null==(sj=void 0!==r.g?r.g:void 0)?void 0:sj.crypto)||(null==(sD="undefined"!=typeof window?window:void 0)?void 0:sD.crypto)||(null==(sM="undefined"!=typeof self?self:void 0)?void 0:sM.crypto)||(null==(s$=null==(sq="undefined"!=typeof frames?frames:void 0)?void 0:sq[0])?void 0:s$.crypto);sB=sH?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(sH.getRandomValues(new Uint32Array(1))[0]);return new sW(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let n=0,i;n<e;n+=4){let e=r(0x100000000*(i||Math.random()));i=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new sW(t,e)};var sF=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},sW=class extends sF{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let n=0;n<e;n+=1)t[n>>>2]|=r[n]<<24-n%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=sJ){return e.stringify(this)}concat(e){let t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(let e=0;e<i;e+=1){let i=r[e>>>2]>>>24-e%4*8&255;t[n+e>>>2]|=i<<24-(n+e)%4*8}else for(let e=0;e<i;e+=4)t[n+e>>>2]=r[e>>>2];return this.sigBytes+=i,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>sK(e,"symbol"!=typeof t?t+"":t,r))(sW,"random",sB);var sJ={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push((r>>>4).toString(16)),n.push((15&r).toString(16))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new sW(r,t/2)}},sV={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push(String.fromCharCode(r))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=1)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new sW(r,t)}},sZ={stringify(e){try{return decodeURIComponent(escape(sV.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>sV.parse(unescape(encodeURIComponent(e)))},sX=class extends sF{constructor(){super(),this._minBufferSize=0}reset(){this._data=new sW,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=sZ.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:n}=this,i=r.words,s=r.sigBytes,a=s/(4*n),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*n,l=Math.min(4*o,s);if(o){for(let e=0;e<o;e+=n)this._doProcessBlock(i,e);t=i.splice(0,o),r.sigBytes-=l}return new sW(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},sG=class extends sX{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new sF,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new sY(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},sY=class extends sF{constructor(e,t){super();let r=new e;this._hasher=r;let n=t;"string"==typeof n&&(n=sZ.parse(n));let i=r.blockSize,s=4*i;n.sigBytes>s&&(n=r.finalize(t)),n.clamp();let a=n.clone();this._oKey=a;let o=n.clone();this._iKey=o;let l=a.words,c=o.words;for(let e=0;e<i;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;a.sigBytes=s,o.sigBytes=s,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},sQ=(e,t,r)=>{let n=[],i=0;for(let s=0;s<t;s+=1)if(s%4){let t=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;n[i>>>2]|=t<<24-i%4*8,i+=1}return sW.create(n,i)},s0={stringify(e){let{words:t,sigBytes:r}=e,n=this._map;e.clamp();let i=[];for(let e=0;e<r;e+=3){let s=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)i.push(n.charAt(s>>>6*(3-t)&63))}let s=n.charAt(64);if(s)for(;i.length%4;)i.push(s);return i.join("")},parse(e){let t=e.length,r=this._map,n=this._reverseMap;if(!n){this._reverseMap=[],n=this._reverseMap;for(let e=0;e<r.length;e+=1)n[r.charCodeAt(e)]=e}let i=r.charAt(64);if(i){let r=e.indexOf(i);-1!==r&&(t=r)}return sQ(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},s1=[];for(let e=0;e<64;e+=1)s1[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var s2=(e,t,r,n,i,s,a)=>{let o=e+(t&r|~t&n)+i+a;return(o<<s|o>>>32-s)+t},s4=(e,t,r,n,i,s,a)=>{let o=e+(t&n|r&~n)+i+a;return(o<<s|o>>>32-s)+t},s3=(e,t,r,n,i,s,a)=>{let o=e+(t^r^n)+i+a;return(o<<s|o>>>32-s)+t},s5=(e,t,r,n,i,s,a)=>{let o=e+(r^(t|~n))+i+a;return(o<<s|o>>>32-s)+t},s6=class extends sG{_doReset(){this._hash=new sW([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let n=t+r,i=e[n];e[n]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00}let r=this._hash.words,n=e[t+0],i=e[t+1],s=e[t+2],a=e[t+3],o=e[t+4],l=e[t+5],c=e[t+6],u=e[t+7],d=e[t+8],h=e[t+9],p=e[t+10],f=e[t+11],m=e[t+12],g=e[t+13],y=e[t+14],_=e[t+15],v=r[0],b=r[1],w=r[2],E=r[3];v=s2(v,b,w,E,n,7,s1[0]),E=s2(E,v,b,w,i,12,s1[1]),w=s2(w,E,v,b,s,17,s1[2]),b=s2(b,w,E,v,a,22,s1[3]),v=s2(v,b,w,E,o,7,s1[4]),E=s2(E,v,b,w,l,12,s1[5]),w=s2(w,E,v,b,c,17,s1[6]),b=s2(b,w,E,v,u,22,s1[7]),v=s2(v,b,w,E,d,7,s1[8]),E=s2(E,v,b,w,h,12,s1[9]),w=s2(w,E,v,b,p,17,s1[10]),b=s2(b,w,E,v,f,22,s1[11]),v=s2(v,b,w,E,m,7,s1[12]),E=s2(E,v,b,w,g,12,s1[13]),w=s2(w,E,v,b,y,17,s1[14]),b=s2(b,w,E,v,_,22,s1[15]),v=s4(v,b,w,E,i,5,s1[16]),E=s4(E,v,b,w,c,9,s1[17]),w=s4(w,E,v,b,f,14,s1[18]),b=s4(b,w,E,v,n,20,s1[19]),v=s4(v,b,w,E,l,5,s1[20]),E=s4(E,v,b,w,p,9,s1[21]),w=s4(w,E,v,b,_,14,s1[22]),b=s4(b,w,E,v,o,20,s1[23]),v=s4(v,b,w,E,h,5,s1[24]),E=s4(E,v,b,w,y,9,s1[25]),w=s4(w,E,v,b,a,14,s1[26]),b=s4(b,w,E,v,d,20,s1[27]),v=s4(v,b,w,E,g,5,s1[28]),E=s4(E,v,b,w,s,9,s1[29]),w=s4(w,E,v,b,u,14,s1[30]),b=s4(b,w,E,v,m,20,s1[31]),v=s3(v,b,w,E,l,4,s1[32]),E=s3(E,v,b,w,d,11,s1[33]),w=s3(w,E,v,b,f,16,s1[34]),b=s3(b,w,E,v,y,23,s1[35]),v=s3(v,b,w,E,i,4,s1[36]),E=s3(E,v,b,w,o,11,s1[37]),w=s3(w,E,v,b,u,16,s1[38]),b=s3(b,w,E,v,p,23,s1[39]),v=s3(v,b,w,E,g,4,s1[40]),E=s3(E,v,b,w,n,11,s1[41]),w=s3(w,E,v,b,a,16,s1[42]),b=s3(b,w,E,v,c,23,s1[43]),v=s3(v,b,w,E,h,4,s1[44]),E=s3(E,v,b,w,m,11,s1[45]),w=s3(w,E,v,b,_,16,s1[46]),b=s3(b,w,E,v,s,23,s1[47]),v=s5(v,b,w,E,n,6,s1[48]),E=s5(E,v,b,w,u,10,s1[49]),w=s5(w,E,v,b,y,15,s1[50]),b=s5(b,w,E,v,l,21,s1[51]),v=s5(v,b,w,E,m,6,s1[52]),E=s5(E,v,b,w,a,10,s1[53]),w=s5(w,E,v,b,p,15,s1[54]),b=s5(b,w,E,v,i,21,s1[55]),v=s5(v,b,w,E,d,6,s1[56]),E=s5(E,v,b,w,_,10,s1[57]),w=s5(w,E,v,b,c,15,s1[58]),b=s5(b,w,E,v,g,21,s1[59]),v=s5(v,b,w,E,o,6,s1[60]),E=s5(E,v,b,w,f,10,s1[61]),w=s5(w,E,v,b,s,15,s1[62]),b=s5(b,w,E,v,h,21,s1[63]),r[0]=r[0]+v|0,r[1]=r[1]+b|0,r[2]=r[2]+w|0,r[3]=r[3]+E|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32;let i=Math.floor(r/0x100000000);t[(n+64>>>9<<4)+15]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,t[(n+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let s=this._hash,a=s.words;for(let e=0;e<4;e+=1){let t=a[e];a[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return s}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};sG._createHelper(s6),sG._createHmacHelper(s6);var s9=class extends sF{constructor(e){super(),this.cfg=Object.assign(new sF,{keySize:4,hasher:s6,iterations:1},e)}compute(e,t){let r,{cfg:n}=this,i=n.hasher.create(),s=sW.create(),a=s.words,{keySize:o,iterations:l}=n;for(;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(let e=1;e<l;e+=1)r=i.finalize(r),i.reset();s.concat(r)}return s.sigBytes=4*o,s}},s8=class extends sX{constructor(e,t,r){super(),this.cfg=Object.assign(new sF,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?aa:as;return{encrypt:(r,n,i)=>t(n).encrypt(e,r,n,i),decrypt:(r,n,i)=>t(n).decrypt(e,r,n,i)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};s8._ENC_XFORM_MODE=1,s8._DEC_XFORM_MODE=2,s8.keySize=4,s8.ivSize=4;var s7=class extends sF{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function ae(e,t,r){let n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(let i=0;i<r;i+=1)e[t+i]^=n[i]}var at=class extends s7{};at.Encryptor=class extends at{processBlock(e,t){let r=this._cipher,{blockSize:n}=r;ae.call(this,e,t,n),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}},at.Decryptor=class extends at{processBlock(e,t){let r=this._cipher,{blockSize:n}=r,i=e.slice(t,t+n);r.decryptBlock(e,t),ae.call(this,e,t,n),this._prevBlock=i}};var ar={pad(e,t){let r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[];for(let e=0;e<n;e+=4)s.push(i);let a=sW.create(s,n);e.concat(a)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},an=class extends s8{constructor(e,t,r){super(e,t,Object.assign({mode:at,padding:ar},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:n}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},ai=class extends sF{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},as=class extends sF{static encrypt(e,t,r,n){let i=Object.assign(new sF,this.cfg,n),s=e.createEncryptor(r,i),a=s.finalize(t),o=s.cfg;return ai.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:s.blockSize,formatter:i.format})}static decrypt(e,t,r,n){let i=t,s=Object.assign(new sF,this.cfg,n);return i=this._parse(i,s.format),e.createDecryptor(r,s).finalize(i.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};as.cfg=Object.assign(new sF,{format:{stringify(e){let t,{ciphertext:r,salt:n}=e;return(n?sW.create([0x53616c74,0x65645f5f]).concat(n).concat(r):r).toString(s0)},parse(e){let t,r=s0.parse(e),n=r.words;return 0x53616c74===n[0]&&0x65645f5f===n[1]&&(t=sW.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),ai.create({ciphertext:r,salt:t})}}});var aa=class extends as{static encrypt(e,t,r,n){let i=Object.assign(new sF,this.cfg,n),s=i.kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=s.iv;let a=as.encrypt.call(this,e,t,s.key,i);return a.mixIn(s),a}static decrypt(e,t,r,n){let i=t,s=Object.assign(new sF,this.cfg,n);i=this._parse(i,s.format);let a=s.kdf.execute(r,e.keySize,e.ivSize,i.salt,s.hasher);return s.iv=a.iv,as.decrypt.call(this,e,i,a.key,s)}};aa.cfg=Object.assign(as.cfg,{kdf:{execute(e,t,r,n,i){let s,a=n;a||(a=sW.random(8)),s=i?s9.create({keySize:t+r,hasher:i}).compute(e,a):s9.create({keySize:t+r}).compute(e,a);let o=sW.create(s.words.slice(t),4*r);return s.sigBytes=4*t,ai.create({key:s,iv:o,salt:a})}}});var ao=[],al=[],ac=[],au=[],ad=[],ah=[],ap=[],af=[],am=[],ag=[],ay=[];for(let e=0;e<256;e+=1)e<128?ay[e]=e<<1:ay[e]=e<<1^283;var a_=0,av=0;for(let e=0;e<256;e+=1){let e=av^av<<1^av<<2^av<<3^av<<4;e=e>>>8^255&e^99,ao[a_]=e,al[e]=a_;let t=ay[a_],r=ay[t],n=ay[r],i=257*ay[e]^0x1010100*e;ac[a_]=i<<24|i>>>8,au[a_]=i<<16|i>>>16,ad[a_]=i<<8|i>>>24,ah[a_]=i,i=0x1010101*n^65537*r^257*t^0x1010100*a_,ap[e]=i<<24|i>>>8,af[e]=i<<16|i>>>16,am[e]=i<<8|i>>>24,ag[e]=i,a_?(a_=t^ay[ay[ay[n^t]]],av^=ay[ay[av]]):a_=av=1}var ab=[0,1,2,4,8,16,32,64,128,27,54],aw=class extends an{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,n=t.sigBytes/4;this._nRounds=n+6;let i=(this._nRounds+1)*4;this._keySchedule=[];let s=this._keySchedule;for(let t=0;t<i;t+=1)t<n?s[t]=r[t]:(e=s[t-1],t%n?n>6&&t%n==4&&(e=ao[e>>>24]<<24|ao[e>>>16&255]<<16|ao[e>>>8&255]<<8|ao[255&e]):e=(ao[(e=e<<8|e>>>24)>>>24]<<24|ao[e>>>16&255]<<16|ao[e>>>8&255]<<8|ao[255&e])^ab[t/n|0]<<24,s[t]=s[t-n]^e);this._invKeySchedule=[];let a=this._invKeySchedule;for(let t=0;t<i;t+=1){let r=i-t;e=t%4?s[r]:s[r-4],t<4||r<=4?a[t]=e:a[t]=ap[ao[e>>>24]]^af[ao[e>>>16&255]]^am[ao[e>>>8&255]]^ag[ao[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,ac,au,ad,ah,ao)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,ap,af,am,ag,al),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,n,i,s,a,o){let l=this._nRounds,c=e[t]^r[0],u=e[t+1]^r[1],d=e[t+2]^r[2],h=e[t+3]^r[3],p=4;for(let e=1;e<l;e+=1){let e=n[c>>>24]^i[u>>>16&255]^s[d>>>8&255]^a[255&h]^r[p];p+=1;let t=n[u>>>24]^i[d>>>16&255]^s[h>>>8&255]^a[255&c]^r[p];p+=1;let o=n[d>>>24]^i[h>>>16&255]^s[c>>>8&255]^a[255&u]^r[p];p+=1;let l=n[h>>>24]^i[c>>>16&255]^s[u>>>8&255]^a[255&d]^r[p];p+=1,c=e,u=t,d=o,h=l}let f=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p];p+=1;let m=(o[u>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&c])^r[p];p+=1;let g=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^r[p];p+=1;let y=(o[h>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&d])^r[p];p+=1,e[t]=f,e[t+1]=m,e[t+2]=g,e[t+3]=y}};aw.keySize=8;var aE=an._createHelper(aw),ak=[],aS=class extends sG{_doReset(){this._hash=new sW([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,n=r[0],i=r[1],s=r[2],a=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)ak[r]=0|e[t+r];else{let e=ak[r-3]^ak[r-8]^ak[r-14]^ak[r-16];ak[r]=e<<1|e>>>31}let l=(n<<5|n>>>27)+o+ak[r];r<20?l+=(i&s|~i&a)+0x5a827999:r<40?l+=(i^s^a)+0x6ed9eba1:r<60?l+=(i&s|i&a|s&a)-0x70e44324:l+=(i^s^a)-0x359d3e2a,o=a,a=s,s=i<<30|i>>>2,i=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[(n+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(n+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},ax=(sG._createHelper(aS),sG._createHmacHelper(aS));let aT=`
Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.

1) With middleware
   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'
   `,aC=`
Invalid signInUrl. A satellite application requires a signInUrl for development instances.
Check if signInUrl is missing from your configuration or if it is not an absolute URL

1) With middleware
   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`,aR=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`,aO=tj({packageName:"@clerk/nextjs"}),aP="x-middleware-override-headers",aI="x-middleware-request",aN=(e,t,r)=>{e.headers.get(aP)||(e.headers.set(aP,[...t.headers.keys()]),t.headers.forEach((t,r)=>{e.headers.set(`${aI}-${r}`,t)})),Object.entries(r).forEach(([t,r])=>{e.headers.set(aP,`${e.headers.get(aP)},${t}`),e.headers.set(`${aI}-${t}`,r)})},aA=(e,t)=>{let r,n=iG(null==t?void 0:t.proxyUrl,e.clerkUrl,i6);r=n&&!sm(n)?new URL(n,e.clerkUrl).toString():n;let i=iG(t.isSatellite,new URL(e.url),i9),s=iG(t.domain,new URL(e.url),i5),a=(null==t?void 0:t.signInUrl)||i8;if(i&&!r&&!s)throw Error(aT);if(i&&!sm(a)&&tT(t.secretKey||i1))throw Error(aC);return{proxyUrl:r,isSatellite:i,domain:s,signInUrl:a}},aU=e=>X.redirect(e,{headers:{[rx.Headers.ClerkRedirectTo]:"true"}}),aL="clerk_keyless_dummy_key";function aj(){if(tP())throw Error("Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)");throw Error(aR)}function aD(e,t){return JSON.parse(aE.decrypt(e,t).toString(sZ))}let aM=async()=>{var e,t;let r;try{let e=await si(),t=function(e,t){var r,n;return function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(n=null==(r=e.socket)?void 0:r._httpMessage)?void 0:n.getHeader(t))}(e,rx.Headers.ClerkRequestData);r=function(e){if(!e)return{};let t=tP()?i4||i1:i4||i1||aL;try{return aD(e,t)}catch{if(sr)try{return aD(e,aL)}catch{aj()}aj()}}(t)}catch(e){if(e&&sn(e))throw e}let n=null!=(t=null==(e=sh.getStore())?void 0:e.get("requestData"))?t:r;return(null==n?void 0:n.secretKey)||(null==n?void 0:n.publishableKey)?sc(n):sc({})};class aq{static createDefaultDirectives(){return Object.entries(this.DEFAULT_DIRECTIVES).reduce((e,[t,r])=>(e[t]=new Set(r),e),{})}static isKeyword(e){return this.KEYWORDS.has(e.replace(/^'|'$/g,""))}static formatValue(e){let t=e.replace(/^'|'$/g,"");return this.isKeyword(t)?`'${t}'`:e}static handleDirectiveValues(e){let t=new Set;return e.includes("'none'")||e.includes("none")?t.add("'none'"):e.forEach(e=>t.add(this.formatValue(e))),t}}aq.KEYWORDS=new Set(["none","self","strict-dynamic","unsafe-eval","unsafe-hashes","unsafe-inline"]),aq.DEFAULT_DIRECTIVES={"connect-src":["self","https://clerk-telemetry.com","https://*.clerk-telemetry.com","https://api.stripe.com","https://maps.googleapis.com"],"default-src":["self"],"form-action":["self"],"frame-src":["self","https://challenges.cloudflare.com","https://*.js.stripe.com","https://js.stripe.com","https://hooks.stripe.com"],"img-src":["self","https://img.clerk.com"],"script-src":["self","unsafe-inline","https:","http:","https://*.js.stripe.com","https://js.stripe.com","https://maps.googleapis.com"],"style-src":["self","unsafe-inline"],"worker-src":["self","blob:"]};let a$="__clerk_keys_";async function aB(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function az(){let e=process.env.PWD;if(!e)return`${a$}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await aB(t);return`${a$}${r}`}async function aK(e){let t;if(!sr)return;let r=await az();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}let aH={REDIRECT_TO_URL:"CLERK_PROTECT_REDIRECT_TO_URL",REDIRECT_TO_SIGN_IN:"CLERK_PROTECT_REDIRECT_TO_SIGN_IN",REDIRECT_TO_SIGN_UP:"CLERK_PROTECT_REDIRECT_TO_SIGN_UP"},aF={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},aW=new Set(Object.values(aF)),aJ="NEXT_REDIRECT";function aV(e,t,r="replace",n=307){let i=Error(aJ);throw i.digest=`${aJ};${r};${e};${n};`,i.clerk_digest=aH.REDIRECT_TO_URL,Object.assign(i,t),i}function aZ(e,t){return null===t?"":t||e}function aX(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,i=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===aJ&&("replace"===n||"push"===n)&&"string"==typeof i&&!isNaN(s)&&307===s}let aG=e=>{var t,r;return!!e.headers.get(iq.Headers.NextUrl)&&((null==(t=e.headers.get(rx.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(rx.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(iq.Headers.NextAction))},aY=e=>{var t;return"document"===e.headers.get(rx.Headers.SecFetchDest)||"iframe"===e.headers.get(rx.Headers.SecFetchDest)||(null==(t=e.headers.get(rx.Headers.Accept))?void 0:t.includes("text/html"))||aQ(e)||a1(e)},aQ=e=>!!e.headers.get(iq.Headers.NextUrl)&&!aG(e)||a0(),a0=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},a1=e=>!!e.headers.get(iq.Headers.NextjsData),a2=e=>[e[0]instanceof Request?e[0]:void 0,e[0]instanceof Request?e[1]:void 0],a4=e=>["function"==typeof e[0]?e[0]:void 0,(2===e.length?e[1]:"function"==typeof e[0]?{}:e[0])||{}],a3=e=>"/clerk-sync-keyless"===e.nextUrl.pathname,a5=e=>{let t=e.nextUrl.searchParams.get("returnUrl"),r=new URL(e.url);return r.pathname="",X.redirect(t||r.toString())},a6=(e,t)=>({...t,...aA(e,t)}),a9=e=>(t={})=>{!function(e,t){aV(e,{clerk_digest:aH.REDIRECT_TO_SIGN_IN,returnBackUrl:aZ(e,t)})}(e.clerkUrl.toString(),t.returnBackUrl)},a8=e=>(t={})=>{!function(e,t){aV(e,{clerk_digest:aH.REDIRECT_TO_SIGN_UP,returnBackUrl:aZ(e,t)})}(e.clerkUrl.toString(),t.returnBackUrl)},a7=(e,t,r)=>async(n,i)=>(function(e){let{redirectToSignIn:t,authObject:r,redirect:n,notFound:i,request:s}=e;return async(...e)=>{var a,o,l,c,u,d;let h=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],p=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),f=(null==(u=e[0])?void 0:u.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),m=()=>f?n(f):i();return"pending"!==r.sessionStatus&&r.userId?h?"function"==typeof h?h(r.has)?r:m():r.has(h)?r:m():r:p?n(p):aY(s)?t():i()}})({request:e,redirect:e=>aV(e,{redirectUrl:e}),notFound:()=>(function(){let e=Object.defineProperty(Error(iM),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=iM,e})(),authObject:t,redirectToSignIn:r})(n,i),oe=(e,t,r,n)=>{var i;if(function(e){return"object"==typeof e&&null!==e&&"digest"in e&&"NEXT_NOT_FOUND"===e.digest||function(e){if(!function(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return"NEXT_HTTP_ERROR_FALLBACK"===t&&aW.has(Number(r))}(e))return;let[,t]=e.digest.split(";");return Number(t)}(e)===aF.NOT_FOUND}(e))return iB(X.rewrite(new URL(`/clerk_${Date.now()}`,r.url)),rx.Headers.AuthReason,"protect-rewrite");let s=function(e){return!!aX(e)&&"clerk_digest"in e&&e.clerk_digest===aH.REDIRECT_TO_SIGN_IN}(e),a=function(e){return!!aX(e)&&"clerk_digest"in e&&e.clerk_digest===aH.REDIRECT_TO_SIGN_UP}(e);if(s||a){let r=ij({redirectAdapter:aU,baseUrl:t.clerkUrl,signInUrl:n.signInUrl,signUpUrl:n.signUpUrl,publishableKey:n.publishableKey,sessionStatus:null==(i=n.toAuth())?void 0:i.sessionStatus}),{returnBackUrl:a}=e;return r[s?"redirectToSignIn":"redirectToSignUp"]({returnBackUrl:a})}if(aX(e))return aU(e.redirectUrl);throw e},ot=new Map([["baseUri","base-uri"],["childSrc","child-src"],["defaultSrc","default-src"],["frameSrc","frame-src"],["workerSrc","worker-src"],["connectSrc","connect-src"],["fontSrc","font-src"],["imgSrc","img-src"],["manifestSrc","manifest-src"],["mediaSrc","media-src"],["objectSrc","object-src"],["prefetchSrc","prefetch-src"],["scriptSrc","script-src"],["scriptSrcElem","script-src-elem"],["scriptSrcAttr","script-src-attr"],["styleSrc","style-src"],["styleSrcElem","style-src-elem"],["styleSrcAttr","style-src-attr"],["sandbox","sandbox"],["formAction","form-action"],["frameAncestors","frame-ancestors"],["navigateTo","navigate-to"],["reportUri","report-uri"],["reportTo","report-to"],["requireTrustedTypesFor","require-trusted-types-for"],["trustedTypes","trusted-types"],["upgradeInsecureRequests","upgrade-insecure-requests"]]),or=new Set(["require-corp","credentialless","unsafe-none"]),on=new Set(["same-origin","same-origin-allow-popups","unsafe-none"]),oi=new Set(["same-origin","same-site","cross-origin"]),os=new Set(["no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url",""]),oa=new Set(["none","master-only","by-content-type","all"]),oo=new Set(["allow-downloads-without-user-activation","allow-forms","allow-modals","allow-orientation-lock","allow-pointer-lock","allow-popups","allow-popups-to-escape-sandbox","allow-presentation","allow-same-origin","allow-scripts","allow-storage-access-by-user-activation","allow-top-navigation","allow-top-navigation-by-user-activation"]),ol=new Map([["self","'self'"],["unsafe-eval","'unsafe-eval'"],["unsafe-hashes","'unsafe-hashes'"],["unsafe-inline","'unsafe-inline'"],["none","'none'"],["strict-dynamic","'strict-dynamic'"],["report-sample","'report-sample'"],["wasm-unsafe-eval","'wasm-unsafe-eval'"],["script","'script'"]]),oc={contentSecurityPolicy:{directives:{baseUri:["'none'"],childSrc:["'none'"],connectSrc:["'self'"],defaultSrc:["'self'"],fontSrc:["'self'"],formAction:["'self'"],frameAncestors:["'none'"],frameSrc:["'none'"],imgSrc:["'self'","blob:","data:"],manifestSrc:["'self'"],mediaSrc:["'self'"],objectSrc:["'none'"],scriptSrc:["'self'"],styleSrc:["'self'"],workerSrc:["'self'"]}},crossOriginEmbedderPolicy:{policy:"require-corp"},crossOriginOpenerPolicy:{policy:"same-origin"},crossOriginResourcePolicy:{policy:"same-origin"},originAgentCluster:!0,referrerPolicy:{policy:["no-referrer"]},strictTransportSecurity:{maxAge:31536e3,includeSubDomains:!0,preload:!1},xContentTypeOptions:!0,xDnsPrefetchControl:{allow:!1},xDownloadOptions:!0,xFrameOptions:{action:"sameorigin"},xPermittedCrossDomainPolicies:{permittedPolicies:"none"},xXssProtection:!0};function ou(e){return"function"==typeof e?e():e}class od extends Error{constructor(e){super(`validation error: ${e}`)}}let oh={...oc,contentSecurityPolicy:{directives:{...oc.contentSecurityPolicy.directives,scriptSrc:[...oc.contentSecurityPolicy.directives.scriptSrc,op],styleSrc:[...oc.contentSecurityPolicy.directives.styleSrc,"'unsafe-inline'"]}}};function op(){return`'nonce-${btoa(crypto.randomUUID())}'`}let of={...oh,contentSecurityPolicy:!1},om=function(e){let t=e.contentSecurityPolicy;!0===t&&(t=oc.contentSecurityPolicy);let r=t;if(t){let e=t.directives?.scriptSrc;!0===e&&(e=oc.contentSecurityPolicy.directives.scriptSrc);let n=t.directives?.connectSrc;!0===n&&(n=oc.contentSecurityPolicy.directives.connectSrc);let i=t.directives?.imgSrc;!0===i&&(i=oc.contentSecurityPolicy.directives.imgSrc);let s=t.directives?.frameSrc;!0===s&&(s=oc.contentSecurityPolicy.directives.frameSrc);let a=t.directives?.styleSrc;!0===a&&(a=oc.contentSecurityPolicy.directives.styleSrc);let o=t.directives?.fontSrc;!0===o&&(o=oc.contentSecurityPolicy.directives.fontSrc),r={...t,directives:{...t.directives,scriptSrc:e?[...e.filter(e=>"'none'"!==e&&"https://vercel.live"!==e),"https://vercel.live"]:e,connectSrc:n?[...n.filter(e=>"'none'"!==e&&"https://vercel.live"!==e&&"wss://ws-us3.pusher.com"!==e),"https://vercel.live","wss://ws-us3.pusher.com"]:n,imgSrc:i?[...i.filter(e=>"'none'"!==e&&"https://vercel.live"!==e&&"https://vercel.com"!==e&&"data:"!==e&&"blob:"!==e),"https://vercel.live","https://vercel.com","data:","blob:"]:i,frameSrc:s?[...s.filter(e=>"'none'"!==e&&"https://vercel.live"!==e),"https://vercel.live"]:s,styleSrc:a?[...a.filter(e=>"'none'"!==e&&"https://vercel.live"!==e&&"'unsafe-inline'"!==e),"https://vercel.live","'unsafe-inline'"]:a,fontSrc:o?[...o.filter(e=>"'none'"!==e&&"https://vercel.live"!==e&&"https://assets.vercel.com"!==e),"https://vercel.live","https://assets.vercel.com"]:o}}}let n=e.crossOriginEmbedderPolicy;!0===n&&(n=oc.crossOriginEmbedderPolicy);let i=n;return n&&(i={policy:n.policy?"unsafe-none":n.policy}),{...e,contentSecurityPolicy:r,crossOriginEmbedderPolicy:i}}(of);function og(e,t){if(e instanceof Promise)throw Error(t)}function oy(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},n="object"==typeof e.server?e.server:{},i="object"==typeof e.shared?e.shared:{},s=e.isServer??("undefined"==typeof window||"Deno"in window),a=s?{...n,...i,...r}:{...r,...i},o=e.createFinalSchema?.(a,s)["~standard"].validate(t)??function(e,t){let r={},n=[];for(let i in e){let s=e[i]["~standard"].validate(t[i]);if(og(s,`Validation must be synchronous, but ${i} returned a Promise.`),s.issues){n.push(...s.issues.map(e=>({...e,path:[i,...e.path??[]]})));continue}r[i]=s.value}return n.length?{issues:n}:{value:r}}(a,t);og(o,"Validation must be synchronous");let l=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),c=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(o.issues)return l(o.issues);let u=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in i),d=e=>s||!u(e),h=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),o.value),{get(e,t){if("string"==typeof t&&!h(t))return d(t)?Reflect.get(e,t):c(t)}})}function o_(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},n=e.shared,i=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return oy({...e,shared:n,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:i})}!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(s||(s={})),(a||(a={})).mergeShapes=(e,t)=>({...e,...t});let ov=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ob=e=>{switch(typeof e){case"undefined":return ov.undefined;case"string":return ov.string;case"number":return Number.isNaN(e)?ov.nan:ov.number;case"boolean":return ov.boolean;case"function":return ov.function;case"bigint":return ov.bigint;case"symbol":return ov.symbol;case"object":if(Array.isArray(e))return ov.array;if(null===e)return ov.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return ov.promise;if("undefined"!=typeof Map&&e instanceof Map)return ov.map;if("undefined"!=typeof Set&&e instanceof Set)return ov.set;if("undefined"!=typeof Date&&e instanceof Date)return ov.date;return ov.object;default:return ov.unknown}},ow=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class oE extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(n);else if("invalid_return_type"===i.code)n(i.returnTypeError);else if("invalid_arguments"===i.code)n(i.argumentsError);else if(0===i.path.length)r._errors.push(t(i));else{let e=r,n=0;for(;n<i.path.length;){let r=i.path[n];n===i.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(i))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof oE))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}oE.create=e=>new oE(e);let ok=(e,t)=>{let r;switch(e.code){case ow.invalid_type:r=e.received===ov.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case ow.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case ow.unrecognized_keys:r=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case ow.invalid_union:r="Invalid input";break;case ow.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case ow.invalid_enum_value:r=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case ow.invalid_arguments:r="Invalid function arguments";break;case ow.invalid_return_type:r="Invalid function return type";break;case ow.invalid_date:r="Invalid date";break;case ow.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case ow.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case ow.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case ow.custom:r="Invalid input";break;case ow.invalid_intersection_types:r="Intersection results could not be merged";break;case ow.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case ow.not_finite:r="Number must be finite";break;default:r=t.defaultError,s.assertNever(e)}return{message:r}},oS=e=>{let{data:t,path:r,errorMaps:n,issueData:i}=e,s=[...r,...i.path||[]],a={...i,path:s};if(void 0!==i.message)return{...i,path:s,message:i.message};let o="";for(let e of n.filter(e=>!!e).slice().reverse())o=e(a,{data:t,defaultError:o}).message;return{...i,path:s,message:o}};function ox(e,t){let r=oS({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,ok,ok==ok?void 0:ok].filter(e=>!!e)});e.common.issues.push(r)}class oT{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let n of t){if("aborted"===n.status)return oC;"dirty"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,n=await e.value;r.push({key:t,value:n})}return oT.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let n of t){let{key:t,value:i}=n;if("aborted"===t.status||"aborted"===i.status)return oC;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||n.alwaysSet)&&(r[t.value]=i.value)}return{status:e.value,value:r}}}let oC=Object.freeze({status:"aborted"}),oR=e=>({status:"dirty",value:e}),oO=e=>({status:"valid",value:e}),oP=e=>"aborted"===e.status,oI=e=>"dirty"===e.status,oN=e=>"valid"===e.status,oA=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(o||(o={}));var oU=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)},oL=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r};class oj{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let oD=(e,t)=>{if(oN(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new oE(e.common.issues);return this._error=t,this._error}}};function oM(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:n,description:i}=e;if(t&&(r||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{let{message:s}=e;return"invalid_enum_value"===t.code?{message:s??i.defaultError}:void 0===i.data?{message:s??n??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:s??r??i.defaultError}},description:i}}class oq{get description(){return this._def.description}_getType(e){return ob(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ob(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new oT,ctx:{common:e.parent.common,data:e.data,parsedType:ob(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(oA(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ob(e)},n=this._parseSync({data:e,path:r.path,parent:r});return oD(r,n)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ob(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return oN(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>oN(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ob(e)},n=this._parse({data:e,path:r.path,parent:r});return oD(r,await (oA(n)?n:Promise.resolve(n)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let i=e(t),s=()=>n.addIssue({code:ow.custom,...r(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(s(),!1)):!!i||(s(),!1)})}refinement(e,t){return this._refinement((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1))}_refinement(e){return new lE({schema:this,typeName:u.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return lk.create(this,this._def)}nullable(){return lS.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ls.create(this)}promise(){return lw.create(this,this._def)}or(e){return lo.create([this,e],this._def)}and(e){return lu.create(this,e,this._def)}transform(e){return new lE({...oM(this._def),schema:this,typeName:u.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new lx({...oM(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:u.ZodDefault})}brand(){return new lR({typeName:u.ZodBranded,type:this,...oM(this._def)})}catch(e){return new lT({...oM(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:u.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return lO.create(this,e)}readonly(){return lP.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let o$=/^c[^\s-]{8,}$/i,oB=/^[0-9a-z]+$/,oz=/^[0-9A-HJKMNP-TV-Z]{26}$/i,oK=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,oH=/^[a-z0-9_-]{21}$/i,oF=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,oW=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,oJ=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,oV=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,oZ=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,oX=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,oG=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,oY=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,oQ=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,o0="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",o1=RegExp(`^${o0}$`);function o2(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}class o4 extends oq{_parse(e){var t,r,n,a;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==ov.string){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.string,received:t.parsedType}),oC}let l=new oT;for(let c of this._def.checks)if("min"===c.kind)e.data.length<c.value&&(ox(o=this._getOrReturnCtx(e,o),{code:ow.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),l.dirty());else if("max"===c.kind)e.data.length>c.value&&(ox(o=this._getOrReturnCtx(e,o),{code:ow.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),l.dirty());else if("length"===c.kind){let t=e.data.length>c.value,r=e.data.length<c.value;(t||r)&&(o=this._getOrReturnCtx(e,o),t?ox(o,{code:ow.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}):r&&ox(o,{code:ow.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}),l.dirty())}else if("email"===c.kind)oJ.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"email",code:ow.invalid_string,message:c.message}),l.dirty());else if("emoji"===c.kind)i||(i=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),i.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:ow.invalid_string,message:c.message}),l.dirty());else if("uuid"===c.kind)oK.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:ow.invalid_string,message:c.message}),l.dirty());else if("nanoid"===c.kind)oH.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:ow.invalid_string,message:c.message}),l.dirty());else if("cuid"===c.kind)o$.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:ow.invalid_string,message:c.message}),l.dirty());else if("cuid2"===c.kind)oB.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:ow.invalid_string,message:c.message}),l.dirty());else if("ulid"===c.kind)oz.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:ow.invalid_string,message:c.message}),l.dirty());else if("url"===c.kind)try{new URL(e.data)}catch{ox(o=this._getOrReturnCtx(e,o),{validation:"url",code:ow.invalid_string,message:c.message}),l.dirty()}else"regex"===c.kind?(c.regex.lastIndex=0,c.regex.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"regex",code:ow.invalid_string,message:c.message}),l.dirty())):"trim"===c.kind?e.data=e.data.trim():"includes"===c.kind?e.data.includes(c.value,c.position)||(ox(o=this._getOrReturnCtx(e,o),{code:ow.invalid_string,validation:{includes:c.value,position:c.position},message:c.message}),l.dirty()):"toLowerCase"===c.kind?e.data=e.data.toLowerCase():"toUpperCase"===c.kind?e.data=e.data.toUpperCase():"startsWith"===c.kind?e.data.startsWith(c.value)||(ox(o=this._getOrReturnCtx(e,o),{code:ow.invalid_string,validation:{startsWith:c.value},message:c.message}),l.dirty()):"endsWith"===c.kind?e.data.endsWith(c.value)||(ox(o=this._getOrReturnCtx(e,o),{code:ow.invalid_string,validation:{endsWith:c.value},message:c.message}),l.dirty()):"datetime"===c.kind?(function(e){let t=`${o0}T${o2(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)})(c).test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{code:ow.invalid_string,validation:"datetime",message:c.message}),l.dirty()):"date"===c.kind?o1.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{code:ow.invalid_string,validation:"date",message:c.message}),l.dirty()):"time"===c.kind?RegExp(`^${o2(c)}$`).test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{code:ow.invalid_string,validation:"time",message:c.message}),l.dirty()):"duration"===c.kind?oW.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"duration",code:ow.invalid_string,message:c.message}),l.dirty()):"ip"===c.kind?(t=e.data,!(("v4"===(r=c.version)||!r)&&oV.test(t)||("v6"===r||!r)&&oX.test(t))&&1&&(ox(o=this._getOrReturnCtx(e,o),{validation:"ip",code:ow.invalid_string,message:c.message}),l.dirty())):"jwt"===c.kind?!function(e,t){if(!oF.test(e))return!1;try{let[r]=e.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),i=JSON.parse(atob(n));if("object"!=typeof i||null===i||"typ"in i&&i?.typ!=="JWT"||!i.alg||t&&i.alg!==t)return!1;return!0}catch{return!1}}(e.data,c.alg)&&(ox(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:ow.invalid_string,message:c.message}),l.dirty()):"cidr"===c.kind?(n=e.data,!(("v4"===(a=c.version)||!a)&&oZ.test(n)||("v6"===a||!a)&&oG.test(n))&&1&&(ox(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:ow.invalid_string,message:c.message}),l.dirty())):"base64"===c.kind?oY.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"base64",code:ow.invalid_string,message:c.message}),l.dirty()):"base64url"===c.kind?oQ.test(e.data)||(ox(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:ow.invalid_string,message:c.message}),l.dirty()):s.assertNever(c);return{status:l.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:ow.invalid_string,...o.errToObj(r)})}_addCheck(e){return new o4({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...o.errToObj(e)})}url(e){return this._addCheck({kind:"url",...o.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...o.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...o.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...o.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...o.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...o.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...o.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...o.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...o.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...o.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...o.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...o.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...o.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...o.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...o.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...o.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...o.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...o.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...o.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...o.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...o.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...o.errToObj(t)})}nonempty(e){return this.min(1,o.errToObj(e))}trim(){return new o4({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new o4({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new o4({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}o4.create=e=>new o4({checks:[],typeName:u.ZodString,coerce:e?.coerce??!1,...oM(e)});class o3 extends oq{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==ov.number){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.number,received:t.parsedType}),oC}let r=new oT;for(let n of this._def.checks)"int"===n.kind?s.isInteger(e.data)||(ox(t=this._getOrReturnCtx(e,t),{code:ow.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"multipleOf"===n.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,i=r>n?r:n;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}(e.data,n.value)&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(ox(t=this._getOrReturnCtx(e,t),{code:ow.not_finite,message:n.message}),r.dirty()):s.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,o.toString(t))}gt(e,t){return this.setLimit("min",e,!1,o.toString(t))}lte(e,t){return this.setLimit("max",e,!0,o.toString(t))}lt(e,t){return this.setLimit("max",e,!1,o.toString(t))}setLimit(e,t,r,n){return new o3({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:o.toString(n)}]})}_addCheck(e){return new o3({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:o.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:o.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:o.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:o.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:o.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:o.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:o.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:o.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:o.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}o3.create=e=>new o3({checks:[],typeName:u.ZodNumber,coerce:e?.coerce||!1,...oM(e)});class o5 extends oq{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==ov.bigint)return this._getInvalidInput(e);let r=new oT;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):s.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.bigint,received:t.parsedType}),oC}gte(e,t){return this.setLimit("min",e,!0,o.toString(t))}gt(e,t){return this.setLimit("min",e,!1,o.toString(t))}lte(e,t){return this.setLimit("max",e,!0,o.toString(t))}lt(e,t){return this.setLimit("max",e,!1,o.toString(t))}setLimit(e,t,r,n){return new o5({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:o.toString(n)}]})}_addCheck(e){return new o5({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:o.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:o.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:o.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:o.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:o.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}o5.create=e=>new o5({checks:[],typeName:u.ZodBigInt,coerce:e?.coerce??!1,...oM(e)});class o6 extends oq{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==ov.boolean){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.boolean,received:t.parsedType}),oC}return oO(e.data)}}o6.create=e=>new o6({typeName:u.ZodBoolean,coerce:e?.coerce||!1,...oM(e)});class o9 extends oq{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==ov.date){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.date,received:t.parsedType}),oC}if(Number.isNaN(e.data.getTime()))return ox(this._getOrReturnCtx(e),{code:ow.invalid_date}),oC;let r=new oT;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),r.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(ox(t=this._getOrReturnCtx(e,t),{code:ow.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),r.dirty()):s.assertNever(n);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new o9({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:o.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:o.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}o9.create=e=>new o9({checks:[],coerce:e?.coerce||!1,typeName:u.ZodDate,...oM(e)});class o8 extends oq{_parse(e){if(this._getType(e)!==ov.symbol){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.symbol,received:t.parsedType}),oC}return oO(e.data)}}o8.create=e=>new o8({typeName:u.ZodSymbol,...oM(e)});class o7 extends oq{_parse(e){if(this._getType(e)!==ov.undefined){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.undefined,received:t.parsedType}),oC}return oO(e.data)}}o7.create=e=>new o7({typeName:u.ZodUndefined,...oM(e)});class le extends oq{_parse(e){if(this._getType(e)!==ov.null){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.null,received:t.parsedType}),oC}return oO(e.data)}}le.create=e=>new le({typeName:u.ZodNull,...oM(e)});class lt extends oq{constructor(){super(...arguments),this._any=!0}_parse(e){return oO(e.data)}}lt.create=e=>new lt({typeName:u.ZodAny,...oM(e)});class lr extends oq{constructor(){super(...arguments),this._unknown=!0}_parse(e){return oO(e.data)}}lr.create=e=>new lr({typeName:u.ZodUnknown,...oM(e)});class ln extends oq{_parse(e){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.never,received:t.parsedType}),oC}}ln.create=e=>new ln({typeName:u.ZodNever,...oM(e)});class li extends oq{_parse(e){if(this._getType(e)!==ov.undefined){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.void,received:t.parsedType}),oC}return oO(e.data)}}li.create=e=>new li({typeName:u.ZodVoid,...oM(e)});class ls extends oq{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==ov.array)return ox(t,{code:ow.invalid_type,expected:ov.array,received:t.parsedType}),oC;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,i=t.data.length<n.exactLength.value;(e||i)&&(ox(t,{code:e?ow.too_big:ow.too_small,minimum:i?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(ox(t,{code:ow.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(ox(t,{code:ow.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>n.type._parseAsync(new oj(t,e,t.path,r)))).then(e=>oT.mergeArray(r,e));let i=[...t.data].map((e,r)=>n.type._parseSync(new oj(t,e,t.path,r)));return oT.mergeArray(r,i)}get element(){return this._def.type}min(e,t){return new ls({...this._def,minLength:{value:e,message:o.toString(t)}})}max(e,t){return new ls({...this._def,maxLength:{value:e,message:o.toString(t)}})}length(e,t){return new ls({...this._def,exactLength:{value:e,message:o.toString(t)}})}nonempty(e){return this.min(1,e)}}ls.create=(e,t)=>new ls({type:e,minLength:null,maxLength:null,exactLength:null,typeName:u.ZodArray,...oM(t)});class la extends oq{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==ov.object){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.object,received:t.parsedType}),oC}let{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),s=[];if(!(this._def.catchall instanceof ln&&"strip"===this._def.unknownKeys))for(let e in r.data)i.includes(e)||s.push(e);let a=[];for(let e of i){let t=n[e],i=r.data[e];a.push({key:{status:"valid",value:e},value:t._parse(new oj(r,i,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof ln){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of s)a.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)s.length>0&&(ox(r,{code:ow.unrecognized_keys,keys:s}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of s){let n=r.data[t];a.push({key:{status:"valid",value:t},value:e._parse(new oj(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of a){let r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>oT.mergeObjectSync(t,e)):oT.mergeObjectSync(t,a)}get shape(){return this._def.shape()}strict(e){return o.errToObj,new la({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let n=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:o.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new la({...this._def,unknownKeys:"strip"})}passthrough(){return new la({...this._def,unknownKeys:"passthrough"})}extend(e){return new la({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new la({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:u.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new la({...this._def,catchall:e})}pick(e){let t={};for(let r of s.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new la({...this._def,shape:()=>t})}omit(e){let t={};for(let r of s.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new la({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof la){let r={};for(let n in t.shape){let i=t.shape[n];r[n]=lk.create(e(i))}return new la({...t._def,shape:()=>r})}if(t instanceof ls)return new ls({...t._def,type:e(t.element)});if(t instanceof lk)return lk.create(e(t.unwrap()));if(t instanceof lS)return lS.create(e(t.unwrap()));if(t instanceof ld)return ld.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of s.objectKeys(this.shape)){let n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new la({...this._def,shape:()=>t})}required(e){let t={};for(let r of s.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof lk;)e=e._def.innerType;t[r]=e}return new la({...this._def,shape:()=>t})}keyof(){return l_(s.objectKeys(this.shape))}}la.create=(e,t)=>new la({shape:()=>e,unknownKeys:"strip",catchall:ln.create(),typeName:u.ZodObject,...oM(t)}),la.strictCreate=(e,t)=>new la({shape:()=>e,unknownKeys:"strict",catchall:ln.create(),typeName:u.ZodObject,...oM(t)}),la.lazycreate=(e,t)=>new la({shape:e,unknownKeys:"strip",catchall:ln.create(),typeName:u.ZodObject,...oM(t)});class lo extends oq{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new oE(e.ctx.common.issues));return ox(t,{code:ow.invalid_union,unionErrors:r}),oC});{let e,n=[];for(let i of r){let r={...t,common:{...t.common,issues:[]},parent:null},s=i._parseSync({data:t.data,path:t.path,parent:r});if("valid"===s.status)return s;"dirty"!==s.status||e||(e={result:s,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=n.map(e=>new oE(e));return ox(t,{code:ow.invalid_union,unionErrors:i}),oC}}get options(){return this._def.options}}lo.create=(e,t)=>new lo({options:e,typeName:u.ZodUnion,...oM(t)});let ll=e=>{if(e instanceof lg)return ll(e.schema);if(e instanceof lE)return ll(e.innerType());if(e instanceof ly)return[e.value];if(e instanceof lv)return e.options;if(e instanceof lb)return s.objectValues(e.enum);else if(e instanceof lx)return ll(e._def.innerType);else if(e instanceof o7)return[void 0];else if(e instanceof le)return[null];else if(e instanceof lk)return[void 0,...ll(e.unwrap())];else if(e instanceof lS)return[null,...ll(e.unwrap())];else if(e instanceof lR)return ll(e.unwrap());else if(e instanceof lP)return ll(e.unwrap());else if(e instanceof lT)return ll(e._def.innerType);else return[]};class lc extends oq{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==ov.object)return ox(t,{code:ow.invalid_type,expected:ov.object,received:t.parsedType}),oC;let r=this.discriminator,n=t.data[r],i=this.optionsMap.get(n);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(ox(t,{code:ow.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),oC)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let n=new Map;for(let r of t){let t=ll(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(n.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);n.set(i,r)}}return new lc({typeName:u.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...oM(r)})}}class lu extends oq{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if(oP(e)||oP(n))return oC;let i=function e(t,r){let n=ob(t),i=ob(r);if(t===r)return{valid:!0,data:t};if(n===ov.object&&i===ov.object){let n=s.objectKeys(r),i=s.objectKeys(t).filter(e=>-1!==n.indexOf(e)),a={...t,...r};for(let n of i){let i=e(t[n],r[n]);if(!i.valid)return{valid:!1};a[n]=i.data}return{valid:!0,data:a}}if(n===ov.array&&i===ov.array){if(t.length!==r.length)return{valid:!1};let n=[];for(let i=0;i<t.length;i++){let s=e(t[i],r[i]);if(!s.valid)return{valid:!1};n.push(s.data)}return{valid:!0,data:n}}if(n===ov.date&&i===ov.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,n.value);return i.valid?((oI(e)||oI(n))&&t.dirty(),{status:t.value,value:i.data}):(ox(r,{code:ow.invalid_intersection_types}),oC)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}lu.create=(e,t,r)=>new lu({left:e,right:t,typeName:u.ZodIntersection,...oM(r)});class ld extends oq{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ov.array)return ox(r,{code:ow.invalid_type,expected:ov.array,received:r.parsedType}),oC;if(r.data.length<this._def.items.length)return ox(r,{code:ow.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),oC;!this._def.rest&&r.data.length>this._def.items.length&&(ox(r,{code:ow.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...r.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new oj(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(n).then(e=>oT.mergeArray(t,e)):oT.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new ld({...this._def,rest:e})}}ld.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ld({items:e,typeName:u.ZodTuple,rest:null,...oM(t)})};class lh extends oq{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ov.object)return ox(r,{code:ow.invalid_type,expected:ov.object,received:r.parsedType}),oC;let n=[],i=this._def.keyType,s=this._def.valueType;for(let e in r.data)n.push({key:i._parse(new oj(r,e,r.path,e)),value:s._parse(new oj(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?oT.mergeObjectAsync(t,n):oT.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return new lh(t instanceof oq?{keyType:e,valueType:t,typeName:u.ZodRecord,...oM(r)}:{keyType:o4.create(),valueType:e,typeName:u.ZodRecord,...oM(t)})}}class lp extends oq{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ov.map)return ox(r,{code:ow.invalid_type,expected:ov.map,received:r.parsedType}),oC;let n=this._def.keyType,i=this._def.valueType,s=[...r.data.entries()].map(([e,t],s)=>({key:n._parse(new oj(r,e,r.path,[s,"key"])),value:i._parse(new oj(r,t,r.path,[s,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of s){let n=await r.key,i=await r.value;if("aborted"===n.status||"aborted"===i.status)return oC;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of s){let n=r.key,i=r.value;if("aborted"===n.status||"aborted"===i.status)return oC;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}}}}lp.create=(e,t,r)=>new lp({valueType:t,keyType:e,typeName:u.ZodMap,...oM(r)});class lf extends oq{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ov.set)return ox(r,{code:ow.invalid_type,expected:ov.set,received:r.parsedType}),oC;let n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&(ox(r,{code:ow.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&(ox(r,{code:ow.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let i=this._def.valueType;function s(e){let r=new Set;for(let n of e){if("aborted"===n.status)return oC;"dirty"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}let a=[...r.data.values()].map((e,t)=>i._parse(new oj(r,e,r.path,t)));return r.common.async?Promise.all(a).then(e=>s(e)):s(a)}min(e,t){return new lf({...this._def,minSize:{value:e,message:o.toString(t)}})}max(e,t){return new lf({...this._def,maxSize:{value:e,message:o.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}lf.create=(e,t)=>new lf({valueType:e,minSize:null,maxSize:null,typeName:u.ZodSet,...oM(t)});class lm extends oq{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==ov.function)return ox(t,{code:ow.invalid_type,expected:ov.function,received:t.parsedType}),oC;function r(e,r){return oS({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ok,ok].filter(e=>!!e),issueData:{code:ow.invalid_arguments,argumentsError:r}})}function n(e,r){return oS({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ok,ok].filter(e=>!!e),issueData:{code:ow.invalid_return_type,returnTypeError:r}})}let i={errorMap:t.common.contextualErrorMap},s=t.data;if(this._def.returns instanceof lw){let e=this;return oO(async function(...t){let a=new oE([]),o=await e._def.args.parseAsync(t,i).catch(e=>{throw a.addIssue(r(t,e)),a}),l=await Reflect.apply(s,this,o);return await e._def.returns._def.type.parseAsync(l,i).catch(e=>{throw a.addIssue(n(l,e)),a})})}{let e=this;return oO(function(...t){let a=e._def.args.safeParse(t,i);if(!a.success)throw new oE([r(t,a.error)]);let o=Reflect.apply(s,this,a.data),l=e._def.returns.safeParse(o,i);if(!l.success)throw new oE([n(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new lm({...this._def,args:ld.create(e).rest(lr.create())})}returns(e){return new lm({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new lm({args:e||ld.create([]).rest(lr.create()),returns:t||lr.create(),typeName:u.ZodFunction,...oM(r)})}}class lg extends oq{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}lg.create=(e,t)=>new lg({getter:e,typeName:u.ZodLazy,...oM(t)});class ly extends oq{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return ox(t,{received:t.data,code:ow.invalid_literal,expected:this._def.value}),oC}return{status:"valid",value:e.data}}get value(){return this._def.value}}function l_(e,t){return new lv({values:e,typeName:u.ZodEnum,...oM(t)})}ly.create=(e,t)=>new ly({value:e,typeName:u.ZodLiteral,...oM(t)});class lv extends oq{constructor(){super(...arguments),l.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return ox(t,{expected:s.joinValues(r),received:t.parsedType,code:ow.invalid_type}),oC}if(oU(this,l,"f")||oL(this,l,new Set(this._def.values),"f"),!oU(this,l,"f").has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return ox(t,{received:t.data,code:ow.invalid_enum_value,options:r}),oC}return oO(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return lv.create(e,{...this._def,...t})}exclude(e,t=this._def){return lv.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}l=new WeakMap,lv.create=l_;class lb extends oq{constructor(){super(...arguments),c.set(this,void 0)}_parse(e){let t=s.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==ov.string&&r.parsedType!==ov.number){let e=s.objectValues(t);return ox(r,{expected:s.joinValues(e),received:r.parsedType,code:ow.invalid_type}),oC}if(oU(this,c,"f")||oL(this,c,new Set(s.getValidEnumValues(this._def.values)),"f"),!oU(this,c,"f").has(e.data)){let e=s.objectValues(t);return ox(r,{received:r.data,code:ow.invalid_enum_value,options:e}),oC}return oO(e.data)}get enum(){return this._def.values}}c=new WeakMap,lb.create=(e,t)=>new lb({values:e,typeName:u.ZodNativeEnum,...oM(t)});class lw extends oq{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==ov.promise&&!1===t.common.async?(ox(t,{code:ow.invalid_type,expected:ov.promise,received:t.parsedType}),oC):oO((t.parsedType===ov.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}lw.create=(e,t)=>new lw({type:e,typeName:u.ZodPromise,...oM(t)});class lE extends oq{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===u.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,i={addIssue:e=>{ox(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===n.type){let e=n.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return oC;let n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===n.status?oC:"dirty"===n.status||"dirty"===t.value?oR(n.value):n});{if("aborted"===t.value)return oC;let n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===n.status?oC:"dirty"===n.status||"dirty"===t.value?oR(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?oC:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===n.status?oC:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>oN(e)?Promise.resolve(n.transform(e.value,i)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!oN(e))return e;let s=n.transform(e.value,i);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}s.assertNever(n)}}lE.create=(e,t,r)=>new lE({schema:e,typeName:u.ZodEffects,effect:t,...oM(r)}),lE.createWithPreprocess=(e,t,r)=>new lE({schema:t,effect:{type:"preprocess",transform:e},typeName:u.ZodEffects,...oM(r)});class lk extends oq{_parse(e){return this._getType(e)===ov.undefined?oO(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}lk.create=(e,t)=>new lk({innerType:e,typeName:u.ZodOptional,...oM(t)});class lS extends oq{_parse(e){return this._getType(e)===ov.null?oO(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}lS.create=(e,t)=>new lS({innerType:e,typeName:u.ZodNullable,...oM(t)});class lx extends oq{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===ov.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}lx.create=(e,t)=>new lx({innerType:e,typeName:u.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...oM(t)});class lT extends oq{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return oA(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new oE(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new oE(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}lT.create=(e,t)=>new lT({innerType:e,typeName:u.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...oM(t)});class lC extends oq{_parse(e){if(this._getType(e)!==ov.nan){let t=this._getOrReturnCtx(e);return ox(t,{code:ow.invalid_type,expected:ov.nan,received:t.parsedType}),oC}return{status:"valid",value:e.data}}}lC.create=e=>new lC({typeName:u.ZodNaN,...oM(e)}),Symbol("zod_brand");class lR extends oq{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class lO extends oq{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?oC:"dirty"===e.status?(t.dirty(),oR(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?oC:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new lO({in:e,out:t,typeName:u.ZodPipeline})}}class lP extends oq{_parse(e){let t=this._def.innerType._parse(e),r=e=>(oN(e)&&(e.value=Object.freeze(e.value)),e);return oA(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}lP.create=(e,t)=>new lP({innerType:e,typeName:u.ZodReadonly,...oM(t)}),la.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(u||(u={}));let lI=o4.create;o3.create,lC.create,o5.create,o6.create,o9.create,o8.create,o7.create,le.create,lt.create,lr.create,ln.create,li.create,ls.create,la.create,la.strictCreate;let lN=lo.create;lc.create,lu.create,ld.create,lh.create,lp.create,lf.create,lm.create,lg.create,ly.create;let lA=lv.create;lb.create,lw.create,lE.create,lk.create,lS.create,lE.createWithPreprocess,lO.create;let lU=function(e=oh){return async()=>{let t=function({contentSecurityPolicy:e=oc.contentSecurityPolicy,crossOriginEmbedderPolicy:t=oc.crossOriginEmbedderPolicy,crossOriginOpenerPolicy:r=oc.crossOriginOpenerPolicy,crossOriginResourcePolicy:n=oc.crossOriginResourcePolicy,originAgentCluster:i=oc.originAgentCluster,referrerPolicy:s=oc.referrerPolicy,strictTransportSecurity:a=oc.strictTransportSecurity,xContentTypeOptions:o=oc.xContentTypeOptions,xDnsPrefetchControl:l=oc.xDnsPrefetchControl,xDownloadOptions:c=oc.xDownloadOptions,xFrameOptions:u=oc.xFrameOptions,xPermittedCrossDomainPolicies:d=oc.xPermittedCrossDomainPolicies,xXssProtection:h=oc.xXssProtection}=oc){!0===e&&(e=oc.contentSecurityPolicy),!0===t&&(t=oc.crossOriginEmbedderPolicy),!0===r&&(r=oc.crossOriginOpenerPolicy),!0===n&&(n=oc.crossOriginResourcePolicy),!0===s&&(s=oc.referrerPolicy),!0===a&&(a=oc.strictTransportSecurity),!0===l&&(l=oc.xDnsPrefetchControl),!0===u&&(u=oc.xFrameOptions),!0===d&&(d=oc.xPermittedCrossDomainPolicies);let p=new Headers;if(e){let[t,r]=function({directives:e=oc.contentSecurityPolicy.directives}=oc.contentSecurityPolicy){let t=[];for(let[r,n]of Object.entries(e)){let e=ot.get(r);if(!e)throw new od(`${r} is not a Content-Security-Policy directive`);if(!n)continue;let i=Array.isArray(n)?new Set(n.map(ou)):new Set;for(let t of i){if(ol.has(t))throw new od(`"${t}" must be quoted using single-quotes, e.g. "'${t}'"`);if("sandbox"===e&&!oo.has(t))throw new od("invalid sandbox value in Content-Security-Policy")}let s=Array.from(i),a=`${e} ${s.join(" ")}`.trim(),o=`${a};`;t.push(o)}return["content-security-policy",t.join(" ")]}(e);p.set(t,r)}if(t){let[e,r]=function({policy:e=oc.crossOriginEmbedderPolicy.policy}=oc.crossOriginEmbedderPolicy){if(or.has(e))return["cross-origin-embedder-policy",e];throw new od("invalid value for Cross-Origin-Embedder-Policy")}(t);p.set(e,r)}if(r){let[e,t]=function({policy:e=oc.crossOriginOpenerPolicy.policy}=oc.crossOriginOpenerPolicy){if(on.has(e))return["cross-origin-opener-policy",e];throw new od("invalid value for Cross-Origin-Opener-Policy")}(r);p.set(e,t)}if(n){let[e,t]=function({policy:e=oc.crossOriginResourcePolicy.policy}=oc.crossOriginResourcePolicy){if(oi.has(e))return["cross-origin-resource-policy",e];throw new od("invalid value for Cross-Origin-Resource-Policy")}(n);p.set(e,t)}if(i){let[e,t]=["origin-agent-cluster","?1"];p.set(e,t)}if(s){let[e,t]=function({policy:e=oc.referrerPolicy.policy}=oc.referrerPolicy){if(Array.isArray(e))if(e.length>0){let t=new Set;for(let r of e)if(os.has(r))t.add(r);else throw new od("invalid value for Referrer-Policy");return["referrer-policy",Array.from(t).join(",")]}else throw new od("must provide at least one policy for Referrer-Policy");throw new od("must provide array for Referrer-Policy")}(s);p.set(e,t)}if(a){let[e,t]=function({maxAge:e=oc.strictTransportSecurity.maxAge,includeSubDomains:t=oc.strictTransportSecurity.includeSubDomains,preload:r=oc.strictTransportSecurity.preload}=oc.strictTransportSecurity){if(e>=0&&Number.isFinite(e))e=Math.floor(e);else throw new od("must provide a finite, positive integer for the maxAge of Strict-Transport-Security");let n=[`max-age=${e}`];return t&&n.push("includeSubDomains"),r&&n.push("preload"),["strict-transport-security",n.join("; ")]}(a);p.set(e,t)}if(o){let[e,t]=["x-content-type-options","nosniff"];p.set(e,t)}if(l){let[e,t]=function({allow:e=oc.xDnsPrefetchControl.allow}=oc.xDnsPrefetchControl){return["x-dns-prefetch-control",e?"on":"off"]}(l);p.set(e,t)}if(c){let[e,t]=["x-download-options","noopen"];p.set(e,t)}if(u){let[e,t]=function({action:e=oc.xFrameOptions.action}=oc.xFrameOptions){if("string"==typeof e){let t=e.toUpperCase();if("SAMEORIGIN"===t||"DENY"===t)return["x-frame-options",t]}throw new od("invalid value for X-Frame-Options")}(u);p.set(e,t)}if(d){let[e,t]=function({permittedPolicies:e=oc.xPermittedCrossDomainPolicies.permittedPolicies}=oc.xPermittedCrossDomainPolicies){if(oa.has(e))return["x-permitted-cross-domain-policies",e];throw new od("invalid value for X-Permitted-Cross-Domain-Policies")}(d);p.set(e,t)}if(h){let[e,t]=["x-xss-protection","0"];p.set(e,t)}return p}(e);return t.set("x-middleware-next","1"),new Response(null,{headers:t})}}(o_({extends:[o_({server:{CLERK_SECRET_KEY:lI().startsWith("sk_"),CLERK_WEBHOOK_SECRET:lI().startsWith("whsec_").optional()},client:{NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:lI().startsWith("pk_"),NEXT_PUBLIC_CLERK_DOMAIN:lI().optional(),NEXT_PUBLIC_CLERK_SIGN_IN_URL:lI().startsWith("/"),NEXT_PUBLIC_CLERK_SIGN_UP_URL:lI().startsWith("/"),NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL:lI().startsWith("/"),NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL:lI().startsWith("/")},runtimeEnv:{CLERK_SECRET_KEY:process.env.CLERK_SECRET_KEY,CLERK_WEBHOOK_SECRET:process.env.CLERK_WEBHOOK_SECRET,NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:"pk_live_Y2xlcmsuY3ViZW50LmRldiQ",NEXT_PUBLIC_CLERK_DOMAIN:"cubent.dev",NEXT_PUBLIC_CLERK_SIGN_IN_URL:"/sign-in",NEXT_PUBLIC_CLERK_SIGN_UP_URL:"/sign-up",NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL:"/auth-success",NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL:"/terms"}}),o_({client:{NEXT_PUBLIC_POSTHOG_KEY:lI().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:lI().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:lI().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}}),o_({server:{LIVEBLOCKS_SECRET:lI().startsWith("sk_").optional()},runtimeEnv:{LIVEBLOCKS_SECRET:process.env.LIVEBLOCKS_SECRET}}),o_({extends:[oy({server:{VERCEL:lI().optional(),CI:lI().optional(),VERCEL_ENV:lA(["development","preview","production"]).optional(),VERCEL_URL:lI().optional(),VERCEL_PROJECT_PRODUCTION_URL:lI().optional(),VERCEL_BRANCH_URL:lI().optional(),VERCEL_REGION:lI().optional(),VERCEL_DEPLOYMENT_ID:lI().optional(),VERCEL_SKEW_PROTECTION_ENABLED:lI().optional(),VERCEL_AUTOMATION_BYPASS_SECRET:lI().optional(),VERCEL_GIT_PROVIDER:lI().optional(),VERCEL_GIT_REPO_SLUG:lI().optional(),VERCEL_GIT_REPO_OWNER:lI().optional(),VERCEL_GIT_REPO_ID:lI().optional(),VERCEL_GIT_COMMIT_REF:lI().optional(),VERCEL_GIT_COMMIT_SHA:lI().optional(),VERCEL_GIT_COMMIT_MESSAGE:lI().optional(),VERCEL_GIT_COMMIT_AUTHOR_LOGIN:lI().optional(),VERCEL_GIT_COMMIT_AUTHOR_NAME:lI().optional(),VERCEL_GIT_PREVIOUS_SHA:lI().optional(),VERCEL_GIT_PULL_REQUEST_ID:lI().optional()},runtimeEnv:process.env})],server:{ANALYZE:lI().optional(),NEXT_RUNTIME:lA(["nodejs","edge"]).optional()},client:{NEXT_PUBLIC_APP_URL:lI().url(),NEXT_PUBLIC_WEB_URL:lI().url(),NEXT_PUBLIC_API_URL:lI().url().optional(),NEXT_PUBLIC_DOCS_URL:lI().url().optional()},runtimeEnv:{ANALYZE:process.env.ANALYZE,NEXT_RUNTIME:"edge",NEXT_PUBLIC_APP_URL:"http://localhost:3000",NEXT_PUBLIC_WEB_URL:"http://localhost:3001",NEXT_PUBLIC_API_URL:process.env.NEXT_PUBLIC_API_URL,NEXT_PUBLIC_DOCS_URL:"https://cubentdev.mintlify.app"}}),o_({server:{DATABASE_URL:lI().url()},runtimeEnv:{DATABASE_URL:process.env.DATABASE_URL}}),o_({server:{RESEND_FROM:lI().email(),RESEND_TOKEN:lI().startsWith("re_")},runtimeEnv:{RESEND_FROM:process.env.RESEND_FROM,RESEND_TOKEN:process.env.RESEND_TOKEN}}),o_({server:{FLAGS_SECRET:lI().optional()},runtimeEnv:{FLAGS_SECRET:process.env.FLAGS_SECRET}}),o_({server:{KNOCK_SECRET_API_KEY:lI().optional()},client:{NEXT_PUBLIC_KNOCK_API_KEY:lI().optional(),NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:lI().optional()},runtimeEnv:{NEXT_PUBLIC_KNOCK_API_KEY:"",NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:"",KNOCK_SECRET_API_KEY:process.env.KNOCK_SECRET_API_KEY}}),o_({server:{BETTERSTACK_API_KEY:lI().optional(),BETTERSTACK_URL:lI().optional(),SENTRY_ORG:lI().optional(),SENTRY_PROJECT:lI().optional()},client:{NEXT_PUBLIC_SENTRY_DSN:lI().url().optional()},runtimeEnv:{BETTERSTACK_API_KEY:process.env.BETTERSTACK_API_KEY,BETTERSTACK_URL:process.env.BETTERSTACK_URL,SENTRY_ORG:process.env.SENTRY_ORG,SENTRY_PROJECT:process.env.SENTRY_PROJECT,NEXT_PUBLIC_SENTRY_DSN:process.env.NEXT_PUBLIC_SENTRY_DSN}}),o_({server:{ARCJET_KEY:lI().startsWith("ajkey_").optional()},runtimeEnv:{ARCJET_KEY:process.env.ARCJET_KEY}}),o_({server:{SVIX_TOKEN:lN([lI().startsWith("sk_"),lI().startsWith("testsk_")]).optional()},runtimeEnv:{SVIX_TOKEN:process.env.SVIX_TOKEN}})],server:{CRON_SECRET:lI().optional()},client:{},runtimeEnv:{CRON_SECRET:process.env.CRON_SECRET}}).FLAGS_SECRET?om:of),lL=((...e)=>{let[t,r]=a2(e),[n,i]=a4(e);return sh.run(sd,()=>{let e=iX("clerkMiddleware",e=>async(t,r)=>{var s,a;let o="function"==typeof i?await i(t):i,l=await aK(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value}),c=function(e,t){return e||t(),e}(o.publishableKey||i2||(null==l?void 0:l.publishableKey),()=>aO.throwMissingPublishableKeyError()),u=function(e,t){return e||t(),e}(o.secretKey||i1||(null==l?void 0:l.secretKey),()=>aO.throwMissingSecretKeyError()),d={publishableKey:c,secretKey:u,signInUrl:o.signInUrl||i8,signUpUrl:o.signUpUrl||"/sign-up",...o};sd.set("requestData",d);let h=await aM();d.debug&&e.enable();let p=ic(t);e.debug("options",d),e.debug("url",()=>p.toJSON());let f=t.headers.get(rx.Headers.Authorization);f&&f.startsWith("Basic ")&&e.debug("Basic Auth detected");let m=t.headers.get(rx.Headers.ContentSecurityPolicy);m&&e.debug("Content-Security-Policy detected",()=>({value:m}));let g=await h.authenticateRequest(p,a6(p,d));if(e.debug("requestState",()=>({status:g.status,headers:JSON.stringify(Object.fromEntries(g.headers)),reason:g.reason})),g.headers.get(rx.Headers.Location))return new Response(null,{status:307,headers:g.headers});if(g.status===ie.Handshake)throw Error("Clerk: handshake status without redirect");let y=g.toAuth();e.debug("auth",()=>({auth:y,debug:y.debug()}));let _=a9(p),v=a8(p),b=await a7(p,y,_),w=e=>Promise.resolve(Object.assign(g.toAuth(e),{redirectToSignIn:_,redirectToSignUp:v}));w.protect=b;let E=X.next();try{E=await sh.run(sd,async()=>null==n?void 0:n(w,t,r))||E}catch(e){E=oe(e,p,t,g)}if(d.contentSecurityPolicy){let{headers:t}=function(e,t){var r;let n=[],i=t.strict?function(){let e=new Uint8Array(16);return crypto.getRandomValues(e),btoa(Array.from(e,e=>String.fromCharCode(e)).join(""))}():void 0,s=function(e,t,r,n){let i=Object.entries(aq.DEFAULT_DIRECTIVES).reduce((e,[t,r])=>(e[t]=new Set(r),e),{});if(i["connect-src"].add(t),e&&(i["script-src"].delete("http:"),i["script-src"].delete("https:"),i["script-src"].add("'strict-dynamic'"),n&&i["script-src"].add(`'nonce-${n}'`)),r){let e=new Map;Object.entries(r).forEach(([t,r])=>{let n=Array.isArray(r)?r:[r];aq.DEFAULT_DIRECTIVES[t]?function(e,t,r){if(r.includes("'none'")||r.includes("none")){e[t]=new Set(["'none'"]);return}let n=new Set;e[t].forEach(e=>{n.add(aq.formatValue(e))}),r.forEach(e=>{n.add(aq.formatValue(e))}),e[t]=n}(i,t,n):function(e,t,r){if(r.includes("'none'")||r.includes("none"))return e.set(t,new Set(["'none'"]));let n=new Set;r.forEach(e=>{let t=aq.formatValue(e);n.add(t)}),e.set(t,n)}(e,t,n)}),e.forEach((e,t)=>{i[t]=e})}return Object.entries(i).sort(([e],[t])=>e.localeCompare(t)).map(([e,t])=>{let r=Array.from(t).map(e=>({raw:e,formatted:aq.formatValue(e)}));return`${e} ${r.map(e=>e.formatted).join(" ")}`}).join("; ")}(null!=(r=t.strict)&&r,e,t.directives,i);return t.reportTo&&(s+="; report-to csp-endpoint",n.push([rx.Headers.ReportingEndpoints,`csp-endpoint="${t.reportTo}"`])),t.reportOnly?n.push([rx.Headers.ContentSecurityPolicyReportOnly,s]):n.push([rx.Headers.ContentSecurityPolicy,s]),i&&n.push([rx.Headers.Nonce,i]),{headers:n}}((null!=(a=null==(s=tS(c))?void 0:s.frontendApi)?a:"").replace("$",""),d.contentSecurityPolicy);t.forEach(([e,t])=>{iB(E,e,t)}),e.debug("Clerk generated CSP",()=>({headers:t}))}if(g.headers&&g.headers.forEach((t,r)=>{r===rx.Headers.ContentSecurityPolicy&&e.debug("Content-Security-Policy detected",()=>({value:t})),E.headers.append(r,t)}),i$(E))return e.debug("handlerResult is redirect"),iF(p,E,d);d.debug&&aN(E,p,{[rx.Headers.EnableDebug]:"true"});let k=u===(null==l?void 0:l.secretKey)?{publishableKey:null==l?void 0:l.publishableKey,secretKey:null==l?void 0:l.secretKey}:{};return!function(e,t,r,n,i){let s,{reason:a,message:o,status:l,token:c}=r;if(t||(t=X.next()),t.headers.get(iq.Headers.NextRedirect))return;"1"===t.headers.get(iq.Headers.NextResume)&&(t.headers.delete(iq.Headers.NextResume),s=new URL(e.url));let u=t.headers.get(iq.Headers.NextRewrite);if(u){let t=new URL(e.url);if((s=new URL(u)).origin!==t.origin)return}if(s){let r=function(e,t){var r;let n=e=>!e||!Object.values(e).some(e=>void 0!==e);if(n(e)&&n(t))return;if(e.secretKey&&!i4)return void sf.warnOnce("Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys");let i=tP()?i4||(r=()=>aO.throwMissingSecretKeyError(),i1||r(),i1):i4||i1||aL;return aE.encrypt(JSON.stringify({...t,...e}),i).toString()}(n,i);aN(t,e,{[rx.Headers.AuthStatus]:l,[rx.Headers.AuthToken]:c||"",[rx.Headers.AuthSignature]:c?ax(c,(null==n?void 0:n.secretKey)||i1||i.secretKey||"").toString():"",[rx.Headers.AuthMessage]:o||"",[rx.Headers.AuthReason]:a||"",[rx.Headers.ClerkUrl]:e.clerkUrl.toString(),...r?{[rx.Headers.ClerkRequestData]:r}:{}}),t.headers.set(iq.Headers.NextRewrite,s.href)}}(p,E,g,o,k),E}),s=async(t,r)=>{if(a3(t))return a5(t);let n="function"==typeof i?await i(t):i,s=await aK(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value});if(!(n.publishableKey||i2||(null==s?void 0:s.publishableKey))){let e=X.next();return aN(e,t,{[rx.Headers.AuthStatus]:"signed-out"}),e}return e(t,r)},a=async(t,r)=>sr?s(t,r):e(t,r);return t&&r?a(t,r):a})})(()=>lU()),lj={matcher:["/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)","/(api|trpc)(.*)"]};r(965);let lD={...d},lM=lD.middleware||lD.default,lq="/middleware";if("function"!=typeof lM)throw Object.defineProperty(Error(`The Middleware "${lq}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function l$(e){return te({...e,page:lq,handler:async(...e)=>{try{return await lM(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await m(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},521:e=>{"use strict";e.exports=require("node:async_hooks")},600:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,n,s,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var o=new i(n,s||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,s=n.length,a=Array(s);i<s;i++)a[i]=n[i].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,c,u=this._events[o],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,s),!0;case 6:return u.fn.call(u.context,t,n,i,s,a),!0}for(c=1,l=Array(d-1);c<d;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var h,p=u.length;for(c=0;c<p;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),d){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,i);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];u[c].fn.apply(u[c].context,l)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||a(this,s);else{for(var l=0,c=[],u=o.length;l<u;l++)(o[l].fn!==t||i&&!o[l].once||n&&o[l].context!==n)&&c.push(o[l]);c.length?this._events[s]=1===c.length?c[0]:c:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let s=i/2|0,a=n+s;0>=r(e[a],t)?(n=++a,i-=s+1):i=s}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let s=(e,t,r)=>new Promise((s,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void s(e);let o=setTimeout(()=>{if("function"==typeof r){try{s(r())}catch(e){a(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),a(o)},t);n(e.then(s,a),()=>{clearTimeout(o)})});e.exports=s,e.exports.default=s,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),s=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,n,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let s=async()=>{this._pendingCount++,this._intervalCount++;try{let s=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(a)});n(await s)}catch(e){i(e)}this._next()};this._queue.enqueue(s,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=o})(),e.exports=i})()},666:(e,t,r)=>{"use strict";r.d(t,{iC:()=>i}),r(476);var n=r(149);function i(){let e=n.Z.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},679:(e,t,r)=>{"use strict";r.d(t,{t3:()=>l,I3:()=>d,Ui:()=>c,xI:()=>a,Pk:()=>o});var n=r(449),i=r(830);r(476),r(86),r(72),r(727);let s="function"==typeof n.unstable_postpone;function a(e,t,r){let n=Object.defineProperty(new i.F(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function o(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function l(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function c(e,t,r){(function(){if(!s)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(u(e,t))}function u(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&h(e.message)}function h(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===h(u("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function p(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},683:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return s},wrapRequestHandler:function(){return a}});let n=r(819),i=r(302);function s(){return(0,i.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},727:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===i}r.d(t,{T:()=>n,W:()=>o});let i="HANGING_PROMISE_REJECTION";class s extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=i}}let a=new WeakMap;function o(e,t){if(e.aborted)return Promise.reject(new s(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new s(t)),o=a.get(e);if(o)o.push(i);else{let t=[i];a.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(l),r}}function l(){}},737:(e,t,r)=>{"use strict";r.d(t,{RM:()=>s,s8:()=>i});let n=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),i="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}},782:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...n]=o(e),{domain:i,expires:s,httponly:a,maxage:l,path:d,samesite:h,secure:p,partitioned:f,priority:m}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var g,y,_={name:t,value:decodeURIComponent(r),domain:i,...s&&{expires:new Date(s)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:d,...h&&{sameSite:c.includes(g=(g=h).toLowerCase())?g:void 0},...p&&{secure:!0},...m&&{priority:u.includes(y=(y=m).toLowerCase())?y:void 0},...f&&{partitioned:!0}};let e={};for(let t in _)_[t]&&(e[t]=_[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>d,ResponseCookies:()=>h,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,o)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let l of n(s))i.call(e,l)||l===a||t(e,l,{get:()=>s[l],enumerable:!(o=r(s,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),s);var c=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},819:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return s}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function s(e,t,r){let s=i(e,t);return s?n.run(s,r):r()}function a(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},830:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,h:()=>s});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},843:(e,t)=>{"use strict";t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||s,c=0;do{let t=e.indexOf("=",c);if(-1===t)break;let r=e.indexOf(";",c),s=-1===r?o:r;if(t>s){c=e.lastIndexOf(";",t-1)+1;continue}let u=n(e,c,t),d=i(e,t,u),h=e.slice(u,d);if(void 0===a[h]){let r=n(e,t+1,s),o=i(e,s,r),c=l(e.slice(r,o));a[h]=c}c=s+1}while(c<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function s(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},866:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,s,a=new WeakMap)=>{if(s={deep:!1,target:{},...s},a.has(e))return a.get(e);a.set(e,s.target);let{target:o}=s;delete s.target;let l=e=>e.map(e=>n(e)?i(e,t,s,a):e);if(Array.isArray(e))return l(e);for(let[c,u]of Object.entries(e)){let d=t(c,u,e);if(d===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=d;"__proto__"!==h&&(s.deep&&f&&n(p)&&(p=Array.isArray(p)?l(p):i(p,t,s,a)),o[h]=p)}return o};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,n)},e.exports.mapObjectSkip=r},878:(e,t,r)=>{"use strict";r.d(t,{X:()=>function e(t){if((0,s.p)(t)||"object"==typeof t&&null!==t&&"digest"in t&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t.digest||(0,o.h)(t)||(0,a.I3)(t)||"object"==typeof t&&null!==t&&t.$$typeof===i||(0,n.T)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}});var n=r(727);let i=Symbol.for("react.postpone");var s=r(965),a=r(679),o=r(830)},902:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(21).xl)()},906:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},917:(e,t,r)=>{"use strict";r.d(t,{nJ:()=>i});var n=r(51);function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,s=t.slice(2,-2).join(";"),a=Number(t.at(-2));return"NEXT_REDIRECT"===r&&("replace"===i||"push"===i)&&"string"==typeof s&&!isNaN(a)&&a in n.Q}},965:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(737),i=r(917);function s(e){return(0,i.nJ)(e)||(0,n.RM)(e)}}},e=>{var t=e(e.s=486);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map