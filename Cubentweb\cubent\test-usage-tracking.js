/**
 * Test script to debug usage tracking issues
 * This simulates how the extension communicates with the website
 */

const API_BASE_URL = 'http://localhost:3000';

// Test 1: Check if usage stats endpoint is accessible
async function testUsageStatsEndpoint() {
  console.log('🧪 Testing usage stats endpoint...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/extension/usage/stats`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Status Text: ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('✅ Endpoint is working (401 expected without auth)');
      return true;
    } else {
      const data = await response.text();
      console.log('Response:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing endpoint:', error);
    return false;
  }
}

// Test 2: Test with a mock Bearer token
async function testWithMockToken() {
  console.log('🧪 Testing with mock Bearer token...');
  
  const mockToken = 'test-token-123';
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/extension/usage/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${mockToken}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`Status: ${response.status}`);
    const data = await response.text();
    console.log('Response:', data);
    
    return response.status;
  } catch (error) {
    console.error('❌ Error testing with token:', error);
    return null;
  }
}

// Test 3: Test usage tracking endpoint
async function testUsageTracking() {
  console.log('🧪 Testing usage tracking endpoint...');
  
  const mockToken = 'test-token-123';
  const usageData = {
    modelId: 'test-model',
    provider: 'test-provider',
    configName: 'test-config',
    cubentUnits: 1.5,
    messageCount: 1,
    timestamp: Date.now(),
  };
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/extension/usage`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${mockToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(usageData),
    });
    
    console.log(`Status: ${response.status}`);
    const data = await response.text();
    console.log('Response:', data);
    
    return response.status;
  } catch (error) {
    console.error('❌ Error testing usage tracking:', error);
    return null;
  }
}

// Test 4: Test units tracking endpoint
async function testUnitsTracking() {
  console.log('🧪 Testing units tracking endpoint...');
  
  const mockToken = 'test-token-123';
  const usageData = {
    modelId: 'test-model',
    provider: 'test-provider',
    configName: 'test-config',
    cubentUnits: 1.5,
    messageCount: 1,
    timestamp: Date.now(),
  };
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/extension/units/track`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${mockToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(usageData),
    });
    
    console.log(`Status: ${response.status}`);
    const data = await response.text();
    console.log('Response:', data);
    
    return response.status;
  } catch (error) {
    console.error('❌ Error testing units tracking:', error);
    return null;
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting usage tracking tests...\n');
  
  const test1 = await testUsageStatsEndpoint();
  console.log('');
  
  const test2 = await testWithMockToken();
  console.log('');
  
  const test3 = await testUsageTracking();
  console.log('');
  
  const test4 = await testUnitsTracking();
  console.log('');
  
  console.log('📊 Test Results Summary:');
  console.log(`- Usage stats endpoint accessible: ${test1 ? '✅' : '❌'}`);
  console.log(`- Mock token test status: ${test2}`);
  console.log(`- Usage tracking test status: ${test3}`);
  console.log(`- Units tracking test status: ${test4}`);
  
  if (test2 === 401 && test3 === 401 && test4 === 401) {
    console.log('\n✅ All endpoints are working correctly (401 expected without valid auth)');
    console.log('🔍 Issue is likely with authentication token generation or validation');
  } else {
    console.log('\n❌ Some endpoints may have issues');
  }
}

// Run the tests
runTests().catch(console.error);
