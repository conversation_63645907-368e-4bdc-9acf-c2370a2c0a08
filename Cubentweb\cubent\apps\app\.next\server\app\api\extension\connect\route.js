(()=>{var e={};e.id=2082,e.ids=[2082],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},93786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>w,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>q,POST:()=>m});var i=r(26142),n=r(94327),a=r(34862),o=r(37838),u=r(1359),c=r(18815),p=r(26239),d=r(25),x=r(55511);let l=d.z.object({sessionId:d.z.string(),extensionVersion:d.z.string(),platform:d.z.string().optional()});async function m(e){try{let{userId:t}=await (0,o.j)(),r=await (0,u.N)();if(!t||!r)return p.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),{sessionId:i,extensionVersion:n,platform:a}=l.parse(s),d=await c.database.user.findUnique({where:{clerkId:t}});if(d||(d=await c.database.user.create({data:{clerkId:t,email:r.emailAddresses[0]?.emailAddress||"",name:`${r.firstName||""} ${r.lastName||""}`.trim()||null,picture:r.imageUrl}})),!d.termsAccepted)return p.NextResponse.json({error:"Terms not accepted",requiresTermsAcceptance:!0,redirectUrl:"/terms"},{status:403});let m=d.extensionApiKey;return m||(m=`cubent_${(0,x.randomBytes)(32).toString("hex")}`,await c.database.user.update({where:{id:d.id},data:{extensionApiKey:m}})),await c.database.extensionSession.upsert({where:{userId_sessionId:{userId:d.id,sessionId:i}},update:{isActive:!0,lastActiveAt:new Date},create:{userId:d.id,sessionId:i,isActive:!0,lastActiveAt:new Date}}),await c.database.user.update({where:{id:d.id},data:{lastExtensionSync:new Date}}),p.NextResponse.json({success:!0,message:"Extension connected successfully",apiKey:m,user:{id:d.id,name:d.name,email:d.email,subscriptionTier:d.subscriptionTier,subscriptionStatus:d.subscriptionStatus},settings:{extensionSettings:d.extensionSettings||{},preferences:d.preferences||{}}})}catch(e){return console.error("Extension connect error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}async function q(e){try{let{userId:t}=await (0,o.j)();if(!t)return p.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("sessionId"),i=await c.database.user.findUnique({where:{clerkId:t}});if(!i)return p.NextResponse.json({error:"User not found"},{status:404});return s?await c.database.extensionSession.updateMany({where:{userId:i.id,sessionId:s},data:{isActive:!1}}):await c.database.extensionSession.updateMany({where:{userId:i.id},data:{isActive:!1}}),p.NextResponse.json({success:!0,message:"Extension disconnected successfully"})}catch(e){return console.error("Extension disconnect error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}let w=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/connect/route",pathname:"/api/extension/connect",filename:"route",bundlePath:"app/api/extension/connect/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\connect\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:y}=w;function v(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,1359,864],()=>r(93786));module.exports=s})();