"use strict";exports.id=4841,exports.ids=[4841],exports.modules={3093:(e,t,r)=>{r.d(t,{LR:()=>s,_r:()=>a,u$:()=>i});function i(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var s=class e extends Error{constructor(t,{data:r,status:s,clerkTraceId:n,retryAfter:a}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=s,this.message=t,this.clerkTraceId=n,this.retryAfter=a,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(i):[]}(r)}},n=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function a({packageName:e,customMessages:t}){let r=e,i={...n,...t};function s(e,t){if(!t)return`${r}: ${e}`;let i=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();i=i.replace(`{{${r[1]}}}`,e)}return`${r}: ${i}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(s(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(s(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(s(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(s(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(s(i.MissingClerkProvider,e))},throw(e){throw Error(s(e))}}}r(85932)},3312:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return i},isBailoutToCSRError:function(){return s}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class i extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},3636:(e,t,r)=>{var i=r(76316);r.o(i,"RedirectType")&&r.d(t,{RedirectType:function(){return i.RedirectType}}),r.o(i,"notFound")&&r.d(t,{notFound:function(){return i.notFound}}),r.o(i,"redirect")&&r.d(t,{redirect:function(){return i.redirect}})},5844:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return n}});let i=r(64432),s=r(20508);function n(e){return(0,s.isRedirectError)(e)||(0,i.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20508:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return s},RedirectType:function(){return n},isRedirectError:function(){return a}});let i=r(84126),s="NEXT_REDIRECT";var n=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,a=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===s&&("replace"===n||"push"===n)&&"string"==typeof a&&!isNaN(o)&&o in i.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24290:(e,t,r)=>{r.d(t,{Fj:()=>i.Fj,b_:()=>i.b_});var i=r(26664);r(85932)},24651:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=r(31706).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25398:(e,t,r)=>{r.d(t,{RZ:()=>i.RZ,ky:()=>i.ky,mC:()=>i.mC,q5:()=>i.q5,qS:()=>i.qS});var i=r(61541);r(85932)},26664:(e,t,r)=>{r.d(t,{Fj:()=>n,MC:()=>s,b_:()=>i});var i=()=>!1,s=()=>!1,n=()=>{try{return!0}catch{}return!1}},27320:(e,t,r)=>{r.r(t),r.d(t,{snakeCase:()=>l});var i=function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}).apply(this,arguments)};Object.create;function s(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var n=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=i({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,i=t.stripRegexp,l=t.transform,u=t.delimiter,d=o(o(e,void 0===r?n:r,"$1\0$2"),void 0===i?a:i,"\0"),c=0,h=d.length;"\0"===d.charAt(c);)c++;for(;"\0"===d.charAt(h-1);)h--;return d.slice(c,h).split("\0").map(void 0===l?s:l).join(void 0===u?" ":u)}(e,i({delimiter:"."},r))}},28634:(e,t,r)=>{r.d(t,{io:()=>c,sb:()=>h,qS:()=>l.qS,ky:()=>l.ky,Ve:()=>l.mC,q5:()=>l.q5,L5:()=>o}),r(85932);var i={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},s=async e=>new Promise(t=>setTimeout(t,e)),n=(e,t)=>t?e*(1+Math.random()):e,a=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=n(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await s(r()),t++}},o=async(e,t={})=>{let r=0,{shouldRetry:o,initialDelay:l,maxDelayBetweenRetries:u,factor:d,retryImmediately:c,jitter:h}={...i,...t},p=a({initialDelay:l,maxDelayBetweenRetries:u,factor:d,jitter:h});for(;;)try{return await e()}catch(e){if(!o(e,++r))throw e;c&&1===r?await s(n(100,h)):await p()}},l=r(25398),u=r(26664),d=new Set,c=(e,t,r)=>{let i=(0,u.MC)()||(0,u.Fj)(),s=r??e;d.has(s)||i||(d.add(s),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},h=(0,r(3093)._r)({packageName:"@clerk/backend"}),{isDevOrStagingUrl:p}=(0,l.RZ)()},31706:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,n.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,s.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=r(14026),s=r(38131),n=r(3312),a=r(5844),o=r(3881),l=r(92395);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32298:(e,t)=>{t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||n,u=0;do{let t=e.indexOf("=",u);if(-1===t)break;let r=e.indexOf(";",u),n=-1===r?o:r;if(t>n){u=e.lastIndexOf(";",t-1)+1;continue}let d=i(e,u,t),c=s(e,t,d),h=e.slice(d,c);if(void 0===a[h]){let r=i(e,t+1,n),o=s(e,n,r),u=l(e.slice(r,o));a[h]=u}u=n+1}while(u<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function s(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function n(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},36870:(e,t,r)=>{r.d(t,{zz:()=>i.zz});var i=r(72391);r(85932)},37012:(e,t,r)=>{r.d(t,{I:()=>n});var i=r(24290),s=r(86072);let n=!r(48917).M&&(0,i.b_)()&&!s.ev},37563:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return s}});let i=""+r(64432).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function s(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38131:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return i}});let r=Symbol.for("react.postpone");function i(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},43226:(e,t,r)=>{r.d(t,{l3:()=>_,qf:()=>y,r0:()=>l,iU:()=>O,hJ:()=>f,nk:()=>E,Fh:()=>T,fA:()=>o,J0:()=>I});var i=r(83625),s=r(77598),n=r(98176);r(85932);var a=fetch.bind(globalThis),o={crypto:s.webcrypto,get fetch(){return a},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},l={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let i=e.length;for(;"="===e[i-1];)if(--i,!r.loose&&!((e.length-i)*t.bits&7))throw SyntaxError("Invalid padding");let s=new(r.out??Uint8Array)(i*t.bits/8|0),n=0,a=0,o=0;for(let r=0;r<i;++r){let i=t.codes[e[r]];if(void 0===i)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|i,(n+=t.bits)>=8&&(n-=8,s[o++]=255&a>>n)}if(n>=t.bits||255&a<<8-n)throw SyntaxError("Unexpected end of data");return s})(e,u,t),stringify:(e,t)=>(function(e,t,r={}){let{pad:i=!0}=r,s=(1<<t.bits)-1,n="",a=0,o=0;for(let r=0;r<e.length;++r)for(o=o<<8|255&e[r],a+=8;a>t.bits;)a-=t.bits,n+=t.chars[s&o>>a];if(a&&(n+=t.chars[s&o<<t.bits-a]),i)for(;n.length*t.bits&7;)n+="=";return n})(e,u,t)},u={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},d={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},c="RSASSA-PKCS1-v1_5",h={RS256:c,RS384:c,RS512:c},p=Object.keys(d);function f(e){let t=d[e],r=h[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${p.join(",")}.`);return{hash:{name:d[e]},name:h[e]}}var m=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),g=(e,t)=>{let r=[t].flat().filter(e=>!!e),s=[e].flat().filter(e=>!!e);if(r.length>0&&s.length>0){if("string"==typeof e){if(!r.includes(e))throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(m(e)&&!e.some(e=>r.includes(e)))throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},y=e=>{if(void 0!==e&&"JWT"!==e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},_=e=>{if(!p.includes(e))throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${p}.`})},k=e=>{if("string"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},b=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new i.zF({reason:i.jn.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},v=(e,t)=>{if("number"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()<=r.getTime()-t)throw new i.zF({reason:i.jn.TokenExpired,message:`JWT is expired. Expiry date: ${s.toUTCString()}, Current date: ${r.toUTCString()}.`})},w=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()>r.getTime()+t)throw new i.zF({reason:i.jn.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${s.toUTCString()}; Current date: ${r.toUTCString()};`})},S=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()>r.getTime()+t)throw new i.zF({reason:i.jn.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${s.toUTCString()}; Current date: ${r.toUTCString()};`})};function T(e,t,r){if("object"==typeof e)return o.crypto.subtle.importKey("jwk",e,t,!1,[r]);let i=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,n.y)(t),i=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)i[e]=r.charCodeAt(e);return i}(e),s="sign"===r?"pkcs8":"spki";return o.crypto.subtle.importKey(s,i,t,!1,[r])}async function E(e,t){let{header:r,signature:s,raw:n}=e,a=new TextEncoder().encode([n.header,n.payload].join(".")),l=f(r.alg);try{let e=await T(t,l,"verify");return{data:await o.crypto.subtle.verify(l.name,e,s,a)}}catch(e){return{errors:[new i.zF({reason:i.jn.TokenInvalidSignature,message:e?.message})]}}}function O(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new i.zF({reason:i.jn.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,s,n]=t,a=new TextDecoder,o=JSON.parse(a.decode(l.parse(r,{loose:!0}))),u=JSON.parse(a.decode(l.parse(s,{loose:!0})));return{data:{header:o,payload:u,signature:l.parse(n,{loose:!0}),raw:{header:r,payload:s,signature:n,text:e}}}}async function I(e,t){let{audience:r,authorizedParties:s,clockSkewInMs:n,key:a}=t,o=n||5e3,{data:l,errors:u}=O(e);if(u)return{errors:u};let{header:d,payload:c}=l;try{let{typ:e,alg:t}=d;y(e),_(t);let{azp:i,sub:n,aud:a,iat:l,exp:u,nbf:h}=c;k(n),g([a],[r]),b(i,s),v(u,o),w(h,o),S(l,o)}catch(e){return{errors:[e]}}let{data:h,errors:p}=await E(l,a);return p?{errors:[new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Error verifying JWT signature. ${p[0]}`})]}:h?{data:c}:{errors:[new i.zF({reason:i.jn.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},47633:e=>{e.exports={rE:"15.3.2"}},48917:(e,t,r)=>{r.d(t,{M:()=>s});var i=r(47633);let s=i.rE.startsWith("13.")||i.rE.startsWith("14.0")},54841:(e,t,r)=>{r.d(t,{ai:()=>g,at:()=>y,ot:()=>m});var i=r(68602);r(98668);var s=r(87749),n=r(3636);let a=(0,r(3093)._r)({packageName:"@clerk/nextjs"});var o=r(97296),l=r(37012);let u="__clerk_keys_";async function d(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function c(){let e=process.env.PWD;if(!e)return`${u}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await d(t);return`${u}${r}`}async function h(e){let t;if(!l.I)return;let r=await c();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}var p=r(92516);let f={secure:!1,httpOnly:!1,sameSite:"lax"};async function m(e){let{claimUrl:t,publishableKey:r,secretKey:i,returnUrl:a}=e,l=await (0,s.UL)(),u=new Request("https://placeholder.com",{headers:await (0,s.headers)()}),d=await h(e=>{var t;return null==(t=l.get(e))?void 0:t.value}),p=(null==d?void 0:d.publishableKey)===r,m=(null==d?void 0:d.secretKey)===i;if((!p||!m)&&(l.set(await c(),JSON.stringify({claimUrl:t,publishableKey:r,secretKey:i}),f),(0,o.Zd)(u)))return void(0,n.redirect)(`/clerk-sync-keyless?returnUrl=${a}`,n.RedirectType.replace)}async function g(){if(!l.I)return null;let e=await r.e(581).then(r.bind(r,30581)).then(e=>e.createOrReadKeyless()).catch(()=>null);if(!e)return a.throwMissingPublishableKeyError(),null;let{clerkDevelopmentCache:t,createKeylessModeMessage:i}=await r.e(8256).then(r.bind(r,18256));null==t||t.log({cacheKey:e.publishableKey,msg:i(e)});let{claimUrl:n,publishableKey:o,secretKey:u,apiKeysUrl:d}=e;return(await (0,s.UL)()).set(await c(),JSON.stringify({claimUrl:n,publishableKey:o,secretKey:u}),f),{claimUrl:n,publishableKey:o,apiKeysUrl:d}}async function y(){l.I&&await r.e(581).then(r.bind(r,30581)).then(e=>e.removeKeyless()).catch(()=>{})}(0,p.D)([g,y,m]),(0,i.A)(g,"7fa248ee4cee001992d543e3927d536ddea63c121c",null),(0,i.A)(y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f",null),(0,i.A)(m,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937",null)},59608:(e,t,r)=>{r.d(t,{TD:()=>i.TD,AA:()=>i.AA,tl:()=>i.tl,vH:()=>o,Z5:()=>i.Z5,wI:()=>i.wI});var i=r(96158),s=r(28634);r(43226),r(83625),r(85932);var n=(e,t,r,s)=>{if(""===e)return a(t.toString(),r?.toString());let n=new URL(e),o=r?new URL(r,n):void 0,l=new URL(t,n);return o&&l.searchParams.set("redirect_url",o.toString()),s&&n.hostname!==l.hostname&&l.searchParams.set(i.AA.QueryParameters.DevBrowser,s),l.toString()},a=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let i=new URL(t);r=new URL(e,i.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},o=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:i,signUpUrl:a,baseUrl:o,sessionStatus:l}=e,u=(0,s.q5)(t),d=u?.frontendApi,c=u?.instanceType==="development",h=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(d),p="pending"===l,f=(t,{returnBackUrl:i})=>r(n(o,`${t}/tasks`,i,c?e.devBrowserToken:null));return{redirectToSignUp:({returnBackUrl:t}={})=>{a||h||s.sb.throwMissingPublishableKeyError();let l=`${h}/sign-up`,u=a||function(e){if(!e)return;let t=new URL(e,o);return t.pathname=`${t.pathname}/create`,t.toString()}(i)||l;return p?f(u,{returnBackUrl:t}):r(n(o,u,t,c?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{i||h||s.sb.throwMissingPublishableKeyError();let a=`${h}/sign-in`,l=i||a;return p?f(l,{returnBackUrl:t}):r(n(o,l,t,c?e.devBrowserToken:null))}}}},61541:(e,t,r)=>{r.d(t,{RZ:()=>u,qS:()=>c,ky:()=>h,mC:()=>d,q5:()=>o});var i=r(98176),s=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,n=r(71298),a="pk_live_";function o(e,t={}){if(!(e=e||"")||!l(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!l(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(a)?"production":"development",s=(0,i.y)(e.split("_")[2]);return s=s.slice(0,-1),t.proxyUrl?s=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(s=`clerk.${t.domain}`),{instanceType:r,frontendApi:s}}function l(e=""){try{let t=e.startsWith(a)||e.startsWith("pk_test_"),r=(0,i.y)(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function u(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,i=e.get(r);return void 0===i&&(i=n.gE.some(e=>r.endsWith(e)),e.set(r,i)),i}}}function d(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function c(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return s(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var h=(e,t)=>`${e}_${t}`},64432:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return s},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return n}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},i=new Set(Object.values(r)),s="NEXT_HTTP_ERROR_FALLBACK";function n(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===s&&i.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69227:(e,t,r)=>{let i=r(84755),{snakeCase:s}=r(27320),n={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==n))throw Error("obj must be array of plain objects")}else if(e.constructor!==n)throw Error("obj must be an plain object");return i(e,function(e,r){var i,n,a,o,l;return[(i=t.exclude,n=e,i.some(function(e){return"string"==typeof e?e===n:e.test(n)}))?e:s(e,t.parsingOptions),r,(a=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(a,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},71298:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>l,Vc:()=>o,gE:()=>s,iM:()=>i,mG:()=>n,ub:()=>a});var i=[".lcl.dev",".lclstage.dev",".lclclerk.com"],s=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],n=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],a=[".accountsstage.dev"],o="https://api.lclclerk.com",l="https://api.clerkstage.dev",u="https://api.clerk.com"},72391:(e,t,r)=>{r.d(t,{zz:()=>s});var i=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let i={...r};for(let r of Object.keys(i)){let s=e(r.toString());s!==r&&(i[s]=i[r],delete i[r]),"object"==typeof i[s]&&(i[s]=t(i[s]))}return i};return t};function s(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}i(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),i(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},76316:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return s.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return n.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let i=r(76813),s=r(20508),n=r(37563),a=r(84288),o=r(82663),l=r(24651);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76813:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return o}});let i=r(84126),s=r(20508),n=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let n=Object.defineProperty(Error(s.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=s.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",n}function o(e,t){var r;throw null!=t||(t=(null==n||null==(r=n.getStore())?void 0:r.isAction)?s.RedirectType.push:s.RedirectType.replace),a(e,t,i.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=s.RedirectType.replace),a(e,t,i.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,s.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82663:(e,t,r)=>{function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),r(64432).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83625:(e,t,r)=>{r.d(t,{jn:()=>s,qu:()=>i,xy:()=>o,z:()=>n,zF:()=>a});var i={InvalidSecretKey:"clerk_key_invalid"},s={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},n={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},a=class e extends Error{constructor({action:t,message:r,reason:i}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=i,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},o=class extends Error{}},84126:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84288:(e,t,r)=>{function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),r(64432).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84755:e=>{let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),i=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),s=(e,t,n,a=new WeakMap)=>{if(n={deep:!1,target:{},...n},a.has(e))return a.get(e);a.set(e,n.target);let{target:o}=n;delete n.target;let l=e=>e.map(e=>i(e)?s(e,t,n,a):e);if(Array.isArray(e))return l(e);for(let[u,d]of Object.entries(e)){let c=t(u,d,e);if(c===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=c;"__proto__"!==h&&(n.deep&&f&&i(p)&&(p=Array.isArray(p)?l(p):s(p,t,n,a)),o[h]=p)}return o};e.exports=(e,r,i)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return s(e,r,i)},e.exports.mapObjectSkip=r},85932:(e,t,r)=>{r.d(t,{OV:()=>c,S7:()=>u,VK:()=>d,jq:()=>h});var i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o=e=>{throw TypeError(e)},l=(e,t,r)=>t.has(e)||o("Cannot "+r),u=(e,t,r)=>(l(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?o("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),c=(e,t,r,i)=>(l(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r),h=(e,t,r)=>(l(e,t,"access private method"),r)},86072:(e,t,r)=>{r.d(t,{H$:()=>d,mG:()=>a,V2:()=>c,o7:()=>u,fS:()=>p,ev:()=>k,Rg:()=>h,At:()=>l,tm:()=>g,rB:()=>o,qW:()=>f,sE:()=>m,Mh:()=>_,nN:()=>y});var i=r(61541),s=r(71298);r(85932);var n=r(36870);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let a=process.env.CLERK_API_VERSION||"v1",o=process.env.CLERK_SECRET_KEY||"",l="pk_live_Y2xlcmsuY3ViZW50LmRldiQ",u=process.env.CLERK_ENCRYPTION_KEY||"",d=process.env.CLERK_API_URL||(e=>{let t=(0,i.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&s.iM.some(e=>t?.endsWith(e))?s.FW:s.mG.some(e=>t?.endsWith(e))?s.Vc:s.ub.some(e=>t?.endsWith(e))?s.HG:s.FW})(l),c="cubent.dev",h=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",p=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,f="/sign-in",m="/sign-up",g={name:"@clerk/nextjs",version:"6.20.0",environment:"production"},y=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),_=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),k=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1},96158:(e,t,r)=>{r.d(t,{TD:()=>t_,AA:()=>R,Bs:()=>tQ,y3:()=>tp,tl:()=>tO,Z5:()=>tm,wI:()=>tg,nr:()=>tN});var i=r(28634),s=r(43226),n=r(83625),a=r(3093),o=r(69227),l={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},u=new Set(["first_factor","second_factor","multi_factor"]),d=new Set(["strict_mfa","strict","moderate","lax"]),c=e=>"number"==typeof e&&e>0,h=e=>u.has(e),p=e=>d.has(e),f=e=>e.startsWith("org:")?e:`org:${e}`,m=(e,t)=>{let{orgId:r,orgRole:i,orgPermissions:s}=t;return(e.role||e.permission)&&r&&i&&s?e.permission?s.includes(f(e.permission)):e.role?i===f(e.role):null:null},g=(e,t)=>{let{org:r,user:i}=_(e),[s,n]=t.split(":"),a=n||s;return"org"===s?r.includes(a):"user"===s?i.includes(a):[...r,...i].includes(a)},y=(e,t)=>{let{features:r,plans:i}=t;return e.feature&&r?g(r,e.feature):e.plan&&i?g(i,e.plan):null},_=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},k=e=>{if(!e)return!1;let t="string"==typeof e&&p(e),r="object"==typeof e&&h(e.level)&&c(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?l[e]:e).bind(null,e)},b=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=k(e.reverification);if(!r)return null;let{level:i,afterMinutes:s}=r(),[n,a]=t,o=-1!==n?s>n:null,l=-1!==a?s>a:null;switch(i){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},v=e=>t=>{if(!e.userId)return!1;let r=y(t,e),i=m(t,e),s=b(t,e);return[r||i,s].some(e=>null===e)?[r||i,s].some(e=>!0===e):[r||i,s].every(e=>!0===e)};r(85932);var w=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),i=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:i}},S=e=>{let t,r,i,s,n=e.fva??null,a=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,i=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:n}=_(e.fea),{permissions:a,featurePermissionMap:o}=w({per:e.o?.per,fpm:e.o?.fpm});s=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let i=[];for(let s=0;s<e.length;s++){let n=e[s];if(s>=r.length)continue;let a=r[s];if(a)for(let e=0;e<a.length;e++)1===a[e]&&i.push(`org:${n}:${t[e]}`)}return i}({features:n,featurePermissionMap:o,permissions:a})}}else t=e.org_id,r=e.org_role,i=e.org_slug,s=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:a,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:i,orgPermissions:s,factorVerificationAge:n}},T=r(32298);function E(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function O(e){return e&&e.sensitive?"":"i"}var I="https://api.clerk.com",A="@clerk/backend@1.33.0",C="2025-04-10",P={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},x={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:P.DevBrowser,Handshake:P.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:P.HandshakeNonce},R={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:P,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:x},q=RegExp("(?<!:)/{1,}","g");function U(...e){return e.filter(e=>e).join("/").replace(q,"/")}var N=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},j="/actor_tokens",z=class extends N{async create(e){return this.request({method:"POST",path:j,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:U(j,e,"revoke")})}},J="/accountless_applications",M=class extends N{async createAccountlessApplication(){return this.request({method:"POST",path:J})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:U(J,"complete")})}},F="/allowlist_identifiers",H=class extends N{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:F,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:F,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:U(F,e)})}},L=class extends N{async changeDomain(e){return this.request({method:"POST",path:U("/beta_features","change_domain"),bodyParams:e})}},D="/blocklist_identifiers",K=class extends N{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:D,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:D,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:U(D,e)})}},W="/clients",B=class extends N{async getClientList(e={}){return this.request({method:"GET",path:W,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:U(W,e)})}verifyClient(e){return this.request({method:"POST",path:U(W,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:U(W,"handshake_payload"),queryParams:e})}},$="/domains",G=class extends N{async list(){return this.request({method:"GET",path:$})}async add(e){return this.request({method:"POST",path:$,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:U($,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:U($,e)})}},V="/email_addresses",Q=class extends N{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:U(V,e)})}async createEmailAddress(e){return this.request({method:"POST",path:V,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:U(V,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:U(V,e)})}},Y="/instance",Z=class extends N{async get(){return this.request({method:"GET",path:Y})}async update(e){return this.request({method:"PATCH",path:Y,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:U(Y,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:U(Y,"organization_settings"),bodyParams:e})}},X="/invitations",ee=class extends N{async getInvitationList(e={}){return this.request({method:"GET",path:X,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:X,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:U(X,e,"revoke")})}},et=class extends N{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},er="/jwt_templates",ei=class extends N{async list(e={}){return this.request({method:"GET",path:er,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:U(er,e)})}async create(e){return this.request({method:"POST",path:er,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:U(er,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:U(er,e)})}},es="/organizations",en=class extends N{async getOrganizationList(e){return this.request({method:"GET",path:es,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:es,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:U(es,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:U(es,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new s.fA.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:U(es,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:U(es,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:U(es,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:U(es,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:U(es,t,"memberships"),queryParams:r})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:U(es,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...i}=e;return this.requireId(t),this.request({method:"PATCH",path:U(es,t,"memberships",r),bodyParams:i})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...i}=e;return this.request({method:"PATCH",path:U(es,t,"memberships",r,"metadata"),bodyParams:i})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:U(es,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:U(es,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:U(es,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:U(es,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:U(es,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...i}=e;return this.requireId(t),this.request({method:"POST",path:U(es,t,"invitations",r,"revoke"),bodyParams:i})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:U(es,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:U(es,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...i}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:U(es,t,"domains",r),bodyParams:i})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:U(es,t,"domains",r)})}},ea="/oauth_applications",eo=class extends N{async list(e={}){return this.request({method:"GET",path:ea,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:U(ea,e)})}async create(e){return this.request({method:"POST",path:ea,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:U(ea,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:U(ea,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:U(ea,e,"rotate_secret")})}},el="/phone_numbers",eu=class extends N{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:U(el,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:el,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:U(el,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:U(el,e)})}},ed=class extends N{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},ec="/redirect_urls",eh=class extends N{async getRedirectUrlList(){return this.request({method:"GET",path:ec,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:U(ec,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:ec,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:U(ec,e)})}},ep="/saml_connections",ef=class extends N{async getSamlConnectionList(e={}){return this.request({method:"GET",path:ep,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:ep,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:U(ep,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:U(ep,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:U(ep,e)})}},em="/sessions",eg=class extends N{async getSessionList(e={}){return this.request({method:"GET",path:em,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:U(em,e)})}async createSession(e){return this.request({method:"POST",path:em,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:U(em,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:U(em,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:U(em,e,"tokens",t||"")})}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...i}=t;return this.request({method:"POST",path:U(em,e,"refresh"),bodyParams:i,queryParams:{suffixed_cookies:r}})}},ey="/sign_in_tokens",e_=class extends N{async createSignInToken(e){return this.request({method:"POST",path:ey,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:U(ey,e,"revoke")})}},ek="/sign_ups",eb=class extends N{async get(e){return this.requireId(e),this.request({method:"GET",path:U(ek,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:U(ek,t),bodyParams:r})}},ev=class extends N{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},ew="/users",eS=class extends N{async getUserList(e={}){let{limit:t,offset:r,orderBy:i,...s}=e,[n,a]=await Promise.all([this.request({method:"GET",path:ew,queryParams:e}),this.getCount(s)]);return{data:n,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:U(ew,e)})}async createUser(e){return this.request({method:"POST",path:ew,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:U(ew,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new s.fA.FormData;return r.append("file",t?.file),this.request({method:"POST",path:U(ew,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:U(ew,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:U(ew,e)})}async getCount(e={}){return this.request({method:"GET",path:U(ew,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),s=r?t:`oauth_${t}`;return r&&(0,i.io)("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:U(ew,e,"oauth_access_tokens",s),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:U(ew,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:U(ew,t,"organization_memberships"),queryParams:{limit:r,offset:i}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:U(ew,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:U(ew,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:U(ew,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:U(ew,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:U(ew,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:U(ew,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:U(ew,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:U(ew,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:U(ew,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:U(ew,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:U(ew,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:U(ew,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:U(ew,e,"totp")})}},eT="/waitlist_entries",eE=class extends N{async list(e={}){return this.request({method:"GET",path:eT,queryParams:e})}async create(e){return this.request({method:"POST",path:eT,bodyParams:e})}},eO="/webhooks",eI=class extends N{async createSvixApp(){return this.request({method:"POST",path:U(eO,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:U(eO,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:U(eO,"svix")})}};function eA(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var eC=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.status=t,this.userId=r,this.actor=i,this.token=s,this.url=n,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},eP=class e{constructor(e,t,r,i){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=i}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},ex=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=i,this.updatedAt=s,this.instanceId=n,this.invitationId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},eR=class e{constructor(e,t,r,i,s,n){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=i,this.updatedAt=s,this.instanceId=n}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},eq=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=i,this.country=s,this.browserVersion=n,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},eU=class e{constructor(e,t,r,i,s,n,a,o,l,u,d,c=null){this.id=e,this.clientId=t,this.userId=r,this.status=i,this.lastActiveAt=s,this.expireAt=n,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=u,this.latestActivity=d,this.actor=c}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&eq.fromJSON(t.latest_activity),t.actor)}},eN=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=i,this.signUpId=s,this.lastActiveSessionId=n,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>eU.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},ej=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},ez=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},eJ=class e{constructor(e,t,r,i){this.object=e,this.id=t,this.slug=r,this.deleted=i}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},eM=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=i,this.developmentOrigin=s,this.cnameTargets=n,this.accountsPortalUrl=a,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>ej.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},eF=class e{constructor(e,t,r,i,s,n,a,o,l,u,d){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=i,this.subject=s,this.body=n,this.bodyPlain=a,this.status=o,this.slug=l,this.data=u,this.deliveredByClerk=d}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},eH=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eL=class e{constructor(e,t,r=null,i=null,s=null,n=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=i,this.expireAt=s,this.nonce=n,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},eD=class e{constructor(e,t,r,i){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=i}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eL.fromJSON(t.verification),t.linked_to.map(e=>eH.fromJSON(e)))}},eK=class e{constructor(e,t,r,i,s,n,a,o,l,u,d,c={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=i,this.approvedScopes=s,this.emailAddress=n,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=u,this.phoneNumber=d,this.publicMetadata=c,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&eL.fromJSON(t.verification))}},eW=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},eB=class e{constructor(e,t,r,i,s){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=i,this.ignoreDotsForGmailAddresses=s}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},e$=class e{constructor(e,t,r,i,s){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=i,this.enhancedEmailDeliverability=s}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},eG=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=i,this.updatedAt=s,this.status=n,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},eV={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},eQ=class e{constructor(e,t,r,i,s,n,a,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=i,this.allowedClockSkew=s,this.customSigningKey=n,this.signingAlgorithm=a,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},eY=class e{constructor(e,t,r,i={},s,n,a,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=i,this.label=s,this.scopes=n,this.tokenSecret=a,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},eZ=class e{constructor(e,t,r,i,s,n,a,o,l,u,d,c,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=i,this.isPublic=s,this.scopes=n,this.redirectUris=a,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=u,this.discoveryUrl=d,this.tokenIntrospectionUrl=c,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},eX=class e{constructor(e,t,r,i,s,n,a,o={},l={},u,d,c,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=i,this.hasImage=s,this.createdAt=n,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=u,this.adminDeleteEnabled=d,this.membersCount=c,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},e0=class e{constructor(e,t,r,i,s,n,a,o,l,u,d={},c={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=i,this.organizationId=s,this.createdAt=n,this.updatedAt=a,this.expiresAt=o,this.url=l,this.status=u,this.publicMetadata=d,this.privateMetadata=c,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},e1=class e{constructor(e,t,r,i={},s={},n,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=i,this.privateMetadata=s,this.createdAt=n,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,eX.fromJSON(t.organization),e2.fromJSON(t.public_user_data));return r._raw=t,r}},e2=class e{constructor(e,t,r,i,s,n){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=i,this.hasImage=s,this.userId=n}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},e3=class e{constructor(e,t,r,i,s,n,a,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=i,this.creatorRole=s,this.adminDeleteEnabled=n,this.domainsEnabled=a,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},e8=class e{constructor(e,t,r,i,s,n){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=i,this.verification=s,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eL.fromJSON(t.verification),t.linked_to.map(e=>eH.fromJSON(e)))}},e6=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=i,this.successful=s,this.createdAt=n,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},e5=class e{constructor(e,t,r,i){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=i}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},e4=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.userId=t,this.token=r,this.status=i,this.url=s,this.createdAt=n,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},e9=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},e7=class e{constructor(e,t,r,i){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=i}static fromJSON(t){return new e(t.email_address&&e9.fromJSON(t.email_address),t.phone_number&&e9.fromJSON(t.phone_number),t.web3_wallet&&e9.fromJSON(t.web3_wallet),t.external_account)}},te=class e{constructor(e,t,r,i,s,n,a,o,l,u,d,c,h,p,f,m,g,y,_,k,b,v){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=i,this.missingFields=s,this.unverifiedFields=n,this.verifications=a,this.username=o,this.emailAddress=l,this.phoneNumber=u,this.web3Wallet=d,this.passwordEnabled=c,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=m,this.createdSessionId=g,this.createdUserId=y,this.abandonAt=_,this.legalAcceptedAt=k,this.publicMetadata=b,this.unsafeMetadata=v}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?e7.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},tt=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=i,this.status=s,this.phoneNumberId=n,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},tr=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},ti=class e{constructor(e,t,r,i,s,n,a,o,l,u){this.id=e,this.name=t,this.domain=r,this.active=i,this.provider=s,this.syncUserAttributes=n,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=u}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},ts=class e{constructor(e,t,r,i,s,n,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=i,this.emailAddress=s,this.firstName=n,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&eL.fromJSON(t.verification),t.saml_connection&&ti.fromJSON(t.saml_connection))}},tn=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eL.fromJSON(t.verification))}},ta=class e{constructor(e,t,r,i,s,n,a,o,l,u,d,c,h,p,f,m,g,y,_,k={},b={},v={},w=[],S=[],T=[],E=[],O=[],I,A,C=null,P,x){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=i,this.twoFactorEnabled=s,this.banned=n,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=u,this.hasImage=d,this.primaryEmailAddressId=c,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=m,this.username=g,this.firstName=y,this.lastName=_,this.publicMetadata=k,this.privateMetadata=b,this.unsafeMetadata=v,this.emailAddresses=w,this.phoneNumbers=S,this.web3Wallets=T,this.externalAccounts=E,this.samlAccounts=O,this.lastActiveAt=I,this.createOrganizationEnabled=A,this.createOrganizationsLimit=C,this.deleteSelfEnabled=P,this.legalAcceptedAt=x,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>eD.fromJSON(e)),(t.phone_numbers||[]).map(e=>e8.fromJSON(e)),(t.web3_wallets||[]).map(e=>tn.fromJSON(e)),(t.external_accounts||[]).map(e=>eK.fromJSON(e)),(t.saml_accounts||[]).map(e=>ts.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},to=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.emailAddress=t,this.status=r,this.invitation=i,this.createdAt=s,this.updatedAt=n,this.isLocked=a}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&eG.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function tl(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return eJ.fromJSON(e);switch(e.object){case eV.AccountlessApplication:return eP.fromJSON(e);case eV.ActorToken:return eC.fromJSON(e);case eV.AllowlistIdentifier:return ex.fromJSON(e);case eV.BlocklistIdentifier:return eR.fromJSON(e);case eV.Client:return eN.fromJSON(e);case eV.Cookies:return ez.fromJSON(e);case eV.Domain:return eM.fromJSON(e);case eV.EmailAddress:return eD.fromJSON(e);case eV.Email:return eF.fromJSON(e);case eV.Instance:return eW.fromJSON(e);case eV.InstanceRestrictions:return eB.fromJSON(e);case eV.InstanceSettings:return e$.fromJSON(e);case eV.Invitation:return eG.fromJSON(e);case eV.JwtTemplate:return eQ.fromJSON(e);case eV.OauthAccessToken:return eY.fromJSON(e);case eV.OAuthApplication:return eZ.fromJSON(e);case eV.Organization:return eX.fromJSON(e);case eV.OrganizationInvitation:return e0.fromJSON(e);case eV.OrganizationMembership:return e1.fromJSON(e);case eV.OrganizationSettings:return e3.fromJSON(e);case eV.PhoneNumber:return e8.fromJSON(e);case eV.ProxyCheck:return e6.fromJSON(e);case eV.RedirectUrl:return e5.fromJSON(e);case eV.SignInToken:return e4.fromJSON(e);case eV.SignUpAttempt:return te.fromJSON(e);case eV.Session:return eU.fromJSON(e);case eV.SmsMessage:return tt.fromJSON(e);case eV.Token:return tr.fromJSON(e);case eV.TotalCount:return e.total_count;case eV.User:return ta.fromJSON(e);case eV.WaitlistEntry:return to.fromJSON(e);default:return e}}function tu(e){var t;return t=async t=>{let r,{secretKey:i,requireSecretKey:n=!0,apiUrl:a=I,apiVersion:l="v1",userAgent:u=A}=e,{path:d,method:c,queryParams:h,headerParams:p,bodyParams:f,formData:m}=t;n&&eA(i);let g=new URL(U(a,l,d));if(h)for(let[e,t]of Object.entries(o({...h})))t&&[t].flat().forEach(t=>g.searchParams.append(e,t));let y={"Clerk-API-Version":C,"User-Agent":u,...p};i&&(y.Authorization=`Bearer ${i}`);try{var _;m?r=await s.fA.fetch(g.href,{method:c,headers:y,body:m}):(y["Content-Type"]="application/json",r=await s.fA.fetch(g.href,{method:c,headers:y,...(()=>{if(!("GET"!==c&&f&&Object.keys(f).length>0))return null;let e=e=>o(e,{deep:!1});return{body:JSON.stringify(Array.isArray(f)?f.map(e):e(f))}})()}));let e=r?.headers&&r.headers?.get(R.Headers.ContentType)===R.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:th(t),status:r?.status,statusText:r?.statusText,clerkTraceId:td(t,r?.headers),retryAfter:tc(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>tl(e))}:(_=t)&&"object"==typeof _&&"data"in _&&Array.isArray(_.data)&&void 0!==_.data?{data:t.data.map(e=>tl(e)),totalCount:t.total_count}:{data:tl(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:td(e,r?.headers)};return{data:null,errors:th(e),status:r?.status,statusText:r?.statusText,clerkTraceId:td(e,r?.headers),retryAfter:tc(r?.headers)}}},async(...e)=>{let{data:r,errors:i,totalCount:s,status:n,statusText:o,clerkTraceId:l,retryAfter:u}=await t(...e);if(i){let e=new a.LR(o||"",{data:[],status:n,clerkTraceId:l,retryAfter:u});throw e.errors=i,e}return void 0!==s?{data:r,totalCount:s}:r}}function td(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function tc(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function th(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(a.u$):[]}return[]}function tp(e){let t=tu(e);return{__experimental_accountlessApplications:new M(tu({...e,requireSecretKey:!1})),actorTokens:new z(t),allowlistIdentifiers:new H(t),betaFeatures:new L(t),blocklistIdentifiers:new K(t),clients:new B(t),domains:new G(t),emailAddresses:new Q(t),instance:new Z(t),invitations:new ee(t),jwks:new et(t),jwtTemplates:new ei(t),oauthApplications:new eo(t),organizations:new en(t),phoneNumbers:new eu(t),proxyChecks:new ed(t),redirectUrls:new eh(t),samlConnections:new ef(t),sessions:new eg(t),signInTokens:new e_(t),signUps:new eb(t),testingTokens:new ev(t),users:new eS(t),waitlistEntries:new eE(t),webhooks:new eI(t)}}var tf=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function tm(e,t,r){let{actor:i,sessionId:s,sessionStatus:n,userId:a,orgId:o,orgRole:l,orgSlug:u,orgPermissions:d,factorVerificationAge:c}=S(r),h=tp(e),p=ty({sessionId:s,sessionToken:t,fetcher:async(...e)=>(await h.sessions.getToken(...e)).jwt});return{actor:i,sessionClaims:r,sessionId:s,sessionStatus:n,userId:a,orgId:o,orgRole:l,orgSlug:u,orgPermissions:d,factorVerificationAge:c,getToken:p,has:v({orgId:o,orgRole:l,orgPermissions:d,userId:a,factorVerificationAge:c,features:r.fea||"",plans:r.pla||""}),debug:tf({...e,sessionToken:t})}}function tg(e,t){return{sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:tf(e)}}var ty=e=>{let{fetcher:t,sessionToken:r,sessionId:i}=e||{};return async(e={})=>i?e.template?t(i,e.template):r:null},t_={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},tk={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function tb(e,t,r=new Headers,i){let s=tm(e,i,t);return{status:t_.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:({treatPendingAsSignedOut:e=!0}={})=>e&&"pending"===s.sessionStatus?tg(void 0,s.sessionStatus):s,headers:r,token:i}}function tv(e,t,r="",i=new Headers){return tw({status:t_.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:i,toAuth:()=>tg({...e,status:t_.SignedOut,reason:t,message:r}),token:null})}var tw=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(R.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(R.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(R.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},tS=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},tT=(...e)=>new tS(...e),tE=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(R.Headers.ForwardedProto),i=e.headers.get(R.Headers.ForwardedHost),s=e.headers.get(R.Headers.Host),n=t.protocol,a=this.getFirstValueFromHeader(i)??s,o=this.getFirstValueFromHeader(r)??n?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?tT(t):tT(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,T.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},tO=(...e)=>e[0]instanceof tE?e[0]:new tE(...e),tI={},tA=0;function tC(e,t=!0){tI[e.kid]=e,tA=t?Date.now():-1}var tP="local";function tx(e){if(!tI[tP]){if(!e)throw new n.zF({action:n.z.SetClerkJWTKey,message:"Missing local JWK.",reason:n.jn.LocalJWKMissing});tC({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return tI[tP]}async function tR({secretKey:e,apiUrl:t=I,apiVersion:r="v1",kid:s,skipJwksCache:a}){if(a||function(){if(-1===tA)return!1;let e=Date.now()-tA>=3e5;return e&&(tI={}),e}()||!tI[s]){if(!e)throw new n.zF({action:n.z.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:n.jn.RemoteJWKFailedToLoad});let{keys:s}=await (0,i.L5)(()=>tq(t,e,r));if(!s||!s.length)throw new n.zF({action:n.z.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:n.jn.RemoteJWKFailedToLoad});s.forEach(e=>tC(e))}let o=tI[s];if(!o){let e=Object.values(tI).map(e=>e.kid).sort().join(", ");throw new n.zF({action:`Go to your Dashboard and validate your secret and public keys are correct. ${n.z.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${s}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:n.jn.JWKKidMismatch})}return o}async function tq(e,t,r){if(!t)throw new n.zF({action:n.z.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:n.jn.RemoteJWKFailedToLoad});let i=new URL(e);i.pathname=U(i.pathname,r,"/jwks");let a=await s.fA.fetch(i.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":C,"Content-Type":"application/json","User-Agent":A}});if(!a.ok){let e=await a.json(),t=tU(e?.errors,n.qu.InvalidSecretKey);if(t){let e=n.jn.InvalidSecretKey;throw new n.zF({action:n.z.ContactSupport,message:t.message,reason:e})}throw new n.zF({action:n.z.ContactSupport,message:`Error loading Clerk JWKS from ${i.href} with code=${a.status}`,reason:n.jn.RemoteJWKFailedToLoad})}return a.json()}var tU=(e,t)=>e?e.find(e=>e.code===t):null;async function tN(e,t){let{data:r,errors:i}=(0,s.iU)(e);if(i)return{errors:i};let{header:a}=r,{kid:o}=a;try{let r;if(t.jwtKey)r=tx(t.jwtKey);else{if(!t.secretKey)return{errors:[new n.zF({action:n.z.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:n.jn.JWKFailedToResolve})]};r=await tR({...t,kid:o})}return await (0,s.J0)(e,{...t,key:r})}catch(e){return{errors:[e]}}}var tj=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(R.Cookies.ClientUat),t=this.getCookie(R.Cookies.ClientUat),r=this.getSuffixedCookie(R.Cookies.Session)||"",i=this.getCookie(R.Cookies.Session)||"";if(i&&!this.tokenHasIssuer(i))return!1;if(i&&!this.tokenBelongsToInstance(i))return!0;if(!e&&!r)return!1;let{data:n}=(0,s.iU)(i),a=n?.payload.iat||0,{data:o}=(0,s.iU)(r),l=o?.payload.iat||0;if("0"!==e&&"0"!==t&&a>l||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(o);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,i.q5)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,i.q5)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=r.instanceType,this.frontendApi=r.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.parseAuthorizationHeader(this.getHeader(R.Headers.Authorization)),this.origin=this.getHeader(R.Headers.Origin),this.host=this.getHeader(R.Headers.Host),this.forwardedHost=this.getHeader(R.Headers.ForwardedHost),this.forwardedProto=this.getHeader(R.Headers.CloudFrontForwardedProto)||this.getHeader(R.Headers.ForwardedProto),this.referrer=this.getHeader(R.Headers.Referrer),this.userAgent=this.getHeader(R.Headers.UserAgent),this.secFetchDest=this.getHeader(R.Headers.SecFetchDest),this.accept=this.getHeader(R.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(R.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(R.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(R.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(R.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(R.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(R.QueryParameters.Handshake)||this.getCookie(R.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(R.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(R.QueryParameters.HandshakeNonce)||this.getCookie(R.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,i.ky)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=(0,s.iU)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,s.iU)(e);if(r)return!1;let i=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===i}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},tz=async(e,t)=>new tj(t.publishableKey?await (0,i.qS)(t.publishableKey,s.fA.crypto.subtle):"",e,t),tJ=e=>e.split(";")[0]?.split("=")[0],tM=e=>e.split(";")[0]?.split("=")[1];async function tF(e,{key:t}){let{data:r,errors:i}=(0,s.iU)(e);if(i)throw i[0];let{header:a,payload:o}=r,{typ:l,alg:u}=a;(0,s.qf)(l),(0,s.l3)(u);let{data:d,errors:c}=await (0,s.nk)(r,t);if(c)throw new n.zF({reason:n.jn.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!d)throw new n.zF({reason:n.jn.TokenInvalidSignature,message:"Handshake signature is invalid."});return o}async function tH(e,t){let r,{secretKey:i,apiUrl:a,apiVersion:o,jwksCacheTtlInMs:l,jwtKey:u,skipJwksCache:d}=t,{data:c,errors:h}=(0,s.iU)(e);if(h)throw h[0];let{kid:p}=c.header;if(u)r=tx(u);else if(i)r=await tR({secretKey:i,apiUrl:a,apiVersion:o,kid:p,jwksCacheTtlInMs:l,skipJwksCache:d});else throw new n.zF({action:n.z.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:n.jn.JWKFailedToResolve});return await tF(e,{key:r})}var tL=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.replace(/http(s)?:\/\//,""),i=new URL(`https://${r}/v1/client/handshake`);i.searchParams.append("redirect_url",t?.href||""),i.searchParams.append("__clerk_api_version",C),i.searchParams.append(R.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),i.searchParams.append(R.QueryParameters.HandshakeReason,e),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&i.searchParams.append(R.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let s=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return s&&this.getOrganizationSyncQueryParams(s).forEach((e,t)=>{i.searchParams.append(t,e)}),new Headers({[R.Headers.Location]:i.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await tH(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),tJ(t).startsWith(R.Cookies.Session)&&(r=tM(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(R.QueryParameters.Handshake),t.searchParams.delete(R.QueryParameters.HandshakeHelp),e.append(R.Headers.Location,t.toString()),e.set(R.Headers.CacheControl,"no-store")}if(""===r)return tv(this.authenticateContext,tk.SessionTokenMissing,"",e);let{data:i,errors:[s]=[]}=await tN(r,this.authenticateContext);if(i)return tb(this.authenticateContext,i,e,r);if("development"===this.authenticateContext.instanceType&&(s?.reason===n.jn.TokenExpired||s?.reason===n.jn.TokenNotActiveYet||s?.reason===n.jn.TokenIatInTheFuture)){let t=new n.zF({action:s.action,message:s.message,reason:s.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:i,errors:[a]=[]}=await tN(r,{...this.authenticateContext,clockSkewInMs:864e5});if(i)return tb(this.authenticateContext,i,e,r);throw Error(a?.message||"Clerk: Handshake retry failed.")}throw Error(s?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===n.jn.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=R.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(R.QueryParameters.DevBrowser),t.searchParams.delete(R.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},tD=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,i,s,n,a,o,l;return r=void 0,i=[],s=function e(t,r,i){var s;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,i=0,s=r.exec(e.source);s;)t.push({name:s[1]||i++,prefix:"",suffix:"",modifier:"",pattern:""}),s=r.exec(e.source);return e}(t,r):Array.isArray(t)?(s=t.map(function(t){return e(t,r,i).source}),new RegExp("(?:".concat(s.join("|"),")"),O(i))):function(e,t,r){void 0===r&&(r={});for(var i=r.strict,s=void 0!==i&&i,n=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,u=r.delimiter,d=r.endsWith,c="[".concat(E(void 0===d?"":d),"]|$"),h="[".concat(E(void 0===u?"/#?":u),"]"),p=void 0===n||n?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=E(l(m));else{var g=E(l(m.prefix)),y=E(l(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var _="*"===m.modifier?"?":"";p+="(?:".concat(g,"((?:").concat(m.pattern,")(?:").concat(y).concat(g,"(?:").concat(m.pattern,"))*)").concat(y,")").concat(_)}else p+="(?:".concat(g,"(").concat(m.pattern,")").concat(y,")").concat(m.modifier);else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));p+="(".concat(m.pattern,")").concat(m.modifier)}else p+="(?:".concat(g).concat(y,")").concat(m.modifier)}}if(void 0===a||a)s||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(c,")"):"$";else{var k=e[e.length-1],b="string"==typeof k?h.indexOf(k[k.length-1])>-1:void 0===k;s||(p+="(?:".concat(h,"(?=").concat(c,"))?")),b||(p+="(?=".concat(h,"|").concat(c,")"))}return new RegExp(p,O(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var i=e[r];if("*"===i||"+"===i||"?"===i){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===i){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===i){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===i){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===i){for(var s="",n=r+1;n<e.length;){var a=e.charCodeAt(n);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){s+=e[n++];continue}break}if(!s)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:s}),r=n;continue}if("("===i){var o=1,l="",n=r+1;if("?"===e[n])throw TypeError('Pattern cannot start with "?" at '.concat(n));for(;n<e.length;){if("\\"===e[n]){l+=e[n++]+e[n++];continue}if(")"===e[n]){if(0==--o){n++;break}}else if("("===e[n]&&(o++,"?"!==e[n+1]))throw TypeError("Capturing groups are not allowed at ".concat(n));l+=e[n++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=n;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),i=t.prefixes,s=void 0===i?"./":i,n=t.delimiter,a=void 0===n?"/#?":n,o=[],l=0,u=0,d="",c=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=c(e);if(void 0!==t)return t;var i=r[u],s=i.type,n=i.index;throw TypeError("Unexpected ".concat(s," at ").concat(n,", expected ").concat(e))},p=function(){for(var e,t="";e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(E(a),"]+?"):"(?:(?!".concat(E(r),")[^").concat(E(a),"])+?")};u<r.length;){var g=c("CHAR"),y=c("NAME"),_=c("PATTERN");if(y||_){var k=g||"";-1===s.indexOf(k)&&(d+=k,k=""),d&&(o.push(d),d=""),o.push({name:y||l++,prefix:k,suffix:"",pattern:_||m(k),modifier:c("MODIFIER")||""});continue}var b=g||c("ESCAPED_CHAR");if(b){d+=b;continue}if(d&&(o.push(d),d=""),c("OPEN")){var k=p(),v=c("NAME")||"",w=c("PATTERN")||"",S=p();h("CLOSE"),o.push({name:v||(w?l++:""),pattern:v&&!w?m(k):w,prefix:k,suffix:S,modifier:c("MODIFIER")||""});continue}h("END")}return o}(t,i),r,i)}(e,i,r),n=i,a=r,void 0===a&&(a={}),o=a.decode,l=void 0===o?function(e){return e}:o,function(e){var t=s.exec(e);if(!t)return!1;for(var r=t[0],i=t.index,a=Object.create(null),o=1;o<t.length;o++)!function(e){if(void 0!==t[e]){var r=n[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):a[r.name]=l(t[e],r)}}(o);return{path:r,index:i,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},tK={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};async function tW(e,t){let r=await tz(tO(e),t);if(eA(r.secretKey),r.isSatellite){var a=r.signInUrl,o=r.secretKey;if(!a&&(0,i.Ve)(o))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let l=new tD(t.organizationSyncOptions),u=new tL(r,{organizationSyncOptions:t.organizationSyncOptions},l);async function d(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:tK.MissingApiClient}}};let{sessionToken:i,refreshTokenInCookie:n}=r;if(!i)return{data:null,error:{message:"Session token must be provided.",cause:{reason:tK.MissingSessionToken}}};if(!n)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:tK.MissingRefreshToken}}};let{data:a,errors:o}=(0,s.iU)(i);if(!a||o)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:tK.ExpiredSessionTokenDecodeFailed,errors:o}}};if(!a?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:tK.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(a.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:i||"",refresh_token:n||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:tK.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:tK.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function c(e){let{data:t,error:r}=await d(e);if(!t||0===t.length)return{data:null,error:r};let i=new Headers,s="";t.forEach(e=>{i.append("Set-Cookie",e),tJ(e).startsWith(R.Cookies.Session)&&(s=tM(e))});let{data:n,errors:a}=await tN(s,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:tK.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:n,sessionToken:s,headers:i},error:null}}function h(e,t,r,i){if(!u.isRequestEligibleForHandshake())return tv(e,t,r);let s=i??u.buildRedirectToHandshake(t);return(s.get(R.Headers.Location)&&s.set(R.Headers.CacheControl,"no-store"),u.checkAndTrackRedirectLoop(s))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),tv(e,t,r)):function(e,t,r="",i){return tw({status:t_.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:i,toAuth:()=>null,token:null})}(e,t,r,s)}async function p(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:i}=await tN(e,r);if(i)throw i[0];return tb(r,t,void 0,e)}catch(e){return m(e,"header")}}async function f(){let e=r.clientUat,t=!!r.sessionTokenInCookie,i=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await u.resolveHandshake()}catch(e){e instanceof n.zF&&"development"===r.instanceType?u.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(R.QueryParameters.DevBrowser))return h(r,tk.DevBrowserSync,"");let a=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&a)return h(r,tk.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&a&&!r.clerkUrl.searchParams.has(R.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(R.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[R.Headers.Location]:e.toString()});return h(r,tk.SatelliteCookieNeedsSyncing,"",t)}let o=new URL(r.clerkUrl).searchParams.get(R.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&o){let e=new URL(o);r.devBrowserToken&&e.searchParams.append(R.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(R.QueryParameters.ClerkSynced,"true");let t=new Headers({[R.Headers.Location]:e.toString()});return h(r,tk.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!i)return h(r,tk.DevBrowserMissing,"");if(!e&&!t)return tv(r,tk.SessionTokenAndUATMissing,"");if(!e&&t)return h(r,tk.SessionTokenWithoutClientUAT,"");if(e&&!t)return h(r,tk.ClientUATWithoutSessionToken,"");let{data:d,errors:c}=(0,s.iU)(r.sessionTokenInCookie);if(c)return m(c[0],"cookie");if(d.payload.iat<r.clientUat)return h(r,tk.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await tN(r.sessionTokenInCookie,r);if(t)throw t[0];let i=tb(r,e,void 0,r.sessionTokenInCookie),s=i.toAuth();if(s.userId){let e=function(e,t){let r=l.findTarget(e.clerkUrl);if(!r)return null;let i=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(i=!0),r.organizationId&&r.organizationId!==t.orgId&&(i=!0)),"personalAccount"===r.type&&t.orgId&&(i=!0),!i)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let s=h(e,tk.ActiveOrganizationMismatch,"");return"handshake"!==s.status?null:s}(r,s);if(e)return e}return i}catch(e){return m(e,"cookie")}}async function m(t,i){let s;if(!(t instanceof n.zF))return tv(r,tk.UnexpectedError);if(t.reason===n.jn.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await c(r);if(e)return tb(r,e.jwtPayload,e.headers,e.sessionToken);s=t?.cause?.reason?t.cause.reason:tK.UnexpectedSDKError}else s="GET"!==e.method?tK.NonEligibleNonGet:r.refreshTokenInCookie?null:tK.NonEligibleNoCookie;return(t.tokenCarrier=i,[n.jn.TokenExpired,n.jn.TokenNotActiveYet,n.jn.TokenIatInTheFuture].includes(t.reason))?h(r,t$({tokenError:t.reason,refreshError:s}),t.getFullMessage()):tv(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?p():f()}var tB=e=>{let{isSignedIn:t,proxyUrl:r,reason:i,message:s,publishableKey:n,isSatellite:a,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:i,message:s,publishableKey:n,isSatellite:a,domain:o}},t$=({tokenError:e,refreshError:t})=>{switch(e){case n.jn.TokenExpired:return`${tk.SessionTokenExpired}-refresh-${t}`;case n.jn.TokenNotActiveYet:return tk.SessionTokenNBF;case n.jn.TokenIatInTheFuture:return tk.SessionTokenIatInTheFuture;default:return tk.UnexpectedError}};function tG(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var tV={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function tQ(e){let t=tG(tV,e.options),r=e.apiClient;return{authenticateRequest:(e,i={})=>{let{apiUrl:s,apiVersion:n}=t,a=tG(t,i);return tW(e,{...i,...a,apiUrl:s,apiVersion:n,apiClient:r})},debugRequestState:tB}}},97296:(e,t,r)=>{r.d(t,{NE:()=>s,Zd:()=>a,_b:()=>n});var i=r(59608);function s(e,t){var r;return((r=i.AA.Attributes[t])in e?e[r]:void 0)||n(e,i.AA.Headers[t])}function n(e,t){var r,i;return function(e){try{let{headers:t,nextUrl:r,cookies:i}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==i?void 0:i.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(i=null==(r=e.socket)?void 0:r._httpMessage)?void 0:i.getHeader(t))}function a(e){return!!s(e,"AuthStatus")}},98176:(e,t,r)=>{r.d(t,{y:()=>i});var i=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e}};