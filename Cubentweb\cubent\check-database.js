/**
 * <PERSON><PERSON>t to check database for usage data and authentication tokens
 */

import { PrismaClient } from './packages/database/generated/client/index.js';

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 Checking database for usage analytics data...\n');

    // Check users
    console.log('👥 Users:');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        clerkId: true,
        email: true,
        cubentUnitsUsed: true,
        cubentUnitsLimit: true,
        subscriptionTier: true,
        createdAt: true,
      },
      take: 5,
    });
    console.log(`Found ${users.length} users`);
    users.forEach(user => {
      console.log(`  - ${user.email}: ${user.cubentUnitsUsed}/${user.cubentUnitsLimit} units (${user.subscriptionTier})`);
    });
    console.log('');

    // Check pending logins
    console.log('🔑 Pending Logins:');
    const pendingLogins = await prisma.pendingLogin.findMany({
      select: {
        id: true,
        deviceId: true,
        userId: true,
        token: true,
        expiresAt: true,
        createdAt: true,
      },
      take: 10,
      orderBy: { createdAt: 'desc' },
    });
    console.log(`Found ${pendingLogins.length} pending logins`);
    pendingLogins.forEach(login => {
      const isExpired = login.expiresAt < new Date();
      console.log(`  - Device: ${login.deviceId.slice(0, 8)}... | User: ${login.userId} | Expired: ${isExpired}`);
    });
    console.log('');

    // Check usage analytics
    console.log('📊 Usage Analytics:');
    const usageAnalytics = await prisma.usageAnalytics.findMany({
      select: {
        id: true,
        userId: true,
        modelId: true,
        cubentUnitsUsed: true,
        requestsMade: true,
        createdAt: true,
      },
      take: 10,
      orderBy: { createdAt: 'desc' },
    });
    console.log(`Found ${usageAnalytics.length} usage analytics records`);
    usageAnalytics.forEach(record => {
      console.log(`  - User: ${record.userId} | Model: ${record.modelId} | Units: ${record.cubentUnitsUsed} | Requests: ${record.requestsMade}`);
    });
    console.log('');

    // Check usage metrics
    console.log('📈 Usage Metrics:');
    const usageMetrics = await prisma.usageMetrics.findMany({
      select: {
        id: true,
        userId: true,
        cubentUnitsUsed: true,
        requestsMade: true,
        date: true,
      },
      take: 10,
      orderBy: { date: 'desc' },
    });
    console.log(`Found ${usageMetrics.length} usage metrics records`);
    usageMetrics.forEach(record => {
      console.log(`  - User: ${record.userId} | Date: ${record.date.toISOString().split('T')[0]} | Units: ${record.cubentUnitsUsed} | Requests: ${record.requestsMade}`);
    });
    console.log('');

    // Summary
    console.log('📋 Summary:');
    console.log(`- Total users: ${users.length}`);
    console.log(`- Active pending logins: ${pendingLogins.filter(l => l.expiresAt > new Date()).length}`);
    console.log(`- Total usage analytics: ${usageAnalytics.length}`);
    console.log(`- Total usage metrics: ${usageMetrics.length}`);

    if (usageAnalytics.length === 0 && usageMetrics.length === 0) {
      console.log('\n❌ No usage data found! This explains why /profile/usage shows no data.');
      console.log('🔍 Possible causes:');
      console.log('  1. Extension is not successfully sending usage data');
      console.log('  2. Authentication tokens are not being generated correctly');
      console.log('  3. Extension is not connected to the website');
    } else {
      console.log('\n✅ Usage data found in database');
    }

  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
