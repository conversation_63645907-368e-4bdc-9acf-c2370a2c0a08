(()=>{var e={};e.id=2125,e.ids=[2125],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8842:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{POST:()=>p});var n=r(26142),i=r(94327),a=r(34862),o=r(18815),u=r(26239),c=r(25);let d=c.z.object({modelId:c.z.string(),provider:c.z.string(),configName:c.z.string(),cubentUnits:c.z.number().min(0),messageCount:c.z.number().min(1).default(1),timestamp:c.z.number().optional(),metadata:c.z.record(c.z.any()).optional()});async function p(e){try{let t=e.headers.get("authorization"),s=null;if(!t?.startsWith("Bearer "))return console.error("Extension usage tracking - No Bearer token provided"),u.NextResponse.json({error:"Authorization header required"},{status:401});{let e=t.substring(7);if(!await o.database.pendingLogin.findFirst({where:{token:e,expiresAt:{gt:new Date}}}))return console.error("Extension usage tracking - Token not found in pendingLogin table"),u.NextResponse.json({error:"Invalid or expired token"},{status:401});try{let{clerkClient:t}=await Promise.all([r.e(7873),r.e(3887),r.e(6982)]).then(r.bind(r,14601)),n=await t();s=(await n.sessions.getSession(e)).userId}catch(e){return console.error("Extension usage tracking - Invalid session token:",e),u.NextResponse.json({error:"Invalid token"},{status:401})}}if(!s)return u.NextResponse.json({error:"Unauthorized"},{status:401});let n=await e.json(),{modelId:i,provider:a,configName:c,cubentUnits:p,messageCount:x,timestamp:l,metadata:g}=d.parse(n);console.log("Extension usage tracking:",{userId:s,modelId:i,provider:a,cubentUnits:p,messageCount:x});let m=await o.database.user.findUnique({where:{clerkId:s}});if(!m)return u.NextResponse.json({error:"User not found"},{status:404});await o.database.user.update({where:{id:m.id},data:{cubentUnitsUsed:{increment:p}}}),await o.database.usageAnalytics.create({data:{userId:m.id,modelId:i,cubentUnitsUsed:p,requestsMade:x,metadata:{provider:a,configName:c,timestamp:l||Date.now(),...g}}});let q=new Date;q.setHours(0,0,0,0);let b=await o.database.usageMetrics.findFirst({where:{userId:m.id,date:q}});return b?await o.database.usageMetrics.update({where:{id:b.id},data:{cubentUnitsUsed:{increment:p},requestsMade:{increment:x}}}):await o.database.usageMetrics.create({data:{userId:m.id,cubentUnitsUsed:p,requestsMade:x,date:q}}),console.log("Extension usage tracking successful:",{userId:s,cubentUnits:p,totalUnits:m.cubentUnitsUsed+p}),u.NextResponse.json({success:!0,message:"Usage tracked successfully",cubentUnitsUsed:p,totalCubentUnits:m.cubentUnitsUsed+p,messageCount:x})}catch(e){return console.error("Extension usage tracking error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/units/track/route",pathname:"/api/extension/units/track",filename:"route",bundlePath:"app/api/extension/units/track/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\units\\track\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=x;function q(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16698:e=>{"use strict";e.exports=require("node:async_hooks")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,25,5480,864],()=>r(8842));module.exports=s})();