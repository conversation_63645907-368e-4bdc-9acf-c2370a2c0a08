(()=>{var e={};e.id=2125,e.ids=[2125],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8842:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{POST:()=>p});var i=r(26142),n=r(94327),a=r(34862),o=r(18815),u=r(26239),c=r(25);let d=c.z.object({modelId:c.z.string(),provider:c.z.string(),configName:c.z.string(),cubentUnits:c.z.number().min(0),messageCount:c.z.number().min(1).default(1),timestamp:c.z.number().optional(),metadata:c.z.record(c.z.any()).optional()});async function p(e){try{let t=e.headers.get("authorization"),r=null;if(!t?.startsWith("Bearer "))return console.error("Extension usage tracking - No Bearer token provided"),u.NextResponse.json({error:"Authorization header required"},{status:401});{let e=t.substring(7),s=await o.database.pendingLogin.findFirst({where:{token:e,expiresAt:{gt:new Date}}});if(!s)return console.error("Extension usage tracking - Token not found in pendingLogin table"),u.NextResponse.json({error:"Invalid or expired token"},{status:401});r=s.userId,console.log("Extension usage tracking - PendingLogin validation successful:",{userId:r})}if(!r)return u.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),{modelId:i,provider:n,configName:a,cubentUnits:c,messageCount:p,timestamp:x,metadata:l}=d.parse(s);console.log("Extension usage tracking:",{userId:r,modelId:i,provider:n,cubentUnits:c,messageCount:p});let g=await o.database.user.findUnique({where:{clerkId:r}});if(!g)return u.NextResponse.json({error:"User not found"},{status:404});await o.database.user.update({where:{id:g.id},data:{cubentUnitsUsed:{increment:c}}}),await o.database.usageAnalytics.create({data:{userId:g.id,modelId:i,cubentUnitsUsed:c,requestsMade:p,metadata:{provider:n,configName:a,timestamp:x||Date.now(),...l}}});let m=new Date;m.setHours(0,0,0,0);let b=await o.database.usageMetrics.findFirst({where:{userId:g.id,date:m}});return b?await o.database.usageMetrics.update({where:{id:b.id},data:{cubentUnitsUsed:{increment:c},requestsMade:{increment:p}}}):await o.database.usageMetrics.create({data:{userId:g.id,cubentUnitsUsed:c,requestsMade:p,date:m}}),console.log("Extension usage tracking successful:",{userId:r,cubentUnits:c,totalUnits:g.cubentUnitsUsed+c}),u.NextResponse.json({success:!0,message:"Usage tracked successfully",cubentUnitsUsed:c,totalCubentUnits:g.cubentUnitsUsed+c,messageCount:p})}catch(e){return console.error("Extension usage tracking error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/units/track/route",pathname:"/api/extension/units/track",filename:"route",bundlePath:"app/api/extension/units/track/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\units\\track\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=x;function b(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,25,5480,864],()=>r(8842));module.exports=s})();