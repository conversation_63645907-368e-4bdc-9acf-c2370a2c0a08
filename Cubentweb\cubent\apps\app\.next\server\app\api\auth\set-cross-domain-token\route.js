(()=>{var e={};e.id=535,e.ids=[535],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10529:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{POST:()=>c});var a=r(26142),o=r(94327),n=r(34862),i=r(37838),u=r(1359),p=r(26239),d=r(62644);async function c(){try{let{userId:e}=await (0,i.j)();if(!e)return p.NextResponse.json({error:"Not authenticated"},{status:401});let t=await (0,u.N)();if(!t)return p.NextResponse.json({error:"User not found"},{status:404});let r={id:t.id,fullName:t.fullName,firstName:t.firstName,lastName:t.lastName,emailAddresses:t.emailAddresses.map(e=>({emailAddress:e.emailAddress})),imageUrl:t.imageUrl,timestamp:Date.now()},s=Buffer.from(JSON.stringify(r)).toString("base64");return(await (0,d.cookies)()).set("cubent_auth_token",s,{domain:".cubent.dev",httpOnly:!1,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),p.NextResponse.json({success:!0,token:s})}catch(e){return console.error("Error setting cross-domain token:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/set-cross-domain-token/route",pathname:"/api/auth/set-cross-domain-token",filename:"route",bundlePath:"app/api/auth/set-cross-domain-token/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\auth\\set-cross-domain-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:f}=l;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},89259:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,7873,3887,1359,2644],()=>r(10529));module.exports=s})();