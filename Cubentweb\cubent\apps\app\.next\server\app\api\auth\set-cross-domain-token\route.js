(()=>{var e={};e.id=535,e.ids=[535],e.modules={1359:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(12901),i=s(37838);async function n(){s(1447);let{userId:e}=await (0,i.j)();return e?(await (0,r.$)()).users.getUser(e):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10529:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>S,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{POST:()=>d});var i=s(26142),n=s(94327),a=s(34862),o=s(37838),l=s(1359),u=s(26239),c=s(62644);async function d(){try{let{userId:e}=await (0,o.j)();if(!e)return u.NextResponse.json({error:"Not authenticated"},{status:401});let t=await (0,l.N)();if(!t)return u.NextResponse.json({error:"User not found"},{status:404});let s={id:t.id,fullName:t.fullName,firstName:t.firstName,lastName:t.lastName,emailAddresses:t.emailAddresses.map(e=>({emailAddress:e.emailAddress})),imageUrl:t.imageUrl,timestamp:Date.now()},r=Buffer.from(JSON.stringify(s)).toString("base64");return(await (0,c.cookies)()).set("cubent_auth_token",r,{domain:".cubent.dev",httpOnly:!1,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),u.NextResponse.json({success:!0,token:r})}catch(e){return console.error("Error setting cross-domain token:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/set-cross-domain-token/route",pathname:"/api/auth/set-cross-domain-token",filename:"route",bundlePath:"app/api/auth/set-cross-domain-token/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\auth\\set-cross-domain-token\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:f}=p;function S(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,s)=>{"use strict";s.d(t,{$:()=>u});var r=s(8741),i=s(23056),n=s(76315),a=s(97495);let o=new(s(16698)).AsyncLocalStorage;var l=s(60606);let u=async()=>{var e,t;let s;try{let e=await (0,i.TG)(),t=(0,a._b)(e,r.AA.Headers.ClerkRequestData);s=(0,l.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let u=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:s;return(null==u?void 0:u.secretKey)||(null==u?void 0:u.publishableKey)?(0,n.n)(u):(0,n.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},26790:(e,t,s)=>{"use strict";s.d(t,{z:()=>O});var r,i,n,a,o,l,u,c,d,p,h,m,f,S,k,y,g,v,w,b=s(45940);s(92867);var x=s(37081);s(27322),s(6264);var K=s(49530),R=s(57136),j=s(94051),q=class{constructor(){(0,j.VK)(this,n),(0,j.VK)(this,r,"clerk_telemetry_throttler"),(0,j.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,j.S7)(this,n,l))return!1;let t=Date.now(),s=(0,j.jq)(this,n,a).call(this,e),u=(0,j.S7)(this,n,o)?.[s];if(!u){let e={...(0,j.S7)(this,n,o),[s]:t};localStorage.setItem((0,j.S7)(this,r),JSON.stringify(e))}if(u&&t-u>(0,j.S7)(this,i)){let e=(0,j.S7)(this,n,o);delete e[s],localStorage.setItem((0,j.S7)(this,r),JSON.stringify(e))}return!!u}};r=new WeakMap,i=new WeakMap,n=new WeakSet,a=function(e){let{sk:t,pk:s,payload:r,...i}=e,n={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>n[e]))},o=function(){let e=localStorage.getItem((0,j.S7)(this,r));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,j.S7)(this,r)),!1}};var E={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},V=class{constructor(e){(0,j.VK)(this,m),(0,j.VK)(this,u),(0,j.VK)(this,c),(0,j.VK)(this,d,{}),(0,j.VK)(this,p,[]),(0,j.VK)(this,h),(0,j.OV)(this,u,{maxBufferSize:e.maxBufferSize??E.maxBufferSize,samplingRate:e.samplingRate??E.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:E.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,j.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,j.S7)(this,d).clerkVersion="",(0,j.S7)(this,d).sdk=e.sdk,(0,j.S7)(this,d).sdkVersion=e.sdkVersion,(0,j.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,R.q5)(e.publishableKey);t&&((0,j.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,j.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,j.OV)(this,c,new q)}get isEnabled(){return!("development"!==(0,j.S7)(this,d).instanceType||(0,j.S7)(this,u).disabled||"undefined"!=typeof process&&(0,K.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,j.S7)(this,u).debug||"undefined"!=typeof process&&(0,K.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,j.jq)(this,m,w).call(this,e.event,e.payload);(0,j.jq)(this,m,g).call(this,t.event,t),(0,j.jq)(this,m,f).call(this,t,e.eventSamplingRate)&&((0,j.S7)(this,p).push(t),(0,j.jq)(this,m,k).call(this))}};u=new WeakMap,c=new WeakMap,d=new WeakMap,p=new WeakMap,h=new WeakMap,m=new WeakSet,f=function(e,t){return this.isEnabled&&!this.isDebug&&(0,j.jq)(this,m,S).call(this,e,t)},S=function(e,t){let s=Math.random();return!!(s<=(0,j.S7)(this,u).samplingRate&&(void 0===t||s<=t))&&!(0,j.S7)(this,c).isEventThrottled(e)},k=function(){if("undefined"==typeof window)return void(0,j.jq)(this,m,y).call(this);if((0,j.S7)(this,p).length>=(0,j.S7)(this,u).maxBufferSize){(0,j.S7)(this,h)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,j.S7)(this,h)),(0,j.jq)(this,m,y).call(this);return}(0,j.S7)(this,h)||("requestIdleCallback"in window?(0,j.OV)(this,h,requestIdleCallback(()=>{(0,j.jq)(this,m,y).call(this)})):(0,j.OV)(this,h,setTimeout(()=>{(0,j.jq)(this,m,y).call(this)},0)))},y=function(){fetch(new URL("/v1/event",(0,j.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,j.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,j.OV)(this,p,[])}).catch(()=>void 0)},g=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},v=function(){let e={name:(0,j.S7)(this,d).sdk,version:(0,j.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},w=function(e,t){let s=(0,j.jq)(this,m,v).call(this);return{event:e,cv:(0,j.S7)(this,d).clerkVersion??"",it:(0,j.S7)(this,d).instanceType??"",sdk:s.name,sdkv:s.version,...(0,j.S7)(this,d).publishableKey?{pk:(0,j.S7)(this,d).publishableKey}:{},...(0,j.S7)(this,d).secretKey?{sk:(0,j.S7)(this,d).secretKey}:{},payload:t}};function O(e){let t={...e},s=(0,b.y3)(t),r=(0,b.Bs)({options:t,apiClient:s}),i=new V({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}}(0,x.C)(b.nr)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76315:(e,t,s)=>{"use strict";s.d(t,{n:()=>a});var r=s(26790),i=s(54726);let n={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},a=e=>(0,r.z)({...n,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},89259:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,903,7838,2644],()=>s(10529));module.exports=r})();