(()=>{var e={};e.id=6599,e.ids=[6599],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39112:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>A,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>q});var s={};t.r(s),t.d(s,{GET:()=>d,PATCH:()=>l});var i=t(26142),o=t(94327),a=t(34862),n=t(37838),u=t(1359),p=t(18815),c=t(26239);async function d(){try{let{userId:e}=await (0,n.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let r=await (0,u.N)();if(!r)return c.NextResponse.json({error:"User not found"},{status:404});let t=await p.database.userProfile.findUnique({where:{userId:e}}),s=await p.database.extensionSession.findMany({where:{userId:e,isActive:!0},orderBy:{lastActiveAt:"desc"}});await p.database.usageAnalytics.findMany({where:{userId:e},orderBy:{createdAt:"desc"},take:1}),t||(t=await p.database.userProfile.create({data:{userId:e,email:r.emailAddresses[0]?.emailAddress||"",name:r.fullName||"",subscriptionTier:"FREE",subscriptionStatus:"ACTIVE",termsAccepted:!1}}));let i=await p.database.usageAnalytics.aggregate({where:{userId:e},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),o={user:{id:e,name:r.fullName,email:r.emailAddresses[0]?.emailAddress,imageUrl:r.imageUrl},profile:{subscriptionTier:t.subscriptionTier,subscriptionStatus:t.subscriptionStatus,termsAccepted:t.termsAccepted,extensionEnabled:t.extensionEnabled,settings:t.settings},usage:{tokensUsed:i._sum.tokensUsed||0,requestsMade:i._sum.requestsMade||0,costAccrued:i._sum.costAccrued||0},extensionSessions:s.length,lastActiveSession:s[0]?.lastActiveAt||null};return c.NextResponse.json(o)}catch(e){return console.error("Extension profile error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let{userId:r}=await (0,n.j)();if(!r)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{settings:t,extensionEnabled:s}=await e.json(),i=await p.database.userProfile.update({where:{userId:r},data:{...t&&{settings:t},..."boolean"==typeof s&&{extensionEnabled:s},updatedAt:new Date}});return c.NextResponse.json({success:!0,profile:i})}catch(e){return console.error("Extension profile update error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/extension/profile/route",pathname:"/api/extension/profile",filename:"route",bundlePath:"app/api/extension/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\profile\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:q,serverHooks:m}=x;function A(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:q})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,7873,3887,5480,1359,864],()=>t(39112));module.exports=s})();