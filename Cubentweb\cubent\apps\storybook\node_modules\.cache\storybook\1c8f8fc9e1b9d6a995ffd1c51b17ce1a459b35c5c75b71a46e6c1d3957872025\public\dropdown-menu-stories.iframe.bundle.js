"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["dropdown-menu-stories"],{

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-plus.js":
/*!**************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-plus.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ CirclePlus)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "M8 12h8", key: "1wcyev" }],
  ["path", { d: "M12 8v8", key: "napkw2" }]
];
const CirclePlus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("circle-plus", __iconNode);


//# sourceMappingURL=circle-plus.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ Mail)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [
  ["path", { d: "m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7", key: "132q7q" }],
  ["rect", { x: "2", y: "4", width: "20", height: "16", rx: "2", key: "izxlao" }]
];
const Mail = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("mail", __iconNode);


//# sourceMappingURL=mail.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ Search)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [
  ["path", { d: "m21 21-4.34-4.34", key: "14j7rj" }],
  ["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }]
];
const Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("search", __iconNode);


//# sourceMappingURL=search.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user-plus.js":
/*!************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user-plus.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ UserPlus)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [
  ["path", { d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2", key: "1yyitq" }],
  ["circle", { cx: "9", cy: "7", r: "4", key: "nufk8" }],
  ["line", { x1: "19", x2: "19", y1: "8", y2: "14", key: "1bvyxn" }],
  ["line", { x1: "22", x2: "16", y1: "11", y2: "11", key: "1shjgl" }]
];
const UserPlus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("user-plus", __iconNode);


//# sourceMappingURL=user-plus.js.map


/***/ }),

/***/ "./stories/dropdown-menu.stories.tsx":
/*!*******************************************!*\
  !*** ./stories/dropdown-menu.stories.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   WithCheckboxes: () => (/* binding */ WithCheckboxes),
/* harmony export */   WithRadioItems: () => (/* binding */ WithRadioItems),
/* harmony export */   WithShortcuts: () => (/* binding */ WithShortcuts),
/* harmony export */   WithSubmenus: () => (/* binding */ WithSubmenus),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Plus,PlusCircle,Search,UserPlus!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js");
/* harmony import */ var _barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Plus,PlusCircle,Search,UserPlus!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js");
/* harmony import */ var _barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Plus,PlusCircle,Search,UserPlus!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user-plus.js");
/* harmony import */ var _barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Plus,PlusCircle,Search,UserPlus!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js");
/* harmony import */ var _barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Plus,PlusCircle,Search,UserPlus!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-plus.js");
/* harmony import */ var _repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/dropdown-menu */ "../../packages/design-system/components/ui/dropdown-menu.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");




/**
 * Displays a menu to the user — such as a set of actions or functions —
 * triggered by a button.
 */
const meta = {
  title: 'ui/DropdownMenu',
  component: _repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenu,
  tags: ['autodocs'],
  argTypes: {},
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuTrigger, {
      children: "Open"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 33,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuContent, {
      className: "w-44",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuLabel, {
        children: "My Account"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 35,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuSeparator, {}, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 36,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
        children: "Profile"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 37,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
        children: "Billing"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 38,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
        children: "Team"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 39,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
        children: "Subscription"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 40,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 34,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
    lineNumber: 32,
    columnNumber: 5
  }, undefined),
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: "Displays a menu to the user \u2014 such as a set of actions or functions \u2014\r\ntriggered by a button."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the dropdown menu.
 */
const Default = {};
/**
 * A dropdown menu with shortcuts.
 */
const WithShortcuts = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuTrigger, {
      children: "Open"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 64,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuContent, {
      className: "w-44",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuLabel, {
        children: "Controls"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 66,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
        children: ["Back", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuShortcut, {
          children: "⌘["
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 69,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 67,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
        disabled: true,
        children: ["Forward", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuShortcut, {
          children: "⌘]"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 73,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 71,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 65,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
    lineNumber: 63,
    columnNumber: 5
  }, undefined)
};
/**
 * A dropdown menu with submenus.
 */
const WithSubmenus = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuTrigger, {
      children: "Open"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 86,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuContent, {
      className: "w-44",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], {
          className: "mr-2 size-4"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 89,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
          children: "Search"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 90,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 88,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuSeparator, {}, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 92,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuGroup, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], {
            className: "mr-2 size-4"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
            lineNumber: 95,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
            children: "New Team"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
            lineNumber: 96,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuShortcut, {
            children: "⌘+T"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
            lineNumber: 97,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 94,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuSub, {
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuSubTrigger, {
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {
              className: "mr-2 size-4"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
              lineNumber: 101,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
              children: "Invite users"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
              lineNumber: 102,
              columnNumber: 15
            }, undefined)]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
            lineNumber: 100,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuPortal, {
            children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuSubContent, {
              children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
                children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], {
                  className: "mr-2 size-4"
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
                  lineNumber: 107,
                  columnNumber: 19
                }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                  children: "Email"
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
                  lineNumber: 108,
                  columnNumber: 19
                }, undefined)]
              }, void 0, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
                lineNumber: 106,
                columnNumber: 17
              }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuSeparator, {}, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
                lineNumber: 110,
                columnNumber: 17
              }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {
                children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Plus_PlusCircle_Search_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], {
                  className: "mr-2 size-4"
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
                  lineNumber: 112,
                  columnNumber: 19
                }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                  children: "More..."
                }, void 0, false, {
                  fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
                  lineNumber: 113,
                  columnNumber: 19
                }, undefined)]
              }, void 0, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
                lineNumber: 111,
                columnNumber: 17
              }, undefined)]
            }, void 0, true, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
              lineNumber: 105,
              columnNumber: 15
            }, undefined)
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
            lineNumber: 104,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 99,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 93,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 87,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
    lineNumber: 85,
    columnNumber: 5
  }, undefined)
};
/**
 * A dropdown menu with radio items.
 */
const WithRadioItems = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuTrigger, {
      children: "Open"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 130,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuContent, {
      className: "w-44",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuLabel, {
        inset: true,
        children: "Status"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 132,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuRadioGroup, {
        value: "warning",
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuRadioItem, {
          value: "info",
          children: "Info"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 134,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuRadioItem, {
          value: "warning",
          children: "Warning"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 135,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuRadioItem, {
          value: "error",
          children: "Error"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 136,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 133,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 131,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
    lineNumber: 129,
    columnNumber: 5
  }, undefined)
};
/**
 * A dropdown menu with checkboxes.
 */
const WithCheckboxes = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuTrigger, {
      children: "Open"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 149,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuContent, {
      className: "w-44",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuCheckboxItem, {
        checked: true,
        children: ["Autosave", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuShortcut, {
          children: "⌘S"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
          lineNumber: 153,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 151,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuCheckboxItem, {
        children: "Show Comments"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
        lineNumber: 155,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
      lineNumber: 150,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\storybook\\stories\\dropdown-menu.stories.tsx",
    lineNumber: 148,
    columnNumber: 5
  }, undefined)
};
;
const __namedExportsOrder = ["Default", "WithShortcuts", "WithSubmenus", "WithRadioItems", "WithCheckboxes"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the dropdown menu.",
      ...Default.parameters?.docs?.description
    }
  }
};
WithShortcuts.parameters = {
  ...WithShortcuts.parameters,
  docs: {
    ...WithShortcuts.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <DropdownMenu {...args}>\r\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-44\">\r\n        <DropdownMenuLabel>Controls</DropdownMenuLabel>\r\n        <DropdownMenuItem>\r\n          Back\r\n          <DropdownMenuShortcut>\u2318[</DropdownMenuShortcut>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem disabled>\r\n          Forward\r\n          <DropdownMenuShortcut>\u2318]</DropdownMenuShortcut>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\n}",
      ...WithShortcuts.parameters?.docs?.source
    },
    description: {
      story: "A dropdown menu with shortcuts.",
      ...WithShortcuts.parameters?.docs?.description
    }
  }
};
WithSubmenus.parameters = {
  ...WithSubmenus.parameters,
  docs: {
    ...WithSubmenus.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <DropdownMenu {...args}>\r\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-44\">\r\n        <DropdownMenuItem>\r\n          <Search className=\"mr-2 size-4\" />\r\n          <span>Search</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuGroup>\r\n          <DropdownMenuItem>\r\n            <Plus className=\"mr-2 size-4\" />\r\n            <span>New Team</span>\r\n            <DropdownMenuShortcut>\u2318+T</DropdownMenuShortcut>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSub>\r\n            <DropdownMenuSubTrigger>\r\n              <UserPlus className=\"mr-2 size-4\" />\r\n              <span>Invite users</span>\r\n            </DropdownMenuSubTrigger>\r\n            <DropdownMenuPortal>\r\n              <DropdownMenuSubContent>\r\n                <DropdownMenuItem>\r\n                  <Mail className=\"mr-2 size-4\" />\r\n                  <span>Email</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuSeparator />\r\n                <DropdownMenuItem>\r\n                  <PlusCircle className=\"mr-2 size-4\" />\r\n                  <span>More...</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuSubContent>\r\n            </DropdownMenuPortal>\r\n          </DropdownMenuSub>\r\n        </DropdownMenuGroup>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\n}",
      ...WithSubmenus.parameters?.docs?.source
    },
    description: {
      story: "A dropdown menu with submenus.",
      ...WithSubmenus.parameters?.docs?.description
    }
  }
};
WithRadioItems.parameters = {
  ...WithRadioItems.parameters,
  docs: {
    ...WithRadioItems.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <DropdownMenu {...args}>\r\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-44\">\r\n        <DropdownMenuLabel inset>Status</DropdownMenuLabel>\r\n        <DropdownMenuRadioGroup value=\"warning\">\r\n          <DropdownMenuRadioItem value=\"info\">Info</DropdownMenuRadioItem>\r\n          <DropdownMenuRadioItem value=\"warning\">Warning</DropdownMenuRadioItem>\r\n          <DropdownMenuRadioItem value=\"error\">Error</DropdownMenuRadioItem>\r\n        </DropdownMenuRadioGroup>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\n}",
      ...WithRadioItems.parameters?.docs?.source
    },
    description: {
      story: "A dropdown menu with radio items.",
      ...WithRadioItems.parameters?.docs?.description
    }
  }
};
WithCheckboxes.parameters = {
  ...WithCheckboxes.parameters,
  docs: {
    ...WithCheckboxes.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <DropdownMenu {...args}>\r\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-44\">\r\n        <DropdownMenuCheckboxItem checked>\r\n          Autosave\r\n          <DropdownMenuShortcut>\u2318S</DropdownMenuShortcut>\r\n        </DropdownMenuCheckboxItem>\r\n        <DropdownMenuCheckboxItem>Show Comments</DropdownMenuCheckboxItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\n}",
      ...WithCheckboxes.parameters?.docs?.source
    },
    description: {
      story: "A dropdown menu with checkboxes.",
      ...WithCheckboxes.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=dropdown-menu-stories.iframe.bundle.js.map