(()=>{var e={};e.id=1017,e.ids=[1017],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34830:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>x,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>g,POST:()=>d});var n=r(26142),i=r(94327),a=r(34862),o=r(37838),u=r(18815),c=r(26239),l=r(25);let p=l.z.object({action:l.z.enum(["push","pull","merge"]),settings:l.z.record(l.z.any()).optional(),lastSyncTimestamp:l.z.string().datetime().optional(),conflictResolution:l.z.enum(["client","server","merge"]).default("merge")});async function d(e){try{let{userId:t}=await (0,o.j)();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=p.safeParse(r);if(!s.success)return c.NextResponse.json({error:"Invalid sync request",details:s.error.errors},{status:400});let{action:n,settings:i,lastSyncTimestamp:a,conflictResolution:l}=s.data,d=await u.database.user.findUnique({where:{clerkId:t},select:{id:!0,extensionSettings:!0,preferences:!0,lastSettingsSync:!0,updatedAt:!0}});if(!d)return c.NextResponse.json({error:"User not found"},{status:404});let g=new Date,x={};switch(n){case"pull":x={action:"pull",settings:{extensionSettings:d.extensionSettings||{},preferences:d.preferences||{}},timestamp:d.lastSettingsSync||d.updatedAt,serverTimestamp:g};break;case"push":if(!i)return c.NextResponse.json({error:"Settings required for push action"},{status:400});await u.database.user.update({where:{id:d.id},data:{extensionSettings:i.extensionSettings||d.extensionSettings,preferences:i.preferences||d.preferences,lastSettingsSync:g}}),x={action:"push",success:!0,timestamp:g,message:"Settings pushed to server successfully"};break;case"merge":if(!i)return c.NextResponse.json({error:"Settings required for merge action"},{status:400});let f=d.lastSettingsSync||d.updatedAt,h=new Date(a||0),y={},S=!1,m=f>h;m&&"client"===l?y=i:m&&"server"===l?y={extensionSettings:d.extensionSettings||{},preferences:d.preferences||{}}:(y=function(e,t,r){let s={extensionSettings:{...e.extensionSettings},preferences:{...e.preferences}};return t.extensionSettings&&Object.keys(t.extensionSettings).forEach(e=>{let n=t.extensionSettings[e],i=s.extensionSettings[e];void 0===i?s.extensionSettings[e]=n:n===i||r?Array.isArray(n)&&Array.isArray(i)?s.extensionSettings[e]=[...new Set([...i,...n])]:"object"==typeof n&&"object"==typeof i&&null!==n&&null!==i&&(s.extensionSettings[e]={...i,...n}):s.extensionSettings[e]=n}),t.preferences&&Object.keys(t.preferences).forEach(e=>{let n=t.preferences[e],i=s.preferences[e];void 0===i?s.preferences[e]=n:n===i||r?Array.isArray(n)&&Array.isArray(i)?s.preferences[e]=[...new Set([...i,...n])]:"object"==typeof n&&"object"==typeof i&&null!==n&&null!==i&&(s.preferences[e]={...i,...n}):s.preferences[e]=n}),s}({extensionSettings:d.extensionSettings||{},preferences:d.preferences||{}},i,m),S=m),await u.database.user.update({where:{id:d.id},data:{extensionSettings:y.extensionSettings,preferences:y.preferences,lastSettingsSync:g}}),x={action:"merge",settings:y,hasConflicts:S,conflictResolution:l,timestamp:g,message:S?`Settings merged with conflicts resolved using ${l} strategy`:"Settings merged successfully"}}return c.NextResponse.json(x)}catch(e){return console.error("Settings sync error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(){try{let{userId:e}=await (0,o.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e},select:{lastSettingsSync:!0,updatedAt:!0,extensionSettings:!0,preferences:!0}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});let r=new Date,s=t.lastSettingsSync||t.updatedAt,n=r.getTime()-s.getTime(),i=function(e){let t=JSON.stringify(e,Object.keys(e).sort()),r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e),r&=r;return r.toString(36)}({extensionSettings:t.extensionSettings||{},preferences:t.preferences||{}});return c.NextResponse.json({syncStatus:{lastSync:s.toISOString(),timeSinceSync:Math.round(n/1e3),isStale:n>3e5},settings:{extensionSettings:t.extensionSettings||{},preferences:t.preferences||{},hash:i},serverTimestamp:r.toISOString(),syncOptions:{supportedActions:["push","pull","merge"],conflictResolutions:["client","server","merge"],autoSyncInterval:300}})}catch(e){return console.error("Sync status error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/sync/route",pathname:"/api/extension/sync",filename:"route",bundlePath:"app/api/extension/sync/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\sync\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:y}=x;function S(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>E});var s=r(8741),n=r(62923),i=r(54726),a=r(87553),o=r(3680);let u=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?u(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,u(t)])),null,2)).join(", "),l=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,i;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((i=e,`[clerk debug end: ${i}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},p=(e,t)=>(...r)=>{let s=("string"==typeof e?l(e,c):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var d=r(74365),g=r(37081),x=r(27322),f=r(6264);function h(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return x.r0.stringify(r,{pad:!1})}async function y(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,x.hJ)(r.algorithm);if(!n)return{errors:[new f.xy(`Unsupported algorithm ${r.algorithm}`)]};let i=await (0,x.Fh)(t,n,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=h(a),u=h(e),c=`${o}.${u}`;try{let e=await x.fA.crypto.subtle.sign(n,i,s.encode(c));return{data:`${c}.${x.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new f.xy(e?.message)]}}}(0,g.C)(x.J0);var S=(0,g.R)(x.iU);(0,g.C)(y),(0,g.C)(x.nk);var m=r(97495),w=r(60606);function A(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,a,o;let u,c=(0,m.NE)(e,"AuthStatus"),l=(0,m.NE)(e,"AuthToken"),p=(0,m.NE)(e,"AuthMessage"),d=(0,m.NE)(e,"AuthReason"),g=(0,m.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:c,authMessage:p,authReason:d});let x=(0,m._b)(e,s.AA.Headers.ClerkRequestData),f=(0,w.Kk)(x),h={secretKey:(null==r?void 0:r.secretKey)||f.secretKey||i.rB,publishableKey:f.publishableKey||i.At,apiUrl:i.H$,apiVersion:i.mG,authStatus:c,authMessage:p,authReason:d,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",h),c&&c===s.TD.SignedIn){(0,w._l)(l,h.secretKey,g);let e=S(l);null==(o=r.logger)||o.debug("jwt",e.raw),u=(0,s.Z5)(h,e.raw.text,e.payload)}else u=(0,s.wI)(h);return t&&"pending"===u.sessionStatus&&(u=(0,s.wI)(h,u.sessionStatus)),u}var b=r(68478);let v=({debugLoggerName:e,noAuthStatusMessage:t})=>p(e,e=>async(n,i)=>{if((0,a.zz)((0,m._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,m.Zd)(n)){d.M&&(0,w.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,w.$K)(n,t)}return A(n,{...i,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>p(e,e=>(r,n)=>((0,a.zz)((0,m._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,w.$K)(r,t),A(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,b.AG)()});let j={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},q=e=>{var t,r;return!!e.headers.get(j.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(j.Headers.NextAction))},U=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||N(e)||R(e)},N=e=>!!e.headers.get(j.Headers.NextUrl)&&!q(e)||k(),k=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},R=e=>!!e.headers.get(j.Headers.NextjsData);var T=r(23056);let E=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,T.TG)(),a=async()=>{if(d.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await v({debugLoggerName:"auth()",noAuthStatusMessage:(0,b.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),u=(0,m.NE)(t,"ClerkUrl"),c=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,s.tl)(t),c=a.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||a.cookies.get(s.AA.Cookies.DevBrowser),l=(0,m._b)(t,s.AA.Headers.ClerkRequestData),p=(0,w.Kk)(l);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:c,baseUrl:a.clerkUrl.toString(),publishableKey:p.publishableKey||i.At,signInUrl:p.signInUrl||i.qW,signUpUrl:p.signUpUrl||i.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==u?void 0:u.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=c(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=c(e);return t.redirectToSignUp({returnBackUrl:r})}})};E.protect=async(...e)=>{r(1447);let t=await (0,T.TG)(),s=await E();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:i}=e;return async(...e)=>{var a,o,u,c,l,p;let d=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],g=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),x=(null==(l=e[0])?void 0:l.unauthorizedUrl)||(null==(p=e[1])?void 0:p.unauthorizedUrl),f=()=>x?s(x):n();return"pending"!==r.sessionStatus&&r.userId?d?"function"==typeof d?d(r.has)?r:f():r.has(d)?r:f():r:g?s(g):U(i)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887,5480,864],()=>r(34830));module.exports=s})();