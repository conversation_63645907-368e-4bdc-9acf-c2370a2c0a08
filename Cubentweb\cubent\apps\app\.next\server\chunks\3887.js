"use strict";exports.id=3887,exports.ids=[3887],exports.modules={23056:(e,t,r)=>{r.d(t,{Sz:()=>i,TG:()=>n});var s=r(26239);let i=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),s=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||s||i};async function n(){try{let{headers:e}=await r.e(2644).then(r.bind(r,62644)),t=await e();return new s.NextRequest("https://placeholder.com",{headers:t})}catch(e){if(e&&i(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}},37081:(e,t,r)=>{function s(e){return async(...t)=>{let{data:r,errors:s}=await e(...t);if(s)throw s[0];return r}}function i(e){return(...t)=>{let{data:r,errors:s}=e(...t);if(s)throw s[0];return r}}r.d(t,{C:()=>s,R:()=>i})},60606:(e,t,r)=>{r.d(t,{$K:()=>ed,_l:()=>ef,Kk:()=>ep}),r(8741),r(44401),r(94051);var s=r(21757);r(26239);var i,n,a,o,l,c,h,d=r(65931),f=Object.defineProperty,u=(e,t,r)=>t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,p=(null==(i="undefined"!=typeof globalThis?globalThis:void 0)?void 0:i.crypto)||(null==(n="undefined"!=typeof global?global:void 0)?void 0:n.crypto)||(null==(a="undefined"!=typeof window?window:void 0)?void 0:a.crypto)||(null==(o="undefined"!=typeof self?self:void 0)?void 0:o.crypto)||(null==(c=null==(l="undefined"!=typeof frames?frames:void 0)?void 0:l[0])?void 0:c.crypto);h=p?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(p.getRandomValues(new Uint32Array(1))[0]);return new _(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let s=0,i;s<e;s+=4){let e=r(0x100000000*(i||Math.random()));i=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new _(t,e)};var y=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},_=class extends y{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let s=0;s<e;s+=1)t[s>>>2]|=r[s]<<24-s%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=g){return e.stringify(this)}concat(e){let t=this.words,r=e.words,s=this.sigBytes,i=e.sigBytes;if(this.clamp(),s%4)for(let e=0;e<i;e+=1){let i=r[e>>>2]>>>24-e%4*8&255;t[s+e>>>2]|=i<<24-(s+e)%4*8}else for(let e=0;e<i;e+=4)t[s+e>>>2]=r[e>>>2];return this.sigBytes+=i,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>u(e,"symbol"!=typeof t?t+"":t,r))(_,"random",h);var g={stringify(e){let{words:t,sigBytes:r}=e,s=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;s.push((r>>>4).toString(16)),s.push((15&r).toString(16))}return s.join("")},parse(e){let t=e.length,r=[];for(let s=0;s<t;s+=2)r[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new _(r,t/2)}},m={stringify(e){let{words:t,sigBytes:r}=e,s=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;s.push(String.fromCharCode(r))}return s.join("")},parse(e){let t=e.length,r=[];for(let s=0;s<t;s+=1)r[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new _(r,t)}},w={stringify(e){try{return decodeURIComponent(escape(m.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>m.parse(unescape(encodeURIComponent(e)))},x=class extends y{constructor(){super(),this._minBufferSize=0}reset(){this._data=new _,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=w.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:s}=this,i=r.words,n=r.sigBytes,a=n/(4*s),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*s,l=Math.min(4*o,n);if(o){for(let e=0;e<o;e+=s)this._doProcessBlock(i,e);t=i.splice(0,o),r.sigBytes-=l}return new _(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},k=class extends x{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new y,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new v(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},v=class extends y{constructor(e,t){super();let r=new e;this._hasher=r;let s=t;"string"==typeof s&&(s=w.parse(s));let i=r.blockSize,n=4*i;s.sigBytes>n&&(s=r.finalize(t)),s.clamp();let a=s.clone();this._oKey=a;let o=s.clone();this._iKey=o;let l=a.words,c=o.words;for(let e=0;e<i;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;a.sigBytes=n,o.sigBytes=n,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},B=(e,t,r)=>{let s=[],i=0;for(let n=0;n<t;n+=1)if(n%4){let t=r[e.charCodeAt(n-1)]<<n%4*2|r[e.charCodeAt(n)]>>>6-n%4*2;s[i>>>2]|=t<<24-i%4*8,i+=1}return _.create(s,i)},b={stringify(e){let{words:t,sigBytes:r}=e,s=this._map;e.clamp();let i=[];for(let e=0;e<r;e+=3){let n=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)i.push(s.charAt(n>>>6*(3-t)&63))}let n=s.charAt(64);if(n)for(;i.length%4;)i.push(n);return i.join("")},parse(e){let t=e.length,r=this._map,s=this._reverseMap;if(!s){this._reverseMap=[],s=this._reverseMap;for(let e=0;e<r.length;e+=1)s[r.charCodeAt(e)]=e}let i=r.charAt(64);if(i){let r=e.indexOf(i);-1!==r&&(t=r)}return B(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},z=[];for(let e=0;e<64;e+=1)z[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var S=(e,t,r,s,i,n,a)=>{let o=e+(t&r|~t&s)+i+a;return(o<<n|o>>>32-n)+t},M=(e,t,r,s,i,n,a)=>{let o=e+(t&s|r&~s)+i+a;return(o<<n|o>>>32-n)+t},C=(e,t,r,s,i,n,a)=>{let o=e+(t^r^s)+i+a;return(o<<n|o>>>32-n)+t},O=(e,t,r,s,i,n,a)=>{let o=e+(r^(t|~s))+i+a;return(o<<n|o>>>32-n)+t},R=class extends k{_doReset(){this._hash=new _([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let s=t+r,i=e[s];e[s]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00}let r=this._hash.words,s=e[t+0],i=e[t+1],n=e[t+2],a=e[t+3],o=e[t+4],l=e[t+5],c=e[t+6],h=e[t+7],d=e[t+8],f=e[t+9],u=e[t+10],p=e[t+11],y=e[t+12],_=e[t+13],g=e[t+14],m=e[t+15],w=r[0],x=r[1],k=r[2],v=r[3];w=S(w,x,k,v,s,7,z[0]),v=S(v,w,x,k,i,12,z[1]),k=S(k,v,w,x,n,17,z[2]),x=S(x,k,v,w,a,22,z[3]),w=S(w,x,k,v,o,7,z[4]),v=S(v,w,x,k,l,12,z[5]),k=S(k,v,w,x,c,17,z[6]),x=S(x,k,v,w,h,22,z[7]),w=S(w,x,k,v,d,7,z[8]),v=S(v,w,x,k,f,12,z[9]),k=S(k,v,w,x,u,17,z[10]),x=S(x,k,v,w,p,22,z[11]),w=S(w,x,k,v,y,7,z[12]),v=S(v,w,x,k,_,12,z[13]),k=S(k,v,w,x,g,17,z[14]),x=S(x,k,v,w,m,22,z[15]),w=M(w,x,k,v,i,5,z[16]),v=M(v,w,x,k,c,9,z[17]),k=M(k,v,w,x,p,14,z[18]),x=M(x,k,v,w,s,20,z[19]),w=M(w,x,k,v,l,5,z[20]),v=M(v,w,x,k,u,9,z[21]),k=M(k,v,w,x,m,14,z[22]),x=M(x,k,v,w,o,20,z[23]),w=M(w,x,k,v,f,5,z[24]),v=M(v,w,x,k,g,9,z[25]),k=M(k,v,w,x,a,14,z[26]),x=M(x,k,v,w,d,20,z[27]),w=M(w,x,k,v,_,5,z[28]),v=M(v,w,x,k,n,9,z[29]),k=M(k,v,w,x,h,14,z[30]),x=M(x,k,v,w,y,20,z[31]),w=C(w,x,k,v,l,4,z[32]),v=C(v,w,x,k,d,11,z[33]),k=C(k,v,w,x,p,16,z[34]),x=C(x,k,v,w,g,23,z[35]),w=C(w,x,k,v,i,4,z[36]),v=C(v,w,x,k,o,11,z[37]),k=C(k,v,w,x,h,16,z[38]),x=C(x,k,v,w,u,23,z[39]),w=C(w,x,k,v,_,4,z[40]),v=C(v,w,x,k,s,11,z[41]),k=C(k,v,w,x,a,16,z[42]),x=C(x,k,v,w,c,23,z[43]),w=C(w,x,k,v,f,4,z[44]),v=C(v,w,x,k,y,11,z[45]),k=C(k,v,w,x,m,16,z[46]),x=C(x,k,v,w,n,23,z[47]),w=O(w,x,k,v,s,6,z[48]),v=O(v,w,x,k,h,10,z[49]),k=O(k,v,w,x,g,15,z[50]),x=O(x,k,v,w,l,21,z[51]),w=O(w,x,k,v,y,6,z[52]),v=O(v,w,x,k,a,10,z[53]),k=O(k,v,w,x,u,15,z[54]),x=O(x,k,v,w,i,21,z[55]),w=O(w,x,k,v,d,6,z[56]),v=O(v,w,x,k,m,10,z[57]),k=O(k,v,w,x,c,15,z[58]),x=O(x,k,v,w,_,21,z[59]),w=O(w,x,k,v,o,6,z[60]),v=O(v,w,x,k,p,10,z[61]),k=O(k,v,w,x,n,15,z[62]),x=O(x,k,v,w,f,21,z[63]),r[0]=r[0]+w|0,r[1]=r[1]+x|0,r[2]=r[2]+k|0,r[3]=r[3]+v|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;t[s>>>5]|=128<<24-s%32;let i=Math.floor(r/0x100000000);t[(s+64>>>9<<4)+15]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,t[(s+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let n=this._hash,a=n.words;for(let e=0;e<4;e+=1){let t=a[e];a[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return n}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};k._createHelper(R),k._createHmacHelper(R);var E=class extends y{constructor(e){super(),this.cfg=Object.assign(new y,{keySize:4,hasher:R,iterations:1},e)}compute(e,t){let r,{cfg:s}=this,i=s.hasher.create(),n=_.create(),a=n.words,{keySize:o,iterations:l}=s;for(;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(let e=1;e<l;e+=1)r=i.finalize(r),i.reset();n.concat(r)}return n.sigBytes=4*o,n}},j=class extends x{constructor(e,t,r){super(),this.cfg=Object.assign(new y,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?P:K;return{encrypt:(r,s,i)=>t(s).encrypt(e,r,s,i),decrypt:(r,s,i)=>t(s).decrypt(e,r,s,i)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};j._ENC_XFORM_MODE=1,j._DEC_XFORM_MODE=2,j.keySize=4,j.ivSize=4;var A=class extends y{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function F(e,t,r){let s,i=this._iv;i?(s=i,this._iv=void 0):s=this._prevBlock;for(let i=0;i<r;i+=1)e[t+i]^=s[i]}var D=class extends A{};D.Encryptor=class extends D{processBlock(e,t){let r=this._cipher,{blockSize:s}=r;F.call(this,e,t,s),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+s)}},D.Decryptor=class extends D{processBlock(e,t){let r=this._cipher,{blockSize:s}=r,i=e.slice(t,t+s);r.decryptBlock(e,t),F.call(this,e,t,s),this._prevBlock=i}};var U={pad(e,t){let r=4*t,s=r-e.sigBytes%r,i=s<<24|s<<16|s<<8|s,n=[];for(let e=0;e<s;e+=4)n.push(i);let a=_.create(n,s);e.concat(a)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},I=class extends j{constructor(e,t,r){super(e,t,Object.assign({mode:D,padding:U},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:s}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=s.createEncryptor:(e=s.createDecryptor,this._minBufferSize=1),this._mode=e.call(s,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},H=class extends y{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},K=class extends y{static encrypt(e,t,r,s){let i=Object.assign(new y,this.cfg,s),n=e.createEncryptor(r,i),a=n.finalize(t),o=n.cfg;return H.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:n.blockSize,formatter:i.format})}static decrypt(e,t,r,s){let i=t,n=Object.assign(new y,this.cfg,s);return i=this._parse(i,n.format),e.createDecryptor(r,n).finalize(i.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};K.cfg=Object.assign(new y,{format:{stringify(e){let t,{ciphertext:r,salt:s}=e;return(s?_.create([0x53616c74,0x65645f5f]).concat(s).concat(r):r).toString(b)},parse(e){let t,r=b.parse(e),s=r.words;return 0x53616c74===s[0]&&0x65645f5f===s[1]&&(t=_.create(s.slice(2,4)),s.splice(0,4),r.sigBytes-=16),H.create({ciphertext:r,salt:t})}}});var P=class extends K{static encrypt(e,t,r,s){let i=Object.assign(new y,this.cfg,s),n=i.kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=n.iv;let a=K.encrypt.call(this,e,t,n.key,i);return a.mixIn(n),a}static decrypt(e,t,r,s){let i=t,n=Object.assign(new y,this.cfg,s);i=this._parse(i,n.format);let a=n.kdf.execute(r,e.keySize,e.ivSize,i.salt,n.hasher);return n.iv=a.iv,K.decrypt.call(this,e,i,a.key,n)}};P.cfg=Object.assign(K.cfg,{kdf:{execute(e,t,r,s,i){let n,a=s;a||(a=_.random(8)),n=i?E.create({keySize:t+r,hasher:i}).compute(e,a):E.create({keySize:t+r}).compute(e,a);let o=_.create(n.words.slice(t),4*r);return n.sigBytes=4*t,H.create({key:n,iv:o,salt:a})}}});var N=[],q=[],X=[],T=[],J=[],L=[],$=[],G=[],V=[],Y=[],Z=[];for(let e=0;e<256;e+=1)e<128?Z[e]=e<<1:Z[e]=e<<1^283;var Q=0,W=0;for(let e=0;e<256;e+=1){let e=W^W<<1^W<<2^W<<3^W<<4;e=e>>>8^255&e^99,N[Q]=e,q[e]=Q;let t=Z[Q],r=Z[t],s=Z[r],i=257*Z[e]^0x1010100*e;X[Q]=i<<24|i>>>8,T[Q]=i<<16|i>>>16,J[Q]=i<<8|i>>>24,L[Q]=i,i=0x1010101*s^65537*r^257*t^0x1010100*Q,$[e]=i<<24|i>>>8,G[e]=i<<16|i>>>16,V[e]=i<<8|i>>>24,Y[e]=i,Q?(Q=t^Z[Z[Z[s^t]]],W^=Z[Z[W]]):Q=W=1}var ee=[0,1,2,4,8,16,32,64,128,27,54],et=class extends I{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,s=t.sigBytes/4;this._nRounds=s+6;let i=(this._nRounds+1)*4;this._keySchedule=[];let n=this._keySchedule;for(let t=0;t<i;t+=1)t<s?n[t]=r[t]:(e=n[t-1],t%s?s>6&&t%s==4&&(e=N[e>>>24]<<24|N[e>>>16&255]<<16|N[e>>>8&255]<<8|N[255&e]):e=(N[(e=e<<8|e>>>24)>>>24]<<24|N[e>>>16&255]<<16|N[e>>>8&255]<<8|N[255&e])^ee[t/s|0]<<24,n[t]=n[t-s]^e);this._invKeySchedule=[];let a=this._invKeySchedule;for(let t=0;t<i;t+=1){let r=i-t;e=t%4?n[r]:n[r-4],t<4||r<=4?a[t]=e:a[t]=$[N[e>>>24]]^G[N[e>>>16&255]]^V[N[e>>>8&255]]^Y[N[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,X,T,J,L,N)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,$,G,V,Y,q),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,s,i,n,a,o){let l=this._nRounds,c=e[t]^r[0],h=e[t+1]^r[1],d=e[t+2]^r[2],f=e[t+3]^r[3],u=4;for(let e=1;e<l;e+=1){let e=s[c>>>24]^i[h>>>16&255]^n[d>>>8&255]^a[255&f]^r[u];u+=1;let t=s[h>>>24]^i[d>>>16&255]^n[f>>>8&255]^a[255&c]^r[u];u+=1;let o=s[d>>>24]^i[f>>>16&255]^n[c>>>8&255]^a[255&h]^r[u];u+=1;let l=s[f>>>24]^i[c>>>16&255]^n[h>>>8&255]^a[255&d]^r[u];u+=1,c=e,h=t,d=o,f=l}let p=(o[c>>>24]<<24|o[h>>>16&255]<<16|o[d>>>8&255]<<8|o[255&f])^r[u];u+=1;let y=(o[h>>>24]<<24|o[d>>>16&255]<<16|o[f>>>8&255]<<8|o[255&c])^r[u];u+=1;let _=(o[d>>>24]<<24|o[f>>>16&255]<<16|o[c>>>8&255]<<8|o[255&h])^r[u];u+=1;let g=(o[f>>>24]<<24|o[c>>>16&255]<<16|o[h>>>8&255]<<8|o[255&d])^r[u];u+=1,e[t]=p,e[t+1]=y,e[t+2]=_,e[t+3]=g}};et.keySize=8;var er=I._createHelper(et),es=[],ei=class extends k{_doReset(){this._hash=new _([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,s=r[0],i=r[1],n=r[2],a=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)es[r]=0|e[t+r];else{let e=es[r-3]^es[r-8]^es[r-14]^es[r-16];es[r]=e<<1|e>>>31}let l=(s<<5|s>>>27)+o+es[r];r<20?l+=(i&n|~i&a)+0x5a827999:r<40?l+=(i^n^a)+0x6ed9eba1:r<60?l+=(i&n|i&a|n&a)-0x70e44324:l+=(i^n^a)-0x359d3e2a,o=a,a=n,n=i<<30|i>>>2,i=s,s=l}r[0]=r[0]+s|0,r[1]=r[1]+i|0,r[2]=r[2]+n|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return t[s>>>5]|=128<<24-s%32,t[(s+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(s+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},en=(k._createHelper(ei),k._createHmacHelper(ei)),ea=r(54726),eo=r(68478),el=r(97495);let ec="x-middleware-override-headers",eh="x-middleware-request";function ed(e,t){if(!(0,el.Zd)(e))throw Error(t)}function ef(e,t,r){if(!r||en(e,t).toString()!==r)throw Error(eo._t)}let eu="clerk_keyless_dummy_key";function ep(e){if(!e)return{};let t=(0,s.Fj)()?ea.o7||ea.rB:ea.o7||ea.rB||eu;try{return e_(e,t)}catch{if(d.I)try{return e_(e,eu)}catch{ey()}ey()}}function ey(){if((0,s.Fj)())throw Error(eo.mJ);throw Error(eo.RC)}function e_(e,t){return JSON.parse(er.decrypt(e,t).toString(w))}},68478:(e,t,r)=>{r.d(t,{AG:()=>s,RC:()=>o,_t:()=>n,mJ:()=>a,sd:()=>i});let s=()=>i("getAuth"),i=(e="auth",t)=>`Clerk: ${e}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:
- ${t?[...t,""].join("\n- "):" "}clerkMiddleware() is used in your Next.js Middleware.
- Your Middleware matcher is configured to match this route or page.
- If you are using the src directory, make sure the Middleware file is inside of it.

For more details, see https://clerk.com/docs/quickstarts/nextjs
`,n="Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)",a="Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)",o=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`}};