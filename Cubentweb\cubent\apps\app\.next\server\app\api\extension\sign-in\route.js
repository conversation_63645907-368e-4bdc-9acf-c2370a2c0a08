(()=>{var e={};e.id=2825,e.ids=[2825],e.modules={1447:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,r)=>{"use strict";r.d(t,{$:()=>u});var s=r(8741),n=r(23056),i=r(76315),a=r(97495);let o=new(r(16698)).AsyncLocalStorage;var l=r(60606);let u=async()=>{var e,t;let r;try{let e=await (0,n.TG)(),t=(0,a._b)(e,s.AA.Headers.ClerkRequestData);r=(0,l.Kk)(t)}catch(e){if(e&&(0,n.Sz)(e))throw e}let u=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:r;return(null==u?void 0:u.secretKey)||(null==u?void 0:u.publishableKey)?(0,i.n)(u):(0,i.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},26790:(e,t,r)=>{"use strict";r.d(t,{z:()=>U});var s,n,i,a,o,l,u,c,d,h,p,g,f,y,m,S,w,b,v,x=r(45940);r(92867);var k=r(37081);r(27322),r(6264);var A=r(49530),E=r(57136),K=r(94051),R=class{constructor(){(0,K.VK)(this,i),(0,K.VK)(this,s,"clerk_telemetry_throttler"),(0,K.VK)(this,n,864e5)}isEventThrottled(e){if(!(0,K.S7)(this,i,l))return!1;let t=Date.now(),r=(0,K.jq)(this,i,a).call(this,e),u=(0,K.S7)(this,i,o)?.[r];if(!u){let e={...(0,K.S7)(this,i,o),[r]:t};localStorage.setItem((0,K.S7)(this,s),JSON.stringify(e))}if(u&&t-u>(0,K.S7)(this,n)){let e=(0,K.S7)(this,i,o);delete e[r],localStorage.setItem((0,K.S7)(this,s),JSON.stringify(e))}return!!u}};s=new WeakMap,n=new WeakMap,i=new WeakSet,a=function(e){let{sk:t,pk:r,payload:s,...n}=e,i={...s,...n};return JSON.stringify(Object.keys({...s,...n}).sort().map(e=>i[e]))},o=function(){let e=localStorage.getItem((0,K.S7)(this,s));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,K.S7)(this,s)),!1}};var T={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},j=class{constructor(e){(0,K.VK)(this,g),(0,K.VK)(this,u),(0,K.VK)(this,c),(0,K.VK)(this,d,{}),(0,K.VK)(this,h,[]),(0,K.VK)(this,p),(0,K.OV)(this,u,{maxBufferSize:e.maxBufferSize??T.maxBufferSize,samplingRate:e.samplingRate??T.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:T.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,K.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,K.S7)(this,d).clerkVersion="",(0,K.S7)(this,d).sdk=e.sdk,(0,K.S7)(this,d).sdkVersion=e.sdkVersion,(0,K.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,E.q5)(e.publishableKey);t&&((0,K.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,K.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,K.OV)(this,c,new R)}get isEnabled(){return!("development"!==(0,K.S7)(this,d).instanceType||(0,K.S7)(this,u).disabled||"undefined"!=typeof process&&(0,A.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,K.S7)(this,u).debug||"undefined"!=typeof process&&(0,A.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,K.jq)(this,g,v).call(this,e.event,e.payload);(0,K.jq)(this,g,w).call(this,t.event,t),(0,K.jq)(this,g,f).call(this,t,e.eventSamplingRate)&&((0,K.S7)(this,h).push(t),(0,K.jq)(this,g,m).call(this))}};u=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,p=new WeakMap,g=new WeakSet,f=function(e,t){return this.isEnabled&&!this.isDebug&&(0,K.jq)(this,g,y).call(this,e,t)},y=function(e,t){let r=Math.random();return!!(r<=(0,K.S7)(this,u).samplingRate&&(void 0===t||r<=t))&&!(0,K.S7)(this,c).isEventThrottled(e)},m=function(){if("undefined"==typeof window)return void(0,K.jq)(this,g,S).call(this);if((0,K.S7)(this,h).length>=(0,K.S7)(this,u).maxBufferSize){(0,K.S7)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,K.S7)(this,p)),(0,K.jq)(this,g,S).call(this);return}(0,K.S7)(this,p)||("requestIdleCallback"in window?(0,K.OV)(this,p,requestIdleCallback(()=>{(0,K.jq)(this,g,S).call(this)})):(0,K.OV)(this,p,setTimeout(()=>{(0,K.jq)(this,g,S).call(this)},0)))},S=function(){fetch(new URL("/v1/event",(0,K.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,K.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,K.OV)(this,h,[])}).catch(()=>void 0)},w=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},b=function(){let e={name:(0,K.S7)(this,d).sdk,version:(0,K.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){let r=(0,K.jq)(this,g,b).call(this);return{event:e,cv:(0,K.S7)(this,d).clerkVersion??"",it:(0,K.S7)(this,d).instanceType??"",sdk:r.name,sdkv:r.version,...(0,K.S7)(this,d).publishableKey?{pk:(0,K.S7)(this,d).publishableKey}:{},...(0,K.S7)(this,d).secretKey?{sk:(0,K.S7)(this,d).secretKey}:{},payload:t}};function U(e){let t={...e},r=(0,x.y3)(t),s=(0,x.Bs)({options:t,apiClient:r}),n=new j({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...s,telemetry:n}}(0,k.C)(x.nr)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37838:(e,t,r)=>{"use strict";r.d(t,{j:()=>N});var s=r(8741),n=r(62923),i=r(54726),a=r(87553),o=r(3680);let l=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},u=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?l(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,l(t)])),null,2)).join(", "),c=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var n,i;for(let s of(console.log((n=e,`[clerk debug start: ${n}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),n=r.encode(e).slice(0,4096);return s.decode(n).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((i=e,`[clerk debug end: ${i}] (@clerk/nextjs=6.20.0,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},d=(e,t)=>(...r)=>{let s=("string"==typeof e?c(e,u):e)(),n=t(s);try{let e=n(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var h=r(74365),p=r(37081),g=r(27322),f=r(6264);function y(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return g.r0.stringify(r,{pad:!1})}async function m(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,n=(0,g.hJ)(r.algorithm);if(!n)return{errors:[new f.xy(`Unsupported algorithm ${r.algorithm}`)]};let i=await (0,g.Fh)(t,n,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=y(a),l=y(e),u=`${o}.${l}`;try{let e=await g.fA.crypto.subtle.sign(n,i,s.encode(u));return{data:`${u}.${g.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new f.xy(e?.message)]}}}(0,p.C)(g.J0);var S=(0,p.R)(g.iU);(0,p.C)(m),(0,p.C)(g.nk);var w=r(97495),b=r(60606);function v(e,{treatPendingAsSignedOut:t=!0,...r}={}){var n,a,o;let l,u=(0,w.NE)(e,"AuthStatus"),c=(0,w.NE)(e,"AuthToken"),d=(0,w.NE)(e,"AuthMessage"),h=(0,w.NE)(e,"AuthReason"),p=(0,w.NE)(e,"AuthSignature");null==(n=r.logger)||n.debug("headers",{authStatus:u,authMessage:d,authReason:h});let g=(0,w._b)(e,s.AA.Headers.ClerkRequestData),f=(0,b.Kk)(g),y={secretKey:(null==r?void 0:r.secretKey)||f.secretKey||i.rB,publishableKey:f.publishableKey||i.At,apiUrl:i.H$,apiVersion:i.mG,authStatus:u,authMessage:d,authReason:h,treatPendingAsSignedOut:t};if(null==(a=r.logger)||a.debug("auth options",y),u&&u===s.TD.SignedIn){(0,b._l)(c,y.secretKey,p);let e=S(c);null==(o=r.logger)||o.debug("jwt",e.raw),l=(0,s.Z5)(y,e.raw.text,e.payload)}else l=(0,s.wI)(y);return t&&"pending"===l.sessionStatus&&(l=(0,s.wI)(y,l.sessionStatus)),l}var x=r(68478);let k=({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>async(n,i)=>{if((0,a.zz)((0,w._b)(n,s.AA.Headers.EnableDebug))&&e.enable(),!(0,w.Zd)(n)){h.M&&(0,b.$K)(n,t);let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,b.$K)(n,t)}return v(n,{...i,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>d(e,e=>(r,n)=>((0,a.zz)((0,w._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,b.$K)(r,t),v(r,{...n,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,x.AG)()});let A={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},E=e=>{var t,r;return!!e.headers.get(A.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(A.Headers.NextAction))},K=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||R(e)||j(e)},R=e=>!!e.headers.get(A.Headers.NextUrl)&&!E(e)||T(),T=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},j=e=>!!e.headers.get(A.Headers.NextjsData);var U=r(23056);let N=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await (0,U.TG)(),a=async()=>{if(h.M)return[];try{let e=await r.e(3137).then(r.bind(r,75518)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},o=await k({debugLoggerName:"auth()",noAuthStatusMessage:(0,x.sd)("auth",await a())})(t,{treatPendingAsSignedOut:e}),l=(0,w.NE)(t,"ClerkUrl"),u=(...e)=>{let{returnBackUrl:r}=e[0]||{},a=(0,s.tl)(t),u=a.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||a.cookies.get(s.AA.Cookies.DevBrowser),c=(0,w._b)(t,s.AA.Headers.ClerkRequestData),d=(0,b.Kk)(c);return[(0,s.vH)({redirectAdapter:n.redirect,devBrowserToken:u,baseUrl:a.clerkUrl.toString(),publishableKey:d.publishableKey||i.At,signInUrl:d.signInUrl||i.qW,signUpUrl:d.signUpUrl||i.sE,sessionStatus:o.sessionStatus}),null===r?"":r||(null==l?void 0:l.toString())]};return Object.assign(o,{redirectToSignIn:(e={})=>{let[t,r]=u(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=u(e);return t.redirectToSignUp({returnBackUrl:r})}})};N.protect=async(...e)=>{r(1447);let t=await (0,U.TG)(),s=await N();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:s,notFound:n,request:i}=e;return async(...e)=>{var a,o,l,u,c,d;let h=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],p=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(u=e[1])?void 0:u.unauthenticatedUrl),g=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),f=()=>g?s(g):n();return"pending"!==r.sessionStatus&&r.userId?h?"function"==typeof h?h(r.has)?r:f():r.has(h)?r:f():r:p?s(p):K(i)?t():n()}})({request:t,authObject:s,redirectToSignIn:s.redirectToSignIn,notFound:n.notFound,redirect:n.redirect})(...e)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73999:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>h});var n=r(26142),i=r(94327),a=r(34862),o=r(37838),l=r(12901),u=r(26239),c=r(25);let d=c.z.object({state:c.z.string().min(1),auth_redirect:c.z.string().url()});async function h(e){try{let{searchParams:t}=new URL(e.url),r=t.get("state"),s=t.get("auth_redirect"),n=d.safeParse({state:r,auth_redirect:s});if(!n.success)return u.NextResponse.json({error:"Invalid parameters",details:n.error.errors},{status:400});let{state:i,auth_redirect:a}=n.data,{userId:c}=await (0,o.j)();if(c){let e=await (0,l.$)(),t=await e.signInTokens.createSignInToken({userId:c,expiresInSeconds:300}),r=new URL(a);return r.searchParams.set("ticket",t.token),r.searchParams.set("state",i),u.NextResponse.redirect(r.toString())}let h=new URL("/sign-in",e.url),p=new URL("/api/extension/sign-in",e.url);return p.searchParams.set("state",i),p.searchParams.set("auth_redirect",a),h.searchParams.set("redirect_url",p.toString()),u.NextResponse.redirect(h.toString())}catch(e){return console.error("Extension sign-in error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/sign-in/route",pathname:"/api/extension/sign-in",filename:"route",bundlePath:"app/api/extension/sign-in/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\sign-in\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:y}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},76315:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var s=r(26790),n=r(54726);let i={secretKey:n.rB,publishableKey:n.At,apiUrl:n.H$,apiVersion:n.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:n.Rg,domain:n.V2,isSatellite:n.fS,sdkMetadata:n.tm,telemetry:{disabled:n.nN,debug:n.Mh}},a=e=>(0,s.z)({...i,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},89259:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,7873,3887],()=>r(73999));module.exports=s})();