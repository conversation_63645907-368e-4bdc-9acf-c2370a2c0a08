{"/api/auth/clear-cross-domain-token/route": "/api/auth/clear-cross-domain-token", "/_not-found/page": "/_not-found", "/api/auth/set-cross-domain-token/route": "/api/auth/set-cross-domain-token", "/api/auth/user/route": "/api/auth/user", "/api/extension/api-keys/route": "/api/extension/api-keys", "/api/extension/analytics/route": "/api/extension/analytics", "/api/extension/connect/route": "/api/extension/connect", "/api/extension/export/route": "/api/extension/export", "/api/extension/auth/route": "/api/extension/auth", "/api/extension/generate-key/route": "/api/extension/generate-key", "/api/extension/profile/route": "/api/extension/profile", "/api/extension/sessions/route": "/api/extension/sessions", "/api/extension/settings/route": "/api/extension/settings", "/api/extension/sign-in/route": "/api/extension/sign-in", "/api/extension/subscription/route": "/api/extension/subscription", "/api/extension/status/route": "/api/extension/status", "/api/extension/sync/route": "/api/extension/sync", "/api/extension/units/track/route": "/api/extension/units/track", "/api/extension/usage/stats/route": "/api/extension/usage/stats", "/api/extension/usage/route": "/api/extension/usage", "/api/terms/accept/route": "/api/terms/accept", "/icon.png/route": "/icon.png", "/apple-icon.png/route": "/apple-icon.png", "/opengraph-image.png/route": "/opengraph-image.png", "/.well-known/vercel/flags/route": "/.well-known/vercel/flags", "/api/collaboration/auth/route": "/api/collaboration/auth", "/api/cron/cleanup-tokens/route": "/api/cron/cleanup-tokens", "/api/extension/login/route": "/api/extension/login", "/api/token/route": "/api/token", "/(authenticated)/login/page": "/login", "/(authenticated)/auth-success/page": "/auth-success", "/(authenticated)/debug-auth/page": "/debug-auth", "/(authenticated)/page": "/", "/(authenticated)/profile/extension/page": "/profile/extension", "/(authenticated)/profile/usage/page": "/profile/usage", "/(authenticated)/profile/settings/page": "/profile/settings", "/(unauthenticated)/sign-up/[[...sign-up]]/page": "/sign-up/[[...sign-up]]", "/(authenticated)/terms/page": "/terms", "/(unauthenticated)/sign-in/[[...sign-in]]/page": "/sign-in/[[...sign-in]]", "/(authenticated)/profile/page": "/profile", "/(authenticated)/webhooks/page": "/webhooks", "/(authenticated)/search/page": "/search"}