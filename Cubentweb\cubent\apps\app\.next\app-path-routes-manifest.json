{"/_not-found/page": "/_not-found", "/api/auth/clear-cross-domain-token/route": "/api/auth/clear-cross-domain-token", "/api/auth/user/route": "/api/auth/user", "/api/auth/set-cross-domain-token/route": "/api/auth/set-cross-domain-token", "/api/extension/analytics/route": "/api/extension/analytics", "/api/extension/auth/route": "/api/extension/auth", "/api/extension/api-keys/route": "/api/extension/api-keys", "/api/extension/export/route": "/api/extension/export", "/api/extension/generate-key/route": "/api/extension/generate-key", "/api/extension/connect/route": "/api/extension/connect", "/api/extension/profile/route": "/api/extension/profile", "/api/extension/settings/route": "/api/extension/settings", "/api/extension/sign-in/route": "/api/extension/sign-in", "/api/extension/sessions/route": "/api/extension/sessions", "/api/extension/status/route": "/api/extension/status", "/api/extension/subscription/route": "/api/extension/subscription", "/api/extension/sync/route": "/api/extension/sync", "/api/extension/usage/route": "/api/extension/usage", "/api/extension/units/track/route": "/api/extension/units/track", "/api/extension/usage/stats/route": "/api/extension/usage/stats", "/api/terms/accept/route": "/api/terms/accept", "/apple-icon.png/route": "/apple-icon.png", "/icon.png/route": "/icon.png", "/opengraph-image.png/route": "/opengraph-image.png", "/.well-known/vercel/flags/route": "/.well-known/vercel/flags", "/api/cron/cleanup-tokens/route": "/api/cron/cleanup-tokens", "/api/collaboration/auth/route": "/api/collaboration/auth", "/api/token/route": "/api/token", "/api/extension/login/route": "/api/extension/login", "/(authenticated)/auth-success/page": "/auth-success", "/(authenticated)/login/page": "/login", "/(authenticated)/debug-auth/page": "/debug-auth", "/(authenticated)/page": "/", "/(authenticated)/profile/extension/page": "/profile/extension", "/(authenticated)/profile/settings/page": "/profile/settings", "/(authenticated)/profile/usage/page": "/profile/usage", "/(authenticated)/profile/page": "/profile", "/(authenticated)/webhooks/page": "/webhooks", "/(authenticated)/search/page": "/search", "/(unauthenticated)/sign-in/[[...sign-in]]/page": "/sign-in/[[...sign-in]]", "/(unauthenticated)/sign-up/[[...sign-up]]/page": "/sign-up/[[...sign-up]]", "/(authenticated)/terms/page": "/terms"}