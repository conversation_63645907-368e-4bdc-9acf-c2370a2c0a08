exports.id=1988,exports.ids=[1988],exports.modules={404:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(37058),o=r(74157);function a(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6986:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return g},getUtils:function(){return v},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return p}});let n=r(79551),o=r(47028),a=r(44108),i=r(54620),u=r(98133),l=r(21529),c=r(98940),s=r(74369),f=r(85412),d=r(15637);function p(e,t,r){let o=(0,n.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let n=e!==f.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(f.NEXT_QUERY_PARAM_PREFIX),a=e!==f.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(f.NEXT_INTERCEPTION_MARKER_PREFIX);(n||a||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete o.query[e]}e.url=(0,n.format)(o)}function m(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let o,{optional:a,repeat:i}=r.groups[n],u=`[${i?"...":""}${n}]`;a&&(u=`[${u}]`);let l=t[n];o=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(u,o)}return e}function h(e,t,r,n){let o={};for(let a of Object.keys(t.groups)){let i=e[a];"string"==typeof i?i=(0,s.normalizeRscURL)(i):Array.isArray(i)&&(i=i.map(s.normalizeRscURL));let u=r[a],l=t.groups[a].optional;if((Array.isArray(u)?u.some(e=>Array.isArray(i)?i.some(t=>t.includes(e)):null==i?void 0:i.includes(e)):null==i?void 0:i.includes(u))||void 0===i&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!i||Array.isArray(i)&&1===i.length&&("index"===i[0]||i[0]===`[[...${a}]]`))&&(i=void 0,delete e[a]),i&&"string"==typeof i&&t.groups[a].repeat&&(i=i.split("/")),i&&(o[a]=i)}return{params:o,hasValidParams:!0}}function v({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:s,trailingSlash:f,caseSensitive:v}){let g,y,E;return s&&(g=(0,i.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),E=(y=(0,u.getRouteMatcher)(g))(e)),{handleRewrites:function(i,u){let d={},p=u.pathname,m=n=>{let c=(0,a.getPathMatch)(n.source+(f?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!v});if(!u.pathname)return!1;let m=c(u.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(i,u.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:a,destQuery:i}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:u.query});if(a.protocol)return!0;if(Object.assign(d,i,m),Object.assign(u.query,a.query),delete a.query,Object.assign(u,a),!(p=u.pathname))return!1;if(r&&(p=p.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,o.normalizeLocalePath)(p,t.locales);p=e.pathname,u.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(p===e)return!0;if(s&&y){let e=y(p);if(e)return u.query={...u.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(p!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(p||"");return t===(0,c.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return d},defaultRouteRegex:g,dynamicRouteMatcher:y,defaultRouteMatches:E,getParamsFromRouteMatches:function(e){if(!g)return null;let{groups:t,routeKeys:r}=g,n=(0,u.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,d.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let o={};for(let e of Object.keys(r)){let a=r[e];if(!a)continue;let i=t[a],u=n[e];if(!i.optional&&!u)return null;o[i.pos]=u}return o}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>g&&E?h(e,g,E,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,g),interpolateDynamicPath:(e,t)=>m(e,t,g)}}function g(e,t){return"string"==typeof e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[f.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},9983:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},13680:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},17572:(e,t,r)=>{"use strict";r.d(t,{A:()=>G});var n,o,a=r(60741),i=r(57752),u="right-scroll-bar-position",l="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,o,i=(t=null,void 0===r&&(r=d),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return i.options=(0,a.__assign)({async:!0,ssr:!1},e),i}(),m=function(){},h=i.forwardRef(function(e,t){var r,n,o,u,l=i.useRef(null),d=i.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=d[0],v=d[1],g=e.forwardProps,y=e.children,E=e.className,b=e.removeScrollBar,R=e.enabled,w=e.shards,x=e.sideCar,C=e.noIsolation,_=e.inert,P=e.allowPinchZoom,A=e.as,M=e.gapMode,T=(0,a.__rest)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(r=[l,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,u=o.facade,s(function(){var e=f.get(u);if(e){var t=new Set(e),n=new Set(r),o=u.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,r)},[r]),u),j=(0,a.__assign)((0,a.__assign)({},T),h);return i.createElement(i.Fragment,null,R&&i.createElement(x,{sideCar:p,removeScrollBar:b,shards:w,noIsolation:C,inert:_,setCallbacks:v,allowPinchZoom:!!P,lockRef:l,gapMode:M}),g?i.cloneElement(i.Children.only(y),(0,a.__assign)((0,a.__assign)({},j),{ref:S})):i.createElement(void 0===A?"div":A,(0,a.__assign)({},j,{className:E,ref:S}),y))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:l,zeroRight:u};var v=function(e){var t=e.sideCar,r=(0,a.__rest)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return i.createElement(n,(0,a.__assign)({},r))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},E=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},R=function(e){return parseInt(e||"",10)||0},w=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[R(r),R(n),R(o)]},x=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=w(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},C=E(),_="data-scroll-locked",P=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(c,"px ").concat(n,";\n  }\n  body[").concat(_,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(c,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(c,"px ").concat(n,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(n,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(_,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(_)||"0",10);return isFinite(e)?e:0},M=function(){i.useEffect(function(){return document.body.setAttribute(_,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(_):document.body.setAttribute(_,e.toString())}},[])},T=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;M();var a=i.useMemo(function(){return x(o)},[o]);return i.createElement(C,{styles:P(a,!t,o,r?"":"!important")})},S=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return S=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){S=!1}var O=!!S&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},N=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),k(e,n)){var o=I(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},k=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},D=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*n,l=r.target,c=t.contains(l),s=!1,f=u>0,d=0,p=0;do{var m=I(e,l),h=m[0],v=m[1]-m[2]-i*h;(h||v)&&k(e,l)&&(d+=v,p+=h),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return f&&(o&&1>Math.abs(d)||!o&&u>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-u>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},$=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},K=0,H=[];let W=(n=function(e){var t=i.useRef([]),r=i.useRef([0,0]),n=i.useRef(),o=i.useState(K++)[0],u=i.useState(E)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,a.__spreadArray)([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,a=F(e),i=r.current,u="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=N(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=N(f,s)),!d)return!1;if(!n.current&&"changedTouches"in e&&(u||c)&&(n.current=o),!o)return!0;var p=n.current||o;return D(p,t,e,"h"===p?u:c,!0)},[]),s=i.useCallback(function(e){if(H.length&&H[H.length-1]===u){var r="deltaY"in e?$(e):F(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=i.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){r.current=F(e),n.current=void 0},[]),p=i.useCallback(function(t){f(t.type,$(t),t.target,c(t,e.lockRef.current))},[]),m=i.useCallback(function(t){f(t.type,F(t),t.target,c(t,e.lockRef.current))},[]);i.useEffect(function(){return H.push(u),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:m}),document.addEventListener("wheel",s,O),document.addEventListener("touchmove",s,O),document.addEventListener("touchstart",d,O),function(){H=H.filter(function(e){return e!==u}),document.removeEventListener("wheel",s,O),document.removeEventListener("touchmove",s,O),document.removeEventListener("touchstart",d,O)}},[]);var h=e.removeScrollBar,v=e.inert;return i.createElement(i.Fragment,null,v?i.createElement(u,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(T,{gapMode:e.gapMode}):null)},p.useMedium(n),v);var X=i.forwardRef(function(e,t){return i.createElement(h,(0,a.__assign)({},e,{ref:t,sideCar:W}))});X.classNames=h.classNames;let G=X},21529:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return s},matchHas:function(){return c},parseDestination:function(){return f},prepareDestination:function(){return d}});let n=r(47237),o=r(13680),a=r(404),i=r(42530),u=r(35717);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let o={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,u.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return o[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{o[e]=t.groups[e]}):"host"===r.type&&t[0]&&(o.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&o}function s(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function f(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,o.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let i=r.href;i&&(i=l(i));let u=r.hostname;u&&(u=l(u));let c=r.hash;return c&&(c=l(c)),{...r,pathname:n,hostname:u,href:i,hash:c}}function d(e){let t,r,o=Object.assign({},e.query),a=f(e),{hostname:u,query:c}=a,d=a.pathname;a.hash&&(d=""+d+a.hash);let p=[],m=[];for(let e of((0,n.pathToRegexp)(d,m),m))p.push(e.name);if(u){let e=[];for(let t of((0,n.pathToRegexp)(u,e),e))p.push(t.name)}let h=(0,n.compile)(d,{validate:!1});for(let[r,o]of(u&&(t=(0,n.compile)(u,{validate:!1})),Object.entries(c)))Array.isArray(o)?c[r]=o.map(t=>s(l(t),e.params)):"string"==typeof o&&(c[r]=s(l(o),e.params));let v=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!v.some(e=>p.includes(e)))for(let t of v)t in c||(c[t]=e.params[t]);if((0,i.isInterceptionRouteAppPath)(d))for(let t of d.split("/")){let r=i.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,o]=(r=h(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(o?"#":"")+(o||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...o,...a.query},{newUrl:r,destQuery:c,parsedDestination:a}}},22680:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},25293:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return u},STATIC_METADATA_IMAGES:function(){return i},getExtensionRegexString:function(){return l},isMetadataPage:function(){return f},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return s}});let n=r(68861),o=r(74369),a=r(9983),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},u=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let o=(r?"":"?")+"$",a=`\\d?${r?"":"(-\\w{6})?"}`,u=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${o}`),RegExp(`[\\\\/]${i.icon.filename}${a}${l(i.icon.extensions,t)}${o}`),RegExp(`[\\\\/]${i.apple.filename}${a}${l(i.apple.extensions,t)}${o}`),RegExp(`[\\\\/]${i.openGraph.filename}${a}${l(i.openGraph.extensions,t)}${o}`),RegExp(`[\\\\/]${i.twitter.filename}${a}${l(i.twitter.extensions,t)}${o}`)],c=(0,n.normalizePathSep)(e);return u.some(e=>e.test(c))}function s(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function f(e){return!(0,a.isAppRouteRoute)(e)&&c(e,[],!1)}function d(e){let t=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&c(t,[],!1)}},33187:(e,t,r)=>{"use strict";r.d(t,{UC:()=>eq,q7:()=>eV,ZL:()=>eG,bL:()=>eW,l9:()=>eX});var n=r(57752),o=r(46769),a=r(46854),i=r(1493),u=r(58785),l=r(56750),c=r(46691),s=r(44804),f=r(97727),d=r(55105),p=r(35282),m=r(42703),h=r(32370),v=r(44082),g=r(86552),y=r(74794),E=r(58576),b=r(18526),R=r(34118),w=r(17572),x=r(99730),C=["Enter"," "],_=["ArrowUp","PageDown","End"],P=["ArrowDown","PageUp","Home",..._],A={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},M={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[S,j,O]=(0,c.N)(T),[L,N]=(0,i.A)(T,[O,h.Bk,y.RG]),k=(0,h.Bk)(),I=(0,y.RG)(),[D,F]=L(T),[$,U]=L(T),K=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:u=!0}=e,l=k(t),[c,f]=n.useState(null),d=n.useRef(!1),p=(0,b.c)(i),m=(0,s.jH)(a);return n.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,x.jsx)(h.bL,{...l,children:(0,x.jsx)(D,{scope:t,open:r,onOpenChange:p,content:c,onContentChange:f,children:(0,x.jsx)($,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:m,modal:u,children:o})})})};K.displayName=T;var H=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=k(r);return(0,x.jsx)(h.Mz,{...o,...n,ref:t})});H.displayName="MenuAnchor";var W="MenuPortal",[X,G]=L(W,{forceMount:void 0}),q=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=F(W,t);return(0,x.jsx)(X,{scope:t,forceMount:r,children:(0,x.jsx)(g.C,{present:r||a.open,children:(0,x.jsx)(v.Z,{asChild:!0,container:o,children:n})})})};q.displayName=W;var V="MenuContent",[z,B]=L(V),Z=n.forwardRef((e,t)=>{let r=G(V,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=F(V,e.__scopeMenu),i=U(V,e.__scopeMenu);return(0,x.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(g.C,{present:n||a.open,children:(0,x.jsx)(S.Slot,{scope:e.__scopeMenu,children:i.modal?(0,x.jsx)(Q,{...o,ref:t}):(0,x.jsx)(Y,{...o,ref:t})})})})}),Q=n.forwardRef((e,t)=>{let r=F(V,e.__scopeMenu),i=n.useRef(null),u=(0,a.s)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,R.Eq)(e)},[]),(0,x.jsx)(ee,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Y=n.forwardRef((e,t)=>{let r=F(V,e.__scopeMenu);return(0,x.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,E.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:u,onOpenAutoFocus:l,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:E,onInteractOutside:b,onDismiss:R,disableOutsideScroll:C,...A}=e,M=F(V,r),T=U(V,r),S=k(r),O=I(r),L=j(r),[N,D]=n.useState(null),$=n.useRef(null),K=(0,a.s)(t,$,M.onContentChange),H=n.useRef(0),W=n.useRef(""),X=n.useRef(0),G=n.useRef(null),q=n.useRef("right"),B=n.useRef(0),Z=C?w.A:n.Fragment,Q=e=>{let t=W.current+e,r=L().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let u=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==r?u:void 0}(r.map(e=>e.textValue),t,o),i=r.find(e=>e.textValue===a)?.ref.current;!function e(t){W.current=t,window.clearTimeout(H.current),""!==t&&(H.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(H.current),[]),(0,d.Oh)();let Y=n.useCallback(e=>q.current===G.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],u=t[a],l=i.x,c=i.y,s=u.x,f=u.y;c>n!=f>n&&r<(s-l)*(n-c)/(f-c)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,G.current?.area),[]);return(0,x.jsx)(z,{scope:r,searchRef:W,onItemEnter:n.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),onItemLeave:n.useCallback(e=>{Y(e)||($.current?.focus(),D(null))},[Y]),onTriggerLeave:n.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),pointerGraceTimerRef:X,onPointerGraceIntentChange:n.useCallback(e=>{G.current=e},[]),children:(0,x.jsx)(Z,{...C?{as:J,allowPinchZoom:!0}:void 0,children:(0,x.jsx)(p.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(l,e=>{e.preventDefault(),$.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,x.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:E,onInteractOutside:b,onDismiss:R,children:(0,x.jsx)(y.bL,{asChild:!0,...O,dir:T.dir,orientation:"vertical",loop:i,currentTabStopId:N,onCurrentTabStopIdChange:D,onEntryFocus:(0,o.m)(m,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eP(M.open),"data-radix-menu-content":"",dir:T.dir,...S,...A,ref:K,style:{outline:"none",...A.style},onKeyDown:(0,o.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Q(e.key));let o=$.current;if(e.target!==o||!P.includes(e.key))return;e.preventDefault();let a=L().filter(e=>!e.disabled).map(e=>e.ref.current);_.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(H.current),W.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{let t=e.target,r=B.current!==e.clientX;e.currentTarget.contains(t)&&r&&(q.current=e.clientX>B.current?"right":"left",B.current=e.clientX)}))})})})})})})});Z.displayName=V;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(l.sG.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(l.sG.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...u}=e,c=n.useRef(null),s=U(en,e.__scopeMenu),f=B(en,e.__scopeMenu),d=(0,a.s)(t,c),p=n.useRef(!1);return(0,x.jsx)(ei,{...u,ref:d,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>i?.(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;r||t&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:u,...c}=e,s=B(en,r),f=I(r),d=n.useRef(null),p=(0,a.s)(t,d),[m,h]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=d.current;e&&g((e.textContent??"").trim())},[c.children]),(0,x.jsx)(S.ItemSlot,{scope:r,disabled:i,textValue:u??v,children:(0,x.jsx)(y.q7,{asChild:!0,...f,focusable:!i,children:(0,x.jsx)(l.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{i?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eu=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,x.jsx)(eh,{scope:e.__scopeMenu,checked:r,children:(0,x.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eA(r)?"mixed":r,...a,ref:t,"data-state":eM(r),onSelect:(0,o.m)(a.onSelect,()=>n?.(!!eA(r)||!r),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[ec,es]=L(el,{value:void 0,onValueChange:()=>{}}),ef=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,b.c)(n);return(0,x.jsx)(ec,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,x.jsx)(et,{...o,ref:t})})});ef.displayName=el;var ed="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=es(ed,e.__scopeMenu),i=r===a.value;return(0,x.jsx)(eh,{scope:e.__scopeMenu,checked:i,children:(0,x.jsx)(ea,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eM(i),onSelect:(0,o.m)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ep.displayName=ed;var em="MenuItemIndicator",[eh,ev]=L(em,{checked:!1}),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=ev(em,r);return(0,x.jsx)(g.C,{present:n||eA(a.checked)||!0===a.checked,children:(0,x.jsx)(l.sG.span,{...o,ref:t,"data-state":eM(a.checked)})})});eg.displayName=em;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ey.displayName="MenuSeparator";var eE=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=k(r);return(0,x.jsx)(h.i3,{...o,...n,ref:t})});eE.displayName="MenuArrow";var[eb,eR]=L("MenuSub"),ew="MenuSubTrigger",ex=n.forwardRef((e,t)=>{let r=F(ew,e.__scopeMenu),i=U(ew,e.__scopeMenu),u=eR(ew,e.__scopeMenu),l=B(ew,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=l,d={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,x.jsx)(H,{asChild:!0,...d,children:(0,x.jsx)(ei,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":u.contentId,"data-state":eP(r.open),...e,ref:(0,a.t)(t,u.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eT(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||c.current||(l.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],i=t[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;e.disabled||n&&" "===t.key||A[i.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});ex.displayName=ew;var eC="MenuSubContent",e_=n.forwardRef((e,t)=>{let r=G(V,e.__scopeMenu),{forceMount:i=r.forceMount,...u}=e,l=F(V,e.__scopeMenu),c=U(V,e.__scopeMenu),s=eR(eC,e.__scopeMenu),f=n.useRef(null),d=(0,a.s)(t,f);return(0,x.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(g.C,{present:i||l.open,children:(0,x.jsx)(S.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:d,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=M[c.dir].includes(e.key);t&&r&&(l.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eP(e){return e?"open":"closed"}function eA(e){return"indeterminate"===e}function eM(e){return eA(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return t=>"mouse"===t.pointerType?e(t):void 0}e_.displayName=eC;var eS="DropdownMenu",[ej,eO]=(0,i.A)(eS,[N]),eL=N(),[eN,ek]=ej(eS),eI=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:l,modal:c=!0}=e,s=eL(t),f=n.useRef(null),[d,p]=(0,u.i)({prop:a,defaultProp:i??!1,onChange:l,caller:eS});return(0,x.jsx)(eN,{scope:t,triggerId:(0,m.B)(),triggerRef:f,contentId:(0,m.B)(),open:d,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,x.jsx)(K,{...s,open:d,onOpenChange:p,dir:o,modal:c,children:r})})};eI.displayName=eS;var eD="DropdownMenuTrigger",eF=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,u=ek(eD,r),c=eL(r);return(0,x.jsx)(H,{asChild:!0,...c,children:(0,x.jsx)(l.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,a.t)(t,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eD;var e$=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eL(t);return(0,x.jsx)(q,{...n,...r})};e$.displayName="DropdownMenuPortal";var eU="DropdownMenuContent",eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=ek(eU,r),u=eL(r),l=n.useRef(!1);return(0,x.jsx)(Z,{id:i.contentId,"aria-labelledby":i.triggerId,...u,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{l.current||i.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eK.displayName=eU,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(et,{...o,...n,ref:t})}).displayName="DropdownMenuGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(er,{...o,...n,ref:t})}).displayName="DropdownMenuLabel";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(ea,{...o,...n,ref:t})});eH.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(eu,{...o,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(ef,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(ep,{...o,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(eg,{...o,...n,ref:t})}).displayName="DropdownMenuItemIndicator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(ey,{...o,...n,ref:t})}).displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(eE,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(ex,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,x.jsx)(e_,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eW=eI,eX=eF,eG=e$,eq=eK,eV=eH},33551:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},34118:(e,t,r)=>{"use strict";r.d(t,{Eq:()=>s});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},u=0,l=function(e){return e&&(e.host||l(e.parentNode))},c=function(e,t,r,n){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=l(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[r]||(i[r]=new WeakMap);var s=i[r],f=[],d=new Set,p=new Set(c),m=function(e){!e||d.has(e)||(d.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))h(e);else try{var t=e.getAttribute(n),i=null!==t&&"false"!==t,u=(o.get(e)||0)+1,l=(s.get(e)||0)+1;o.set(e,u),s.set(e,l),f.push(e),1===u&&i&&a.set(e,!0),1===l&&e.setAttribute(r,"true"),i||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),d.clear(),u++,function(){f.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(n),a.delete(e)),i||e.removeAttribute(r)}),--u||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||n(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),c(o,a,r,"aria-hidden")):function(){return null}}},35282:(e,t,r)=>{"use strict";r.d(t,{n:()=>f});var n=r(57752),o=r(46854),a=r(56750),i=r(18526),u=r(99730),l="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[E,b]=n.useState(null),R=(0,i.c)(v),w=(0,i.c)(g),x=n.useRef(null),C=(0,o.s)(t,e=>b(e)),_=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(f){let e=function(e){if(_.paused||!E)return;let t=e.target;E.contains(t)?x.current=t:m(x.current,{select:!0})},t=function(e){if(_.paused||!E)return;let t=e.relatedTarget;null!==t&&(E.contains(t)||m(x.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(E)});return E&&r.observe(E,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[f,E,_.paused]),n.useEffect(()=>{if(E){h.add(_);let e=document.activeElement;if(!E.contains(e)){let t=new CustomEvent(l,s);E.addEventListener(l,R),E.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(m(n,{select:t}),document.activeElement!==r)return}(d(E).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(E))}return()=>{E.removeEventListener(l,R),setTimeout(()=>{let t=new CustomEvent(c,s);E.addEventListener(c,w),E.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),E.removeEventListener(c,w),h.remove(_)},0)}}},[E,R,w,_]);let P=n.useCallback(e=>{if(!r&&!f||_.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,a]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&m(a,{select:!0})):(e.preventDefault(),r&&m(o,{select:!0})):n===t&&e.preventDefault()}},[r,f,_.paused]);return(0,u.jsx)(a.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:P})});function d(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function m(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}f.displayName="FocusScope";var h=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},35717:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(59656);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},37058:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},42530:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return i},isInterceptionRouteAppPath:function(){return a}});let n=r(74369),o=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function i(e){let t,r,a;for(let n of e.split("/"))if(r=o.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let i=t.split("/");if(i.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=i.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},44108:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return o}});let n=r(47237);function o(e,t){let r=[],o=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(o.source),o.flags):o,r);return(e,n)=>{if("string"!=typeof e)return!1;let o=a(e);if(!o)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete o.params[e.name];return{...n,...o.params}}}},44804:(e,t,r)=>{"use strict";r.d(t,{jH:()=>a});var n=r(57752);r(99730);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},46097:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return p}});let n=r(25293),o=function(e){return e&&e.__esModule?e:{default:e}}(r(65454)),a=r(6986),i=r(54620),u=r(33551),l=r(74369),c=r(68861),s=r(90762);function f(e){let t=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,s.isGroupSegment)(e)||(0,s.isParallelRouteSegment)(e))&&(r=(0,u.djb2Hash)(t).toString(36).slice(0,6)),r}function d(e,t,r){let n=(0,l.normalizeAppPath)(e),u=(0,i.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),s=(0,a.interpolateDynamicPath)(n,t,u),{name:d,ext:p}=o.default.parse(r),m=f(o.default.posix.join(e,d)),h=m?`-${m}`:"";return(0,c.normalizePathSep)(o.default.join(s,`${d}${h}${p}`))}function p(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=f(e),!t.endsWith("/route")){let{dir:e,name:n,ext:a}=o.default.parse(t);t=o.default.posix.join(e,`${n}${r?`-${r}`:""}${a}`,"route")}return t}function m(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,o=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${o}`)+(r?"/route":"")}},46691:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(57752),o=r(1493),a=r(46854),i=r(58576),u=r(99730);function l(e){let t=e+"CollectionProvider",[r,l]=(0,o.A)(t),[c,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,o=n.useRef(null),a=n.useRef(new Map).current;return(0,u.jsx)(c,{scope:t,itemMap:a,collectionRef:o,children:r})};f.displayName=t;let d=e+"CollectionSlot",p=(0,i.TL)(d),m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=s(d,r),i=(0,a.s)(t,o.collectionRef);return(0,u.jsx)(p,{ref:i,children:n})});m.displayName=d;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,i.TL)(h),y=n.forwardRef((e,t)=>{let{scope:r,children:o,...i}=e,l=n.useRef(null),c=(0,a.s)(t,l),f=s(h,r);return n.useEffect(()=>(f.itemMap.set(l,{ref:l,...i}),()=>void f.itemMap.delete(l))),(0,u.jsx)(g,{...{[v]:""},ref:c,children:o})});return y.displayName=h,[{Provider:f,Slot:m,ItemSlot:y},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}var c=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=f(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function f(e){return e!=e||0===e?0:Math.trunc(e)}},47237:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var o="",a=r+1;a<e.length;){var i=e.charCodeAt(a);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){o+=e[a++];continue}break}if(!o)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:o}),r=a;continue}if("("===n){var u=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--u){a++;break}}else if("("===e[a]&&(u++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(u)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,i="[^"+o(t.delimiter||"/#?")+"]+?",u=[],l=0,c=0,s="",f=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},d=function(e){var t=f(e);if(void 0!==t)return t;var n=r[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=f("CHAR")||f("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var m=f("CHAR"),h=f("NAME"),v=f("PATTERN");if(h||v){var g=m||"";-1===a.indexOf(g)&&(s+=g,g=""),s&&(u.push(s),s=""),u.push({name:h||l++,prefix:g,suffix:"",pattern:v||i,modifier:f("MODIFIER")||""});continue}var y=m||f("ESCAPED_CHAR");if(y){s+=y;continue}if(s&&(u.push(s),s=""),f("OPEN")){var g=p(),E=f("NAME")||"",b=f("PATTERN")||"",R=p();d("CLOSE"),u.push({name:E||(b?l++:""),pattern:E&&!b?i:b,prefix:g,suffix:R,modifier:f("MODIFIER")||""});continue}d("END")}return u}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,o=void 0===n?function(e){return e}:n,i=t.validate,u=void 0===i||i,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var i=t?t[a.name]:void 0,c="?"===a.modifier||"*"===a.modifier,s="*"===a.modifier||"+"===a.modifier;if(Array.isArray(i)){if(!s)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===i.length){if(c)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var f=0;f<i.length;f++){var d=o(i[f],a);if(u&&!l[n].test(d))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');r+=a.prefix+d+a.suffix}continue}if("string"==typeof i||"number"==typeof i){var d=o(String(i),a);if(u&&!l[n].test(d))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');r+=a.prefix+d+a.suffix;continue}if(!c){var p=s?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,o=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],i=n.index,u=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?u[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return o(e,r)}):u[r.name]=o(n[e],r)}}(l);return{path:a,index:i,params:u}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function i(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,u=r.start,l=r.end,c=r.encode,s=void 0===c?function(e){return e}:c,f="["+o(r.endsWith||"")+"]|$",d="["+o(r.delimiter||"/#?")+"]",p=void 0===u||u?"^":"",m=0;m<e.length;m++){var h=e[m];if("string"==typeof h)p+=o(s(h));else{var v=o(s(h.prefix)),g=o(s(h.suffix));if(h.pattern)if(t&&t.push(h),v||g)if("+"===h.modifier||"*"===h.modifier){var y="*"===h.modifier?"?":"";p+="(?:"+v+"((?:"+h.pattern+")(?:"+g+v+"(?:"+h.pattern+"))*)"+g+")"+y}else p+="(?:"+v+"("+h.pattern+")"+g+")"+h.modifier;else p+="("+h.pattern+")"+h.modifier;else p+="(?:"+v+g+")"+h.modifier}}if(void 0===l||l)i||(p+=d+"?"),p+=r.endsWith?"(?="+f+")":"$";else{var E=e[e.length-1],b="string"==typeof E?d.indexOf(E[E.length-1])>-1:void 0===E;i||(p+="(?:"+d+"(?="+f+"))?"),b||(p+="(?="+d+"|"+f+")")}return new RegExp(p,a(r))}function u(t,r,n){if(t instanceof RegExp){if(!r)return t;var o=t.source.match(/\((?!\?)/g);if(o)for(var l=0;l<o.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return u(e,r,n).source}).join("|")+")",a(n)):i(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(u(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=i,t.pathToRegexp=u})(),e.exports=t})()},54620:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return f},parseParameter:function(){return l}});let n=r(85412),o=r(42530),a=r(13680),i=r(98940),u=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(u);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function s(e,t,r){let n={},l=1,s=[];for(let f of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>f.startsWith(e)),i=f.match(u);if(e&&i&&i[2]){let{key:t,optional:r,repeat:o}=c(i[2]);n[t]={pos:l++,repeat:o,optional:r},s.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(i&&i[2]){let{key:e,repeat:t,optional:o}=c(i[2]);n[e]={pos:l++,repeat:t,optional:o},r&&i[1]&&s.push("/"+(0,a.escapeStringRegexp)(i[1]));let u=t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&i[1]&&(u=u.substring(1)),s.push(u)}else s.push("/"+(0,a.escapeStringRegexp)(f));t&&i&&i[3]&&s.push((0,a.escapeStringRegexp)(i[3]))}return{parameterizedRoute:s.join(""),groups:n}}function f(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:o=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:i}=s(e,r,n),u=a;return o||(u+="(?:/)?"),{re:RegExp("^"+u+"$"),groups:i}}function d(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:o,routeKeys:i,keyPrefix:u,backreferenceDuplicateKeys:l}=e,{key:s,optional:f,repeat:d}=c(o),p=s.replace(/\W/g,"");u&&(p=""+u+p);let m=!1;(0===p.length||p.length>30)&&(m=!0),isNaN(parseInt(p.slice(0,1)))||(m=!0),m&&(p=n());let h=p in i;u?i[p]=""+u+s:i[p]=s;let v=r?(0,a.escapeStringRegexp)(r):"";return t=h&&l?"\\k<"+p+">":d?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",f?"(?:/"+v+t+")?":"/"+v+t}function p(e,t,r,l,c){let s,f=(s=0,()=>{let e="",t=++s;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},m=[];for(let s of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>s.startsWith(e)),i=s.match(u);if(e&&i&&i[2])m.push(d({getSafeRouteKey:f,interceptionMarker:i[1],segment:i[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(i&&i[2]){l&&i[1]&&m.push("/"+(0,a.escapeStringRegexp)(i[1]));let e=d({getSafeRouteKey:f,segment:i[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&i[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,a.escapeStringRegexp)(s));r&&i&&i[3]&&m.push((0,a.escapeStringRegexp)(i[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:p}}function m(e,t){var r,n,o;let a=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(o=t.backreferenceDuplicateKeys)&&o),i=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(i+="(?:/)?"),{...f(e,t),namedRegex:"^"+i+"$",routeKeys:a.routeKeys}}function h(e,t){let{parameterizedRoute:r}=s(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},55105:(e,t,r)=>{"use strict";r.d(t,{Oh:()=>a});var n=r(57752),o=0;function a(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??i()),document.body.insertAdjacentElement("beforeend",e[1]??i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},59110:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return h},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return i},getURL:function(){return u},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return E}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=i();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function E(e){return JSON.stringify({message:e.message,stack:e.stack})}},59656:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},a=t.split(n),i=(r||{}).decode||e,u=0;u<a.length;u++){var l=a[u],c=l.indexOf("=");if(!(c<0)){var s=l.substr(0,c).trim(),f=l.substr(++c,l.length).trim();'"'==f[0]&&(f=f.slice(1,-1)),void 0==o[s]&&(o[s]=function(e,t){try{return t(e)}catch(t){return e}}(f,i))}}return o},t.serialize=function(e,t,n){var a=n||{},i=a.encode||r;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var u=i(t);if(u&&!o.test(u))throw TypeError("argument val is invalid");var l=e+"="+u;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(a.domain){if(!o.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!o.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},62458:(e,t,r)=>{"use strict";r.d(t,{gLX:()=>l,rRK:()=>i});var n=r(57752);function o(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}var a=["color"],i=(0,n.forwardRef)(function(e,t){var r=e.color,i=o(e,a);return(0,n.createElement)("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i,{ref:t}),(0,n.createElement)("path",{d:"M2.89998 0.499976C2.89998 0.279062 2.72089 0.0999756 2.49998 0.0999756C2.27906 0.0999756 2.09998 0.279062 2.09998 0.499976V1.09998H1.49998C1.27906 1.09998 1.09998 1.27906 1.09998 1.49998C1.09998 1.72089 1.27906 1.89998 1.49998 1.89998H2.09998V2.49998C2.09998 2.72089 2.27906 2.89998 2.49998 2.89998C2.72089 2.89998 2.89998 2.72089 2.89998 2.49998V1.89998H3.49998C3.72089 1.89998 3.89998 1.72089 3.89998 1.49998C3.89998 1.27906 3.72089 1.09998 3.49998 1.09998H2.89998V0.499976ZM5.89998 3.49998C5.89998 3.27906 5.72089 3.09998 5.49998 3.09998C5.27906 3.09998 5.09998 3.27906 5.09998 3.49998V4.09998H4.49998C4.27906 4.09998 4.09998 4.27906 4.09998 4.49998C4.09998 4.72089 4.27906 4.89998 4.49998 4.89998H5.09998V5.49998C5.09998 5.72089 5.27906 5.89998 5.49998 5.89998C5.72089 5.89998 5.89998 5.72089 5.89998 5.49998V4.89998H6.49998C6.72089 4.89998 6.89998 4.72089 6.89998 4.49998C6.89998 4.27906 6.72089 4.09998 6.49998 4.09998H5.89998V3.49998ZM1.89998 6.49998C1.89998 6.27906 1.72089 6.09998 1.49998 6.09998C1.27906 6.09998 1.09998 6.27906 1.09998 6.49998V7.09998H0.499976C0.279062 7.09998 0.0999756 7.27906 0.0999756 7.49998C0.0999756 7.72089 0.279062 7.89998 0.499976 7.89998H1.09998V8.49998C1.09998 8.72089 1.27906 8.89997 1.49998 8.89997C1.72089 8.89997 1.89998 8.72089 1.89998 8.49998V7.89998H2.49998C2.72089 7.89998 2.89998 7.72089 2.89998 7.49998C2.89998 7.27906 2.72089 7.09998 2.49998 7.09998H1.89998V6.49998ZM8.54406 0.98184L8.24618 0.941586C8.03275 0.917676 7.90692 1.1655 8.02936 1.34194C8.17013 1.54479 8.29981 1.75592 8.41754 1.97445C8.91878 2.90485 9.20322 3.96932 9.20322 5.10022C9.20322 8.37201 6.82247 11.0878 3.69887 11.6097C3.45736 11.65 3.20988 11.6772 2.96008 11.6906C2.74563 11.702 2.62729 11.9535 2.77721 12.1072C2.84551 12.1773 2.91535 12.2458 2.98667 12.3128L3.05883 12.3795L3.31883 12.6045L3.50684 12.7532L3.62796 12.8433L3.81491 12.9742L3.99079 13.089C4.11175 13.1651 4.23536 13.2375 4.36157 13.3059L4.62496 13.4412L4.88553 13.5607L5.18837 13.6828L5.43169 13.7686C5.56564 13.8128 5.70149 13.8529 5.83857 13.8885C5.94262 13.9155 6.04767 13.9401 6.15405 13.9622C6.27993 13.9883 6.40713 14.0109 6.53544 14.0298L6.85241 14.0685L7.11934 14.0892C7.24637 14.0965 7.37436 14.1002 7.50322 14.1002C11.1483 14.1002 14.1032 11.1453 14.1032 7.50023C14.1032 7.25044 14.0893 7.00389 14.0623 6.76131L14.0255 6.48407C13.991 6.26083 13.9453 6.04129 13.8891 5.82642C13.8213 5.56709 13.7382 5.31398 13.6409 5.06881L13.5279 4.80132L13.4507 4.63542L13.3766 4.48666C13.2178 4.17773 13.0353 3.88295 12.8312 3.60423L12.6782 3.40352L12.4793 3.16432L12.3157 2.98361L12.1961 2.85951L12.0355 2.70246L11.8134 2.50184L11.4925 2.24191L11.2483 2.06498L10.9562 1.87446L10.6346 1.68894L10.3073 1.52378L10.1938 1.47176L9.95488 1.3706L9.67791 1.2669L9.42566 1.1846L9.10075 1.09489L8.83599 1.03486L8.54406 0.98184ZM10.4032 5.30023C10.4032 4.27588 10.2002 3.29829 9.83244 2.40604C11.7623 3.28995 13.1032 5.23862 13.1032 7.50023C13.1032 10.593 10.596 13.1002 7.50322 13.1002C6.63646 13.1002 5.81597 12.9036 5.08355 12.5522C6.5419 12.0941 7.81081 11.2082 8.74322 10.0416C8.87963 10.2284 9.10028 10.3497 9.34928 10.3497C9.76349 10.3497 10.0993 10.0139 10.0993 9.59971C10.0993 9.24256 9.84965 8.94373 9.51535 8.86816C9.57741 8.75165 9.63653 8.63334 9.6926 8.51332C9.88358 8.63163 10.1088 8.69993 10.35 8.69993C11.0403 8.69993 11.6 8.14028 11.6 7.44993C11.6 6.75976 11.0406 6.20024 10.3505 6.19993C10.3853 5.90487 10.4032 5.60464 10.4032 5.30023Z",fill:void 0===r?"currentColor":r,fillRule:"evenodd",clipRule:"evenodd"}))}),u=["color"],l=(0,n.forwardRef)(function(e,t){var r=e.color,a=o(e,u);return(0,n.createElement)("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a,{ref:t}),(0,n.createElement)("path",{d:"M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z",fill:void 0===r?"currentColor":r,fillRule:"evenodd",clipRule:"evenodd"}))})},68861:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},74157:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),r(59110);let n=r(37058);function o(e,t,r){void 0===r&&(r=!0);let o=new URL("http://n"),a=t?new URL(t,o):e.startsWith(".")?new URL("http://n"):o,{pathname:i,searchParams:u,search:l,hash:c,href:s,origin:f}=new URL(e,a);if(f!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:i,query:r?(0,n.searchParamsToUrlQuery)(u):void 0,search:l,hash:c,href:s.slice(f.length)}}},74369:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return i}});let n=r(22680),o=r(90762);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function i(e){return e.replace(/\.rsc($|\?)/,"$1")}},74794:(e,t,r)=>{"use strict";r.d(t,{RG:()=>R,bL:()=>S,q7:()=>j});var n=r(57752),o=r(46769),a=r(46691),i=r(46854),u=r(1493),l=r(42703),c=r(56750),s=r(18526),f=r(58785),d=r(44804),p=r(99730),m="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,E]=(0,a.N)(v),[b,R]=(0,u.A)(v,[E]),[w,x]=b(v),C=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(_,{...e,ref:t})})}));C.displayName=v;var _=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:u=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:E,onCurrentTabStopIdChange:b,onEntryFocus:R,preventScrollOnEntryFocus:x=!1,...C}=e,_=n.useRef(null),P=(0,i.s)(t,_),A=(0,d.jH)(l),[M,S]=(0,f.i)({prop:g,defaultProp:E??null,onChange:b,caller:v}),[j,O]=n.useState(!1),L=(0,s.c)(R),N=y(r),k=n.useRef(!1),[I,D]=n.useState(0);return n.useEffect(()=>{let e=_.current;if(e)return e.addEventListener(m,L),()=>e.removeEventListener(m,L)},[L]),(0,p.jsx)(w,{scope:r,orientation:a,dir:A,loop:u,currentTabStopId:M,onItemFocus:n.useCallback(e=>S(e),[S]),onItemShiftTab:n.useCallback(()=>O(!0),[]),onFocusableItemAdd:n.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>D(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:j||0===I?-1:0,"data-orientation":a,...C,ref:P,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(m,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),x)}}k.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>O(!1))})})}),P="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:u,children:s,...f}=e,d=(0,l.B)(),m=u||d,h=x(P,r),v=h.currentTabStopId===m,E=y(r),{onFocusableItemAdd:b,onFocusableItemRemove:R,currentTabStopId:w}=h;return n.useEffect(()=>{if(a)return b(),()=>R()},[a,b,R]),(0,p.jsx)(g.ItemSlot,{scope:r,id:m,focusable:a,active:i,children:(0,p.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":h.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?h.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return M[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=E().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>T(r))}}),children:"function"==typeof s?s({isCurrentTabStop:v,hasTabStop:null!=w}):s})})});A.displayName=P;var M={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var S=C,j=A},98133:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(59110);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},i={};for(let[e,t]of Object.entries(r)){let r=o[t.pos];void 0!==r&&(t.repeat?i[e]=r.split("/").map(e=>a(e)):i[e]=a(r))}return i}}}};